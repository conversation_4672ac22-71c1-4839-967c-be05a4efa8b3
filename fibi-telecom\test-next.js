const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('Current directory:', process.cwd());
console.log('App directory exists:', fs.existsSync(path.join(process.cwd(), 'app')));
console.log('App directory contents:', fs.readdirSync(path.join(process.cwd(), 'app')));

try {
  const result = execSync('npx next dev --help', { encoding: 'utf8' });
  console.log('Next.js help:', result);
} catch (error) {
  console.error('Error running next:', error.message);
}
