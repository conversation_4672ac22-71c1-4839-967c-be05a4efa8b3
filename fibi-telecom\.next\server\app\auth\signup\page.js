(()=>{var e={};e.id=778,e.ids=[778],e.modules={58:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>h});var r=s(687),a=s(3210),i=s(5814),n=s.n(i),l=s(6189),o=s(6391),d=s(8340),c=s(8869),u=s(3931),m=s(4021),p=s(2597),x=s(3861);function h(){let[e,t]=(0,a.useState)(""),[s,i]=(0,a.useState)(""),[h,b]=(0,a.useState)(""),[f,v]=(0,a.useState)(!1),[y,g]=(0,a.useState)(!1),[j,N]=(0,a.useState)(""),[w,P]=(0,a.useState)(!1),k=(0,l.useRouter)(),q=async t=>{t.preventDefault(),g(!0),N("");try{let{data:t,error:r}=await o.N.auth.signUp({email:e,password:s,options:{data:{full_name:h}}});if(r)return void N(r.message);t.user&&(P(!0),setTimeout(()=>{k.push("/auth/login")},2e3))}catch(e){N("Произошла ошибка при регистрации")}finally{g(!1)}};return w?(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:(0,r.jsx)("div",{className:"max-w-md w-full space-y-8",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"flex justify-center",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(d.A,{className:"h-12 w-12 text-blue-600 mr-2"}),(0,r.jsx)("span",{className:"text-3xl font-bold text-gray-900",children:"Fibi Telecom"})]})}),(0,r.jsx)("h2",{className:"mt-6 text-3xl font-extrabold text-gray-900",children:"Регистрация успешна!"}),(0,r.jsx)("p",{className:"mt-2 text-gray-600",children:"Проверьте вашу почту для подтверждения аккаунта. Вы будете перенаправлены на страницу входа..."})]})})}):(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"flex justify-center",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(d.A,{className:"h-12 w-12 text-blue-600 mr-2"}),(0,r.jsx)("span",{className:"text-3xl font-bold text-gray-900",children:"Fibi Telecom"})]})}),(0,r.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Создать аккаунт"}),(0,r.jsxs)("p",{className:"mt-2 text-center text-sm text-gray-600",children:["Или"," ",(0,r.jsx)(n(),{href:"/auth/login",className:"font-medium text-blue-600 hover:text-blue-500",children:"войти в существующий аккаунт"})]})]}),(0,r.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:q,children:[(0,r.jsxs)("div",{className:"rounded-md shadow-sm -space-y-px",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"fullName",className:"sr-only",children:"Полное имя"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,r.jsx)(c.A,{className:"h-5 w-5 text-gray-400"})}),(0,r.jsx)("input",{id:"fullName",name:"fullName",type:"text",autoComplete:"name",required:!0,className:"appearance-none rounded-none relative block w-full px-3 py-2 pl-10 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm",placeholder:"Полное имя",value:h,onChange:e=>b(e.target.value)})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"email",className:"sr-only",children:"Email адрес"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,r.jsx)(u.A,{className:"h-5 w-5 text-gray-400"})}),(0,r.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,className:"appearance-none rounded-none relative block w-full px-3 py-2 pl-10 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm",placeholder:"Email адрес",value:e,onChange:e=>t(e.target.value)})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"password",className:"sr-only",children:"Пароль"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,r.jsx)(m.A,{className:"h-5 w-5 text-gray-400"})}),(0,r.jsx)("input",{id:"password",name:"password",type:f?"text":"password",autoComplete:"new-password",required:!0,className:"appearance-none rounded-none relative block w-full px-3 py-2 pl-10 pr-10 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm",placeholder:"Пароль (минимум 6 символов)",value:s,onChange:e=>i(e.target.value),minLength:6}),(0,r.jsx)("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center",children:(0,r.jsx)("button",{type:"button",className:"text-gray-400 hover:text-gray-600",onClick:()=>v(!f),children:f?(0,r.jsx)(p.A,{className:"h-5 w-5"}):(0,r.jsx)(x.A,{className:"h-5 w-5"})})})]})]})]}),j&&(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded",children:j}),(0,r.jsxs)("div",{className:"text-xs text-gray-500",children:["Создавая аккаунт, вы соглашаетесь с нашими"," ",(0,r.jsx)("a",{href:"#",className:"text-blue-600 hover:text-blue-500",children:"Условиями использования"})," ","и"," ",(0,r.jsx)("a",{href:"#",className:"text-blue-600 hover:text-blue-500",children:"Политикой конфиденциальности"})]}),(0,r.jsx)("div",{children:(0,r.jsx)("button",{type:"submit",disabled:y,className:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",children:y?"Создание аккаунта...":"Создать аккаунт"})}),(0,r.jsx)("div",{className:"text-center",children:(0,r.jsx)(n(),{href:"/",className:"font-medium text-blue-600 hover:text-blue-500",children:"← Вернуться на главную"})})]})]})})}},440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(1658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1135:()=>{},1630:e=>{"use strict";e.exports=require("http")},1645:e=>{"use strict";e.exports=require("net")},1997:e=>{"use strict";e.exports=require("punycode")},2597:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(2688).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3861:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(2688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},3873:e=>{"use strict";e.exports=require("path")},3931:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(2688).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},4021:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(2688).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},4075:e=>{"use strict";e.exports=require("zlib")},4431:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d,metadata:()=>o});var r=s(7413),a=s(2376),i=s.n(a),n=s(8726),l=s.n(n);s(1135);let o={title:"Create Next App",description:"Generated by create next app"};function d({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsx)("body",{className:`${i().variable} ${l().variable} antialiased`,children:e})})}},4631:e=>{"use strict";e.exports=require("tls")},4735:e=>{"use strict";e.exports=require("events")},4796:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Fibi-telesominication\\\\fibi-telecom\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Fibi-telesominication\\fibi-telecom\\src\\app\\auth\\signup\\page.tsx","default")},5089:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var r=s(5239),a=s(8088),i=s(8170),n=s.n(i),l=s(893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let d={children:["",{children:["auth",{children:["signup",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,4796)),"C:\\Users\\<USER>\\Fibi-telesominication\\fibi-telecom\\src\\app\\auth\\signup\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,4431)),"C:\\Users\\<USER>\\Fibi-telesominication\\fibi-telecom\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Fibi-telesominication\\fibi-telecom\\src\\app\\auth\\signup\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/auth/signup/page",pathname:"/auth/signup",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},5511:e=>{"use strict";e.exports=require("crypto")},5591:e=>{"use strict";e.exports=require("https")},6234:()=>{},6391:(e,t,s)=>{"use strict";s.d(t,{N:()=>r});let r=(0,s(4025).UU)("your_supabase_url_here","your_supabase_anon_key_here")},6427:(e,t,s)=>{Promise.resolve().then(s.bind(s,4796))},6515:(e,t,s)=>{Promise.resolve().then(s.bind(s,58))},7910:e=>{"use strict";e.exports=require("stream")},8835:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6346,23)),Promise.resolve().then(s.t.bind(s,7924,23)),Promise.resolve().then(s.t.bind(s,5656,23)),Promise.resolve().then(s.t.bind(s,99,23)),Promise.resolve().then(s.t.bind(s,8243,23)),Promise.resolve().then(s.t.bind(s,8827,23)),Promise.resolve().then(s.t.bind(s,2763,23)),Promise.resolve().then(s.t.bind(s,7173,23))},8869:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(2688).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9282:()=>{},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9428:e=>{"use strict";e.exports=require("buffer")},9515:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6444,23)),Promise.resolve().then(s.t.bind(s,6042,23)),Promise.resolve().then(s.t.bind(s,8170,23)),Promise.resolve().then(s.t.bind(s,9477,23)),Promise.resolve().then(s.t.bind(s,9345,23)),Promise.resolve().then(s.t.bind(s,2089,23)),Promise.resolve().then(s.t.bind(s,6577,23)),Promise.resolve().then(s.t.bind(s,1307,23))},9551:e=>{"use strict";e.exports=require("url")}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[447,145,658,552,814],()=>s(5089));module.exports=r})();