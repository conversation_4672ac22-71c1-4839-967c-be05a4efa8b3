import { NextRequest, NextResponse } from 'next/server'
import { stripe } from '@/lib/stripe'
import { createClient } from '@/lib/supabase-server'
import { headers } from 'next/headers'

export async function POST(request: NextRequest) {
  const body = await request.text()
  const signature = headers().get('stripe-signature')

  if (!signature) {
    return NextResponse.json(
      { error: 'Missing stripe signature' },
      { status: 400 }
    )
  }

  let event

  try {
    event = stripe.webhooks.constructEvent(
      body,
      signature,
      process.env.STRIPE_WEBHOOK_SECRET!
    )
  } catch (error) {
    console.error('Webhook signature verification failed:', error)
    return NextResponse.json(
      { error: 'Invalid signature' },
      { status: 400 }
    )
  }

  const supabase = createClient()

  try {
    switch (event.type) {
      case 'payment_intent.succeeded': {
        const paymentIntent = event.data.object
        
        // Обновляем статус платежа в базе данных
        const { error } = await supabase
          .from('payments')
          .update({
            status: 'completed',
            completed_at: new Date().toISOString()
          })
          .eq('stripe_payment_intent_id', paymentIntent.id)

        if (error) {
          console.error('Error updating payment status:', error)
        }

        // Активируем услугу для пользователя
        if (paymentIntent.metadata.userId && paymentIntent.metadata.serviceId) {
          const { error: serviceError } = await supabase
            .from('user_services')
            .upsert({
              user_id: paymentIntent.metadata.userId,
              service_id: paymentIntent.metadata.serviceId,
              status: 'active',
              started_at: new Date().toISOString(),
              expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() // 30 дней
            })

          if (serviceError) {
            console.error('Error activating service:', serviceError)
          }
        }

        break
      }

      case 'payment_intent.payment_failed': {
        const paymentIntent = event.data.object
        
        // Обновляем статус платежа
        const { error } = await supabase
          .from('payments')
          .update({
            status: 'failed',
            failed_at: new Date().toISOString()
          })
          .eq('stripe_payment_intent_id', paymentIntent.id)

        if (error) {
          console.error('Error updating failed payment:', error)
        }

        break
      }

      case 'invoice.payment_succeeded': {
        // Обработка успешной оплаты подписки
        const invoice = event.data.object
        console.log('Invoice payment succeeded:', invoice.id)
        break
      }

      case 'customer.subscription.deleted': {
        // Обработка отмены подписки
        const subscription = event.data.object
        console.log('Subscription deleted:', subscription.id)
        break
      }

      default:
        console.log(`Unhandled event type: ${event.type}`)
    }

    return NextResponse.json({ received: true })

  } catch (error) {
    console.error('Webhook processing error:', error)
    return NextResponse.json(
      { error: 'Webhook processing failed' },
      { status: 500 }
    )
  }
}
