import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase-server'
import { z } from 'zod'

const contactSchema = z.object({
  name: z.string().min(2, 'Имя должно содержать минимум 2 символа'),
  email: z.string().email('Введите корректный email'),
  phone: z.string().min(10, 'Введите корректный номер телефона'),
  message: z.string().min(10, 'Сообщение должно содержать минимум 10 символов')
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Валидация данных
    const validatedData = contactSchema.parse(body)
    
    // Создание клиента Supabase
    const supabase = createClient()
    
    // Сохранение в базу данных
    const { data, error } = await supabase
      .from('contact_messages')
      .insert([
        {
          name: validatedData.name,
          email: validatedData.email,
          phone: validatedData.phone,
          message: validatedData.message,
          status: 'new'
        }
      ])
      .select()
    
    if (error) {
      console.error('Database error:', error)
      return NextResponse.json(
        { error: 'Ошибка при сохранении сообщения' },
        { status: 500 }
      )
    }
    
    // Здесь можно добавить отправку email уведомления
    // await sendEmailNotification(validatedData)
    
    return NextResponse.json(
      { 
        message: 'Сообщение успешно отправлено',
        data: data[0]
      },
      { status: 201 }
    )
    
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: 'Ошибка валидации',
          details: error.errors
        },
        { status: 400 }
      )
    }
    
    console.error('Unexpected error:', error)
    return NextResponse.json(
      { error: 'Внутренняя ошибка сервера' },
      { status: 500 }
    )
  }
}

export async function GET() {
  return NextResponse.json(
    { message: 'Contact API endpoint' },
    { status: 200 }
  )
}
