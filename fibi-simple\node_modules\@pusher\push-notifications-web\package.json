{"name": "@pusher/push-notifications-web", "version": "1.1.0", "description": "", "main": "dist/push-notifications-esm.js", "types": "index.d.ts", "scripts": {"build:esm": "rollup -c ./rollup/esm.js", "build:cdn": "rollup -c ./rollup/cdn.js", "build:sw": "rollup -c ./rollup/service-worker.js && node ./scripts/add-version-to-service-worker.js", "format": "prettier ./src/**/*.js --write", "lint": "eslint ./src/**/*.js && prettier ./src/**/*.js -l", "test": "npm run test:unit", "test:unit": "jest ./src/*", "test:ts": "tsd .", "test:e2e": "jest ./end-to-end-tests/* --runInBand", "prepublishchecks": "npm run lint && npm run test:unit && npm run build:cdn && npm run build:esm && npm run test:e2e", "prepublishOnly": "publish-please guard", "publish-please": "publish-please"}, "keywords": [], "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "devDependencies": {"@babel/core": "^7.4.3", "@babel/plugin-transform-runtime": "^7.4.4", "@babel/preset-env": "^7.4.3", "@babel/runtime": "^7.4.5", "babel-jest": "^24.7.1", "eslint": "^4.14.0", "eslint-config-prettier": "^4.0.0", "express": "^4.16.4", "jest": "^24.7.1", "jest-fetch-mock": "^2.1.2", "prettier": "1.17.0", "publish-please": "^5.5.1", "rollup": "^1.10.0", "rollup-plugin-babel": "^4.3.2", "rollup-plugin-commonjs": "^9.3.4", "rollup-plugin-json": "^4.0.0", "rollup-plugin-node-resolve": "^4.2.3", "selenium-webdriver": "^4.0.0-alpha.3", "tsd": "^0.13.1"}, "browserify": {"transform": [["babe<PERSON>", {"presets": ["es2015"]}]]}}