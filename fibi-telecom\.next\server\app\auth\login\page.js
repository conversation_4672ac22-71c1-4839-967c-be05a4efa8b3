(()=>{var e={};e.id=859,e.ids=[859],e.modules={440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(1658);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1135:()=>{},1351:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Fibi-telesominication\\\\fibi-telecom\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Fibi-telesominication\\fibi-telecom\\src\\app\\auth\\login\\page.tsx","default")},1630:e=>{"use strict";e.exports=require("http")},1645:e=>{"use strict";e.exports=require("net")},1997:e=>{"use strict";e.exports=require("punycode")},2597:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(2688).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},2915:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var s=r(5239),i=r(8088),a=r(8170),n=r.n(a),o=r(893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d={children:["",{children:["auth",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,1351)),"C:\\Users\\<USER>\\Fibi-telesominication\\fibi-telecom\\src\\app\\auth\\login\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"C:\\Users\\<USER>\\Fibi-telesominication\\fibi-telecom\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Fibi-telesominication\\fibi-telecom\\src\\app\\auth\\login\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/auth/login/page",pathname:"/auth/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3861:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(2688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},3873:e=>{"use strict";e.exports=require("path")},3931:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(2688).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},4021:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(2688).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},4075:e=>{"use strict";e.exports=require("zlib")},4096:(e,t,r)=>{Promise.resolve().then(r.bind(r,1351))},4431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d,metadata:()=>l});var s=r(7413),i=r(2376),a=r.n(i),n=r(8726),o=r.n(n);r(1135);let l={title:"Create Next App",description:"Generated by create next app"};function d({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{className:`${a().variable} ${o().variable} antialiased`,children:e})})}},4592:(e,t,r)=>{Promise.resolve().then(r.bind(r,9457))},4631:e=>{"use strict";e.exports=require("tls")},4735:e=>{"use strict";e.exports=require("events")},5511:e=>{"use strict";e.exports=require("crypto")},5591:e=>{"use strict";e.exports=require("https")},6234:()=>{},6391:(e,t,r)=>{"use strict";r.d(t,{N:()=>s});let s=(0,r(4025).UU)("your_supabase_url_here","your_supabase_anon_key_here")},7910:e=>{"use strict";e.exports=require("stream")},8835:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9282:()=>{},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9428:e=>{"use strict";e.exports=require("buffer")},9457:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>x});var s=r(687),i=r(3210),a=r(5814),n=r.n(a),o=r(6189),l=r(6391),d=r(8340),c=r(3931),u=r(4021),m=r(2597),p=r(3861);function x(){let[e,t]=(0,i.useState)(""),[r,a]=(0,i.useState)(""),[x,h]=(0,i.useState)(!1),[b,f]=(0,i.useState)(!1),[v,y]=(0,i.useState)(""),g=(0,o.useRouter)(),j=async t=>{t.preventDefault(),f(!0),y("");try{let{data:t,error:s}=await l.N.auth.signInWithPassword({email:e,password:r});if(s)return void y(s.message);if(t.user){let{data:e}=await l.N.from("profiles").select("role").eq("id",t.user.id).single();e?.role==="admin"?g.push("/admin"):g.push("/dashboard")}}catch(e){y("Произошла ошибка при входе")}finally{f(!1)}};return(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"flex justify-center",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(d.A,{className:"h-12 w-12 text-blue-600 mr-2"}),(0,s.jsx)("span",{className:"text-3xl font-bold text-gray-900",children:"Fibi Telecom"})]})}),(0,s.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Войти в аккаунт"}),(0,s.jsxs)("p",{className:"mt-2 text-center text-sm text-gray-600",children:["Или"," ",(0,s.jsx)(n(),{href:"/auth/signup",className:"font-medium text-blue-600 hover:text-blue-500",children:"создать новый аккаунт"})]})]}),(0,s.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:j,children:[(0,s.jsxs)("div",{className:"rounded-md shadow-sm -space-y-px",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"sr-only",children:"Email адрес"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)(c.A,{className:"h-5 w-5 text-gray-400"})}),(0,s.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,className:"appearance-none rounded-none relative block w-full px-3 py-2 pl-10 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm",placeholder:"Email адрес",value:e,onChange:e=>t(e.target.value)})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"password",className:"sr-only",children:"Пароль"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)(u.A,{className:"h-5 w-5 text-gray-400"})}),(0,s.jsx)("input",{id:"password",name:"password",type:x?"text":"password",autoComplete:"current-password",required:!0,className:"appearance-none rounded-none relative block w-full px-3 py-2 pl-10 pr-10 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm",placeholder:"Пароль",value:r,onChange:e=>a(e.target.value)}),(0,s.jsx)("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center",children:(0,s.jsx)("button",{type:"button",className:"text-gray-400 hover:text-gray-600",onClick:()=>h(!x),children:x?(0,s.jsx)(m.A,{className:"h-5 w-5"}):(0,s.jsx)(p.A,{className:"h-5 w-5"})})})]})]})]}),v&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded",children:v}),(0,s.jsx)("div",{className:"flex items-center justify-between",children:(0,s.jsx)("div",{className:"text-sm",children:(0,s.jsx)("a",{href:"#",className:"font-medium text-blue-600 hover:text-blue-500",children:"Забыли пароль?"})})}),(0,s.jsx)("div",{children:(0,s.jsx)("button",{type:"submit",disabled:b,className:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",children:b?"Вход...":"Войти"})}),(0,s.jsx)("div",{className:"text-center",children:(0,s.jsx)(n(),{href:"/",className:"font-medium text-blue-600 hover:text-blue-500",children:"← Вернуться на главную"})})]})]})})}},9515:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))},9551:e=>{"use strict";e.exports=require("url")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,145,658,552,814],()=>r(2915));module.exports=s})();