{"version": 3, "sources": ["webpack://Pusher/webpack/bootstrap", "webpack://Pusher/./src/core/utils/collections.ts", "webpack://Pusher/./src/core/logger.ts", "webpack://Pusher/./src/core/util.ts", "webpack://Pusher/./src/core/events/dispatcher.ts", "webpack://Pusher/./src/core/utils/timers/index.ts", "webpack://Pusher/./src/core/defaults.ts", "webpack://Pusher/./src/core/transports/url_schemes.ts", "webpack://Pusher/./src/core/transports/transport_connection.ts", "webpack://Pusher/./src/core/transports/transport.ts", "webpack://Pusher/./src/runtimes/isomorphic/transports/transports.ts", "webpack://Pusher/./src/core/transports/assistant_to_the_transport_manager.ts", "webpack://Pusher/./src/core/connection/protocol/protocol.ts", "webpack://Pusher/./src/core/connection/connection.ts", "webpack://Pusher/./src/core/connection/handshake/index.ts", "webpack://Pusher/./src/core/timeline/timeline_sender.ts", "webpack://Pusher/./src/core/errors.ts", "webpack://Pusher/./src/core/utils/url_store.ts", "webpack://Pusher/./src/core/channels/channel.ts", "webpack://Pusher/./src/core/channels/private_channel.ts", "webpack://Pusher/./src/core/channels/members.ts", "webpack://Pusher/./src/core/channels/presence_channel.ts", "webpack://Pusher/./src/core/channels/encrypted_channel.ts", "webpack://Pusher/./src/core/connection/connection_manager.ts", "webpack://Pusher/./src/core/channels/channels.ts", "webpack://Pusher/./src/core/utils/factory.ts", "webpack://Pusher/./src/core/transports/transport_manager.ts", "webpack://Pusher/./src/core/strategies/sequential_strategy.ts", "webpack://Pusher/./src/core/strategies/best_connected_ever_strategy.ts", "webpack://Pusher/./src/core/strategies/websocket_prioritized_cached_strategy.ts", "webpack://Pusher/./src/core/strategies/delayed_strategy.ts", "webpack://Pusher/./src/core/strategies/if_strategy.ts", "webpack://Pusher/./src/core/strategies/first_connected_strategy.ts", "webpack://Pusher/./src/runtimes/isomorphic/default_strategy.ts", "webpack://Pusher/./src/core/http/http_request.ts", "webpack://Pusher/./src/core/http/state.ts", "webpack://Pusher/./src/core/http/http_socket.ts", "webpack://Pusher/./src/core/http/http_streaming_socket.ts", "webpack://Pusher/./src/core/http/http_polling_socket.ts", "webpack://Pusher/./src/runtimes/isomorphic/http/http_xhr_request.ts", "webpack://Pusher/./src/runtimes/isomorphic/runtime.ts", "webpack://Pusher/./src/runtimes/isomorphic/transports/transport_connection_initializer.ts", "webpack://Pusher/./src/runtimes/isomorphic/http/http.ts", "webpack://Pusher/./src/runtimes/react-native/net_info.ts", "webpack://Pusher/./src/core/auth/options.ts", "webpack://Pusher/./src/runtimes/isomorphic/auth/xhr_auth.ts", "webpack://Pusher/./src/runtimes/isomorphic/timeline/xhr_timeline.ts", "webpack://Pusher/./src/runtimes/react-native/runtime.ts", "webpack://Pusher/./src/core/timeline/level.ts", "webpack://Pusher/./src/core/timeline/timeline.ts", "webpack://Pusher/./src/core/strategies/transport_strategy.ts", "webpack://Pusher/./src/core/strategies/strategy_builder.ts", "webpack://Pusher/./src/core/auth/user_authenticator.ts", "webpack://Pusher/./src/core/auth/channel_authorizer.ts", "webpack://Pusher/./src/core/config.ts", "webpack://Pusher/./src/core/auth/deprecated_channel_authorizer.ts", "webpack://Pusher/./src/core/watchlist.ts", "webpack://Pusher/./src/core/utils/flat_promise.ts", "webpack://Pusher/./src/core/user.ts", "webpack://Pusher/./src/core/pusher.ts", "webpack://Pusher/(webpack)/buildin/global.js", "webpack://Pusher/./src/core/options.ts", "webpack://Pusher/./node_modules/@stablelib/base64/base64.ts", "webpack://Pusher/./src/core/utils/timers/abstract_timer.ts", "webpack://Pusher/external \"@react-native-community/netinfo\"", "webpack://Pusher/./src/core/base64.ts", "webpack://Pusher/./src/core/events/callback_registry.ts", "webpack://Pusher/./node_modules/@stablelib/utf8/utf8.ts", "webpack://Pusher/./node_modules/tweetnacl/nacl-fast.js", "webpack://Pusher/./src/core/pusher-with-encryption.js", "webpack://Pusher/./src/core/pusher-with-encryption.ts"], "names": ["installedModules", "__webpack_require__", "moduleId", "exports", "module", "i", "l", "modules", "call", "m", "c", "d", "name", "getter", "o", "Object", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "object", "property", "prototype", "hasOwnProperty", "p", "s", "extend", "target", "sources", "length", "extensions", "constructor", "stringify", "arguments", "push", "safeJSONStringify", "join", "arrayIndexOf", "array", "item", "nativeIndexOf", "Array", "indexOf", "objectApply", "f", "keys", "_", "values", "apply", "context", "global", "map", "result", "filter", "test", "filterObject", "Boolean", "any", "all", "encodeParamsObject", "data", "encodeURIComponent", "toString", "buildQueryString", "params", "undefined", "method", "source", "JSON", "e", "objects", "paths", "derez", "path", "nu", "$ref", "globalLog", "message", "console", "log", "args", "this", "globalLogWarn", "globalLogError", "warn", "error", "defaultLoggingFunction", "logToConsole", "<PERSON><PERSON>", "now", "Date", "valueOf", "defer", "callback", "boundArguments", "slice", "concat", "Di<PERSON>atcher", "failThrough", "callbacks", "global_callbacks", "eventName", "add", "remove", "unbind", "unbind_global", "metadata", "fn", "clearTimeout", "timer", "clearInterval", "OneOffTimer", "delay", "super", "setTimeout", "PeriodicTimer", "setInterval", "VERSION", "PROTOCOL", "wsPort", "wssPort", "wsPath", "httpHost", "httpPort", "httpsPort", "httpPath", "stats_host", "authEndpoint", "authTransport", "activityTimeout", "pongTimeout", "unavailableTimeout", "userAuthentication", "endpoint", "transport", "channelAuthorization", "cdn_http", "cdn_https", "dependency_suffix", "getGenericURL", "baseScheme", "useTLS", "hostTLS", "hostNonTLS", "getGenericPath", "queryString", "ws", "getInitial", "http", "hooks", "priority", "options", "initialize", "transportConnectionInitializer", "state", "timeline", "id", "generateUniqueID", "handlesActivityChecks", "supportsPing", "socket", "url", "urls", "getSocket", "onError", "changeState", "bindListeners", "debug", "close", "send", "ping", "beforeOpen", "<PERSON><PERSON><PERSON>", "onopen", "emit", "type", "buildTimelineMessage", "closeEvent", "code", "reason", "<PERSON><PERSON><PERSON>", "unbindListeners", "onOpen", "onerror", "onclose", "onClose", "onmessage", "onMessage", "onactivity", "onActivity", "info", "cid", "environment", "isSupported", "WSTransport", "isInitialized", "getWebSocketAPI", "createWebSocket", "httpConfiguration", "streamingConfiguration", "HTTPFactory", "createStreamingSocket", "pollingConfiguration", "createPollingSocket", "xhrConfiguration", "isXHRSupported", "xhr_streaming", "xhr_polling", "manager", "min<PERSON>ing<PERSON>elay", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "ping<PERSON><PERSON><PERSON>", "connection", "createConnection", "openTimestamp", "onClosed", "reportDeath", "lifespan", "Math", "max", "isAlive", "Protocol", "decodeMessage", "messageEvent", "messageData", "parse", "pusherEventData", "pusherEvent", "event", "channel", "user_id", "encodeMessage", "processHandshake", "activity_timeout", "action", "socket_id", "getCloseAction", "getCloseError", "send_event", "listeners", "activity", "closed", "handleCloseEvent", "listener", "finish", "isEmpty", "TimelineTransport", "getAgent", "BadEventName", "Error", "msg", "setPrototypeOf", "BadChannelName", "TransportPriorityTooLow", "TransportClosed", "UnsupportedFeature", "UnsupportedTransport", "UnsupportedStrategy", "HTTPAuthError", "status", "urlStore", "baseUrl", "authenticationEndpoint", "authorizationEndpoint", "javascriptQuickStart", "triggeringClientEvents", "encryptedChannelSupport", "fullUrl", "url<PERSON>bj", "pusher", "subscribed", "subscriptionPending", "subscriptionCancelled", "socketId", "auth", "suffix", "handleSubscriptionSucceededEvent", "handleSubscriptionCountEvent", "unsubscribe", "subscription_count", "subscriptionCount", "authorize", "assign", "channel_data", "config", "channelAuthorizer", "channelName", "reset", "members", "member", "myID", "subscriptionData", "presence", "hash", "count", "me", "memberData", "user_info", "authData", "channelData", "setMyID", "user", "signinDonePromise", "user_data", "handleInternalEvent", "addedMember", "addMember", "removedMember", "removeMember", "onSubscription", "disconnect", "nacl", "sharedSecret", "handleEncryptedEvent", "handleEvent", "ciphertext", "nonce", "cipherText", "secretbox", "overheadLength", "non<PERSON><PERSON><PERSON><PERSON>", "bytes", "open", "getDataToEmit", "raw", "usingTLS", "errorCallbacks", "buildErrorCallbacks", "connectionCallbacks", "buildConnectionCallbacks", "handshakeCallbacks", "buildHandshakeCallbacks", "Network", "getNetwork", "netinfo", "retryIn", "sendActivityCheck", "updateStrategy", "runner", "strategy", "updateState", "startConnecting", "setUnavailableTimer", "disconnectInternally", "handshake", "connect", "handshake<PERSON><PERSON><PERSON>", "abortConnecting", "abort", "clearRetryTimer", "clearUnavailableTimer", "abandonConnection", "getStrategy", "round", "retryTimer", "ensureAborted", "unavailableTimer", "stopActivityCheck", "activityTimer", "pong_timed_out", "resetActivity<PERSON>heck", "shouldRetry", "connected", "min", "Infinity", "setConnection", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tls_only", "refused", "backoff", "retry", "newState", "previousState", "newStateDescription", "previous", "current", "channels", "createEncryptedChannel", "errMsg", "createPrivateChannel", "createPresenceChannel", "createChannel", "createChannels", "createConnectionManager", "createTimelineSender", "createHandshake", "createAssistantToTheTransportManager", "livesLeft", "lives", "strategies", "loop", "failFast", "timeout", "timeoutLimit", "minPriority", "tryNextStrategy", "tryStrategy", "forceMinPriority", "isRunning", "callbackBuilder", "runners", "rs", "abort<PERSON><PERSON><PERSON>", "allRunnersFailed", "aborted", "transports", "ttl", "storage", "getLocalStorage", "serializedCache", "getTransportCacheKey", "flushTransportCache", "fetchTransportCache", "cacheSkipCount", "timestamp", "includes", "cached", "latency", "startTimestamp", "pop", "cb", "storeTransportCache", "number", "IfStrategy", "trueBranch", "falseBranch", "FirstConnectedStrategy", "testSupportsStrategy", "baseOptions", "defineTransport", "definedTransports", "defineTransportStrategy", "wsStrategy", "ws_options", "wsHost", "wss_options", "http_options", "timeouts", "ws_manager", "streaming_manager", "ws_transport", "wss_transport", "xhr_streaming_transport", "xhr_polling_transport", "ws_loop", "wss_loop", "streaming_loop", "polling_loop", "http_loop", "payload", "position", "xhr", "getRequest", "unloader", "addUnloadListener", "setRequestHeader", "removeUnloadListener", "abortRequest", "chunk", "advanceBuffer", "isBufferTooLong", "buffer", "unreadData", "endOfLinePosition", "State", "autoIncrement", "getUniqueURL", "separator", "randomNumber", "randomInt", "session", "randomString", "location", "parts", "exec", "base", "getLocation", "readyState", "CONNECTING", "openStream", "sendRaw", "sendHeartbeat", "OPEN", "createSocketRequest", "start", "closeStream", "CLOSED", "onEvent", "onHeartbeat", "hostname", "urlParts", "stream", "getReceiveURL", "onChunk", "onFinished", "reconnect", "unbind_all", "getXHRAPI", "onreadystatechange", "onprogress", "responseText", "getDefaultStrategy", "Transports", "createSocket", "createRequest", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ready", "getClientFeatures", "getProtocol", "createXHR", "hasOnlineConnectionState", "connectionState", "toLowerCase", "online", "fetch", "then", "addEventListener", "isNowOnline", "AuthRequestType", "query", "authOptions", "authRequestType", "headerName", "headers", "headers<PERSON>rovider", "dynamicHeaders", "parsed", "UserAuthentication", "ChannelAuthorization", "sender", "host", "setup", "TimelineLevel", "getAuthorizers", "ajax", "WebSocket", "XMLHttpRequest", "floor", "random", "events", "sent", "uniqueID", "level", "limit", "shift", "ERROR", "INFO", "DEBUG", "sendfn", "bundle", "lib", "version", "cluster", "features", "failAttempt", "onInitialized", "serializedTransport", "transportClass", "enabledTransports", "disabledTransports", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getAssistant", "deferred", "params<PERSON>rov<PERSON>", "dynamicParams", "composeChannel<PERSON><PERSON>y", "getHttpHost", "opts", "getWebsocketHost", "shouldUseTLS", "forceTLS", "getEnableStatsConfig", "enableStats", "disableStats", "buildUserAuthenticator", "buildChannelAuthorizer", "customHandler", "channelAuthorizerGenerator", "deprecatedAuthorizerOptions", "ChannelAuthorizerProxy", "authorizer", "buildChannelAuth", "bindWatchlistInternalEvent", "for<PERSON>ach", "watchlistEvent", "resolve", "reject", "promise", "Promise", "res", "rej", "signin_requested", "serverToUserChannel", "_signinDoneResolve", "_onAuthorize", "err", "_cleanup", "_signin", "_newSigninPromiseIfNeeded", "watchlist", "_onSigninSuccess", "userAuthenticator", "_subscribeChannels", "bind_global", "reinstateSubscription", "subscribe", "ensure_subscribed", "done", "setDone", "catch", "isReady", "instances", "app_key", "check<PERSON><PERSON><PERSON><PERSON>", "statsHost", "timelineParams", "getConfig", "global_emitter", "sessionID", "timelineSender", "subscribeAll", "isUsingTLS", "internal", "find", "timelineSenderTimer", "event_name", "channel_name", "cancelSubscription", "signin", "Runtime", "ScriptReceivers", "DependenciesReceivers", "auth_callbacks", "g", "Function", "window", "validateOptions", "_padding<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "encode", "out", "_encodeByte", "left", "maxDecoded<PERSON><PERSON>th", "decodedLength", "_getPaddingLength", "decode", "Uint8Array", "paddingLength", "op", "haveBad", "v0", "v1", "v2", "v3", "_decodeChar", "charCodeAt", "b", "String", "fromCharCode", "Coder", "stdCoder", "URLSafeCoder", "urlSafeCoder", "set", "clear", "require", "btoa", "utob", "b64chars", "b64tab", "char<PERSON>t", "cb_utob", "cc", "u", "replace", "cb_encode", "ccc", "padlen", "ord", "CallbackRegistry", "_callbacks", "prefix", "prefixedEventName", "names", "removeCallback", "removeAllCallbacks", "binding", "INVALID_UTF8", "arr", "pos", "chars", "n1", "n2", "n3", "gf", "init", "Float64Array", "randombytes", "_0", "_9", "gf0", "gf1", "_121665", "D", "D2", "X", "Y", "I", "ts64", "x", "h", "vn", "xi", "y", "yi", "crypto_verify_16", "crypto_verify_32", "crypto_core_salsa20", "inp", "k", "j0", "j1", "j2", "j3", "j4", "j5", "j6", "j7", "j8", "j9", "j10", "j11", "j12", "j13", "j14", "j15", "x0", "x1", "x2", "x3", "x4", "x5", "x6", "x7", "x8", "x9", "x10", "x11", "x12", "x13", "x14", "x15", "core_salsa20", "crypto_core_hsalsa20", "core_hsalsa20", "sigma", "crypto_stream_salsa20_xor", "cpos", "mpos", "z", "crypto_stream_salsa20", "crypto_stream", "sn", "crypto_stream_xor", "poly1305", "t0", "t1", "t2", "t3", "t4", "t5", "t6", "t7", "Uint16Array", "pad", "leftover", "fin", "crypto_onetimeauth", "outpos", "update", "crypto_onetimeauth_verify", "hpos", "crypto_secretbox", "crypto_secretbox_open", "set25519", "a", "car25519", "v", "sel25519", "q", "pack25519", "j", "neq25519", "par25519", "unpack25519", "A", "Z", "M", "t8", "t9", "t10", "t11", "t12", "t13", "t14", "t15", "t16", "t17", "t18", "t19", "t20", "t21", "t22", "t23", "t24", "t25", "t26", "t27", "t28", "t29", "t30", "b0", "b1", "b2", "b3", "b4", "b5", "b6", "b7", "b8", "b9", "b10", "b11", "b12", "b13", "b14", "b15", "S", "inv25519", "pow2523", "crypto_scalarmult", "x32", "subarray", "x16", "crypto_scalarmult_base", "crypto_box_keypair", "crypto_box_beforenm", "blocks", "d0", "d1", "d2", "d3", "d4", "d5", "d6", "d7", "d8", "d9", "hibit", "h0", "h1", "h2", "h3", "h4", "h5", "h6", "h7", "h8", "h9", "r0", "r1", "r2", "r3", "r4", "r5", "r6", "r7", "r8", "r9", "mac", "macpos", "mask", "want", "crypto_box_afternm", "crypto_box_open_afternm", "K", "crypto_hashblocks_hl", "hh", "hl", "bh0", "bh1", "bh2", "bh3", "bh4", "bh5", "bh6", "bh7", "bl0", "bl1", "bl2", "bl3", "bl4", "bl5", "bl6", "bl7", "th", "tl", "wh", "Int32Array", "wl", "ah0", "ah1", "ah2", "ah3", "ah4", "ah5", "ah6", "ah7", "al0", "al1", "al2", "al3", "al4", "al5", "al6", "al7", "crypto_hash", "cswap", "pack", "tx", "ty", "zi", "scalarmult", "scalarbase", "crypto_sign_keypair", "pk", "sk", "seeded", "L", "modL", "carry", "reduce", "crypto_sign", "sm", "smlen", "crypto_sign_open", "chk", "num", "den", "den2", "den4", "den6", "unpackneg", "checkLengths", "checkArrayTypes", "TypeError", "cleanup", "lowlevel", "crypto_box", "crypto_box_open", "crypto_secretbox_KEYBYTES", "crypto_secretbox_NONCEBYTES", "crypto_secretbox_ZEROBYTES", "crypto_secretbox_BOXZEROBYTES", "crypto_scalarmult_BYTES", "crypto_scalarmult_SCALARBYTES", "crypto_box_PUBLICKEYBYTES", "crypto_box_SECRETKEYBYTES", "crypto_box_BEFORENMBYTES", "crypto_box_NONCEBYTES", "crypto_box_ZEROBYTES", "crypto_box_BOXZEROBYTES", "crypto_sign_BYTES", "crypto_sign_PUBLICKEYBYTES", "crypto_sign_SECRETKEYBYTES", "crypto_sign_SEEDBYTES", "crypto_hash_BYTES", "randomBytes", "box", "<PERSON><PERSON><PERSON><PERSON>", "scalarMult", "scalar<PERSON>ength", "groupElementLength", "public<PERSON>ey", "secret<PERSON>ey", "before", "checkBoxLengths", "after", "keyPair", "fromSecretKey", "publicKeyLength", "secretKeyLength", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sign", "signed<PERSON>g", "tmp", "mlen", "detached", "sig", "verify", "fromSeed", "seed", "seedLength", "<PERSON><PERSON><PERSON><PERSON>", "hash<PERSON><PERSON><PERSON>", "setPRNG", "crypto", "self", "msCrypto", "getRandomValues", "default", "PusherWithEncryption"], "mappings": ";;;;;;;2BACE,IAAIA,EAAmB,GAGvB,SAASC,EAAoBC,GAG5B,GAAGF,EAAiBE,GACnB,OAAOF,EAAiBE,GAAUC,QAGnC,IAAIC,EAASJ,EAAiBE,GAAY,CACzCG,EAAGH,EACHI,GAAG,EACHH,QAAS,IAUV,OANAI,EAAQL,GAAUM,KAAKJ,EAAOD,QAASC,EAAQA,EAAOD,QAASF,GAG/DG,EAAOE,GAAI,EAGJF,EAAOD,QA0Df,OArDAF,EAAoBQ,EAAIF,EAGxBN,EAAoBS,EAAIV,EAGxBC,EAAoBU,EAAI,SAASR,EAASS,EAAMC,GAC3CZ,EAAoBa,EAAEX,EAASS,IAClCG,OAAOC,eAAeb,EAASS,EAAM,CAAEK,YAAY,EAAMC,IAAKL,KAKhEZ,EAAoBkB,EAAI,SAAShB,GACX,oBAAXiB,QAA0BA,OAAOC,aAC1CN,OAAOC,eAAeb,EAASiB,OAAOC,YAAa,CAAEC,MAAO,WAE7DP,OAAOC,eAAeb,EAAS,aAAc,CAAEmB,OAAO,KAQvDrB,EAAoBsB,EAAI,SAASD,EAAOE,GAEvC,GADU,EAAPA,IAAUF,EAAQrB,EAAoBqB,IAC/B,EAAPE,EAAU,OAAOF,EACpB,GAAW,EAAPE,GAA8B,iBAAVF,GAAsBA,GAASA,EAAMG,WAAY,OAAOH,EAChF,IAAII,EAAKX,OAAOY,OAAO,MAGvB,GAFA1B,EAAoBkB,EAAEO,GACtBX,OAAOC,eAAeU,EAAI,UAAW,CAAET,YAAY,EAAMK,MAAOA,IACtD,EAAPE,GAA4B,iBAATF,EAAmB,IAAI,IAAIM,KAAON,EAAOrB,EAAoBU,EAAEe,EAAIE,EAAK,SAASA,GAAO,OAAON,EAAMM,IAAQC,KAAK,KAAMD,IAC9I,OAAOF,GAIRzB,EAAoB6B,EAAI,SAAS1B,GAChC,IAAIS,EAAST,GAAUA,EAAOqB,WAC7B,WAAwB,OAAOrB,EAAgB,SAC/C,WAA8B,OAAOA,GAEtC,OADAH,EAAoBU,EAAEE,EAAQ,IAAKA,GAC5BA,GAIRZ,EAAoBa,EAAI,SAASiB,EAAQC,GAAY,OAAOjB,OAAOkB,UAAUC,eAAe1B,KAAKuB,EAAQC,IAGzG/B,EAAoBkC,EAAI,GAIjBlC,EAAoBA,EAAoBmC,EAAI,I,gCClFrD,2fAiBO,SAASC,EAAUC,KAAgBC,GACxC,IAAK,IAAIlC,EAAI,EAAGA,EAAIkC,EAAQC,OAAQnC,IAAK,CACvC,IAAIoC,EAAaF,EAAQlC,GACzB,IAAK,IAAI2B,KAAYS,EAEjBA,EAAWT,IACXS,EAAWT,GAAUU,aACrBD,EAAWT,GAAUU,cAAgB3B,OAErCuB,EAAON,GAAYK,EAAOC,EAAON,IAAa,GAAIS,EAAWT,IAE7DM,EAAON,GAAYS,EAAWT,GAIpC,OAAOM,EAGF,SAASK,IAEd,IADA,IAAIlC,EAAI,CAAC,UACAJ,EAAI,EAAGA,EAAIuC,UAAUJ,OAAQnC,IACR,iBAAjBuC,UAAUvC,GACnBI,EAAEoC,KAAKD,UAAUvC,IAEjBI,EAAEoC,KAAKC,EAAkBF,UAAUvC,KAGvC,OAAOI,EAAEsC,KAAK,OAGT,SAASC,EAAaC,EAAcC,GAEzC,IAAIC,EAAgBC,MAAMnB,UAAUoB,QACpC,GAAc,OAAVJ,EACF,OAAQ,EAEV,GAAIE,GAAiBF,EAAMI,UAAYF,EACrC,OAAOF,EAAMI,QAAQH,GAEvB,IAAK,IAAI7C,EAAI,EAAGC,EAAI2C,EAAMT,OAAQnC,EAAIC,EAAGD,IACvC,GAAI4C,EAAM5C,KAAO6C,EACf,OAAO7C,EAGX,OAAQ,EAaH,SAASiD,EAAYvB,EAAawB,GACvC,IAAK,IAAI3B,KAAOG,EACVhB,OAAOkB,UAAUC,eAAe1B,KAAKuB,EAAQH,IAC/C2B,EAAExB,EAAOH,GAAMA,EAAKG,GAUnB,SAASyB,EAAKzB,GACnB,IAAIyB,EAAO,GAIX,OAHAF,EAAYvB,GAAQ,SAAS0B,EAAG7B,GAC9B4B,EAAKX,KAAKjB,MAEL4B,EAQF,SAASE,EAAO3B,GACrB,IAAI2B,EAAS,GAIb,OAHAJ,EAAYvB,GAAQ,SAAST,GAC3BoC,EAAOb,KAAKvB,MAEPoC,EAaF,SAASC,EAAMV,EAAcM,EAAaK,GAC/C,IAAK,IAAIvD,EAAI,EAAGA,EAAI4C,EAAMT,OAAQnC,IAChCkD,EAAE/C,KAAKoD,GAAWC,EAAQZ,EAAM5C,GAAIA,EAAG4C,GAepC,SAASa,EAAIb,EAAcM,GAEhC,IADA,IAAIQ,EAAS,GACJ1D,EAAI,EAAGA,EAAI4C,EAAMT,OAAQnC,IAChC0D,EAAOlB,KAAKU,EAAEN,EAAM5C,GAAIA,EAAG4C,EAAOc,IAEpC,OAAOA,EAiCF,SAASC,EAAOf,EAAcgB,GACnCA,EACEA,GACA,SAAS3C,GACP,QAASA,GAIb,IADA,IAAIyC,EAAS,GACJ1D,EAAI,EAAGA,EAAI4C,EAAMT,OAAQnC,IAC5B4D,EAAKhB,EAAM5C,GAAIA,EAAG4C,EAAOc,IAC3BA,EAAOlB,KAAKI,EAAM5C,IAGtB,OAAO0D,EAcF,SAASG,EAAanC,EAAgBkC,GAC3C,IAAIF,EAAS,GAMb,OALAT,EAAYvB,GAAQ,SAAST,EAAOM,IAC7BqC,GAAQA,EAAK3C,EAAOM,EAAKG,EAAQgC,IAAYI,QAAQ7C,MACxDyC,EAAOnC,GAAON,MAGXyC,EA0BF,SAASK,EAAInB,EAAcgB,GAChC,IAAK,IAAI5D,EAAI,EAAGA,EAAI4C,EAAMT,OAAQnC,IAChC,GAAI4D,EAAKhB,EAAM5C,GAAIA,EAAG4C,GACpB,OAAO,EAGX,OAAO,EAaF,SAASoB,EAAIpB,EAAcgB,GAChC,IAAK,IAAI5D,EAAI,EAAGA,EAAI4C,EAAMT,OAAQnC,IAChC,IAAK4D,EAAKhB,EAAM5C,GAAIA,EAAG4C,GACrB,OAAO,EAGX,OAAO,EAGF,SAASqB,EAAmBC,GACjC,OA5GqChB,EA4Gd,SAASjC,GAI9B,MAHqB,iBAAVA,IACTA,EAAQwB,EAAkBxB,IAErBkD,mBAAmB,YAAalD,EAAMmD,cA/G3CV,EAAS,GACbT,EA0GiBiB,GA1GG,SAASjD,EAAOM,GAClCmC,EAAOnC,GAAO2B,EAAEjC,MAEXyC,EALF,IAAgCR,EACjCQ,EAmHC,SAASW,EAAiBH,GAC/B,IAxDsBxC,EAClBgC,EAuDAY,EAAST,EAAaK,GAAM,SAASjD,GACvC,YAAiBsD,IAAVtD,KAQT,OALYwC,GA5DU/B,EA6DZuC,EAAmBK,GA5DzBZ,EAAS,GACbT,EAAYvB,GAAQ,SAAST,EAAOM,GAClCmC,EAAOlB,KAAK,CAACjB,EAAKN,OAEbyC,GAyDL,IAAKc,OAAO,OAAQ,MACpB9B,KAAK,KAoEF,SAASD,EAAkBgC,GAChC,IACE,OAAOC,KAAKpC,UAAUmC,GACtB,MAAOE,GACP,OAAOD,KAAKpC,WAzDVsC,EAAU,GACZC,EAAQ,GAEH,SAAUC,EAAM7D,EAAO8D,GAC5B,IAAI/E,EAAGO,EAAMyE,EAEb,cAAe/D,GACb,IAAK,SACH,IAAKA,EACH,OAAO,KAET,IAAKjB,EAAI,EAAGA,EAAI4E,EAAQzC,OAAQnC,GAAK,EACnC,GAAI4E,EAAQ5E,KAAOiB,EACjB,MAAO,CAAEgE,KAAMJ,EAAM7E,IAOzB,GAHA4E,EAAQpC,KAAKvB,GACb4D,EAAMrC,KAAKuC,GAEoC,mBAA3CrE,OAAOkB,UAAUwC,SAASd,MAAMrC,GAElC,IADA+D,EAAK,GACAhF,EAAI,EAAGA,EAAIiB,EAAMkB,OAAQnC,GAAK,EACjCgF,EAAGhF,GAAK8E,EAAM7D,EAAMjB,GAAI+E,EAAO,IAAM/E,EAAI,UAI3C,IAAKO,KADLyE,EAAK,GACQ/D,EACPP,OAAOkB,UAAUC,eAAe1B,KAAKc,EAAOV,KAC9CyE,EAAGzE,GAAQuE,EACT7D,EAAMV,GACNwE,EAAO,IAAML,KAAKpC,UAAU/B,GAAQ,MAK5C,OAAOyE,EACT,IAAK,SACL,IAAK,SACL,IAAK,UACH,OAAO/D,GArCN,CAsD+BwD,EAf3B,OA3CN,IACDG,EACFC,K,+CCtSJ,8BAoDe,QAjDf,oBAaU,KAAAK,UAAaC,IACf3B,EAAO4B,SAAW5B,EAAO4B,QAAQC,KACnC7B,EAAO4B,QAAQC,IAAIF,IAdvB,SAASG,GACPC,KAAKF,IAAIE,KAAKL,UAAWI,GAG3B,QAAQA,GACNC,KAAKF,IAAIE,KAAKC,cAAeF,GAG/B,SAASA,GACPC,KAAKF,IAAIE,KAAKE,eAAgBH,GASxB,cAAcH,GAChB3B,EAAO4B,SAAW5B,EAAO4B,QAAQM,KACnClC,EAAO4B,QAAQM,KAAKP,GAEpBI,KAAKL,UAAUC,GAIX,eAAeA,GACjB3B,EAAO4B,SAAW5B,EAAO4B,QAAQO,MACnCnC,EAAO4B,QAAQO,MAAMR,GAErBI,KAAKC,cAAcL,GAIf,IACNS,KACGN,GAEH,IAAIH,EAAU,IAAU7B,MAAMiC,KAAMhD,WACpC,GAAI,IAAO8C,IACT,IAAOA,IAAIF,QACN,GAAI,IAAOU,aAAc,CAClBD,EAAuBpE,KAAK+D,KACxCF,CAAIF,Q,8CC7CV,WAEIW,EAAO,CACTC,IAAG,IACGC,KAAKD,IACAC,KAAKD,OAEL,IAAIC,MAAOC,UAItBC,MAAMC,GACG,IAAI,IAAY,EAAGA,GAW5B,OAAO5F,KAAiB+E,GACtB,IAAIc,EAAiBrD,MAAMnB,UAAUyE,MAAMlG,KAAKoC,UAAW,GAC3D,OAAO,SAASb,GACd,OAAOA,EAAOnB,GAAM+C,MAAM5B,EAAQ0E,EAAeE,OAAO/D,eAK/C,O,8BCjCf,iEASe,MAAMgE,EAKnB,YAAYC,GACVjB,KAAKkB,UAAY,IAAI,IACrBlB,KAAKmB,iBAAmB,GACxBnB,KAAKiB,YAAcA,EAGrB,KAAKG,EAAmBR,EAAoB5C,GAE1C,OADAgC,KAAKkB,UAAUG,IAAID,EAAWR,EAAU5C,GACjCgC,KAGT,YAAYY,GAEV,OADAZ,KAAKmB,iBAAiBlE,KAAK2D,GACpBZ,KAGT,OAAOoB,EAAoBR,EAAqB5C,GAE9C,OADAgC,KAAKkB,UAAUI,OAAOF,EAAWR,EAAU5C,GACpCgC,KAGT,cAAcY,GACZ,OAAKA,GAKLZ,KAAKmB,iBAAmB,IACtBnB,KAAKmB,kBAAoB,GACzBrG,GAAKA,IAAM8F,GAGNZ,OATLA,KAAKmB,iBAAmB,GACjBnB,MAWX,aAGE,OAFAA,KAAKuB,SACLvB,KAAKwB,gBACExB,KAGT,KAAKoB,EAAmBzC,EAAY8C,GAClC,IAAK,IAAIhH,EAAI,EAAGA,EAAIuF,KAAKmB,iBAAiBvE,OAAQnC,IAChDuF,KAAKmB,iBAAiB1G,GAAG2G,EAAWzC,GAGtC,IAAIuC,EAAYlB,KAAKkB,UAAU5F,IAAI8F,GAC/BrB,EAAO,GAYX,GAVI0B,EAGF1B,EAAK9C,KAAK0B,EAAM8C,GACP9C,GAGToB,EAAK9C,KAAK0B,GAGRuC,GAAaA,EAAUtE,OAAS,EAClC,IAASnC,EAAI,EAAGA,EAAIyG,EAAUtE,OAAQnC,IACpCyG,EAAUzG,GAAGiH,GAAG3D,MAAMmD,EAAUzG,GAAGuD,SAAWC,EAAQ8B,QAE/CC,KAAKiB,aACdjB,KAAKiB,YAAYG,EAAWzC,GAG9B,OAAOqB,S,+CCjFX,2FAKA,SAAS2B,EAAaC,GACpB3D,EAAO0D,aAAaC,GAEtB,SAASC,EAAcD,GACrB3D,EAAO4D,cAAcD,GAQhB,MAAME,UAAoB,IAC/B,YAAYC,EAAcnB,GACxBoB,MAAMC,WAAYN,EAAcI,GAAO,SAASH,GAE9C,OADAhB,IACO,SAUN,MAAMsB,UAAsB,IACjC,YAAYH,EAAcnB,GACxBoB,MAAMG,YAAaN,EAAeE,GAAO,SAASH,GAEhD,OADAhB,IACOgB,S,yDC8BE,EAnCe,CAC5BQ,QAAS,QACTC,SAAU,EAEVC,OAAQ,GACRC,QAAS,IACTC,OAAQ,GAERC,SAAU,oBACVC,SAAU,GACVC,UAAW,IACXC,SAAU,UAEVC,WAAY,mBAEZC,aAAc,eACdC,cAAe,OACfC,gBAAiB,KACjBC,YAAa,IACbC,mBAAoB,IACpBC,mBAAoB,CAClBC,SAAU,oBACVC,UAAW,QAEbC,qBAAsB,CACpBF,SAAU,eACVC,UAAW,QAIbE,SAAU,uBACVC,UAAW,wBACXC,kBAAmB,IC3DrB,SAASC,EACPC,EACA5E,EACAS,GAIA,OAFamE,GAAc5E,EAAO6E,OAAS,IAAM,IAEjC,OADL7E,EAAO6E,OAAS7E,EAAO8E,QAAU9E,EAAO+E,YACpBtE,EAGjC,SAASuE,EAAe/H,EAAagI,GASnC,MARW,QAAUhI,GAEnB,aACA,EAASqG,SADT,sBAIA,EAASD,SACR4B,EAAc,IAAMA,EAAc,KAIhC,IAAIC,EAAgB,CACzBC,WAAY,SAASlI,EAAa+C,GAEhC,OAAO2E,EAAc,KAAM3E,GADfA,EAAO6D,UAAY,IAAMmB,EAAe/H,EAAK,kBAKlDmI,EAAkB,CAC3BD,WAAY,SAASlI,EAAa+C,GAEhC,OAAO2E,EAAc,OAAQ3E,GADjBA,EAAO6D,UAAY,WAAamB,EAAe/H,M,qBCIhD,MAAM,UAA4B,IAc/C,YACEoI,EACApJ,EACAqJ,EACArI,EACAsI,GAEAtC,QACAhC,KAAKuE,WAAa,GAAQC,+BAC1BxE,KAAKoE,MAAQA,EACbpE,KAAKhF,KAAOA,EACZgF,KAAKqE,SAAWA,EAChBrE,KAAKhE,IAAMA,EACXgE,KAAKsE,QAAUA,EAEftE,KAAKyE,MAAQ,MACbzE,KAAK0E,SAAWJ,EAAQI,SACxB1E,KAAKgD,gBAAkBsB,EAAQtB,gBAC/BhD,KAAK2E,GAAK3E,KAAK0E,SAASE,mBAO1B,wBACE,OAAOrG,QAAQyB,KAAKoE,MAAMS,uBAO5B,eACE,OAAOtG,QAAQyB,KAAKoE,MAAMU,cAO5B,UACE,GAAI9E,KAAK+E,QAAyB,gBAAf/E,KAAKyE,MACtB,OAAO,EAGT,IAAIO,EAAMhF,KAAKoE,MAAMa,KAAKf,WAAWlE,KAAKhE,IAAKgE,KAAKsE,SACpD,IACEtE,KAAK+E,OAAS/E,KAAKoE,MAAMc,UAAUF,EAAKhF,KAAKsE,SAC7C,MAAOlF,GAKP,OAJA,IAAKuB,MAAM,KACTX,KAAKmF,QAAQ/F,GACbY,KAAKoF,YAAY,aAEZ,EAOT,OAJApF,KAAKqF,gBAEL,IAAOC,MAAM,aAAc,CAAEjC,UAAWrD,KAAKhF,KAAMgK,QACnDhF,KAAKoF,YAAY,eACV,EAOT,QACE,QAAIpF,KAAK+E,SACP/E,KAAK+E,OAAOQ,SACL,GAWX,KAAK5G,GACH,MAAmB,SAAfqB,KAAKyE,QAEP,IAAK9D,MAAM,KACLX,KAAK+E,QACP/E,KAAK+E,OAAOS,KAAK7G,MAGd,GAOX,OACqB,SAAfqB,KAAKyE,OAAoBzE,KAAK8E,gBAChC9E,KAAK+E,OAAOU,OAIR,SACFzF,KAAKoE,MAAMsB,YACb1F,KAAKoE,MAAMsB,WACT1F,KAAK+E,OACL/E,KAAKoE,MAAMa,KAAKU,QAAQ3F,KAAKhE,IAAKgE,KAAKsE,UAG3CtE,KAAKoF,YAAY,QACjBpF,KAAK+E,OAAOa,YAAS5G,EAGf,QAAQoB,GACdJ,KAAK6F,KAAK,QAAS,CAAEC,KAAM,iBAAkB1F,MAAOA,IACpDJ,KAAK0E,SAAStE,MAAMJ,KAAK+F,qBAAqB,CAAE3F,MAAOA,EAAMvB,cAGvD,QAAQmH,GACVA,EACFhG,KAAKoF,YAAY,SAAU,CACzBa,KAAMD,EAAWC,KACjBC,OAAQF,EAAWE,OACnBC,SAAUH,EAAWG,WAGvBnG,KAAKoF,YAAY,UAEnBpF,KAAKoG,kBACLpG,KAAK+E,YAAS/F,EAGR,UAAUY,GAChBI,KAAK6F,KAAK,UAAWjG,GAGf,aACNI,KAAK6F,KAAK,YAGJ,gBACN7F,KAAK+E,OAAOa,OAAS,KACnB5F,KAAKqG,UAEPrG,KAAK+E,OAAOuB,QAAUlG,IACpBJ,KAAKmF,QAAQ/E,IAEfJ,KAAK+E,OAAOwB,QAAUP,IACpBhG,KAAKwG,QAAQR,IAEfhG,KAAK+E,OAAO0B,UAAY7G,IACtBI,KAAK0G,UAAU9G,IAGbI,KAAK8E,iBACP9E,KAAK+E,OAAO4B,WAAa,KACvB3G,KAAK4G,eAKH,kBACF5G,KAAK+E,SACP/E,KAAK+E,OAAOa,YAAS5G,EACrBgB,KAAK+E,OAAOuB,aAAUtH,EACtBgB,KAAK+E,OAAOwB,aAAUvH,EACtBgB,KAAK+E,OAAO0B,eAAYzH,EACpBgB,KAAK8E,iBACP9E,KAAK+E,OAAO4B,gBAAa3H,IAKvB,YAAYyF,EAAe1F,GACjCiB,KAAKyE,MAAQA,EACbzE,KAAK0E,SAASmC,KACZ7G,KAAK+F,qBAAqB,CACxBtB,MAAOA,EACP1F,OAAQA,KAGZiB,KAAK6F,KAAKpB,EAAO1F,GAGnB,qBAAqBa,GACnB,OAAO,IAAmB,CAAEkH,IAAK9G,KAAK2E,IAAM/E,ICzNjC,MAAM,EAGnB,YAAYwE,GACVpE,KAAKoE,MAAQA,EAQf,YAAY2C,GACV,OAAO/G,KAAKoE,MAAM4C,YAAYD,GAWhC,iBACE/L,EACAqJ,EACArI,EACAsI,GAEA,OAAO,IAAI,EAAoBtE,KAAKoE,MAAOpJ,EAAMqJ,EAAUrI,EAAKsI,ICrCpE,IAAI2C,EAAc,IAAI,EAA0B,CAC9ChC,KAAM,EACNJ,uBAAuB,EACvBC,cAAc,EAEdoC,cAAe,WACb,OAAO3I,QAAQ,GAAQ4I,oBAEzBH,YAAa,WACX,OAAOzI,QAAQ,GAAQ4I,oBAEzBjC,UAAW,SAASF,GAClB,OAAO,GAAQoC,gBAAgBpC,MAI/BqC,EAAoB,CACtBpC,KAAM,EACNJ,uBAAuB,EACvBC,cAAc,EACdoC,cAAe,WACb,OAAO,IAIAI,EAAyB,IAClC,CACEpC,UAAW,SAASF,GAClB,OAAO,GAAQuC,YAAYC,sBAAsBxC,KAGrDqC,GAESI,EAAuB,IAChC,CACEvC,UAAW,SAASF,GAClB,OAAO,GAAQuC,YAAYG,oBAAoB1C,KAGnDqC,GAGEM,EAAmB,CACrBX,YAAa,WACX,OAAO,GAAQY,mBAsBJ,EANmB,CAChC3D,GAAIgD,EACJY,cAb0B,IAAI,EAE5B,IAAmB,GAAIP,EAAwBK,IAYjDG,YAPwB,IAAI,EACZ,IAAmB,GAAIL,EAAsBE,KCnDhD,MAAM,EAOnB,YACEI,EACA1E,EACAiB,GAEAtE,KAAK+H,QAAUA,EACf/H,KAAKqD,UAAYA,EACjBrD,KAAKgI,aAAe1D,EAAQ0D,aAC5BhI,KAAKiI,aAAe3D,EAAQ2D,aAC5BjI,KAAKkI,eAAYlJ,EAanB,iBACEhE,EACAqJ,EACArI,EACAsI,GAEAA,EAAU,IAAmB,GAAIA,EAAS,CACxCtB,gBAAiBhD,KAAKkI,YAExB,IAAIC,EAAanI,KAAKqD,UAAU+E,iBAC9BpN,EACAqJ,EACArI,EACAsI,GAGE+D,EAAgB,KAEhBhC,EAAS,WACX8B,EAAW5G,OAAO,OAAQ8E,GAC1B8B,EAAWlM,KAAK,SAAUqM,GAC1BD,EAAgB,IAAK7H,OAEnB8H,EAAWtC,IAGb,GAFAmC,EAAW5G,OAAO,SAAU+G,GAEJ,OAApBtC,EAAWC,MAAqC,OAApBD,EAAWC,KAEzCjG,KAAK+H,QAAQQ,mBACR,IAAKvC,EAAWG,UAAYkC,EAAe,CAEhD,IAAIG,EAAW,IAAKhI,MAAQ6H,EACxBG,EAAW,EAAIxI,KAAKiI,eACtBjI,KAAK+H,QAAQQ,cACbvI,KAAKkI,UAAYO,KAAKC,IAAIF,EAAW,EAAGxI,KAAKgI,iBAMnD,OADAG,EAAWlM,KAAK,OAAQoK,GACjB8B,EAWT,YAAYpB,GACV,OAAO/G,KAAK+H,QAAQY,WAAa3I,KAAKqD,UAAU2D,YAAYD,IC/FhE,MAAM6B,EAAW,CAgBfC,cAAe,SAASC,GACtB,IACE,IAAIC,EAAc5J,KAAK6J,MAAMF,EAAanK,MACtCsK,EAAkBF,EAAYpK,KAClC,GAA+B,iBAApBsK,EACT,IACEA,EAAkB9J,KAAK6J,MAAMD,EAAYpK,MACzC,MAAOS,IAEX,IAAI8J,EAA2B,CAC7BC,MAAOJ,EAAYI,MACnBC,QAASL,EAAYK,QACrBzK,KAAMsK,GAKR,OAHIF,EAAYM,UACdH,EAAYG,QAAUN,EAAYM,SAE7BH,EACP,MAAO9J,GACP,KAAM,CAAE0G,KAAM,oBAAqB1F,MAAOhB,EAAGT,KAAMmK,EAAanK,QAUpE2K,cAAe,SAASH,GACtB,OAAOhK,KAAKpC,UAAUoM,IAiBxBI,iBAAkB,SAAST,GACzB,IAAIlJ,EAAUgJ,EAASC,cAAcC,GAErC,GAAsB,kCAAlBlJ,EAAQuJ,MAA2C,CACrD,IAAKvJ,EAAQjB,KAAK6K,iBAChB,KAAM,6CAER,MAAO,CACLC,OAAQ,YACR9E,GAAI/E,EAAQjB,KAAK+K,UACjB1G,gBAAiD,IAAhCpD,EAAQjB,KAAK6K,kBAE3B,GAAsB,iBAAlB5J,EAAQuJ,MAGjB,MAAO,CACLM,OAAQzJ,KAAK2J,eAAe/J,EAAQjB,MACpCyB,MAAOJ,KAAK4J,cAAchK,EAAQjB,OAGpC,KAAM,qBAcVgL,eAAgB,SAAS3D,GACvB,OAAIA,EAAWC,KAAO,IAMhBD,EAAWC,MAAQ,MAAQD,EAAWC,MAAQ,KACzC,UAEA,KAEoB,MAApBD,EAAWC,KACb,WACED,EAAWC,KAAO,KACpB,UACED,EAAWC,KAAO,KACpB,UACED,EAAWC,KAAO,KACpB,QAGA,WAaX2D,cAAe,SAAS5D,GACtB,OAAwB,MAApBA,EAAWC,MAAqC,OAApBD,EAAWC,KAClC,CACLH,KAAM,cACNnH,KAAM,CACJsH,KAAMD,EAAWC,KACjBrG,QAASoG,EAAWE,QAAUF,EAAWpG,UAItC,OAKE,QClIA,MAAM,UAAmB,IAKtC,YAAY+E,EAAYtB,GACtBrB,QACAhC,KAAK2E,GAAKA,EACV3E,KAAKqD,UAAYA,EACjBrD,KAAKgD,gBAAkBK,EAAUL,gBACjChD,KAAKqF,gBAOP,wBACE,OAAOrF,KAAKqD,UAAUwB,wBAOxB,KAAKlG,GACH,OAAOqB,KAAKqD,UAAUmC,KAAK7G,GAU7B,WAAW3D,EAAc2D,EAAWyK,GAClC,IAAID,EAAqB,CAAEA,MAAOnO,EAAM2D,KAAMA,GAK9C,OAJIyK,IACFD,EAAMC,QAAUA,GAElB,IAAO9D,MAAM,aAAc6D,GACpBnJ,KAAKwF,KAAK,EAAS8D,cAAcH,IAQ1C,OACMnJ,KAAKqD,UAAUyB,eACjB9E,KAAKqD,UAAUoC,OAEfzF,KAAK6J,WAAW,cAAe,IAKnC,QACE7J,KAAKqD,UAAUkC,QAGT,gBACN,IAAIuE,EAAY,CACdlK,QAAUkJ,IACR,IAAII,EACJ,IACEA,EAAc,EAASL,cAAcC,GACrC,MAAO1J,GACPY,KAAK6F,KAAK,QAAS,CACjBC,KAAM,oBACN1F,MAAOhB,EACPT,KAAMmK,EAAanK,OAIvB,QAAoBK,IAAhBkK,EAA2B,CAG7B,OAFA,IAAO5D,MAAM,aAAc4D,GAEnBA,EAAYC,OAClB,IAAK,eACHnJ,KAAK6F,KAAK,QAAS,CACjBC,KAAM,cACNnH,KAAMuK,EAAYvK,OAEpB,MACF,IAAK,cACHqB,KAAK6F,KAAK,QACV,MACF,IAAK,cACH7F,KAAK6F,KAAK,QAGd7F,KAAK6F,KAAK,UAAWqD,KAGzBa,SAAU,KACR/J,KAAK6F,KAAK,aAEZzF,MAAOA,IACLJ,KAAK6F,KAAK,QAASzF,IAErB4J,OAAQhE,IACNI,IAEIJ,GAAcA,EAAWC,MAC3BjG,KAAKiK,iBAAiBjE,GAGxBhG,KAAKqD,UAAY,KACjBrD,KAAK6F,KAAK,YAIVO,EAAkB,KACpB,IAAwB0D,EAAW,CAACI,EAAUf,KAC5CnJ,KAAKqD,UAAU9B,OAAO4H,EAAOe,MAIjC,IAAwBJ,EAAW,CAACI,EAAUf,KAC5CnJ,KAAKqD,UAAUpH,KAAKkN,EAAOe,KAIvB,iBAAiBlE,GACvB,IAAIyD,EAAS,EAASE,eAAe3D,GACjC5F,EAAQ,EAASwJ,cAAc5D,GAC/B5F,GACFJ,KAAK6F,KAAK,QAASzF,GAEjBqJ,GACFzJ,KAAK6F,KAAK4D,EAAQ,CAAEA,OAAQA,EAAQrJ,MAAOA,KCrIlC,MAAM,EAMnB,YACEiD,EACAzC,GAEAZ,KAAKqD,UAAYA,EACjBrD,KAAKY,SAAWA,EAChBZ,KAAKqF,gBAGP,QACErF,KAAKoG,kBACLpG,KAAKqD,UAAUkC,QAGT,gBACNvF,KAAK0G,UAAY7L,IAGf,IAAIsD,EAFJ6B,KAAKoG,kBAGL,IACEjI,EAAS,EAASoL,iBAAiB1O,GACnC,MAAOuE,GAGP,OAFAY,KAAKmK,OAAO,QAAS,CAAE/J,MAAOhB,SAC9BY,KAAKqD,UAAUkC,QAIK,cAAlBpH,EAAOsL,OACTzJ,KAAKmK,OAAO,YAAa,CACvBhC,WAAY,IAAI,EAAWhK,EAAOwG,GAAI3E,KAAKqD,WAC3CL,gBAAiB7E,EAAO6E,mBAG1BhD,KAAKmK,OAAOhM,EAAOsL,OAAQ,CAAErJ,MAAOjC,EAAOiC,QAC3CJ,KAAKqD,UAAUkC,UAInBvF,KAAKsI,SAAWtC,IACdhG,KAAKoG,kBAEL,IAAIqD,EAAS,EAASE,eAAe3D,IAAe,UAChD5F,EAAQ,EAASwJ,cAAc5D,GACnChG,KAAKmK,OAAOV,EAAQ,CAAErJ,MAAOA,KAG/BJ,KAAKqD,UAAUpH,KAAK,UAAW+D,KAAK0G,WACpC1G,KAAKqD,UAAUpH,KAAK,SAAU+D,KAAKsI,UAG7B,kBACNtI,KAAKqD,UAAU9B,OAAO,UAAWvB,KAAK0G,WACtC1G,KAAKqD,UAAU9B,OAAO,SAAUvB,KAAKsI,UAG/B,OAAOmB,EAAgB1K,GAC7BiB,KAAKY,SACH,IAAmB,CAAEyC,UAAWrD,KAAKqD,UAAWoG,OAAQA,GAAU1K,KC1EzD,MAAM,EAKnB,YAAY2F,EAAoBJ,GAC9BtE,KAAK0E,SAAWA,EAChB1E,KAAKsE,QAAUA,GAAW,GAG5B,KAAKV,EAAiBhD,GAChBZ,KAAK0E,SAAS0F,WAIlBpK,KAAK0E,SAASc,KACZ,GAAQ6E,kBAAkBC,SAAStK,KAAM4D,GACzChD,IC3BC,MAAM2J,UAAqBC,MAChC,YAAYC,GACVzI,MAAMyI,GAENtP,OAAOuP,eAAe1K,gBAAiB3D,YAIpC,MAAMsO,UAAuBH,MAClC,YAAYC,GACVzI,MAAMyI,GAENtP,OAAOuP,eAAe1K,gBAAiB3D,YAINmO,MAO9B,MAAMI,UAAgCJ,MAC3C,YAAYC,GACVzI,MAAMyI,GAENtP,OAAOuP,eAAe1K,gBAAiB3D,YAGpC,MAAMwO,UAAwBL,MACnC,YAAYC,GACVzI,MAAMyI,GAENtP,OAAOuP,eAAe1K,gBAAiB3D,YAGpC,MAAMyO,UAA2BN,MACtC,YAAYC,GACVzI,MAAMyI,GAENtP,OAAOuP,eAAe1K,gBAAiB3D,YAGpC,MAAM0O,UAA6BP,MACxC,YAAYC,GACVzI,MAAMyI,GAENtP,OAAOuP,eAAe1K,gBAAiB3D,YAGpC,MAAM2O,UAA4BR,MACvC,YAAYC,GACVzI,MAAMyI,GAENtP,OAAOuP,eAAe1K,gBAAiB3D,YAGpC,MAAM4O,UAAsBT,MAEjC,YAAYU,EAAgBT,GAC1BzI,MAAMyI,GACNzK,KAAKkL,OAASA,EAEd/P,OAAOuP,eAAe1K,gBAAiB3D,YC9D3C,MAAM8O,EAAW,CACfC,QAAS,qBACTnG,KAAM,CACJoG,uBAAwB,CACtB7L,KAAM,kDAER8L,sBAAuB,CACrB9L,KAAM,gDAER+L,qBAAsB,CACpB/L,KAAM,gCAERgM,uBAAwB,CACtBhM,KAAM,uDAERiM,wBAAyB,CACvBC,QACE,iHA0BO,MAhBQ,SAAS1P,GAC9B,MACM2P,EAASR,EAASlG,KAAKjJ,GAC7B,IAAK2P,EAAQ,MAAO,GAEpB,IAAI3G,EAOJ,OANI2G,EAAOD,QACT1G,EAAM2G,EAAOD,QACJC,EAAOnM,OAChBwF,EAAMmG,EAASC,QAAUO,EAAOnM,MAG7BwF,EACE,QAAgBA,EADN,ICrBJ,MAAM,UAAgB,IAQnC,YAAYhK,EAAc4Q,GACxB5J,OAAM,SAASmH,EAAOxK,GACpB,IAAO2G,MAAM,mBAAqBtK,EAAO,QAAUmO,MAGrDnJ,KAAKhF,KAAOA,EACZgF,KAAK4L,OAASA,EACd5L,KAAK6L,YAAa,EAClB7L,KAAK8L,qBAAsB,EAC3B9L,KAAK+L,uBAAwB,EAO/B,UAAUC,EAAkBpL,GAC1B,OAAOA,EAAS,KAAM,CAAEqL,KAAM,KAIhC,QAAQ9C,EAAexK,GACrB,GAAiC,IAA7BwK,EAAM1L,QAAQ,WAChB,MAAM,IAAI,EACR,UAAY0L,EAAQ,mCAGxB,IAAKnJ,KAAK6L,WAAY,CACpB,IAAIK,EAAS,EAAwB,0BACrC,IAAO/L,KACL,0EAA0E+L,GAG9E,OAAOlM,KAAK4L,OAAO/B,WAAWV,EAAOxK,EAAMqB,KAAKhF,MAIlD,aACEgF,KAAK6L,YAAa,EAClB7L,KAAK8L,qBAAsB,EAO7B,YAAY3C,GACV,IAAI/H,EAAY+H,EAAMA,MAClBxK,EAAOwK,EAAMxK,KACjB,GAAkB,2CAAdyC,EACFpB,KAAKmM,iCAAiChD,QACjC,GAAkB,uCAAd/H,EACTpB,KAAKoM,6BAA6BjD,QAC7B,GAA8C,IAA1C/H,EAAU3D,QAAQ,oBAA2B,CAEtDuC,KAAK6F,KAAKzE,EAAWzC,EADI,KAK7B,iCAAiCwK,GAC/BnJ,KAAK8L,qBAAsB,EAC3B9L,KAAK6L,YAAa,EACd7L,KAAK+L,sBACP/L,KAAK4L,OAAOS,YAAYrM,KAAKhF,MAE7BgF,KAAK6F,KAAK,gCAAiCsD,EAAMxK,MAIrD,6BAA6BwK,GACvBA,EAAMxK,KAAK2N,qBACbtM,KAAKuM,kBAAoBpD,EAAMxK,KAAK2N,oBAGtCtM,KAAK6F,KAAK,4BAA6BsD,EAAMxK,MAI/C,YACMqB,KAAK6L,aAGT7L,KAAK8L,qBAAsB,EAC3B9L,KAAK+L,uBAAwB,EAC7B/L,KAAKwM,UACHxM,KAAK4L,OAAOzD,WAAWuB,UACvB,CAACtJ,EAAqBzB,KAChByB,GACFJ,KAAK8L,qBAAsB,EAI3B,IAAO1L,MAAMA,EAAMvB,YACnBmB,KAAK6F,KACH,4BACA1K,OAAOsR,OACL,GACA,CACE3G,KAAM,YACN1F,MAAOA,EAAMR,SAEfQ,aAAiB6K,EAAgB,CAAEC,OAAQ9K,EAAM8K,QAAW,MAIhElL,KAAK4L,OAAO/B,WAAW,mBAAoB,CACzCoC,KAAMtN,EAAKsN,KACXS,aAAc/N,EAAK+N,aACnBtD,QAASpJ,KAAKhF,UAQxB,cACEgF,KAAK6L,YAAa,EAClB7L,KAAK4L,OAAO/B,WAAW,qBAAsB,CAC3CT,QAASpJ,KAAKhF,OAKlB,qBACEgF,KAAK+L,uBAAwB,EAI/B,wBACE/L,KAAK+L,uBAAwB,GCvJlB,MAAM,UAAuB,EAM1C,UAAUC,EAAkBpL,GAC1B,OAAOZ,KAAK4L,OAAOe,OAAOC,kBACxB,CACEC,YAAa7M,KAAKhF,KAClBgR,SAAUA,GAEZpL,IClBS,MAAM,EAMnB,cACEZ,KAAK8M,QAUP,IAAInI,GACF,OAAIxJ,OAAOkB,UAAUC,eAAe1B,KAAKoF,KAAK+M,QAASpI,GAC9C,CACLA,GAAIA,EACJkC,KAAM7G,KAAK+M,QAAQpI,IAGd,KAQX,KAAK/D,GACH,IAAwBZ,KAAK+M,QAAS,CAACC,EAAQrI,KAC7C/D,EAASZ,KAAK1E,IAAIqJ,MAKtB,QAAQA,GACN3E,KAAKiN,KAAOtI,EAId,eAAeuI,GACblN,KAAK+M,QAAUG,EAAiBC,SAASC,KACzCpN,KAAKqN,MAAQH,EAAiBC,SAASE,MACvCrN,KAAKsN,GAAKtN,KAAK1E,IAAI0E,KAAKiN,MAI1B,UAAUM,GAKR,OAJqC,OAAjCvN,KAAK1E,IAAIiS,EAAWlE,UACtBrJ,KAAKqN,QAEPrN,KAAK+M,QAAQQ,EAAWlE,SAAWkE,EAAWC,UACvCxN,KAAK1E,IAAIiS,EAAWlE,SAI7B,aAAakE,GACX,IAAIP,EAAShN,KAAK1E,IAAIiS,EAAWlE,SAKjC,OAJI2D,WACKhN,KAAK+M,QAAQQ,EAAWlE,SAC/BrJ,KAAKqN,SAEAL,EAIT,QACEhN,KAAK+M,QAAU,GACf/M,KAAKqN,MAAQ,EACbrN,KAAKiN,KAAO,KACZjN,KAAKsN,GAAK,M,0SCpEC,MAAM,UAAwB,EAQ3C,YAAYtS,EAAc4Q,GACxB5J,MAAMhH,EAAM4Q,GACZ5L,KAAK+M,QAAU,IAAI,EAQrB,UAAUf,EAAkBpL,GAC1BoB,MAAMwK,UAAUR,EAAU,CAAO5L,EAAOqN,IAAa,EAAD,gCAClD,IAAKrN,EAEH,GAA6B,OAD7BqN,EAAWA,GACEf,aAAsB,CACjC,IAAIgB,EAAcvO,KAAK6J,MAAMyE,EAASf,cACtC1M,KAAK+M,QAAQY,QAAQD,EAAYrE,aAC5B,CAEL,SADMrJ,KAAK4L,OAAOgC,KAAKC,kBACW,MAA9B7N,KAAK4L,OAAOgC,KAAKE,UAId,CACL,IAAI5B,EAAS,EAAwB,yBAOrC,OANA,IAAO9L,MACL,sCAAsCJ,KAAKhF,yCACPkR,4CAGtCtL,EAAS,yBARTZ,KAAK+M,QAAQY,QAAQ3N,KAAK4L,OAAOgC,KAAKE,UAAUnJ,IAatD/D,EAASR,EAAOqN,OAQpB,YAAYtE,GACV,IAAI/H,EAAY+H,EAAMA,MACtB,GAA8C,IAA1C/H,EAAU3D,QAAQ,oBACpBuC,KAAK+N,oBAAoB5E,OACpB,CACL,IAAIxK,EAAOwK,EAAMxK,KACb8C,EAAqB,GACrB0H,EAAME,UACR5H,EAAS4H,QAAUF,EAAME,SAE3BrJ,KAAK6F,KAAKzE,EAAWzC,EAAM8C,IAG/B,oBAAoB0H,GAClB,IAAI/H,EAAY+H,EAAMA,MAClBxK,EAAOwK,EAAMxK,KACjB,OAAQyC,GACN,IAAK,yCACHpB,KAAKmM,iCAAiChD,GACtC,MACF,IAAK,qCACHnJ,KAAKoM,6BAA6BjD,GAClC,MACF,IAAK,+BACH,IAAI6E,EAAchO,KAAK+M,QAAQkB,UAAUtP,GACzCqB,KAAK6F,KAAK,sBAAuBmI,GACjC,MACF,IAAK,iCACH,IAAIE,EAAgBlO,KAAK+M,QAAQoB,aAAaxP,GAC1CuP,GACFlO,KAAK6F,KAAK,wBAAyBqI,IAM3C,iCAAiC/E,GAC/BnJ,KAAK8L,qBAAsB,EAC3B9L,KAAK6L,YAAa,EACd7L,KAAK+L,sBACP/L,KAAK4L,OAAOS,YAAYrM,KAAKhF,OAE7BgF,KAAK+M,QAAQqB,eAAejF,EAAMxK,MAClCqB,KAAK6F,KAAK,gCAAiC7F,KAAK+M,UAKpD,aACE/M,KAAK+M,QAAQD,QACb9K,MAAMqM,c,mBC3FK,MAAM,UAAyB,EAI5C,YAAYrT,EAAc4Q,EAAgB0C,GACxCtM,MAAMhH,EAAM4Q,GAJd,KAAA5P,IAAkB,KAKhBgE,KAAKsO,KAAOA,EAQd,UAAUtC,EAAkBpL,GAC1BoB,MAAMwK,UACJR,EACA,CAAC5L,EAAqBqN,KACpB,GAAIrN,EAEF,YADAQ,EAASR,EAAOqN,GAGlB,IAAIc,EAAed,EAAwB,cACtCc,GASLvO,KAAKhE,IAAM,iBAAauS,UACjBd,EAAwB,cAC/B7M,EAAS,KAAM6M,IAVb7M,EACE,IAAI4J,MACF,+DAA+DxK,KAAKhF,MAEtE,QAWV,QAAQmO,EAAexK,GACrB,MAAM,IAAI,EACR,oEAQJ,YAAYwK,GACV,IAAI/H,EAAY+H,EAAMA,MAClBxK,EAAOwK,EAAMxK,KAE2B,IAA1CyC,EAAU3D,QAAQ,qBACe,IAAjC2D,EAAU3D,QAAQ,WAKpBuC,KAAKwO,qBAAqBpN,EAAWzC,GAHnCqD,MAAMyM,YAAYtF,GAMd,qBAAqBA,EAAexK,GAC1C,IAAKqB,KAAKhE,IAIR,YAHA,IAAOsJ,MACL,gFAIJ,IAAK3G,EAAK+P,aAAe/P,EAAKgQ,MAK5B,YAJA,IAAOvO,MACL,qGACEzB,GAIN,IAAIiQ,EAAa,iBAAajQ,EAAK+P,YACnC,GAAIE,EAAWhS,OAASoD,KAAKsO,KAAKO,UAAUC,eAI1C,YAHA,IAAO1O,MACL,oDAAoDJ,KAAKsO,KAAKO,UAAUC,wBAAwBF,EAAWhS,UAI/G,IAAI+R,EAAQ,iBAAahQ,EAAKgQ,OAC9B,GAAIA,EAAM/R,OAASoD,KAAKsO,KAAKO,UAAUE,YAIrC,YAHA,IAAO3O,MACL,+CAA+CJ,KAAKsO,KAAKO,UAAUE,qBAAqBJ,EAAM/R,UAKlG,IAAIoS,EAAQhP,KAAKsO,KAAKO,UAAUI,KAAKL,EAAYD,EAAO3O,KAAKhE,KAC7D,GAAc,OAAVgT,EAuBF,OAtBA,IAAO1J,MACL,wIAIFtF,KAAKwM,UAAUxM,KAAK4L,OAAOzD,WAAWuB,UAAW,CAACtJ,EAAOqN,KACnDrN,EACF,IAAOA,MACL,iDAAiDqN,4DAIrDuB,EAAQhP,KAAKsO,KAAKO,UAAUI,KAAKL,EAAYD,EAAO3O,KAAKhE,KAC3C,OAAVgT,EAMJhP,KAAK6F,KAAKsD,EAAOnJ,KAAKkP,cAAcF,IALlC,IAAO5O,MACL,qEASRJ,KAAK6F,KAAKsD,EAAOnJ,KAAKkP,cAAcF,IAKtC,cAAcA,GACZ,IAAIG,EAAM,iBAAWH,GACrB,IACE,OAAO7P,KAAK6J,MAAMmG,GAClB,SACA,OAAOA,I,WCpGE,MAAM,UAA0B,IAkB7C,YAAYnT,EAAasI,GACvBtC,QACAhC,KAAKyE,MAAQ,cACbzE,KAAKmI,WAAa,KAElBnI,KAAKhE,IAAMA,EACXgE,KAAKsE,QAAUA,EACftE,KAAK0E,SAAW1E,KAAKsE,QAAQI,SAC7B1E,KAAKoP,SAAWpP,KAAKsE,QAAQV,OAE7B5D,KAAKqP,eAAiBrP,KAAKsP,sBAC3BtP,KAAKuP,oBAAsBvP,KAAKwP,yBAC9BxP,KAAKqP,gBAEPrP,KAAKyP,mBAAqBzP,KAAK0P,wBAAwB1P,KAAKqP,gBAE5D,IAAIM,EAAU,GAAQC,aAEtBD,EAAQ1T,KAAK,SAAU,KACrB+D,KAAK0E,SAASmC,KAAK,CAAEgJ,QAAS,WACX,eAAf7P,KAAKyE,OAAyC,gBAAfzE,KAAKyE,OACtCzE,KAAK8P,QAAQ,KAGjBH,EAAQ1T,KAAK,UAAW,KACtB+D,KAAK0E,SAASmC,KAAK,CAAEgJ,QAAS,YAC1B7P,KAAKmI,YACPnI,KAAK+P,sBAIT/P,KAAKgQ,iBAQP,UACMhQ,KAAKmI,YAAcnI,KAAKiQ,SAGvBjQ,KAAKkQ,SAASlJ,eAInBhH,KAAKmQ,YAAY,cACjBnQ,KAAKoQ,kBACLpQ,KAAKqQ,uBALHrQ,KAAKmQ,YAAY,WAYrB,KAAKxR,GACH,QAAIqB,KAAKmI,YACAnI,KAAKmI,WAAW3C,KAAK7G,GAahC,WAAW3D,EAAc2D,EAAWyK,GAClC,QAAIpJ,KAAKmI,YACAnI,KAAKmI,WAAW0B,WAAW7O,EAAM2D,EAAMyK,GAOlD,aACEpJ,KAAKsQ,uBACLtQ,KAAKmQ,YAAY,gBAGnB,aACE,OAAOnQ,KAAKoP,SAGN,kBACN,IAAIxO,EAAW,CAACR,EAAOmQ,KACjBnQ,EACFJ,KAAKiQ,OAASjQ,KAAKkQ,SAASM,QAAQ,EAAG5P,GAEd,UAArB2P,EAAU9G,QACZzJ,KAAK6F,KAAK,QAAS,CACjBC,KAAM,iBACN1F,MAAOmQ,EAAUnQ,QAEnBJ,KAAK0E,SAAStE,MAAM,CAAEqQ,eAAgBF,EAAUnQ,UAEhDJ,KAAK0Q,kBACL1Q,KAAKyP,mBAAmBc,EAAU9G,QAAQ8G,KAIhDvQ,KAAKiQ,OAASjQ,KAAKkQ,SAASM,QAAQ,EAAG5P,GAGjC,kBACFZ,KAAKiQ,SACPjQ,KAAKiQ,OAAOU,QACZ3Q,KAAKiQ,OAAS,MAIV,wBACNjQ,KAAK0Q,kBACL1Q,KAAK4Q,kBACL5Q,KAAK6Q,wBACD7Q,KAAKmI,aACUnI,KAAK8Q,oBACXvL,QAIP,iBACNvF,KAAKkQ,SAAWlQ,KAAKsE,QAAQyM,YAAY,CACvC/U,IAAKgE,KAAKhE,IACV0I,SAAU1E,KAAK0E,SACfd,OAAQ5D,KAAKoP,WAIT,QAAQrN,GACd/B,KAAK0E,SAASmC,KAAK,CAAE4C,OAAQ,QAAS1H,MAAOA,IACzCA,EAAQ,GACV/B,KAAK6F,KAAK,gBAAiB4C,KAAKuI,MAAMjP,EAAQ,MAEhD/B,KAAKiR,WAAa,IAAI,IAAMlP,GAAS,EAAG,KACtC/B,KAAKsQ,uBACLtQ,KAAKwQ,YAID,kBACFxQ,KAAKiR,aACPjR,KAAKiR,WAAWC,gBAChBlR,KAAKiR,WAAa,MAId,sBACNjR,KAAKmR,iBAAmB,IAAI,IAAMnR,KAAKsE,QAAQpB,mBAAoB,KACjElD,KAAKmQ,YAAY,iBAIb,wBACFnQ,KAAKmR,kBACPnR,KAAKmR,iBAAiBD,gBAIlB,oBACNlR,KAAKoR,oBACLpR,KAAKmI,WAAW1C,OAEhBzF,KAAKqR,cAAgB,IAAI,IAAMrR,KAAKsE,QAAQrB,YAAa,KACvDjD,KAAK0E,SAAStE,MAAM,CAAEkR,eAAgBtR,KAAKsE,QAAQrB,cACnDjD,KAAK8P,QAAQ,KAIT,qBACN9P,KAAKoR,oBAEDpR,KAAKmI,aAAenI,KAAKmI,WAAWtD,0BACtC7E,KAAKqR,cAAgB,IAAI,IAAMrR,KAAKgD,gBAAiB,KACnDhD,KAAK+P,uBAKH,oBACF/P,KAAKqR,eACPrR,KAAKqR,cAAcH,gBAIf,yBACN7B,GAEA,OAAO,IAAwC,GAAIA,EAAgB,CACjEzP,QAASA,IAEPI,KAAKuR,qBACLvR,KAAK6F,KAAK,UAAWjG,IAEvB6F,KAAM,KACJzF,KAAK6J,WAAW,cAAe,KAEjCE,SAAU,KACR/J,KAAKuR,sBAEPnR,MAAOA,IAELJ,KAAK6F,KAAK,QAASzF,IAErB4J,OAAQ,KACNhK,KAAK8Q,oBACD9Q,KAAKwR,eACPxR,KAAK8P,QAAQ,QAMb,wBACNT,GAEA,OAAO,IAAuC,GAAIA,EAAgB,CAChEoC,UAAYlB,IACVvQ,KAAKgD,gBAAkByF,KAAKiJ,IAC1B1R,KAAKsE,QAAQtB,gBACbuN,EAAUvN,gBACVuN,EAAUpI,WAAWnF,iBAAmB2O,KAE1C3R,KAAK6Q,wBACL7Q,KAAK4R,cAAcrB,EAAUpI,YAC7BnI,KAAK0J,UAAY1J,KAAKmI,WAAWxD,GACjC3E,KAAKmQ,YAAY,YAAa,CAAEzG,UAAW1J,KAAK0J,eAK9C,sBACN,IAAImI,EAAmBjR,GACbzC,IACFA,EAAOiC,OACTJ,KAAK6F,KAAK,QAAS,CAAEC,KAAM,iBAAkB1F,MAAOjC,EAAOiC,QAE7DQ,EAASzC,IAIb,MAAO,CACL2T,SAAUD,EAAiB,KACzB7R,KAAKoP,UAAW,EAChBpP,KAAKgQ,iBACLhQ,KAAK8P,QAAQ,KAEfiC,QAASF,EAAiB,KACxB7R,KAAKqO,eAEP2D,QAASH,EAAiB,KACxB7R,KAAK8P,QAAQ,OAEfmC,MAAOJ,EAAiB,KACtB7R,KAAK8P,QAAQ,MAKX,cAAc3H,GAEpB,IAAK,IAAIgB,KADTnJ,KAAKmI,WAAaA,EACAnI,KAAKuP,oBACrBvP,KAAKmI,WAAWlM,KAAKkN,EAAOnJ,KAAKuP,oBAAoBpG,IAEvDnJ,KAAKuR,qBAGC,oBACN,GAAKvR,KAAKmI,WAAV,CAIA,IAAK,IAAIgB,KADTnJ,KAAKoR,oBACapR,KAAKuP,oBACrBvP,KAAKmI,WAAW5G,OAAO4H,EAAOnJ,KAAKuP,oBAAoBpG,IAEzD,IAAIhB,EAAanI,KAAKmI,WAEtB,OADAnI,KAAKmI,WAAa,KACXA,GAGD,YAAY+J,EAAkBvT,GACpC,IAAIwT,EAAgBnS,KAAKyE,MAEzB,GADAzE,KAAKyE,MAAQyN,EACTC,IAAkBD,EAAU,CAC9B,IAAIE,EAAsBF,EACE,cAAxBE,IACFA,GAAuB,uBAAyBzT,EAAK+K,WAEvD,IAAOpE,MACL,gBACA6M,EAAgB,OAASC,GAE3BpS,KAAK0E,SAASmC,KAAK,CAAEpC,MAAOyN,EAAUnT,OAAQJ,IAC9CqB,KAAK6F,KAAK,eAAgB,CAAEwM,SAAUF,EAAeG,QAASJ,IAC9DlS,KAAK6F,KAAKqM,EAAUvT,IAIhB,cACN,MAAsB,eAAfqB,KAAKyE,OAAyC,cAAfzE,KAAKyE,OCtWhC,MAAM,EAGnB,cACEzE,KAAKuS,SAAW,GASlB,IAAIvX,EAAc4Q,GAIhB,OAHK5L,KAAKuS,SAASvX,KACjBgF,KAAKuS,SAASvX,GAwCpB,SAAuBA,EAAc4Q,GACnC,GAA2C,IAAvC5Q,EAAKyC,QAAQ,sBAA6B,CAC5C,GAAImO,EAAOe,OAAO2B,KAChB,OAAO,EAAQkE,uBAAuBxX,EAAM4Q,EAAQA,EAAOe,OAAO2B,MAEpE,IAAImE,EACF,0FACEvG,EAAS,EAAwB,2BACrC,MAAM,IAAI,EAA0B,GAAGuG,MAAWvG,KAC7C,GAAiC,IAA7BlR,EAAKyC,QAAQ,YACtB,OAAO,EAAQiV,qBAAqB1X,EAAM4Q,GACrC,GAAkC,IAA9B5Q,EAAKyC,QAAQ,aACtB,OAAO,EAAQkV,sBAAsB3X,EAAM4Q,GACtC,GAA0B,IAAtB5Q,EAAKyC,QAAQ,KACtB,MAAM,IAAI,EACR,sCAAwCzC,EAAO,MAGjD,OAAO,EAAQ4X,cAAc5X,EAAM4Q,GA1DXgH,CAAc5X,EAAM4Q,IAErC5L,KAAKuS,SAASvX,GAOvB,MACE,OAAO,IAAmBgF,KAAKuS,UAQjC,KAAKvX,GACH,OAAOgF,KAAKuS,SAASvX,GAOvB,OAAOA,GACL,IAAIoO,EAAUpJ,KAAKuS,SAASvX,GAE5B,cADOgF,KAAKuS,SAASvX,GACdoO,EAIT,aACE,IAAwBpJ,KAAKuS,UAAU,SAASnJ,GAC9CA,EAAQiF,iBClCd,IAoDe,EApDD,CACZwE,eAAc,IACL,IAAI,EAGbC,wBAAuB,CACrB9W,EACAsI,IAEO,IAAI,EAAkBtI,EAAKsI,GAGpCsO,cAAa,CAAC5X,EAAc4Q,IACnB,IAAI,EAAQ5Q,EAAM4Q,GAG3B8G,qBAAoB,CAAC1X,EAAc4Q,IAC1B,IAAI,EAAe5Q,EAAM4Q,GAGlC+G,sBAAqB,CAAC3X,EAAc4Q,IAC3B,IAAI,EAAgB5Q,EAAM4Q,GAGnC4G,uBAAsB,CACpBxX,EACA4Q,EACA0C,IAEO,IAAI,EAAiBtT,EAAM4Q,EAAQ0C,GAG5CyE,qBAAoB,CAACrO,EAAoBJ,IAChC,IAAI,EAAeI,EAAUJ,GAGtC0O,gBAAe,CACb3P,EACAzC,IAEO,IAAI,EAAUyC,EAAWzC,GAGlCqS,qCAAoC,CAClClL,EACA1E,EACAiB,IAEO,IAAI,EAA+ByD,EAAS1E,EAAWiB,ICxDnD,MAAM,EAInB,YAAYA,GACVtE,KAAKsE,QAAUA,GAAW,GAC1BtE,KAAKkT,UAAYlT,KAAKsE,QAAQ6O,OAASxB,IAQzC,aAAatO,GACX,OAAO,EAAQ4P,qCAAqCjT,KAAMqD,EAAW,CACnE2E,aAAchI,KAAKsE,QAAQ0D,aAC3BC,aAAcjI,KAAKsE,QAAQ2D,eAQ/B,UACE,OAAOjI,KAAKkT,UAAY,EAI1B,cACElT,KAAKkT,WAAa,GCjCP,MAAM,EAOnB,YAAYE,EAAwB9O,GAClCtE,KAAKoT,WAAaA,EAClBpT,KAAKqT,KAAO9U,QAAQ+F,EAAQ+O,MAC5BrT,KAAKsT,SAAW/U,QAAQ+F,EAAQgP,UAChCtT,KAAKuT,QAAUjP,EAAQiP,QACvBvT,KAAKwT,aAAelP,EAAQkP,aAG9B,cACE,OAAO,IAAgBxT,KAAKoT,WAAY,IAAKnU,OAAO,gBAGtD,QAAQwU,EAAqB7S,GAC3B,IAAIwS,EAAapT,KAAKoT,WAClBd,EAAU,EACViB,EAAUvT,KAAKuT,QACftD,EAAS,KAETyD,EAAkB,CAACtT,EAAOmQ,KACxBA,EACF3P,EAAS,KAAM2P,IAEf+B,GAAoB,EAChBtS,KAAKqT,OACPf,GAAoBc,EAAWxW,QAG7B0V,EAAUc,EAAWxW,QACnB2W,IACFA,GAAoB,EAChBvT,KAAKwT,eACPD,EAAU9K,KAAKiJ,IAAI6B,EAASvT,KAAKwT,gBAGrCvD,EAASjQ,KAAK2T,YACZP,EAAWd,GACXmB,EACA,CAAEF,UAASD,SAAUtT,KAAKsT,UAC1BI,IAGF9S,GAAS,KAYf,OAPAqP,EAASjQ,KAAK2T,YACZP,EAAWd,GACXmB,EACA,CAAEF,QAASA,EAASD,SAAUtT,KAAKsT,UACnCI,GAGK,CACL/C,MAAO,WACLV,EAAOU,SAETiD,iBAAkB,SAASrX,GACzBkX,EAAclX,EACV0T,GACFA,EAAO2D,iBAAiBrX,KAMxB,YACN2T,EACAuD,EACAnP,EACA1D,GAEA,IAAIgB,EAAQ,KACRqO,EAAS,KAoBb,OAlBI3L,EAAQiP,QAAU,IACpB3R,EAAQ,IAAI,IAAM0C,EAAQiP,SAAS,WACjCtD,EAAOU,QACP/P,GAAS,OAIbqP,EAASC,EAASM,QAAQiD,GAAa,SAASrT,EAAOmQ,GACjDnQ,GAASwB,GAASA,EAAMiS,cAAgBvP,EAAQgP,WAIhD1R,GACFA,EAAMsP,gBAERtQ,EAASR,EAAOmQ,OAGX,CACLI,MAAO,WACD/O,GACFA,EAAMsP,gBAERjB,EAAOU,SAETiD,iBAAkB,SAASrX,GACzB0T,EAAO2D,iBAAiBrX,MCpHjB,MAAM,EAGnB,YAAY6W,GACVpT,KAAKoT,WAAaA,EAGpB,cACE,OAAO,IAAgBpT,KAAKoT,WAAY,IAAKnU,OAAO,gBAGtD,QAAQwU,EAAqB7S,GAC3B,OA6BJ,SACEwS,EACAK,EACAK,GAEA,IAAIC,EAAU,IAAgBX,GAAY,SAASlD,EAAUzV,EAAGoD,EAAGmW,GACjE,OAAO9D,EAASM,QAAQiD,EAAaK,EAAgBrZ,EAAGuZ,OAE1D,MAAO,CACLrD,MAAO,WACL,IAAkBoD,EAASE,IAE7BL,iBAAkB,SAASrX,GACzB,IAAkBwX,GAAS,SAAS9D,GAClCA,EAAO2D,iBAAiBrX,QA3CrBiU,CAAQxQ,KAAKoT,WAAYK,GAAa,SAAShZ,EAAGsZ,GACvD,OAAO,SAAS3T,EAAOmQ,GACrBwD,EAAQtZ,GAAG2F,MAAQA,EACfA,EA8CZ,SAA0B2T,GACxB,OAAO,IAAgBA,GAAS,SAAS9D,GACvC,OAAO1R,QAAQ0R,EAAO7P,UA/CZ8T,CAAiBH,IACnBnT,GAAS,IAIb,IAAkBmT,GAAS,SAAS9D,GAClCA,EAAO2D,iBAAiBrD,EAAUlN,UAAUgB,aAE9CzD,EAAS,KAAM2P,SA2CvB,SAAS0D,EAAYhE,GACdA,EAAO7P,OAAU6P,EAAOkE,UAC3BlE,EAAOU,QACPV,EAAOkE,SAAU,GC1DN,MAAM,EAOnB,YACEjE,EACAkE,EACA9P,GAEAtE,KAAKkQ,SAAWA,EAChBlQ,KAAKoU,WAAaA,EAClBpU,KAAKqU,IAAM/P,EAAQ+P,KAAO,KAC1BrU,KAAKoP,SAAW9K,EAAQV,OACxB5D,KAAK0E,SAAWJ,EAAQI,SAG1B,cACE,OAAO1E,KAAKkQ,SAASlJ,cAGvB,QAAQyM,EAAqB7S,GAC3B,IAAIwO,EAAWpP,KAAKoP,SAChBvI,EAkER,SAA6BuI,GAC3B,IAAIkF,EAAU,GAAQC,kBACtB,GAAID,EACF,IACE,IAAIE,EAAkBF,EAAQG,EAAqBrF,IACnD,GAAIoF,EACF,OAAOrV,KAAK6J,MAAMwL,GAEpB,MAAOpV,GACPsV,GAAoBtF,GAGxB,OAAO,KA9EMuF,CAAoBvF,GAC3BwF,EAAiB/N,GAAQA,EAAK+N,eAAiB/N,EAAK+N,eAAiB,EAErExB,EAAa,CAACpT,KAAKkQ,UACvB,GAAIrJ,GAAQA,EAAKgO,UAAY7U,KAAKqU,KAAO,IAAK7T,MAAO,CACnD,IAAI6C,EAAYrD,KAAKoU,WAAWvN,EAAKxD,WACjCA,IACE,CAAC,KAAM,OAAOyR,SAASjO,EAAKxD,YAAcuR,EAAiB,GAC7D5U,KAAK0E,SAASmC,KAAK,CACjBkO,QAAQ,EACR1R,UAAWwD,EAAKxD,UAChB2R,QAASnO,EAAKmO,UAEhB5B,EAAWnW,KACT,IAAI,EAAmB,CAACoG,GAAY,CAClCkQ,QAAwB,EAAf1M,EAAKmO,QAAc,IAC5B1B,UAAU,MAIdsB,KAKN,IAAIK,EAAiB,IAAKzU,MACtByP,EAASmD,EACV8B,MACA1E,QAAQiD,GAAa,SAAS0B,EAAG/U,EAAOmQ,GACnCnQ,GACFsU,GAAoBtF,GAChBgE,EAAWxW,OAAS,GACtBqY,EAAiB,IAAKzU,MACtByP,EAASmD,EAAW8B,MAAM1E,QAAQiD,EAAa0B,IAE/CvU,EAASR,MA8CrB,SACEgP,EACA/L,EACA2R,EACAJ,GAEA,IAAIN,EAAU,GAAQC,kBACtB,GAAID,EACF,IACEA,EAAQG,EAAqBrF,IAAa,IAA8B,CACtEyF,UAAW,IAAKrU,MAChB6C,UAAWA,EACX2R,QAASA,EACTJ,eAAgBA,IAElB,MAAOxV,KA1DHgW,CACEhG,EACAmB,EAAUlN,UAAUrI,KACpB,IAAKwF,MAAQyU,EACbL,GAEFhU,EAAS,KAAM2P,OAIrB,MAAO,CACLI,MAAO,WACLV,EAAOU,SAETiD,iBAAkB,SAASrX,GACzBkX,EAAclX,EACV0T,GACFA,EAAO2D,iBAAiBrX,MAOlC,SAASkY,EAAqBrF,GAC5B,MAAO,mBAAqBA,EAAW,MAAQ,UAuCjD,SAASsF,GAAoBtF,GAC3B,IAAIkF,EAAU,GAAQC,kBACtB,GAAID,EACF,WACSA,EAAQG,EAAqBrF,IACpC,MAAOhQ,KC5IE,MAAM,GAInB,YAAY8Q,GAAsBnO,MAAOsT,IACvCrV,KAAKkQ,SAAWA,EAChBlQ,KAAKsE,QAAU,CAAEvC,MAAOsT,GAG1B,cACE,OAAOrV,KAAKkQ,SAASlJ,cAGvB,QAAQyM,EAAqB7S,GAC3B,IACIqP,EADAC,EAAWlQ,KAAKkQ,SAEhBtO,EAAQ,IAAI,IAAM5B,KAAKsE,QAAQvC,OAAO,WACxCkO,EAASC,EAASM,QAAQiD,EAAa7S,MAGzC,MAAO,CACL+P,MAAO,WACL/O,EAAMsP,gBACFjB,GACFA,EAAOU,SAGXiD,iBAAkB,SAASrX,GACzBkX,EAAclX,EACV0T,GACFA,EAAO2D,iBAAiBrX,MCjCnB,MAAM+Y,GAKnB,YACEjX,EACAkX,EACAC,GAEAxV,KAAK3B,KAAOA,EACZ2B,KAAKuV,WAAaA,EAClBvV,KAAKwV,YAAcA,EAGrB,cAEE,OADaxV,KAAK3B,OAAS2B,KAAKuV,WAAavV,KAAKwV,aACpCxO,cAGhB,QAAQyM,EAAqB7S,GAE3B,OADaZ,KAAK3B,OAAS2B,KAAKuV,WAAavV,KAAKwV,aACpChF,QAAQiD,EAAa7S,ICxBxB,MAAM6U,GAGnB,YAAYvF,GACVlQ,KAAKkQ,SAAWA,EAGlB,cACE,OAAOlQ,KAAKkQ,SAASlJ,cAGvB,QAAQyM,EAAqB7S,GAC3B,IAAIqP,EAASjQ,KAAKkQ,SAASM,QAAQiD,GAAa,SAASrT,EAAOmQ,GAC1DA,GACFN,EAAOU,QAET/P,EAASR,EAAOmQ,MAElB,OAAON,GCXX,SAASyF,GAAqBxF,GAC5B,OAAO,WACL,OAAOA,EAASlJ,eAIpB,IAsIe,GAtIU,SACvB2F,EACAgJ,EACAC,GAEA,IAAIC,EAAiD,GAErD,SAASC,EACP9a,EACA8K,EACAzB,EACAC,EACAyD,GAEA,IAAI1E,EAAYuS,EACdjJ,EACA3R,EACA8K,EACAzB,EACAC,EACAyD,GAKF,OAFA8N,EAAkB7a,GAAQqI,EAEnBA,EAGT,IA+EI0S,EA/EAC,EAA8B7a,OAAOsR,OAAO,GAAIkJ,EAAa,CAC/D7R,WAAY6I,EAAOsJ,OAAS,IAAMtJ,EAAOrK,OACzCuB,QAAS8I,EAAOsJ,OAAS,IAAMtJ,EAAOpK,QACtCK,SAAU+J,EAAOnK,SAEf0T,EAA+B,IAAmB,GAAIF,EAAY,CACpEpS,QAAQ,IAENuS,EAAgChb,OAAOsR,OAAO,GAAIkJ,EAAa,CACjE7R,WAAY6I,EAAOlK,SAAW,IAAMkK,EAAOjK,SAC3CmB,QAAS8I,EAAOlK,SAAW,IAAMkK,EAAOhK,UACxCC,SAAU+J,EAAO/J,WAEfwT,EAAW,CACb/C,MAAM,EACNE,QAAS,KACTC,aAAc,KAGZ6C,EAAa,IAAI,EAAiB,CACpCrO,aAAc,IACdC,aAAc0E,EAAO3J,kBAEnBsT,EAAoB,IAAI,EAAiB,CAC3CnD,MAAO,EACPnL,aAAc,IACdC,aAAc0E,EAAO3J,kBAGnBuT,EAAeT,EACjB,KACA,KACA,EACAE,EACAK,GAEEG,EAAgBV,EAClB,MACA,KACA,EACAI,EACAG,GAEEI,EAA0BX,EAC5B,gBACA,gBACA,EACAK,EACAG,GAEEI,EAAwBZ,EAC1B,cACA,cACA,EACAK,GAGEQ,EAAU,IAAI,EAAmB,CAACJ,GAAeH,GACjDQ,EAAW,IAAI,EAAmB,CAACJ,GAAgBJ,GACnDS,EAAiB,IAAI,EACvB,CAACJ,GACDL,GAEEU,EAAe,IAAI,EAAmB,CAACJ,GAAwBN,GAE/DW,EAAY,IAAI,EAClB,CACE,IAAIzB,GACFI,GAAqBmB,GACrB,IAAI,EAA0B,CAC5BA,EACA,IAAI,GAAgBC,EAAc,CAAE/U,MAAO,QAE7C+U,IAGJV,GAiBF,OAZEL,EADEJ,EAAY/R,OACD,IAAI,EAA0B,CACzC+S,EACA,IAAI,GAAgBI,EAAW,CAAEhV,MAAO,QAG7B,IAAI,EAA0B,CACzC4U,EACA,IAAI,GAAgBC,EAAU,CAAE7U,MAAO,MACvC,IAAI,GAAgBgV,EAAW,CAAEhV,MAAO,QAIrC,IAAI,EACT,IAAI0T,GACF,IAAIH,GAAWI,GAAqBa,GAAeR,EAAYgB,IAEjElB,EACA,CACExB,IAAK,KACL3P,SAAUiR,EAAYjR,SACtBd,OAAQ+R,EAAY/R,UC9IX,MAAM,WAAoB,IAQvC,YAAYQ,EAAqBnF,EAAgB+F,GAC/ChD,QACAhC,KAAKoE,MAAQA,EACbpE,KAAKf,OAASA,EACde,KAAKgF,IAAMA,EAGb,MAAMgS,GACJhX,KAAKiX,SAAW,EAChBjX,KAAKkX,IAAMlX,KAAKoE,MAAM+S,WAAWnX,MAEjCA,KAAKoX,SAAW,KACdpX,KAAKuF,SAEP,GAAQ8R,kBAAkBrX,KAAKoX,UAE/BpX,KAAKkX,IAAIjI,KAAKjP,KAAKf,OAAQe,KAAKgF,KAAK,GAEjChF,KAAKkX,IAAII,kBACXtX,KAAKkX,IAAII,iBAAiB,eAAgB,oBAE5CtX,KAAKkX,IAAI1R,KAAKwR,GAGhB,QACMhX,KAAKoX,WACP,GAAQG,qBAAqBvX,KAAKoX,UAClCpX,KAAKoX,SAAW,MAEdpX,KAAKkX,MACPlX,KAAKoE,MAAMoT,aAAaxX,KAAKkX,KAC7BlX,KAAKkX,IAAM,MAIf,QAAQhM,EAAgBvM,GACtB,OAAa,CACX,IAAI8Y,EAAQzX,KAAK0X,cAAc/Y,GAC/B,IAAI8Y,EAGF,MAFAzX,KAAK6F,KAAK,QAAS,CAAEqF,OAAQA,EAAQvM,KAAM8Y,IAK3CzX,KAAK2X,gBAAgBhZ,IACvBqB,KAAK6F,KAAK,mBAIN,cAAc+R,GACpB,IAAIC,EAAaD,EAAO9W,MAAMd,KAAKiX,UAC/Ba,EAAoBD,EAAWpa,QAAQ,MAE3C,OAA2B,IAAvBqa,GACF9X,KAAKiX,UAAYa,EAAoB,EAC9BD,EAAW/W,MAAM,EAAGgX,IAGpB,KAIH,gBAAgBF,GACtB,OAAO5X,KAAKiX,WAAaW,EAAOhb,QAAUgb,EAAOhb,OAzE3B,QCL1B,IAAKmb,IAAL,SAAKA,GACH,+BACA,mBACA,uBAHF,CAAKA,QAAK,KAMK,UCGXC,GAAgB,EA0LpB,SAASC,GAAajT,GACpB,IAAIkT,GAAkC,IAAtBlT,EAAIvH,QAAQ,KAAc,IAAM,IAChD,OAAOuH,EAAMkT,EAAY,OAAQ,IAAIzX,KAAS,MAAQuX,KAQxD,SAASG,GAAazP,GACpB,OAAO,GAAQ0P,UAAU1P,GAaZ,OAhNf,MAaE,YAAYtE,EAAoBY,GAC9BhF,KAAKoE,MAAQA,EACbpE,KAAKqY,QAAUF,GAAa,KAAQ,IAuLxC,SAAsBvb,GAGpB,IAFA,IAAIuB,EAAS,GAEJ1D,EAAI,EAAGA,EAAImC,EAAQnC,IAC1B0D,EAAOlB,KAAKkb,GAAa,IAAItZ,SAAS,KAGxC,OAAOV,EAAOhB,KAAK,IA9LyBmb,CAAa,GACvDtY,KAAKuY,SA4JT,SAAqBvT,GACnB,IAAIwT,EAAQ,qBAAqBC,KAAKzT,GACtC,MAAO,CACL0T,KAAMF,EAAM,GACZxU,YAAawU,EAAM,IAhKHG,CAAY3T,GAC5BhF,KAAK4Y,WAAa,GAAMC,WACxB7Y,KAAK8Y,aAGP,KAAK9B,GACH,OAAOhX,KAAK+Y,QAAQ5Z,KAAKpC,UAAU,CAACia,KAGtC,OACEhX,KAAKoE,MAAM4U,cAAchZ,MAG3B,MAAMiG,EAAWC,GACflG,KAAKwG,QAAQP,EAAMC,GAAQ,GAI7B,QAAQ8Q,GACN,GAAIhX,KAAK4Y,aAAe,GAAMK,KAW5B,OAAO,EAVP,IAKE,OAJA,GAAQC,oBACN,OACAjB,IA6IUjT,EA7IchF,KAAKuY,SA6IDF,EA7IWrY,KAAKqY,QA8I7CrT,EAAI0T,KAAO,IAAML,EAAU,eA7I1Bc,MAAMnC,IACD,EACP,MAAO5X,GACP,OAAO,EAyIf,IAAoB4F,EAAkBqT,EAjIpC,YACErY,KAAKoZ,cACLpZ,KAAK8Y,aAIP,QAAQ7S,EAAMC,EAAQC,GACpBnG,KAAKoZ,cACLpZ,KAAK4Y,WAAa,GAAMS,OACpBrZ,KAAKuG,SACPvG,KAAKuG,QAAQ,CACXN,KAAMA,EACNC,OAAQA,EACRC,SAAUA,IAKR,QAAQsR,GAQd,IAAIT,EAPJ,GAAqB,MAAjBS,EAAMvM,OASV,OANIlL,KAAK4Y,aAAe,GAAMK,MAC5BjZ,KAAK4G,aAII6Q,EAAM9Y,KAAKmC,MAAM,EAAG,IAE7B,IAAK,IACHkW,EAAU7X,KAAK6J,MAAMyO,EAAM9Y,KAAKmC,MAAM,IAAM,MAC5Cd,KAAKqG,OAAO2Q,GACZ,MACF,IAAK,IACHA,EAAU7X,KAAK6J,MAAMyO,EAAM9Y,KAAKmC,MAAM,IAAM,MAC5C,IAAK,IAAIrG,EAAI,EAAGA,EAAIuc,EAAQpa,OAAQnC,IAClCuF,KAAKsZ,QAAQtC,EAAQvc,IAEvB,MACF,IAAK,IACHuc,EAAU7X,KAAK6J,MAAMyO,EAAM9Y,KAAKmC,MAAM,IAAM,QAC5Cd,KAAKsZ,QAAQtC,GACb,MACF,IAAK,IACHhX,KAAKoE,MAAMmV,YAAYvZ,MACvB,MACF,IAAK,IACHgX,EAAU7X,KAAK6J,MAAMyO,EAAM9Y,KAAKmC,MAAM,IAAM,MAC5Cd,KAAKwG,QAAQwQ,EAAQ,GAAIA,EAAQ,IAAI,IAKnC,OAAO1S,GAqFjB,IAAqBU,EAAawU,EAC5BC,EArFEzZ,KAAK4Y,aAAe,GAAMC,YACxBvU,GAAWA,EAAQkV,WACrBxZ,KAAKuY,SAASG,MAkFD1T,EAlFoBhF,KAAKuY,SAASG,KAkFrBc,EAlF2BlV,EAAQkV,UAmF/DC,EAAW,oCAAoChB,KAAKzT,IACxC,GAAKwU,EAAWC,EAAS,KAlFrCzZ,KAAK4Y,WAAa,GAAMK,KAEpBjZ,KAAK4F,QACP5F,KAAK4F,UAGP5F,KAAKwG,QAAQ,KAAM,uBAAuB,GAItC,QAAQ2C,GACVnJ,KAAK4Y,aAAe,GAAMK,MAAQjZ,KAAKyG,WACzCzG,KAAKyG,UAAU,CAAE9H,KAAMwK,IAInB,aACFnJ,KAAK2G,YACP3G,KAAK2G,aAID,QAAQvG,GACVJ,KAAKsG,SACPtG,KAAKsG,QAAQlG,GAIT,aACNJ,KAAK0Z,OAAS,GAAQR,oBACpB,OACAjB,GAAajY,KAAKoE,MAAMuV,cAAc3Z,KAAKuY,SAAUvY,KAAKqY,WAG5DrY,KAAK0Z,OAAOzd,KAAK,QAASwb,IACxBzX,KAAK4Z,QAAQnC,KAEfzX,KAAK0Z,OAAOzd,KAAK,WAAYiP,IAC3BlL,KAAKoE,MAAMyV,WAAW7Z,KAAMkL,KAE9BlL,KAAK0Z,OAAOzd,KAAK,kBAAmB,KAClC+D,KAAK8Z,cAGP,IACE9Z,KAAK0Z,OAAOP,QACZ,MAAO/Y,GACP,IAAKO,MAAM,KACTX,KAAKmF,QAAQ/E,GACbJ,KAAKwG,QAAQ,KAAM,6BAA6B,MAK9C,cACFxG,KAAK0Z,SACP1Z,KAAK0Z,OAAOK,aACZ/Z,KAAK0Z,OAAOnU,QACZvF,KAAK0Z,OAAS,QChKL,GAfU,CACvBC,cAAe,SAAS3U,EAAKqT,GAC3B,OAAOrT,EAAI0T,KAAO,IAAML,EAAU,iBAAmBrT,EAAIhB,aAE3DuV,YAAa,SAASxU,GACpBA,EAAOgU,QAAQ,OAEjBC,cAAe,SAASjU,GACtBA,EAAOgU,QAAQ,OAEjBc,WAAY,SAAS9U,EAAQmG,GAC3BnG,EAAOyB,QAAQ,KAAM,2BAA6B0E,EAAS,KAAK,KCSrD,GAnBU,CACvByO,cAAe,SAAS3U,EAAkBqT,GACxC,OAAOrT,EAAI0T,KAAO,IAAML,EAAU,OAASrT,EAAIhB,aAEjDuV,YAAa,aAGbP,cAAe,SAASjU,GACtBA,EAAOgU,QAAQ,OAEjBc,WAAY,SAAS9U,EAAQmG,GACZ,MAAXA,EACFnG,EAAO+U,YAEP/U,EAAOyB,QAAQ,KAAM,2BAA6B0E,EAAS,KAAK,KCgBvD,GA7BW,CACxBiM,WAAY,SAASpS,GACnB,IACImS,EAAM,IADQ,GAAQ8C,aAmB1B,OAjBA9C,EAAI+C,mBAAqB/C,EAAIgD,WAAa,WACxC,OAAQhD,EAAI0B,YACV,KAAK,EACC1B,EAAIiD,cAAgBjD,EAAIiD,aAAavd,OAAS,GAChDmI,EAAO6U,QAAQ1C,EAAIhM,OAAQgM,EAAIiD,cAEjC,MACF,KAAK,EAECjD,EAAIiD,cAAgBjD,EAAIiD,aAAavd,OAAS,GAChDmI,EAAO6U,QAAQ1C,EAAIhM,OAAQgM,EAAIiD,cAEjCpV,EAAOc,KAAK,WAAYqR,EAAIhM,QAC5BnG,EAAOQ,UAIN2R,GAETM,aAAc,SAASN,GACrBA,EAAI+C,mBAAqB,KACzB/C,EAAIvG,UC+BO,GApDO,CACpByJ,mBAAA,GACAC,WAA6B,EAC7B7V,+BCRa,WACFxE,KAEN0E,SAASmC,KAFH7G,KAGJ+F,qBAAqB,CACxB1C,UAJOrD,KAIShF,MAJTgF,KAIsBsE,QAAQV,OAAS,IAAM,OAJ7C5D,KAQFoE,MAAM8C,gBARJlH,KASJoF,YAAY,eATRpF,KAWJwG,WDHPe,YEJsB,CACtB,sBAAsBvC,GACpB,OAAOhF,KAAKsa,aAAa,GAAgBtV,IAG3C,oBAAoBA,GAClB,OAAOhF,KAAKsa,aAAa,GAActV,IAGzCsV,aAAY,CAAClW,EAAoBY,IACxB,IAAI,GAAWZ,EAAOY,GAG/B,UAAU/F,EAAgB+F,GACxB,OAAOhF,KAAKua,cAAc,GAAUtb,EAAQ+F,IAG9CuV,cAAa,CAACnW,EAAqBnF,EAAgB+F,IAC1C,IAAI,GAAYZ,EAAOnF,EAAQ+F,IFZxC,MAAMwV,GACJA,EAAYC,SAGd,oBAIAC,kBAAiB,IACR,IACL,IAAyB,CAAEzW,GAAI,EAAWA,KAAM,SAAStI,GACvD,OAAOA,EAAEqL,YAAY,QAK3B2T,YAAW,IACF,QAGT/S,eAAc,KACL,EAGT,oBAAoB3I,EAAgB+F,GAClC,GAAIhF,KAAK4H,iBACP,OAAO5H,KAAKuH,YAAYqT,UAAU3b,EAAQ+F,GAE1C,KAAM,gDAIV,YAEE,OAAO,IADWhF,KAAKga,cAIzB,gBAAgBhV,GAEd,OAAO,IADWhF,KAAKmH,kBAChB,CAAgBnC,IAGzB,kBAAkBkF,KAClB,qBAAqBA,M,oBGrDvB,SAAS2Q,GAAyBC,GAChC,MAA8C,SAAvCA,EAAgBhV,KAAKiV,cAGvB,MAAM,WAAgB,IAG3B,cACE/Y,QACAhC,KAAKgb,QAAS,EAEd,KAAcC,QAAQC,KAAKJ,IACzB9a,KAAKgb,OAASH,GAAyBC,KAGzC,KAAcK,iBAAiBL,IAC7B,IAAIM,EAAcP,GAAyBC,GAKvC9a,KAAKgb,SAAWI,IACpBpb,KAAKgb,OAASI,EACVpb,KAAKgb,OACPhb,KAAK6F,KAAK,UAEV7F,KAAK6F,KAAK,cAKhB,WACE,OAAO7F,KAAKgb,QAIT,ICzCKK,GDyCD,GAAU,IAAI,ICzCzB,SAAYA,GACV,2CACA,+CAFF,CAAYA,QAAe,KCyFZ,OA3Ea,SAC1Brd,EACAsd,EACAC,EACAC,EACA5a,GAEA,MAAMsW,EAAM,GAAQ0D,YAKpB,IAAK,IAAIa,KAJTvE,EAAIjI,KAAK,OAAQsM,EAAYnY,UAAU,GAGvC8T,EAAII,iBAAiB,eAAgB,qCACdiE,EAAYG,QACjCxE,EAAII,iBAAiBmE,EAAYF,EAAYG,QAAQD,IAEvD,GAAmC,MAA/BF,EAAYI,gBAAyB,CACvC,IAAIC,EAAiBL,EAAYI,kBACjC,IAAK,IAAIF,KAAcG,EACrB1E,EAAII,iBAAiBmE,EAAYG,EAAeH,IAsDpD,OAlDAvE,EAAI+C,mBAAqB,WACvB,GAAuB,IAAnB/C,EAAI0B,WACN,GAAmB,MAAf1B,EAAIhM,OAAgB,CACtB,IAAIvM,EACAkd,GAAS,EAEb,IACEld,EAAOQ,KAAK6J,MAAMkO,EAAIiD,cACtB0B,GAAS,EACT,MAAOzc,GACPwB,EACE,IAAIqK,EACF,IACA,sBAAsBuQ,EAAgB3c,uEACpCqY,EAAIiD,gBAGR,MAIA0B,GAEFjb,EAAS,KAAMjC,OAEZ,CACL,IAAIuN,EAAS,GACb,OAAQsP,GACN,KAAKH,GAAgBS,mBACnB5P,EAAS,EAAwB,0BACjC,MACF,KAAKmP,GAAgBU,qBACnB7P,EAAS,oEAAoE,EAC3E,yBAINtL,EACE,IAAIqK,EACFiM,EAAIhM,OACJ,uCAAuCsQ,EAAgB3c,0CACjCqY,EAAIhM,eAAeqQ,EAAYnY,aAAa8I,KAEpE,QAMRgL,EAAI1R,KAAK8V,GACFpE,GCrCM,GALL,CACRlc,KAAM,MACNsP,SAvCa,SAAS0R,EAAwBpY,GAC9C,OAAO,SAASjF,EAAWiC,GACzB,IACIoE,EADS,QAAUpB,EAAS,IAAM,IAAM,OAEhCoY,EAAOC,MAAQD,EAAO1X,QAAQ2X,MAAQD,EAAO1X,QAAQ9E,KAGjEwF,GAAO,MAFK,IAA6BrG,GAIzC,IAAIuY,EAAM,GAAQ0D,YAClB1D,EAAIjI,KAAK,MAAOjK,GAAK,GAErBkS,EAAI+C,mBAAqB,WACvB,GAAuB,IAAnB/C,EAAI0B,WAAkB,CACxB,IAAI,OAAE1N,EAAM,aAAEiP,GAAiBjD,EAC/B,GAAe,MAAXhM,EAIF,YAHA,IAAO5F,MACL,kCAAkC4F,2BAKtC,IACE,IAAI,KAAE+Q,GAAS9c,KAAK6J,MAAMmR,GAC1B,MAAO/a,GACP,IAAOkG,MAAM,yCAAyC6U,GAEpD8B,IACFD,EAAOC,KAAOA,KAKpB/E,EAAI1R,UC9BR,MACE4U,mBAAkB,GAClBC,WAAU,SACV6B,GAAK,YACLvB,GAAW,eACX/S,GAAc,gBACd2M,GAAe,UACfqG,GAAS,gBACTxT,GAAe,kBACfiQ,GAAiB,qBACjBE,GAAoB,+BACpB/S,GAA8B,oBAC9B0U,GAAmB,YACnB3R,IACE,GAwCW,IChEV4U,GDgEU,GAtCc,CAC3B/B,mBAAkB,GAClBC,WAAU,GACV6B,SACAvB,eACA/S,kBACA2M,mBACAqG,aACAxT,mBACAiQ,qBACAE,wBACA/S,kCACA0U,uBACA3R,eAEA8C,kBAAmB,GAEnB+R,eAAc,KACL,CAAEC,KAAM,KAGjBlV,gBAAe,IACNmV,UAGTtC,UAAS,IACAuC,eAGT3M,WAAU,IACD,GAGTwI,UAAU1P,GACDD,KAAK+T,MAAM/T,KAAKgU,SAAW/T,KC5DtC,SAAKyT,GACH,qBACA,mBACA,qBAHF,CAAKA,QAAa,KAMH,UCOA,MAAM,GAQnB,YAAYngB,EAAaqc,EAAiB/T,GACxCtE,KAAKhE,IAAMA,EACXgE,KAAKqY,QAAUA,EACfrY,KAAK0c,OAAS,GACd1c,KAAKsE,QAAUA,GAAW,GAC1BtE,KAAK2c,KAAO,EACZ3c,KAAK4c,SAAW,EAGlB,IAAIC,EAAO1T,GACL0T,GAAS7c,KAAKsE,QAAQuY,QACxB7c,KAAK0c,OAAOzf,KACV,IAAmB,GAAIkM,EAAO,CAAE0L,UAAW,IAAKrU,SAE9CR,KAAKsE,QAAQwY,OAAS9c,KAAK0c,OAAO9f,OAASoD,KAAKsE,QAAQwY,OAC1D9c,KAAK0c,OAAOK,SAKlB,MAAM5T,GACJnJ,KAAKF,IAAI,GAAMkd,MAAO7T,GAGxB,KAAKA,GACHnJ,KAAKF,IAAI,GAAMmd,KAAM9T,GAGvB,MAAMA,GACJnJ,KAAKF,IAAI,GAAMod,MAAO/T,GAGxB,UACE,OAA8B,IAAvBnJ,KAAK0c,OAAO9f,OAGrB,KAAKugB,EAAQvc,GACX,IAAIjC,EAAO,IACT,CACE0Z,QAASrY,KAAKqY,QACd+E,OAAQpd,KAAK2c,KAAO,EACpB3gB,IAAKgE,KAAKhE,IACVqhB,IAAK,KACLC,QAAStd,KAAKsE,QAAQgZ,QACtBC,QAASvd,KAAKsE,QAAQiZ,QACtBC,SAAUxd,KAAKsE,QAAQkZ,SACvB9Y,SAAU1E,KAAK0c,QAEjB1c,KAAKsE,QAAQvF,QAaf,OAVAiB,KAAK0c,OAAS,GACdS,EAAOxe,EAAM,CAACyB,EAAOjC,KACdiC,GACHJ,KAAK2c,OAEH/b,GACFA,EAASR,EAAOjC,MAIb,EAGT,mBAEE,OADA6B,KAAK4c,WACE5c,KAAK4c,UCvED,MAAM,GAMnB,YACE5hB,EACAqJ,EACAhB,EACAiB,GAEAtE,KAAKhF,KAAOA,EACZgF,KAAKqE,SAAWA,EAChBrE,KAAKqD,UAAYA,EACjBrD,KAAKsE,QAAUA,GAAW,GAO5B,cACE,OAAOtE,KAAKqD,UAAU2D,YAAY,CAChCpD,OAAQ5D,KAAKsE,QAAQV,SASzB,QAAQ6P,EAAqB7S,GAC3B,IAAKZ,KAAKgH,cACR,OAAOyW,GAAY,IAAI,EAA8B7c,GAChD,GAAIZ,KAAKqE,SAAWoP,EACzB,OAAOgK,GAAY,IAAI,EAAkC7c,GAG3D,IAAI6Q,GAAY,EACZpO,EAAYrD,KAAKqD,UAAU+E,iBAC7BpI,KAAKhF,KACLgF,KAAKqE,SACLrE,KAAKsE,QAAQtI,IACbgE,KAAKsE,SAEHiM,EAAY,KAEZmN,EAAgB,WAClBra,EAAU9B,OAAO,cAAemc,GAChCra,EAAUmN,WAERnK,EAAS,WACXkK,EAAY,EAAQyC,gBAAgB3P,GAAW,SAASlF,GACtDsT,GAAY,EACZrL,IACAxF,EAAS,KAAMzC,OAGfgH,EAAU,SAAS/E,GACrBgG,IACAxF,EAASR,IAEPkI,EAAW,WAEb,IAAIqV,EADJvX,IAOAuX,EAAsB,IAA8Bta,GACpDzC,EAAS,IAAI,EAAuB+c,KAGlCvX,EAAkB,WACpB/C,EAAU9B,OAAO,cAAemc,GAChCra,EAAU9B,OAAO,OAAQ8E,GACzBhD,EAAU9B,OAAO,QAAS4D,GAC1B9B,EAAU9B,OAAO,SAAU+G,IAW7B,OARAjF,EAAUpH,KAAK,cAAeyhB,GAC9Bra,EAAUpH,KAAK,OAAQoK,GACvBhD,EAAUpH,KAAK,QAASkJ,GACxB9B,EAAUpH,KAAK,SAAUqM,GAGzBjF,EAAUkB,aAEH,CACLoM,MAAO,KACDc,IAGJrL,IACImK,EACFA,EAAUhL,QAEVlC,EAAUkC,UAGdqO,iBAAkBrX,IACZkV,GAGAzR,KAAKqE,SAAW9H,IACdgU,EACFA,EAAUhL,QAEVlC,EAAUkC,YAQtB,SAASkY,GAAYrd,EAAcQ,GAIjC,OAHA,IAAKD,OAAM,WACTC,EAASR,MAEJ,CACLuQ,MAAO,aACPiD,iBAAkB,cCnItB,MAAQyG,WAAU,IAAK,GAEhB,IAAI,GAAkB,SAC3B1N,EACA3R,EACA8K,EACAzB,EACAC,EACAyD,GAEA,IAWI1E,EAXAua,EAAiB,GAAW9X,GAChC,IAAK8X,EACH,MAAM,IAAI,EAA4B9X,GA0BxC,QAtBI6G,EAAOkR,oBACuD,IAA9D,IAAyBlR,EAAOkR,kBAAmB7iB,IACnD2R,EAAOmR,qBACwD,IAA/D,IAAyBnR,EAAOmR,mBAAoB9iB,KAItDsJ,EAAUnJ,OAAOsR,OACf,CAAEsR,iBAAkBpR,EAAOoR,kBAC3BzZ,GAGFjB,EAAY,IAAI,GACdrI,EACAqJ,EACA0D,EAAUA,EAAQiW,aAAaJ,GAAkBA,EACjDtZ,IAGFjB,EAAY,GAGPA,GAGL,GAAgC,CAClC2D,YAAa,WACX,OAAO,GAETwJ,QAAS,SAAS3S,EAAG+C,GACnB,IAAIqd,EAAW,IAAKtd,OAAM,WACxBC,EAAS,IAAI,MAEf,MAAO,CACL+P,MAAO,WACLsN,EAAS/M,iBAEX0C,iBAAkB,gB,QCFT,OAtBb2H,IAEA,QAA+D,IAApD,GAAQa,iBAAiBb,EAAYlY,WAC9C,KAAM,IAAIkY,EAAYlY,gDAGxB,MAAO,CACLtE,EACA6B,KAEA,MAAM0a,EAvCkB,EAC1Bvc,EACAwc,KAEA,IAAID,EAAQ,aAAe1c,mBAAmBG,EAAOiN,UAErD,IAAK,IAAIhQ,KAAOuf,EAAYxc,OAC1Buc,GACE,IACA1c,mBAAmB5C,GACnB,IACA4C,mBAAmB2c,EAAYxc,OAAO/C,IAG1C,GAAkC,MAA9Buf,EAAY2C,eAAwB,CACtC,IAAIC,EAAgB5C,EAAY2C,iBAChC,IAAK,IAAIliB,KAAOmiB,EACd7C,GACE,IACA1c,mBAAmB5C,GACnB,IACA4C,mBAAmBuf,EAAcniB,IAIvC,OAAOsf,GAcS8C,CAAoBrf,EAAQwc,GAE1C,GAAQa,iBAAiBb,EAAYlY,WACnC,GACAiY,EACAC,EACAF,GAAgBS,mBAChBlb,KCOS,OAtBb2a,IAEA,QAA+D,IAApD,GAAQa,iBAAiBb,EAAYlY,WAC9C,KAAM,IAAIkY,EAAYlY,gDAGxB,MAAO,CACLtE,EACA6B,KAEA,MAAM0a,EAzCkB,EAC1Bvc,EACAwc,KAEA,IAAID,EAAQ,aAAe1c,mBAAmBG,EAAOiN,UAIrD,IAAK,IAAIhQ,KAFTsf,GAAS,iBAAmB1c,mBAAmBG,EAAO8N,aAEtC0O,EAAYxc,OAC1Buc,GACE,IACA1c,mBAAmB5C,GACnB,IACA4C,mBAAmB2c,EAAYxc,OAAO/C,IAG1C,GAAkC,MAA9Buf,EAAY2C,eAAwB,CACtC,IAAIC,EAAgB5C,EAAY2C,iBAChC,IAAK,IAAIliB,KAAOmiB,EACd7C,GACE,IACA1c,mBAAmB5C,GACnB,IACA4C,mBAAmBuf,EAAcniB,IAIvC,OAAOsf,GAcS,CAAoBvc,EAAQwc,GAE1C,GAAQa,iBAAiBb,EAAYlY,WACnC,GACAiY,EACAC,EACAF,GAAgBU,qBAChBnb,KCgCN,SAASyd,GAAYC,GACnB,OAAIA,EAAK7b,SACA6b,EAAK7b,SAEV6b,EAAKf,QACA,UAAUe,EAAKf,qBAEjB,EAAS9a,SAGlB,SAAS8b,GAAiBD,GACxB,OAAIA,EAAKrI,OACAqI,EAAKrI,OAMP,MAJ4BqI,EAAKf,qBAO1C,SAASiB,GAAaF,GACpB,MAA8B,WAA1B,GAAQ3D,gBAEiB,IAAlB2D,EAAKG,SASlB,SAASC,GAAqBJ,GAC5B,MAAI,gBAAiBA,EACZA,EAAKK,YAEV,iBAAkBL,IACZA,EAAKM,aAKjB,SAASC,GAAuBP,GAC9B,MAAMnb,EAAqB,OAAH,wBACnB,EAASA,oBACTmb,EAAKnb,oBAEV,MACE,kBAAmBA,GACoB,MAAvCA,EAAkC,cAE3BA,EAAkC,cAGpC,GAAkBA,GA8B3B,SAAS2b,GACPR,EACA1S,GAEA,MAAMtI,EA/BR,SAA0Bgb,EAAe1S,GACvC,IAAItI,EAuBJ,MAtBI,yBAA0Bgb,EAC5Bhb,EAAuB,OAAH,wBACf,EAASA,sBACTgb,EAAKhb,uBAGVA,EAAuB,CACrBD,UAAWib,EAAKvb,eAAiB,EAASA,cAC1CK,SAAUkb,EAAKxb,cAAgB,EAASA,cAEtC,SAAUwb,IACR,WAAYA,EAAKrS,OAAM3I,EAAqBvE,OAASuf,EAAKrS,KAAKlN,QAC/D,YAAauf,EAAKrS,OACpB3I,EAAqBoY,QAAU4C,EAAKrS,KAAKyP,UAEzC,eAAgB4C,IAClBhb,EAAqByb,cCxIW,EACpCnT,EACA2P,EACAyD,KAEA,MAAMC,EAA2D,CAC/Dlc,cAAewY,EAAYlY,UAC3BP,aAAcyY,EAAYnY,SAC1B6I,KAAM,CACJlN,OAAQwc,EAAYxc,OACpB2c,QAASH,EAAYG,UAGzB,MAAO,CACL3c,EACA6B,KAEA,MAAMwI,EAAUwC,EAAOxC,QAAQrK,EAAO8N,aAIiBmS,EACrD5V,EACA6V,GAEgBzS,UAAUzN,EAAOiN,SAAUpL,KD+GNse,CACnCtT,EACAtI,EACAgb,EAAKa,cAGJ7b,EAOsB8b,CAAiBd,EAAM1S,GACpD,MACE,kBAAmBtI,GACsB,MAAzCA,EAAoC,cAE7BA,EAAoC,cAGtC,GAAkBA,GEvLZ,MAAM,WAAwB,IAG3C,YAAmBsI,GACjB5J,OAAM,SAASZ,EAAWzC,GACxB,IAAO2G,MAAM,wCAAwClE,MAGvDpB,KAAK4L,OAASA,EACd5L,KAAKqf,6BAGP,YAAYnW,GACVA,EAAYvK,KAAK+d,OAAO4C,QAAQC,IAC9Bvf,KAAK6F,KAAK0Z,EAAevkB,KAAMukB,KAI3B,6BACNvf,KAAK4L,OAAOzD,WAAWlM,KAAK,UAAWiN,IAEnB,qCADFA,EAAYC,OAE1BnJ,KAAKyO,YAAYvF,MCjBV,OATf,WACE,IAAIsW,EAASC,EAKb,MAAO,CAAEC,QAJO,IAAIC,QAAQ,CAACC,EAAKC,KAChCL,EAAUI,EACVH,EAASI,IAEOL,UAASC,WCKd,MAAM,WAAmB,IAStC,YAAmB7T,GACjB5J,OAAM,SAASZ,EAAWzC,GACxB,IAAO2G,MAAM,4BAA8BlE,MAT/C,KAAA0e,kBAA4B,EAC5B,KAAAhS,UAAiB,KACjB,KAAAiS,oBAA+B,KAC/B,KAAAlS,kBAAkC,KAE1B,KAAAmS,mBAA+B,KA8D/B,KAAAC,aAA2C,CACjDC,EACAzS,KAEA,GAAIyS,EAGF,OAFA,IAAO/f,KAAK,wBAAwB+f,QACpClgB,KAAKmgB,WAIPngB,KAAK4L,OAAO/B,WAAW,gBAAiB,CACtCoC,KAAMwB,EAASxB,KACf6B,UAAWL,EAASK,aApEtB9N,KAAK4L,OAASA,EACd5L,KAAK4L,OAAOzD,WAAWlM,KAAK,eAAgB,EAAGoW,WAAUC,cACtC,cAAbD,GAAwC,cAAZC,GAC9BtS,KAAKogB,UAEU,cAAb/N,GAAwC,cAAZC,IAC9BtS,KAAKmgB,WACLngB,KAAKqgB,+BAITrgB,KAAKsgB,UAAY,IAAI,GAAgB1U,GAErC5L,KAAK4L,OAAOzD,WAAWlM,KAAK,UAAWkN,IAEnB,0BADFA,EAAMA,OAEpBnJ,KAAKugB,iBAAiBpX,EAAMxK,MAG5BqB,KAAK+f,qBACL/f,KAAK+f,oBAAoB/kB,OAASmO,EAAMC,SAExCpJ,KAAK+f,oBAAoBtR,YAAYtF,KAKpC,SACDnJ,KAAK8f,mBAIT9f,KAAK8f,kBAAmB,EACxB9f,KAAKogB,WAGC,UACDpgB,KAAK8f,mBAIV9f,KAAKqgB,4BAEgC,cAAjCrgB,KAAK4L,OAAOzD,WAAW1D,OAK3BzE,KAAK4L,OAAOe,OAAO6T,kBACjB,CACExU,SAAUhM,KAAK4L,OAAOzD,WAAWuB,WAEnC1J,KAAKigB,eAsBD,iBAAiBthB,GACvB,IACEqB,KAAK8N,UAAY3O,KAAK6J,MAAMrK,EAAKmP,WACjC,MAAO1O,GAGP,OAFA,IAAOgB,MAAM,0CAA0CzB,EAAKmP,gBAC5D9N,KAAKmgB,WAIP,GAAiC,iBAAtBngB,KAAK8N,UAAUnJ,IAAyC,KAAtB3E,KAAK8N,UAAUnJ,GAK1D,OAJA,IAAOvE,MACL,+CAA+CJ,KAAK8N,gBAEtD9N,KAAKmgB,WAKPngB,KAAKggB,qBACLhgB,KAAKygB,qBAGC,qBAYNzgB,KAAK+f,oBAAsB,IAAI,EAC7B,mBAAmB/f,KAAK8N,UAAUnJ,GAClC3E,KAAK4L,QAEP5L,KAAK+f,oBAAoBW,YAAY,CAACtf,EAAWzC,KAEH,IAA1CyC,EAAU3D,QAAQ,qBACe,IAAjC2D,EAAU3D,QAAQ,YAKpBuC,KAAK6F,KAAKzE,EAAWzC,KAvBGyK,KACpBA,EAAQ0C,qBAAuB1C,EAAQ2C,sBACzC3C,EAAQuX,wBAEPvX,EAAQ0C,qBACwB,cAAjC9L,KAAK4L,OAAOzD,WAAW1D,OAEvB2E,EAAQwX,aAkBZC,CAAkB7gB,KAAK+f,qBAGjB,WACN/f,KAAK8N,UAAY,KACb9N,KAAK+f,sBACP/f,KAAK+f,oBAAoBhG,aACzB/Z,KAAK+f,oBAAoB1R,aACzBrO,KAAK+f,oBAAsB,MAGzB/f,KAAK8f,kBAGP9f,KAAKggB,qBAID,4BACN,IAAKhgB,KAAK8f,iBACR,OAIF,GAAI9f,KAAK6N,oBAAuB7N,KAAK6N,kBAA0BiT,KAC7D,OAKF,MAAM,QAAEpB,EAAO,QAAEF,EAASC,OAAQ5hB,GAAM,KACvC6hB,EAAgBoB,MAAO,EACxB,MAAMC,EAAU,KACbrB,EAAgBoB,MAAO,GAE1BpB,EAAQxE,KAAK6F,GAASC,MAAMD,GAC5B/gB,KAAK6N,kBAAoB6R,EACzB1f,KAAKggB,mBAAqBR,GC/J9B,MAAqB,GAYnB,eACE,GAAOyB,SAAU,EACjB,IAAK,IAAIxmB,EAAI,EAAGC,EAAI,GAAOwmB,UAAUtkB,OAAQnC,EAAIC,EAAGD,IAClD,GAAOymB,UAAUzmB,GAAG+V,UAMhB,2BACN,OAAO,IACL,IAAyB,CAAEvM,GAAI,GAAQoW,WAAWpW,KAAM,SAAStI,GAC/D,OAAOA,EAAEqL,YAAY,QAgB3B,YAAYma,EAAiB7c,IAsL/B,SAAqBtI,GACnB,GAAIA,QACF,KAAM,0DAvLNolB,CAAYD,GACZ,aAAgB7c,GAChBtE,KAAKhE,IAAMmlB,EACXnhB,KAAK2M,OLfF,SAAmB2R,EAAe1S,GACvC,IAAIe,EAAiB,CACnB3J,gBAAiBsb,EAAKtb,iBAAmB,EAASA,gBAClDua,QAASe,EAAKf,QACd3a,SAAU0b,EAAK1b,UAAY,EAASA,SACpCF,SAAU4b,EAAK5b,UAAY,EAASA,SACpCC,UAAW2b,EAAK3b,WAAa,EAASA,UACtCM,YAAaqb,EAAKrb,aAAe,EAASA,YAC1Coe,UAAW/C,EAAK+C,WAAa,EAASxe,WACtCK,mBAAoBob,EAAKpb,oBAAsB,EAASA,mBACxDV,OAAQ8b,EAAK9b,QAAU,EAASA,OAChCF,OAAQgc,EAAKhc,QAAU,EAASA,OAChCC,QAAS+b,EAAK/b,SAAW,EAASA,QAElCoc,YAAaD,GAAqBJ,GAClC7b,SAAU4b,GAAYC,GACtB1a,OAAQ4a,GAAaF,GACrBrI,OAAQsI,GAAiBD,GAEzBkC,kBAAmB3B,GAAuBP,GAC1C1R,kBAAmBkS,GAAuBR,EAAM1S,IAclD,MAXI,uBAAwB0S,IAC1B3R,EAAOmR,mBAAqBQ,EAAKR,oBAC/B,sBAAuBQ,IACzB3R,EAAOkR,kBAAoBS,EAAKT,mBAC9B,qBAAsBS,IACxB3R,EAAOoR,iBAAmBO,EAAKP,kBAC7B,mBAAoBO,IAAM3R,EAAO2U,eAAiBhD,EAAKgD,gBACvD,SAAUhD,IACZ3R,EAAO2B,KAAOgQ,EAAKhQ,MAGd3B,EKnBS4U,CAAUjd,EAAStE,MAEjCA,KAAKuS,SAAW,EAAQM,iBACxB7S,KAAKwhB,eAAiB,IAAI,IAC1BxhB,KAAKyhB,UAAY,GAAQrJ,UAAU,KAEnCpY,KAAK0E,SAAW,IAAI,GAAS1E,KAAKhE,IAAKgE,KAAKyhB,UAAW,CACrDlE,QAASvd,KAAK2M,OAAO4Q,QACrBC,SAAU,GAAO9C,oBACjB3b,OAAQiB,KAAK2M,OAAO2U,gBAAkB,GACtCxE,MAAO,GACPD,MAAO,GAAcI,KACrBK,QAAS,EAASlb,UAEhBpC,KAAK2M,OAAOgS,cACd3e,KAAK0hB,eAAiB,EAAQ3O,qBAAqB/S,KAAK0E,SAAU,CAChEuX,KAAMjc,KAAK2M,OAAO0U,UAClB7hB,KAAM,gBAAkB,GAAQ6K,kBAAkBrP,QAQtDgF,KAAKmI,WAAa,EAAQ2K,wBAAwB9S,KAAKhE,IAAK,CAC1D+U,YALiBzM,GACV,GAAQ8V,mBAAmBpa,KAAK2M,OAAQrI,EAAS,IAKxDI,SAAU1E,KAAK0E,SACf1B,gBAAiBhD,KAAK2M,OAAO3J,gBAC7BC,YAAajD,KAAK2M,OAAO1J,YACzBC,mBAAoBlD,KAAK2M,OAAOzJ,mBAChCU,OAAQrF,QAAQyB,KAAK2M,OAAO/I,UAG9B5D,KAAKmI,WAAWlM,KAAK,YAAa,KAChC+D,KAAK2hB,eACD3hB,KAAK0hB,gBACP1hB,KAAK0hB,eAAelc,KAAKxF,KAAKmI,WAAWyZ,gBAI7C5hB,KAAKmI,WAAWlM,KAAK,UAAWkN,IAC9B,IACI0Y,EAAqD,IADzC1Y,EAAMA,MACG1L,QAAQ,oBACjC,GAAI0L,EAAMC,QAAS,CACjB,IAAIA,EAAUpJ,KAAKoJ,QAAQD,EAAMC,SAC7BA,GACFA,EAAQqF,YAAYtF,GAInB0Y,GACH7hB,KAAKwhB,eAAe3b,KAAKsD,EAAMA,MAAOA,EAAMxK,QAGhDqB,KAAKmI,WAAWlM,KAAK,aAAc,KACjC+D,KAAKuS,SAASlE,eAEhBrO,KAAKmI,WAAWlM,KAAK,eAAgB,KACnC+D,KAAKuS,SAASlE,eAEhBrO,KAAKmI,WAAWlM,KAAK,QAASikB,IAC5B,IAAO/f,KAAK+f,KAGd,GAAOgB,UAAUjkB,KAAK+C,MACtBA,KAAK0E,SAASmC,KAAK,CAAEqa,UAAW,GAAOA,UAAUtkB,SAEjDoD,KAAK4N,KAAO,IAAI,GAAW5N,MAEvB,GAAOihB,SACTjhB,KAAKwQ,UAIT,QAAQxV,GACN,OAAOgF,KAAKuS,SAASuP,KAAK9mB,GAG5B,cACE,OAAOgF,KAAKuS,SAAS9T,MAGvB,UAGE,GAFAuB,KAAKmI,WAAWqI,UAEZxQ,KAAK0hB,iBACF1hB,KAAK+hB,oBAAqB,CAC7B,IAAI3S,EAAWpP,KAAKmI,WAAWyZ,aAC3BF,EAAiB1hB,KAAK0hB,eAC1B1hB,KAAK+hB,oBAAsB,IAAI,IAAc,KAAO,WAClDL,EAAelc,KAAK4J,OAM5B,aACEpP,KAAKmI,WAAWkG,aAEZrO,KAAK+hB,sBACP/hB,KAAK+hB,oBAAoB7Q,gBACzBlR,KAAK+hB,oBAAsB,MAI/B,KAAKC,EAAoBphB,EAAoB5C,GAE3C,OADAgC,KAAKwhB,eAAevlB,KAAK+lB,EAAYphB,EAAU5C,GACxCgC,KAGT,OAAOgiB,EAAqBphB,EAAqB5C,GAE/C,OADAgC,KAAKwhB,eAAejgB,OAAOygB,EAAYphB,EAAU5C,GAC1CgC,KAGT,YAAYY,GAEV,OADAZ,KAAKwhB,eAAed,YAAY9f,GACzBZ,KAGT,cAAcY,GAEZ,OADAZ,KAAKwhB,eAAehgB,cAAcZ,GAC3BZ,KAGT,WAAWY,GAET,OADAZ,KAAKwhB,eAAezH,aACb/Z,KAGT,eACE,IAAI6M,EACJ,IAAKA,KAAe7M,KAAKuS,SAASA,SAC5BvS,KAAKuS,SAASA,SAASjW,eAAeuQ,IACxC7M,KAAK4gB,UAAU/T,GAKrB,UAAUoV,GACR,IAAI7Y,EAAUpJ,KAAKuS,SAASlR,IAAI4gB,EAAcjiB,MAS9C,OARIoJ,EAAQ0C,qBAAuB1C,EAAQ2C,sBACzC3C,EAAQuX,wBAEPvX,EAAQ0C,qBACiB,cAA1B9L,KAAKmI,WAAW1D,OAEhB2E,EAAQwX,YAEHxX,EAGT,YAAY6Y,GACV,IAAI7Y,EAAUpJ,KAAKuS,SAASuP,KAAKG,GAC7B7Y,GAAWA,EAAQ0C,oBACrB1C,EAAQ8Y,sBAER9Y,EAAUpJ,KAAKuS,SAASjR,OAAO2gB,KAChB7Y,EAAQyC,YACrBzC,EAAQiD,cAKd,WAAW2V,EAAoBrjB,EAAWyK,GACxC,OAAOpJ,KAAKmI,WAAW0B,WAAWmY,EAAYrjB,EAAMyK,GAGtD,eACE,OAAOpJ,KAAK2M,OAAO/I,OAGrB,SACE5D,KAAK4N,KAAKuU,UAxNL,GAAAjB,UAAsB,GACtB,GAAAD,SAAmB,EACnB,GAAA3gB,cAAwB,EAGxB,GAAA8hB,QAA2B,GAC3B,GAAAC,gBAA6B,GAASA,gBACtC,GAAAC,sBAAmC,GAASA,sBAC5C,GAAAC,eAA4B,GAASA,eAVzB,OAoOrB,GAAQrG,MAAM,K,cC5Pd,IAAIsG,EAGJA,EAAI,WACH,OAAOxiB,KADJ,GAIJ,IAECwiB,EAAIA,GAAK,IAAIC,SAAS,cAAb,GACR,MAAOrjB,GAEc,iBAAXsjB,SAAqBF,EAAIE,QAOrCloB,EAAOD,QAAUioB,G,6BCRjB,6CAmCO,SAASG,EAAgBre,GAC9B,GAAe,MAAXA,EACF,KAAM,kCAER,GAAuB,MAAnBA,EAAQiZ,QACV,KAAM,wCAEJ,iBAAkBjZ,GACpB,IAAOnE,KACL,mE,8ZC7CN,IAOA,aAGI,WAAoByiB,QAAA,IAAAA,MAAA,UAAAA,oBAwLxB,OAtLI,YAAAC,cAAA,SAAcjmB,GACV,OAAKoD,KAAK4iB,mBAGFhmB,EAAS,GAAK,EAAI,EAAI,GAFT,EAATA,EAAa,GAAK,EAAI,GAKtC,YAAAkmB,OAAA,SAAOnkB,GAIH,IAHA,IAAIokB,EAAM,GAENtoB,EAAI,EACDA,EAAIkE,EAAK/B,OAAS,EAAGnC,GAAK,EAAG,CAChC,IAAIK,EAAK6D,EAAKlE,IAAM,GAAOkE,EAAKlE,EAAI,IAAM,EAAMkE,EAAKlE,EAAI,GACzDsoB,GAAO/iB,KAAKgjB,YAAaloB,IAAM,GAAS,IACxCioB,GAAO/iB,KAAKgjB,YAAaloB,IAAM,GAAS,IACxCioB,GAAO/iB,KAAKgjB,YAAaloB,IAAM,EAAS,IACxCioB,GAAO/iB,KAAKgjB,YAAaloB,IAAM,EAAS,IAG5C,IAAMmoB,EAAOtkB,EAAK/B,OAASnC,EAC3B,GAAIwoB,EAAO,EAAG,CACNnoB,EAAK6D,EAAKlE,IAAM,IAAgB,IAATwoB,EAAatkB,EAAKlE,EAAI,IAAM,EAAI,GAC3DsoB,GAAO/iB,KAAKgjB,YAAaloB,IAAM,GAAS,IACxCioB,GAAO/iB,KAAKgjB,YAAaloB,IAAM,GAAS,IAEpCioB,GADS,IAATE,EACOjjB,KAAKgjB,YAAaloB,IAAM,EAAS,IAEjCkF,KAAK4iB,mBAAqB,GAErCG,GAAO/iB,KAAK4iB,mBAAqB,GAGrC,OAAOG,GAGX,YAAAG,iBAAA,SAAiBtmB,GACb,OAAKoD,KAAK4iB,kBAGHhmB,EAAS,EAAI,EAAI,GAFH,EAATA,EAAa,GAAK,EAAI,GAKtC,YAAAumB,cAAA,SAAc3mB,GACV,OAAOwD,KAAKkjB,iBAAiB1mB,EAAEI,OAASoD,KAAKojB,kBAAkB5mB,KAGnE,YAAA6mB,OAAA,SAAO7mB,GACH,GAAiB,IAAbA,EAAEI,OACF,OAAO,IAAI0mB,WAAW,GAS1B,IAPA,IAAMC,EAAgBvjB,KAAKojB,kBAAkB5mB,GACvCI,EAASJ,EAAEI,OAAS2mB,EACpBR,EAAM,IAAIO,WAAWtjB,KAAKkjB,iBAAiBtmB,IAC7C4mB,EAAK,EACL/oB,EAAI,EACJgpB,EAAU,EACVC,EAAK,EAAGC,EAAK,EAAGC,EAAK,EAAGC,EAAK,EAC1BppB,EAAImC,EAAS,EAAGnC,GAAK,EACxBipB,EAAK1jB,KAAK8jB,YAAYtnB,EAAEunB,WAAWtpB,EAAI,IACvCkpB,EAAK3jB,KAAK8jB,YAAYtnB,EAAEunB,WAAWtpB,EAAI,IACvCmpB,EAAK5jB,KAAK8jB,YAAYtnB,EAAEunB,WAAWtpB,EAAI,IACvCopB,EAAK7jB,KAAK8jB,YAAYtnB,EAAEunB,WAAWtpB,EAAI,IACvCsoB,EAAIS,KAASE,GAAM,EAAMC,IAAO,EAChCZ,EAAIS,KAASG,GAAM,EAAMC,IAAO,EAChCb,EAAIS,KAASI,GAAM,EAAKC,EACxBJ,GA7ES,IA6EEC,EACXD,GA9ES,IA8EEE,EACXF,GA/ES,IA+EEG,EACXH,GAhFS,IAgFEI,EAmBf,GAjBIppB,EAAImC,EAAS,IACb8mB,EAAK1jB,KAAK8jB,YAAYtnB,EAAEunB,WAAWtpB,IACnCkpB,EAAK3jB,KAAK8jB,YAAYtnB,EAAEunB,WAAWtpB,EAAI,IACvCsoB,EAAIS,KAASE,GAAM,EAAMC,IAAO,EAChCF,GAtFS,IAsFEC,EACXD,GAvFS,IAuFEE,GAEXlpB,EAAImC,EAAS,IACbgnB,EAAK5jB,KAAK8jB,YAAYtnB,EAAEunB,WAAWtpB,EAAI,IACvCsoB,EAAIS,KAASG,GAAM,EAAMC,IAAO,EAChCH,GA5FS,IA4FEG,GAEXnpB,EAAImC,EAAS,IACbinB,EAAK7jB,KAAK8jB,YAAYtnB,EAAEunB,WAAWtpB,EAAI,IACvCsoB,EAAIS,KAASI,GAAM,EAAKC,EACxBJ,GAjGS,IAiGEI,GAEC,IAAZJ,EACA,MAAM,IAAIjZ,MAAM,kDAEpB,OAAOuY,GAYD,YAAAC,YAAV,SAAsBgB,GAqBlB,IAAI7lB,EAAS6lB,EAYb,OAVA7lB,GAAU,GAEVA,GAAY,GAAK6lB,IAAO,EAAK,EAE7B7lB,GAAY,GAAK6lB,IAAO,GAAK,GAE7B7lB,GAAY,GAAK6lB,IAAO,GAAK,GAE7B7lB,GAAY,GAAK6lB,IAAO,EAAK,EAEtBC,OAAOC,aAAa/lB,IAKrB,YAAA2lB,YAAV,SAAsBhpB,GAUlB,IAAIqD,EAlKS,IA+Kb,OAVAA,IAAa,GAAKrD,EAAMA,EAAI,MAAS,GArKxB,IAqK8CA,EAAI,GAAK,GAEpEqD,IAAa,GAAKrD,EAAMA,EAAI,MAAS,GAvKxB,IAuK8CA,EAAI,GAAK,GAEpEqD,IAAa,GAAKrD,EAAMA,EAAI,MAAS,GAzKxB,IAyK8CA,EAAI,GAAK,GAEpEqD,IAAa,GAAKrD,EAAMA,EAAI,MAAS,GA3KxB,IA2K8CA,EAAI,GAAK,EAEpEqD,IAAa,GAAKrD,EAAMA,EAAI,OAAU,GA7KzB,IA6K+CA,EAAI,GAAK,IAKjE,YAAAsoB,kBAAR,SAA0B5mB,GACtB,IAAI+mB,EAAgB,EACpB,GAAIvjB,KAAK4iB,kBAAmB,CACxB,IAAK,IAAInoB,EAAI+B,EAAEI,OAAS,EAAGnC,GAAK,GACxB+B,EAAE/B,KAAOuF,KAAK4iB,kBADanoB,IAI/B8oB,IAEJ,GAAI/mB,EAAEI,OAAS,GAAK2mB,EAAgB,EAChC,MAAM,IAAI/Y,MAAM,kCAGxB,OAAO+Y,GAGf,EA3LA,GAAa,EAAAY,QA6Lb,IAAMC,EAAW,IAAID,EAErB,kBAAuBxlB,GACnB,OAAOylB,EAAStB,OAAOnkB,IAG3B,kBAAuBnC,GACnB,OAAO4nB,EAASf,OAAO7mB,IAS3B,+B,+CAwCA,OAxCkC,OAQpB,YAAAwmB,YAAV,SAAsBgB,GAClB,IAAI7lB,EAAS6lB,EAYb,OAVA7lB,GAAU,GAEVA,GAAY,GAAK6lB,IAAO,EAAK,EAE7B7lB,GAAY,GAAK6lB,IAAO,GAAK,GAE7B7lB,GAAY,GAAK6lB,IAAO,GAAK,GAE7B7lB,GAAY,GAAK6lB,IAAO,EAAK,GAEtBC,OAAOC,aAAa/lB,IAGrB,YAAA2lB,YAAV,SAAsBhpB,GAClB,IAAIqD,EA7OS,IA0Pb,OAVAA,IAAa,GAAKrD,EAAMA,EAAI,MAAS,GAhPxB,IAgP8CA,EAAI,GAAK,GAEpEqD,IAAa,GAAKrD,EAAMA,EAAI,MAAS,GAlPxB,IAkP8CA,EAAI,GAAK,GAEpEqD,IAAa,GAAKrD,EAAMA,EAAI,MAAS,GApPxB,IAoP8CA,EAAI,GAAK,GAEpEqD,IAAa,GAAKrD,EAAMA,EAAI,MAAS,GAtPxB,IAsP8CA,EAAI,GAAK,EAEpEqD,IAAa,GAAKrD,EAAMA,EAAI,OAAU,GAxPzB,IAwP+CA,EAAI,GAAK,IAI7E,EAxCA,CAAkCqpB,GAArB,EAAAE,eA0Cb,IAAMC,EAAe,IAAID,EAEzB,yBAA8B1lB,GAC1B,OAAO2lB,EAAaxB,OAAOnkB,IAG/B,yBAA8BnC,GAC1B,OAAO8nB,EAAajB,OAAO7mB,IAIlB,EAAAqmB,cAAgB,SAACjmB,GAC1B,OAAAwnB,EAASvB,cAAcjmB,IAEd,EAAAsmB,iBAAmB,SAACtmB,GAC7B,OAAAwnB,EAASlB,iBAAiBtmB,IAEjB,EAAAumB,cAAgB,SAAC3mB,GAC1B,OAAA4nB,EAASjB,cAAc3mB,K,6BCpPZ,IAnCf,MAIE,YACE+nB,EACAC,EACAziB,EACAnB,GAEAZ,KAAKwkB,MAAQA,EACbxkB,KAAK4B,MAAQ2iB,EAAI,KACXvkB,KAAK4B,QACP5B,KAAK4B,MAAQhB,EAASZ,KAAK4B,SAE5BG,GAOL,YACE,OAAsB,OAAf/B,KAAK4B,MAId,gBACM5B,KAAK4B,QACP5B,KAAKwkB,MAAMxkB,KAAK4B,OAChB5B,KAAK4B,MAAQ,S,cCjCnBpH,EAAOD,QAAUkqB,QAAQ,oC,8BCAzB,YAAe,SAAS3B,EAAOtmB,GAC7B,OAAOkoB,EAAKC,EAAKnoB,IADnB,kCAUA,IANA,IAAI0nB,EAAeD,OAAOC,aAEtBU,EACF,mEACEC,EAAS,GAEJpqB,EAAI,EAAGC,EAAIkqB,EAAShoB,OAAQnC,EAAIC,EAAGD,IAC1CoqB,EAAOD,EAASE,OAAOrqB,IAAMA,EAG/B,IAAIsqB,EAAU,SAASjqB,GACrB,IAAIkqB,EAAKlqB,EAAEipB,WAAW,GACtB,OAAOiB,EAAK,IACRlqB,EACAkqB,EAAK,KACLd,EAAa,IAAQc,IAAO,GAAMd,EAAa,IAAa,GAALc,GACvDd,EAAa,IAASc,IAAO,GAAM,IACnCd,EAAa,IAASc,IAAO,EAAK,IAClCd,EAAa,IAAa,GAALc,IAGvBL,EAAO,SAASM,GAClB,OAAOA,EAAEC,QAAQ,gBAAiBH,IAGhCI,EAAY,SAASC,GACvB,IAAIC,EAAS,CAAC,EAAG,EAAG,GAAGD,EAAIxoB,OAAS,GAChC0oB,EACDF,EAAIrB,WAAW,IAAM,IACpBqB,EAAIxoB,OAAS,EAAIwoB,EAAIrB,WAAW,GAAK,IAAM,GAC5CqB,EAAIxoB,OAAS,EAAIwoB,EAAIrB,WAAW,GAAK,GAOxC,MANY,CACVa,EAASE,OAAOQ,IAAQ,IACxBV,EAASE,OAAQQ,IAAQ,GAAM,IAC/BD,GAAU,EAAI,IAAMT,EAASE,OAAQQ,IAAQ,EAAK,IAClDD,GAAU,EAAI,IAAMT,EAASE,OAAa,GAANQ,IAEzBnoB,KAAK,KAGhBunB,EACFzmB,EAAOymB,MACP,SAASV,GACP,OAAOA,EAAEkB,QAAQ,eAAgBC,M,8CC9CrC,6CAGe,MAAMI,EAGnB,cACEvlB,KAAKwlB,WAAa,GAGpB,IAAIxqB,GACF,OAAOgF,KAAKwlB,WAAWC,EAAOzqB,IAGhC,IAAIA,EAAc4F,EAAoB5C,GACpC,IAAI0nB,EAAoBD,EAAOzqB,GAC/BgF,KAAKwlB,WAAWE,GACd1lB,KAAKwlB,WAAWE,IAAsB,GACxC1lB,KAAKwlB,WAAWE,GAAmBzoB,KAAK,CACtCyE,GAAId,EACJ5C,QAASA,IAIb,OAAOhD,EAAe4F,EAAqB5C,GACzC,GAAKhD,GAAS4F,GAAa5C,EAA3B,CAKA,IAAI2nB,EAAQ3qB,EAAO,CAACyqB,EAAOzqB,IAAS,IAAiBgF,KAAKwlB,YAEtD5kB,GAAY5C,EACdgC,KAAK4lB,eAAeD,EAAO/kB,EAAU5C,GAErCgC,KAAK6lB,mBAAmBF,QATxB3lB,KAAKwlB,WAAa,GAad,eAAeG,EAAiB/kB,EAAoB5C,GAC1D,IACE2nB,GACA,SAAS3qB,GACPgF,KAAKwlB,WAAWxqB,GAAQ,IACtBgF,KAAKwlB,WAAWxqB,IAAS,IACzB,SAAS8qB,GACP,OACGllB,GAAYA,IAAaklB,EAAQpkB,IACjC1D,GAAWA,IAAY8nB,EAAQ9nB,WAID,IAAjCgC,KAAKwlB,WAAWxqB,GAAM4B,eACjBoD,KAAKwlB,WAAWxqB,KAG3BgF,MAII,mBAAmB2lB,GACzB,IACEA,GACA,SAAS3qB,UACAgF,KAAKwlB,WAAWxqB,KAEzBgF,OAKN,SAASylB,EAAOzqB,GACd,MAAO,IAAMA,I,8EClEf,IACM+qB,EAAe,gCA2CrB,SAAgBlD,EAAcrmB,GAE1B,IADA,IAAI2B,EAAS,EACJ1D,EAAI,EAAGA,EAAI+B,EAAEI,OAAQnC,IAAK,CAC/B,IAAMK,EAAI0B,EAAEunB,WAAWtpB,GACvB,GAAIK,EAAI,IACJqD,GAAU,OACP,GAAIrD,EAAI,KACXqD,GAAU,OACP,GAAIrD,EAAI,MACXqD,GAAU,MACP,MAAIrD,GAAK,OAOZ,MAAM,IAAI0P,MA7DA,wBAuDV,GAAI/P,GAAK+B,EAAEI,OAAS,EAChB,MAAM,IAAI4N,MAxDJ,wBA0DV/P,IACA0D,GAAU,GAKlB,OAAOA,EAzDX,kBAAuB3B,GAOnB,IAHA,IAAMwpB,EAAM,IAAI1C,WAAWT,EAAcrmB,IAErCypB,EAAM,EACDxrB,EAAI,EAAGA,EAAI+B,EAAEI,OAAQnC,IAAK,CAC/B,IAAIK,EAAI0B,EAAEunB,WAAWtpB,GACjBK,EAAI,IACJkrB,EAAIC,KAASnrB,EACNA,EAAI,MACXkrB,EAAIC,KAAS,IAAOnrB,GAAK,EACzBkrB,EAAIC,KAAS,IAAW,GAAJnrB,GACbA,EAAI,OACXkrB,EAAIC,KAAS,IAAOnrB,GAAK,GACzBkrB,EAAIC,KAAS,IAAQnrB,GAAK,EAAK,GAC/BkrB,EAAIC,KAAS,IAAW,GAAJnrB,IAEpBL,IACAK,GAAS,KAAJA,IAAc,GACnBA,GAAuB,KAAlB0B,EAAEunB,WAAWtpB,GAClBK,GAAK,MAELkrB,EAAIC,KAAS,IAAOnrB,GAAK,GACzBkrB,EAAIC,KAAS,IAAQnrB,GAAK,GAAM,GAChCkrB,EAAIC,KAAS,IAAQnrB,GAAK,EAAK,GAC/BkrB,EAAIC,KAAS,IAAW,GAAJnrB,GAG5B,OAAOkrB,GAOX,kBA2BA,kBAAuBA,GAEnB,IADA,IAAME,EAAkB,GACfzrB,EAAI,EAAGA,EAAIurB,EAAIppB,OAAQnC,IAAK,CACjC,IAAIupB,EAAIgC,EAAIvrB,GAEZ,GAAQ,IAAJupB,EAAU,CACV,IAAItS,OAAG,EACP,GAAIsS,EAAI,IAAM,CAEV,GAAIvpB,GAAKurB,EAAIppB,OACT,MAAM,IAAI4N,MAAMub,GAGpB,GAAoB,MAAV,KADJI,EAAKH,IAAMvrB,KAEb,MAAM,IAAI+P,MAAMub,GAEpB/B,GAAS,GAAJA,IAAa,EAAU,GAALmC,EACvBzU,EAAM,SACH,GAAIsS,EAAI,IAAM,CAEjB,GAAIvpB,GAAKurB,EAAIppB,OAAS,EAClB,MAAM,IAAI4N,MAAMub,GAEpB,IAAMI,EAAKH,IAAMvrB,GACX2rB,EAAKJ,IAAMvrB,GACjB,GAAoB,MAAV,IAAL0rB,IAAuC,MAAV,IAALC,GACzB,MAAM,IAAI5b,MAAMub,GAEpB/B,GAAS,GAAJA,IAAa,IAAW,GAALmC,IAAc,EAAU,GAALC,EAC3C1U,EAAM,SACH,MAAIsS,EAAI,KAcX,MAAM,IAAIxZ,MAAMub,GAZhB,GAAItrB,GAAKurB,EAAIppB,OAAS,EAClB,MAAM,IAAI4N,MAAMub,GAEdI,EAAKH,IAAMvrB,GACX2rB,EAAKJ,IAAMvrB,GADjB,IAEM4rB,EAAKL,IAAMvrB,GACjB,GAAoB,MAAV,IAAL0rB,IAAuC,MAAV,IAALC,IAAuC,MAAV,IAALC,GACjD,MAAM,IAAI7b,MAAMub,GAEpB/B,GAAS,GAAJA,IAAa,IAAW,GAALmC,IAAc,IAAW,GAALC,IAAc,EAAU,GAALC,EAC/D3U,EAAM,MAKV,GAAIsS,EAAItS,GAAQsS,GAAK,OAAUA,GAAK,MAChC,MAAM,IAAIxZ,MAAMub,GAGpB,GAAI/B,GAAK,MAAS,CAEd,GAAIA,EAAI,QACJ,MAAM,IAAIxZ,MAAMub,GAEpB/B,GAAK,MACLkC,EAAMjpB,KAAKgnB,OAAOC,aAAa,MAAUF,GAAK,KAC9CA,EAAI,MAAc,KAAJA,GAItBkC,EAAMjpB,KAAKgnB,OAAOC,aAAaF,IAEnC,OAAOkC,EAAM/oB,KAAK,M,iBC9ItB,SAAUmR,GACV,aAQA,IAAIgY,EAAK,SAASC,GAChB,IAAI9rB,EAAGc,EAAI,IAAIirB,aAAa,IAC5B,GAAID,EAAM,IAAK9rB,EAAI,EAAGA,EAAI8rB,EAAK3pB,OAAQnC,IAAKc,EAAEd,GAAK8rB,EAAK9rB,GACxD,OAAOc,GAILkrB,EAAc,WAAuB,MAAM,IAAIjc,MAAM,YAErDkc,EAAK,IAAIpD,WAAW,IACpBqD,EAAK,IAAIrD,WAAW,IAAKqD,EAAG,GAAK,EAErC,IAAIC,EAAMN,IACNO,EAAMP,EAAG,CAAC,IACVQ,EAAUR,EAAG,CAAC,MAAQ,IACtBS,EAAIT,EAAG,CAAC,MAAQ,KAAQ,MAAQ,MAAQ,MAAQ,MAAQ,KAAQ,IAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,QAChIU,EAAKV,EAAG,CAAC,MAAQ,KAAQ,MAAQ,MAAQ,MAAQ,MAAQ,KAAQ,IAAQ,MAAQ,MAAQ,MAAQ,KAAQ,MAAQ,MAAQ,MAAQ,OACjIW,EAAIX,EAAG,CAAC,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,OAChIY,EAAIZ,EAAG,CAAC,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,QAChIa,EAAIb,EAAG,CAAC,MAAQ,MAAQ,KAAQ,MAAQ,MAAQ,MAAQ,KAAQ,MAAQ,MAAQ,MAAQ,IAAQ,MAAQ,MAAQ,MAAQ,KAAQ,QAEpI,SAASc,EAAKC,EAAG5sB,EAAG6sB,EAAG5sB,GACrB2sB,EAAE5sB,GAAQ6sB,GAAK,GAAM,IACrBD,EAAE5sB,EAAE,GAAM6sB,GAAK,GAAM,IACrBD,EAAE5sB,EAAE,GAAM6sB,GAAM,EAAK,IACrBD,EAAE5sB,EAAE,GAAS,IAAJ6sB,EACTD,EAAE5sB,EAAE,GAAMC,GAAK,GAAO,IACtB2sB,EAAE5sB,EAAE,GAAMC,GAAK,GAAO,IACtB2sB,EAAE5sB,EAAE,GAAMC,GAAM,EAAM,IACtB2sB,EAAE5sB,EAAE,GAAS,IAAJC,EAGX,SAAS6sB,EAAGF,EAAGG,EAAIC,EAAGC,EAAIxrB,GACxB,IAAIzB,EAAEM,EAAI,EACV,IAAKN,EAAI,EAAGA,EAAIyB,EAAGzB,IAAKM,GAAKssB,EAAEG,EAAG/sB,GAAGgtB,EAAEC,EAAGjtB,GAC1C,OAAQ,EAAMM,EAAI,IAAO,GAAM,EAGjC,SAAS4sB,EAAiBN,EAAGG,EAAIC,EAAGC,GAClC,OAAOH,EAAGF,EAAEG,EAAGC,EAAEC,EAAG,IAGtB,SAASE,EAAiBP,EAAGG,EAAIC,EAAGC,GAClC,OAAOH,EAAGF,EAAEG,EAAGC,EAAEC,EAAG,IA6UtB,SAASG,EAAoB9E,EAAI+E,EAAIC,EAAEjtB,IA1UvC,SAAsBI,EAAGqB,EAAGwrB,EAAGjtB,GAsB7B,IArBA,IAmBemqB,EAnBX+C,EAAc,IAARltB,EAAG,IAAqB,IAARA,EAAG,KAAY,GAAa,IAARA,EAAG,KAAY,IAAc,IAARA,EAAG,KAAY,GAC9EmtB,EAAc,IAARF,EAAG,IAAqB,IAARA,EAAG,KAAY,GAAa,IAARA,EAAG,KAAY,IAAc,IAARA,EAAG,KAAY,GAC9EG,EAAc,IAARH,EAAG,IAAqB,IAARA,EAAG,KAAY,GAAa,IAARA,EAAG,KAAY,IAAc,IAARA,EAAG,KAAY,GAC9EI,EAAc,IAARJ,EAAG,IAAqB,IAARA,EAAG,KAAY,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAC9EK,EAAc,IAARL,EAAE,KAAsB,IAARA,EAAE,MAAa,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAC9EM,EAAc,IAARvtB,EAAG,IAAqB,IAARA,EAAG,KAAY,GAAa,IAARA,EAAG,KAAY,IAAc,IAARA,EAAG,KAAY,GAC9EwtB,EAAc,IAAR/rB,EAAG,IAAqB,IAARA,EAAG,KAAY,GAAa,IAARA,EAAG,KAAY,IAAc,IAARA,EAAG,KAAY,GAC9EgsB,EAAc,IAARhsB,EAAG,IAAqB,IAARA,EAAG,KAAY,GAAa,IAARA,EAAG,KAAY,IAAc,IAARA,EAAG,KAAY,GAC9EisB,EAAc,IAARjsB,EAAG,IAAqB,IAARA,EAAG,KAAY,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAC9EksB,EAAc,IAARlsB,EAAE,KAAsB,IAARA,EAAE,MAAa,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAC9EmsB,EAAc,IAAR5tB,EAAG,IAAqB,IAARA,EAAG,KAAY,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAC9E6tB,EAAc,IAARZ,EAAE,KAAsB,IAARA,EAAE,MAAa,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAC9Ea,EAAc,IAARb,EAAE,KAAsB,IAARA,EAAE,MAAa,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAC9Ec,EAAc,IAARd,EAAE,KAAsB,IAARA,EAAE,MAAa,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAC9Ee,EAAc,IAARf,EAAE,KAAsB,IAARA,EAAE,MAAa,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAC9EgB,EAAc,IAARjuB,EAAE,KAAsB,IAARA,EAAE,MAAa,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAE9EkuB,EAAKhB,EAAIiB,EAAKhB,EAAIiB,EAAKhB,EAAIiB,EAAKhB,EAAIiB,EAAKhB,EAAIiB,EAAKhB,EAAIiB,EAAKhB,EAAIiB,EAAKhB,EACpEiB,EAAKhB,EAAIiB,EAAKhB,EAAIiB,EAAMhB,EAAKiB,EAAMhB,EAAKiB,EAAMhB,EAAKiB,EAAMhB,EAAKiB,EAAMhB,EACpEiB,EAAMhB,EAEDtuB,EAAI,EAAGA,EAAI,GAAIA,GAAK,EAQ3BuuB,IADA/D,GADA2E,IADA3E,GADAuE,IADAvE,GADAmE,IADAnE,EAAI+D,EAAKY,EAAM,IACN,EAAI3E,IAAI,IACR+D,EAAK,IACL,EAAI/D,IAAI,IACRmE,EAAK,IACJ,GAAKnE,IAAI,IACTuE,EAAK,IACN,GAAKvE,IAAI,GASlBoE,IADApE,GADAgE,IADAhE,GADA4E,IADA5E,GADAwE,IADAxE,EAAIoE,EAAKJ,EAAK,IACL,EAAIhE,IAAI,IACRoE,EAAK,IACJ,EAAIpE,IAAI,IACRwE,EAAK,IACN,GAAKxE,IAAI,IACT4E,EAAM,IACN,GAAK5E,IAAI,GASlByE,IADAzE,GADAqE,IADArE,GADAiE,IADAjE,GADA6E,IADA7E,EAAIyE,EAAMJ,EAAK,IACL,EAAIrE,IAAI,IACRyE,EAAM,IACP,EAAIzE,IAAI,IACR6E,EAAM,IACN,GAAK7E,IAAI,IACTiE,EAAK,IACJ,GAAKjE,IAAI,GASnB8E,IADA9E,GADA0E,IADA1E,GADAsE,IADAtE,GADAkE,IADAlE,EAAI8E,EAAMJ,EAAM,IACP,EAAI1E,IAAI,IACR8E,EAAM,IACN,EAAI9E,IAAI,IACRkE,EAAK,IACJ,GAAKlE,IAAI,IACTsE,EAAK,IACL,GAAKtE,IAAI,GASnB+D,IADA/D,GADAkE,IADAlE,GADAiE,IADAjE,GADAgE,IADAhE,EAAI+D,EAAKG,EAAK,IACL,EAAIlE,IAAI,IACR+D,EAAK,IACL,EAAI/D,IAAI,IACRgE,EAAK,IACL,GAAKhE,IAAI,IACTiE,EAAK,IACL,GAAKjE,IAAI,GASlBoE,IADApE,GADAmE,IADAnE,GADAsE,IADAtE,GADAqE,IADArE,EAAIoE,EAAKD,EAAK,IACL,EAAInE,IAAI,IACRoE,EAAK,IACL,EAAIpE,IAAI,IACRqE,EAAK,IACL,GAAKrE,IAAI,IACTsE,EAAK,IACL,GAAKtE,IAAI,GASlByE,IADAzE,GADAwE,IADAxE,GADAuE,IADAvE,GADA0E,IADA1E,EAAIyE,EAAMD,EAAK,IACL,EAAIxE,IAAI,IACRyE,EAAM,IACP,EAAIzE,IAAI,IACR0E,EAAM,IACN,GAAK1E,IAAI,IACTuE,EAAK,IACJ,GAAKvE,IAAI,GASnB8E,IADA9E,GADA6E,IADA7E,GADA4E,IADA5E,GADA2E,IADA3E,EAAI8E,EAAMD,EAAM,IACN,EAAI7E,IAAI,IACR8E,EAAM,IACN,EAAI9E,IAAI,IACR2E,EAAM,IACN,GAAK3E,IAAI,IACT4E,EAAM,IACN,GAAK5E,IAAI,GAEpB+D,EAAMA,EAAMhB,EAAK,EACjBiB,EAAMA,EAAMhB,EAAK,EACjBiB,EAAMA,EAAMhB,EAAK,EACjBiB,EAAMA,EAAMhB,EAAK,EACjBiB,EAAMA,EAAMhB,EAAK,EACjBiB,EAAMA,EAAMhB,EAAK,EACjBiB,EAAMA,EAAMhB,EAAK,EACjBiB,EAAMA,EAAMhB,EAAK,EACjBiB,EAAMA,EAAMhB,EAAK,EACjBiB,EAAMA,EAAMhB,EAAK,EAClBiB,EAAMA,EAAMhB,EAAM,EAClBiB,EAAMA,EAAMhB,EAAM,EAClBiB,EAAMA,EAAMhB,EAAM,EAClBiB,EAAMA,EAAMhB,EAAM,EAClBiB,EAAMA,EAAMhB,EAAM,EAClBiB,EAAMA,EAAMhB,EAAM,EAElB7tB,EAAG,GAAK8tB,IAAQ,EAAI,IACpB9tB,EAAG,GAAK8tB,IAAQ,EAAI,IACpB9tB,EAAG,GAAK8tB,IAAO,GAAK,IACpB9tB,EAAG,GAAK8tB,IAAO,GAAK,IAEpB9tB,EAAG,GAAK+tB,IAAQ,EAAI,IACpB/tB,EAAG,GAAK+tB,IAAQ,EAAI,IACpB/tB,EAAG,GAAK+tB,IAAO,GAAK,IACpB/tB,EAAG,GAAK+tB,IAAO,GAAK,IAEpB/tB,EAAG,GAAKguB,IAAQ,EAAI,IACpBhuB,EAAG,GAAKguB,IAAQ,EAAI,IACpBhuB,EAAE,IAAMguB,IAAO,GAAK,IACpBhuB,EAAE,IAAMguB,IAAO,GAAK,IAEpBhuB,EAAE,IAAMiuB,IAAQ,EAAI,IACpBjuB,EAAE,IAAMiuB,IAAQ,EAAI,IACpBjuB,EAAE,IAAMiuB,IAAO,GAAK,IACpBjuB,EAAE,IAAMiuB,IAAO,GAAK,IAEpBjuB,EAAE,IAAMkuB,IAAQ,EAAI,IACpBluB,EAAE,IAAMkuB,IAAQ,EAAI,IACpBluB,EAAE,IAAMkuB,IAAO,GAAK,IACpBluB,EAAE,IAAMkuB,IAAO,GAAK,IAEpBluB,EAAE,IAAMmuB,IAAQ,EAAI,IACpBnuB,EAAE,IAAMmuB,IAAQ,EAAI,IACpBnuB,EAAE,IAAMmuB,IAAO,GAAK,IACpBnuB,EAAE,IAAMmuB,IAAO,GAAK,IAEpBnuB,EAAE,IAAMouB,IAAQ,EAAI,IACpBpuB,EAAE,IAAMouB,IAAQ,EAAI,IACpBpuB,EAAE,IAAMouB,IAAO,GAAK,IACpBpuB,EAAE,IAAMouB,IAAO,GAAK,IAEpBpuB,EAAE,IAAMquB,IAAQ,EAAI,IACpBruB,EAAE,IAAMquB,IAAQ,EAAI,IACpBruB,EAAE,IAAMquB,IAAO,GAAK,IACpBruB,EAAE,IAAMquB,IAAO,GAAK,IAEpBruB,EAAE,IAAMsuB,IAAQ,EAAI,IACpBtuB,EAAE,IAAMsuB,IAAQ,EAAI,IACpBtuB,EAAE,IAAMsuB,IAAO,GAAK,IACpBtuB,EAAE,IAAMsuB,IAAO,GAAK,IAEpBtuB,EAAE,IAAMuuB,IAAQ,EAAI,IACpBvuB,EAAE,IAAMuuB,IAAQ,EAAI,IACpBvuB,EAAE,IAAMuuB,IAAO,GAAK,IACpBvuB,EAAE,IAAMuuB,IAAO,GAAK,IAEpBvuB,EAAE,IAAMwuB,IAAS,EAAI,IACrBxuB,EAAE,IAAMwuB,IAAS,EAAI,IACrBxuB,EAAE,IAAMwuB,IAAQ,GAAK,IACrBxuB,EAAE,IAAMwuB,IAAQ,GAAK,IAErBxuB,EAAE,IAAMyuB,IAAS,EAAI,IACrBzuB,EAAE,IAAMyuB,IAAS,EAAI,IACrBzuB,EAAE,IAAMyuB,IAAQ,GAAK,IACrBzuB,EAAE,IAAMyuB,IAAQ,GAAK,IAErBzuB,EAAE,IAAM0uB,IAAS,EAAI,IACrB1uB,EAAE,IAAM0uB,IAAS,EAAI,IACrB1uB,EAAE,IAAM0uB,IAAQ,GAAK,IACrB1uB,EAAE,IAAM0uB,IAAQ,GAAK,IAErB1uB,EAAE,IAAM2uB,IAAS,EAAI,IACrB3uB,EAAE,IAAM2uB,IAAS,EAAI,IACrB3uB,EAAE,IAAM2uB,IAAQ,GAAK,IACrB3uB,EAAE,IAAM2uB,IAAQ,GAAK,IAErB3uB,EAAE,IAAM4uB,IAAS,EAAI,IACrB5uB,EAAE,IAAM4uB,IAAS,EAAI,IACrB5uB,EAAE,IAAM4uB,IAAQ,GAAK,IACrB5uB,EAAE,IAAM4uB,IAAQ,GAAK,IAErB5uB,EAAE,IAAM6uB,IAAS,EAAI,IACrB7uB,EAAE,IAAM6uB,IAAS,EAAI,IACrB7uB,EAAE,IAAM6uB,IAAQ,GAAK,IACrB7uB,EAAE,IAAM6uB,IAAQ,GAAK,IA6IrBC,CAAajH,EAAI+E,EAAIC,EAAEjtB,GAGzB,SAASmvB,EAAqBlH,EAAI+E,EAAIC,EAAEjtB,IA7IxC,SAAuBI,EAAEqB,EAAEwrB,EAAEjtB,GAsB3B,IArBA,IAmBemqB,EAFX+D,EAjBc,IAARluB,EAAG,IAAqB,IAARA,EAAG,KAAY,GAAa,IAARA,EAAG,KAAY,IAAc,IAARA,EAAG,KAAY,GAiBrEmuB,EAhBK,IAARlB,EAAG,IAAqB,IAARA,EAAG,KAAY,GAAa,IAARA,EAAG,KAAY,IAAc,IAARA,EAAG,KAAY,GAgB5DmB,EAfJ,IAARnB,EAAG,IAAqB,IAARA,EAAG,KAAY,GAAa,IAARA,EAAG,KAAY,IAAc,IAARA,EAAG,KAAY,GAenDoB,EAdb,IAARpB,EAAG,IAAqB,IAARA,EAAG,KAAY,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAc1CqB,EAbtB,IAARrB,EAAE,KAAsB,IAARA,EAAE,MAAa,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAajCsB,EAZ/B,IAARvuB,EAAG,IAAqB,IAARA,EAAG,KAAY,GAAa,IAARA,EAAG,KAAY,IAAc,IAARA,EAAG,KAAY,GAYxBwuB,EAXxC,IAAR/sB,EAAG,IAAqB,IAARA,EAAG,KAAY,GAAa,IAARA,EAAG,KAAY,IAAc,IAARA,EAAG,KAAY,GAWfgtB,EAVjD,IAARhtB,EAAG,IAAqB,IAARA,EAAG,KAAY,GAAa,IAARA,EAAG,KAAY,IAAc,IAARA,EAAG,KAAY,GAW9EitB,EAVc,IAARjtB,EAAG,IAAqB,IAARA,EAAG,KAAY,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAUrEktB,EATK,IAARltB,EAAE,KAAsB,IAARA,EAAE,MAAa,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAS5DmtB,EARJ,IAAR5uB,EAAG,IAAqB,IAARA,EAAG,KAAY,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAQjD6uB,EAPf,IAAR5B,EAAE,KAAsB,IAARA,EAAE,MAAa,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAOtC6B,EAN1B,IAAR7B,EAAE,KAAsB,IAARA,EAAE,MAAa,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAM3B8B,EALrC,IAAR9B,EAAE,KAAsB,IAARA,EAAE,MAAa,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAKhB+B,EAJhD,IAAR/B,EAAE,KAAsB,IAARA,EAAE,MAAa,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAK9EgC,EAJc,IAARjvB,EAAE,KAAsB,IAARA,EAAE,MAAa,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAMzEL,EAAI,EAAGA,EAAI,GAAIA,GAAK,EAQ3BuuB,IADA/D,GADA2E,IADA3E,GADAuE,IADAvE,GADAmE,IADAnE,EAAI+D,EAAKY,EAAM,IACN,EAAI3E,IAAI,IACR+D,EAAK,IACL,EAAI/D,IAAI,IACRmE,EAAK,IACJ,GAAKnE,IAAI,IACTuE,EAAK,IACN,GAAKvE,IAAI,GASlBoE,IADApE,GADAgE,IADAhE,GADA4E,IADA5E,GADAwE,IADAxE,EAAIoE,EAAKJ,EAAK,IACL,EAAIhE,IAAI,IACRoE,EAAK,IACJ,EAAIpE,IAAI,IACRwE,EAAK,IACN,GAAKxE,IAAI,IACT4E,EAAM,IACN,GAAK5E,IAAI,GASlByE,IADAzE,GADAqE,IADArE,GADAiE,IADAjE,GADA6E,IADA7E,EAAIyE,EAAMJ,EAAK,IACL,EAAIrE,IAAI,IACRyE,EAAM,IACP,EAAIzE,IAAI,IACR6E,EAAM,IACN,GAAK7E,IAAI,IACTiE,EAAK,IACJ,GAAKjE,IAAI,GASnB8E,IADA9E,GADA0E,IADA1E,GADAsE,IADAtE,GADAkE,IADAlE,EAAI8E,EAAMJ,EAAM,IACP,EAAI1E,IAAI,IACR8E,EAAM,IACN,EAAI9E,IAAI,IACRkE,EAAK,IACJ,GAAKlE,IAAI,IACTsE,EAAK,IACL,GAAKtE,IAAI,GASnB+D,IADA/D,GADAkE,IADAlE,GADAiE,IADAjE,GADAgE,IADAhE,EAAI+D,EAAKG,EAAK,IACL,EAAIlE,IAAI,IACR+D,EAAK,IACL,EAAI/D,IAAI,IACRgE,EAAK,IACL,GAAKhE,IAAI,IACTiE,EAAK,IACL,GAAKjE,IAAI,GASlBoE,IADApE,GADAmE,IADAnE,GADAsE,IADAtE,GADAqE,IADArE,EAAIoE,EAAKD,EAAK,IACL,EAAInE,IAAI,IACRoE,EAAK,IACL,EAAIpE,IAAI,IACRqE,EAAK,IACL,GAAKrE,IAAI,IACTsE,EAAK,IACL,GAAKtE,IAAI,GASlByE,IADAzE,GADAwE,IADAxE,GADAuE,IADAvE,GADA0E,IADA1E,EAAIyE,EAAMD,EAAK,IACL,EAAIxE,IAAI,IACRyE,EAAM,IACP,EAAIzE,IAAI,IACR0E,EAAM,IACN,GAAK1E,IAAI,IACTuE,EAAK,IACJ,GAAKvE,IAAI,GASnB8E,IADA9E,GADA6E,IADA7E,GADA4E,IADA5E,GADA2E,IADA3E,EAAI8E,EAAMD,EAAM,IACN,EAAI7E,IAAI,IACR8E,EAAM,IACN,EAAI9E,IAAI,IACR2E,EAAM,IACN,GAAK3E,IAAI,IACT4E,EAAM,IACN,GAAK5E,IAAI,GAGrB/pB,EAAG,GAAK8tB,IAAQ,EAAI,IACpB9tB,EAAG,GAAK8tB,IAAQ,EAAI,IACpB9tB,EAAG,GAAK8tB,IAAO,GAAK,IACpB9tB,EAAG,GAAK8tB,IAAO,GAAK,IAEpB9tB,EAAG,GAAKmuB,IAAQ,EAAI,IACpBnuB,EAAG,GAAKmuB,IAAQ,EAAI,IACpBnuB,EAAG,GAAKmuB,IAAO,GAAK,IACpBnuB,EAAG,GAAKmuB,IAAO,GAAK,IAEpBnuB,EAAG,GAAKwuB,IAAS,EAAI,IACrBxuB,EAAG,GAAKwuB,IAAS,EAAI,IACrBxuB,EAAE,IAAMwuB,IAAQ,GAAK,IACrBxuB,EAAE,IAAMwuB,IAAQ,GAAK,IAErBxuB,EAAE,IAAM6uB,IAAS,EAAI,IACrB7uB,EAAE,IAAM6uB,IAAS,EAAI,IACrB7uB,EAAE,IAAM6uB,IAAQ,GAAK,IACrB7uB,EAAE,IAAM6uB,IAAQ,GAAK,IAErB7uB,EAAE,IAAMouB,IAAQ,EAAI,IACpBpuB,EAAE,IAAMouB,IAAQ,EAAI,IACpBpuB,EAAE,IAAMouB,IAAO,GAAK,IACpBpuB,EAAE,IAAMouB,IAAO,GAAK,IAEpBpuB,EAAE,IAAMquB,IAAQ,EAAI,IACpBruB,EAAE,IAAMquB,IAAQ,EAAI,IACpBruB,EAAE,IAAMquB,IAAO,GAAK,IACpBruB,EAAE,IAAMquB,IAAO,GAAK,IAEpBruB,EAAE,IAAMsuB,IAAQ,EAAI,IACpBtuB,EAAE,IAAMsuB,IAAQ,EAAI,IACpBtuB,EAAE,IAAMsuB,IAAO,GAAK,IACpBtuB,EAAE,IAAMsuB,IAAO,GAAK,IAEpBtuB,EAAE,IAAMuuB,IAAQ,EAAI,IACpBvuB,EAAE,IAAMuuB,IAAQ,EAAI,IACpBvuB,EAAE,IAAMuuB,IAAO,GAAK,IACpBvuB,EAAE,IAAMuuB,IAAO,GAAK,IAQpBS,CAAcnH,EAAI+E,EAAIC,EAAEjtB,GAG1B,IAAIqvB,EAAQ,IAAI7G,WAAW,CAAC,IAAK,IAAK,IAAK,GAAI,IAAK,IAAK,GAAI,GAAI,GAAI,GAAI,GAAI,IAAK,IAAK,IAAK,GAAI,MAGhG,SAAS8G,EAA0BtvB,EAAEuvB,EAAKxvB,EAAEyvB,EAAKtG,EAAE9nB,EAAE6rB,GACnD,IACI9C,EAAGxqB,EADH8vB,EAAI,IAAIjH,WAAW,IAAK+D,EAAI,IAAI/D,WAAW,IAE/C,IAAK7oB,EAAI,EAAGA,EAAI,GAAIA,IAAK8vB,EAAE9vB,GAAK,EAChC,IAAKA,EAAI,EAAGA,EAAI,EAAGA,IAAK8vB,EAAE9vB,GAAKyB,EAAEzB,GACjC,KAAOupB,GAAK,IAAI,CAEd,IADA6D,EAAoBR,EAAEkD,EAAExC,EAAEoC,GACrB1vB,EAAI,EAAGA,EAAI,GAAIA,IAAKK,EAAEuvB,EAAK5vB,GAAKI,EAAEyvB,EAAK7vB,GAAK4sB,EAAE5sB,GAEnD,IADAwqB,EAAI,EACCxqB,EAAI,EAAGA,EAAI,GAAIA,IAClBwqB,EAAIA,GAAY,IAAPsF,EAAE9vB,IAAa,EACxB8vB,EAAE9vB,GAAS,IAAJwqB,EACPA,KAAO,EAETjB,GAAK,GACLqG,GAAQ,GACRC,GAAQ,GAEV,GAAItG,EAAI,EAEN,IADA6D,EAAoBR,EAAEkD,EAAExC,EAAEoC,GACrB1vB,EAAI,EAAGA,EAAIupB,EAAGvpB,IAAKK,EAAEuvB,EAAK5vB,GAAKI,EAAEyvB,EAAK7vB,GAAK4sB,EAAE5sB,GAEpD,OAAO,EAGT,SAAS+vB,EAAsB1vB,EAAEuvB,EAAKrG,EAAE9nB,EAAE6rB,GACxC,IACI9C,EAAGxqB,EADH8vB,EAAI,IAAIjH,WAAW,IAAK+D,EAAI,IAAI/D,WAAW,IAE/C,IAAK7oB,EAAI,EAAGA,EAAI,GAAIA,IAAK8vB,EAAE9vB,GAAK,EAChC,IAAKA,EAAI,EAAGA,EAAI,EAAGA,IAAK8vB,EAAE9vB,GAAKyB,EAAEzB,GACjC,KAAOupB,GAAK,IAAI,CAEd,IADA6D,EAAoBR,EAAEkD,EAAExC,EAAEoC,GACrB1vB,EAAI,EAAGA,EAAI,GAAIA,IAAKK,EAAEuvB,EAAK5vB,GAAK4sB,EAAE5sB,GAEvC,IADAwqB,EAAI,EACCxqB,EAAI,EAAGA,EAAI,GAAIA,IAClBwqB,EAAIA,GAAY,IAAPsF,EAAE9vB,IAAa,EACxB8vB,EAAE9vB,GAAS,IAAJwqB,EACPA,KAAO,EAETjB,GAAK,GACLqG,GAAQ,GAEV,GAAIrG,EAAI,EAEN,IADA6D,EAAoBR,EAAEkD,EAAExC,EAAEoC,GACrB1vB,EAAI,EAAGA,EAAIupB,EAAGvpB,IAAKK,EAAEuvB,EAAK5vB,GAAK4sB,EAAE5sB,GAExC,OAAO,EAGT,SAASgwB,EAAc3vB,EAAEuvB,EAAKtvB,EAAEmB,EAAE6rB,GAChC,IAAIvrB,EAAI,IAAI8mB,WAAW,IACvB2G,EAAqBztB,EAAEN,EAAE6rB,EAAEoC,GAE3B,IADA,IAAIO,EAAK,IAAIpH,WAAW,GACf7oB,EAAI,EAAGA,EAAI,EAAGA,IAAKiwB,EAAGjwB,GAAKyB,EAAEzB,EAAE,IACxC,OAAO+vB,EAAsB1vB,EAAEuvB,EAAKtvB,EAAE2vB,EAAGluB,GAG3C,SAASmuB,EAAkB7vB,EAAEuvB,EAAKxvB,EAAEyvB,EAAKvvB,EAAEmB,EAAE6rB,GAC3C,IAAIvrB,EAAI,IAAI8mB,WAAW,IACvB2G,EAAqBztB,EAAEN,EAAE6rB,EAAEoC,GAE3B,IADA,IAAIO,EAAK,IAAIpH,WAAW,GACf7oB,EAAI,EAAGA,EAAI,EAAGA,IAAKiwB,EAAGjwB,GAAKyB,EAAEzB,EAAE,IACxC,OAAO2vB,EAA0BtvB,EAAEuvB,EAAKxvB,EAAEyvB,EAAKvvB,EAAE2vB,EAAGluB,GAQtD,IAAIouB,EAAW,SAAS5uB,GAQtB,IAAI6uB,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAPhCprB,KAAK4X,OAAS,IAAI0L,WAAW,IAC7BtjB,KAAKzE,EAAI,IAAI8vB,YAAY,IACzBrrB,KAAKsnB,EAAI,IAAI+D,YAAY,IACzBrrB,KAAKsrB,IAAM,IAAID,YAAY,GAC3BrrB,KAAKurB,SAAW,EAChBvrB,KAAKwrB,IAAM,EAIXX,EAAe,IAAV7uB,EAAK,IAAuB,IAAVA,EAAK,KAAc,EAAGgE,KAAKzE,EAAE,GAAkC,KAA7B,EACzDuvB,EAAe,IAAV9uB,EAAK,IAAuB,IAAVA,EAAK,KAAc,EAAGgE,KAAKzE,EAAE,GAAkC,MAA3BsvB,IAAO,GAAOC,GAAO,GAChFC,EAAe,IAAV/uB,EAAK,IAAuB,IAAVA,EAAK,KAAc,EAAGgE,KAAKzE,EAAE,GAAkC,MAA3BuvB,IAAO,GAAOC,GAAO,GAChFC,EAAe,IAAVhvB,EAAK,IAAuB,IAAVA,EAAK,KAAc,EAAGgE,KAAKzE,EAAE,GAAkC,MAA3BwvB,IAAQ,EAAMC,GAAO,GAChFC,EAAe,IAAVjvB,EAAK,IAAuB,IAAVA,EAAK,KAAc,EAAGgE,KAAKzE,EAAE,GAAkC,KAA3ByvB,IAAQ,EAAMC,GAAM,IAC/EjrB,KAAKzE,EAAE,GAAO0vB,IAAQ,EAAM,KAC5BC,EAAe,IAAVlvB,EAAI,KAAwB,IAAVA,EAAI,MAAe,EAAGgE,KAAKzE,EAAE,GAAkC,MAA3B0vB,IAAO,GAAOC,GAAO,GAChFC,EAAe,IAAVnvB,EAAI,KAAwB,IAAVA,EAAI,MAAe,EAAGgE,KAAKzE,EAAE,GAAkC,MAA3B2vB,IAAO,GAAOC,GAAO,GAChFC,EAAe,IAAVpvB,EAAI,KAAwB,IAAVA,EAAI,MAAe,EAAGgE,KAAKzE,EAAE,GAAkC,MAA3B4vB,IAAQ,EAAMC,GAAO,GAChFprB,KAAKzE,EAAE,GAAO6vB,IAAQ,EAAM,IAE5BprB,KAAKsrB,IAAI,GAAe,IAAVtvB,EAAI,KAAwB,IAAVA,EAAI,MAAe,EACnDgE,KAAKsrB,IAAI,GAAe,IAAVtvB,EAAI,KAAwB,IAAVA,EAAI,MAAe,EACnDgE,KAAKsrB,IAAI,GAAe,IAAVtvB,EAAI,KAAwB,IAAVA,EAAI,MAAe,EACnDgE,KAAKsrB,IAAI,GAAe,IAAVtvB,EAAI,KAAwB,IAAVA,EAAI,MAAe,EACnDgE,KAAKsrB,IAAI,GAAe,IAAVtvB,EAAI,KAAwB,IAAVA,EAAI,MAAe,EACnDgE,KAAKsrB,IAAI,GAAe,IAAVtvB,EAAI,KAAwB,IAAVA,EAAI,MAAe,EACnDgE,KAAKsrB,IAAI,GAAe,IAAVtvB,EAAI,KAAwB,IAAVA,EAAI,MAAe,EACnDgE,KAAKsrB,IAAI,GAAe,IAAVtvB,EAAI,KAAwB,IAAVA,EAAI,MAAe,GAoUrD,SAASyvB,EAAmB1I,EAAK2I,EAAQ7wB,EAAGyvB,EAAMpuB,EAAG6rB,GACnD,IAAIvrB,EAAI,IAAIouB,EAAS7C,GAGrB,OAFAvrB,EAAEmvB,OAAO9wB,EAAGyvB,EAAMpuB,GAClBM,EAAE2N,OAAO4Y,EAAK2I,GACP,EAGT,SAASE,EAA0BtE,EAAGuE,EAAMhxB,EAAGyvB,EAAMpuB,EAAG6rB,GACtD,IAAIV,EAAI,IAAI/D,WAAW,IAEvB,OADAmI,EAAmBpE,EAAE,EAAExsB,EAAEyvB,EAAKpuB,EAAE6rB,GACzBJ,EAAiBL,EAAEuE,EAAKxE,EAAE,GAGnC,SAASyE,EAAiBhxB,EAAED,EAAEE,EAAEmB,EAAE6rB,GAChC,IAAIttB,EACJ,GAAIM,EAAI,GAAI,OAAQ,EAGpB,IAFA4vB,EAAkB7vB,EAAE,EAAED,EAAE,EAAEE,EAAEmB,EAAE6rB,GAC9B0D,EAAmB3wB,EAAG,GAAIA,EAAG,GAAIC,EAAI,GAAID,GACpCL,EAAI,EAAGA,EAAI,GAAIA,IAAKK,EAAEL,GAAK,EAChC,OAAO,EAGT,SAASsxB,EAAsBlxB,EAAEC,EAAEC,EAAEmB,EAAE6rB,GACrC,IAAIttB,EACA4sB,EAAI,IAAI/D,WAAW,IACvB,GAAIvoB,EAAI,GAAI,OAAQ,EAEpB,GADA0vB,EAAcpD,EAAE,EAAE,GAAGnrB,EAAE6rB,GACiC,IAApD6D,EAA0B9wB,EAAG,GAAGA,EAAG,GAAGC,EAAI,GAAGssB,GAAU,OAAQ,EAEnE,IADAsD,EAAkB9vB,EAAE,EAAEC,EAAE,EAAEC,EAAEmB,EAAE6rB,GACzBttB,EAAI,EAAGA,EAAI,GAAIA,IAAKI,EAAEJ,GAAK,EAChC,OAAO,EAGT,SAASuxB,EAASzwB,EAAG0wB,GACnB,IAAIxxB,EACJ,IAAKA,EAAI,EAAGA,EAAI,GAAIA,IAAKc,EAAEd,GAAU,EAALwxB,EAAExxB,GAGpC,SAASyxB,EAAShxB,GAChB,IAAIT,EAAG0xB,EAAGrxB,EAAI,EACd,IAAKL,EAAI,EAAGA,EAAI,GAAIA,IAClB0xB,EAAIjxB,EAAET,GAAKK,EAAI,MACfA,EAAI2N,KAAK+T,MAAM2P,EAAI,OACnBjxB,EAAET,GAAK0xB,EAAQ,MAAJrxB,EAEbI,EAAE,IAAMJ,EAAE,EAAI,IAAMA,EAAE,GAGxB,SAASsxB,EAAS7vB,EAAG8vB,EAAGrI,GAEtB,IADA,IAAIroB,EAAGb,IAAMkpB,EAAE,GACNvpB,EAAI,EAAGA,EAAI,GAAIA,IACtBkB,EAAIb,GAAKyB,EAAE9B,GAAK4xB,EAAE5xB,IAClB8B,EAAE9B,IAAMkB,EACR0wB,EAAE5xB,IAAMkB,EAIZ,SAAS2wB,EAAUpxB,EAAGgB,GACpB,IAAIzB,EAAG8xB,EAAGvI,EACNnpB,EAAIyrB,IAAM3qB,EAAI2qB,IAClB,IAAK7rB,EAAI,EAAGA,EAAI,GAAIA,IAAKkB,EAAElB,GAAKyB,EAAEzB,GAIlC,IAHAyxB,EAASvwB,GACTuwB,EAASvwB,GACTuwB,EAASvwB,GACJ4wB,EAAI,EAAGA,EAAI,EAAGA,IAAK,CAEtB,IADA1xB,EAAE,GAAKc,EAAE,GAAK,MACTlB,EAAI,EAAGA,EAAI,GAAIA,IAClBI,EAAEJ,GAAKkB,EAAElB,GAAK,OAAWI,EAAEJ,EAAE,IAAI,GAAM,GACvCI,EAAEJ,EAAE,IAAM,MAEZI,EAAE,IAAMc,EAAE,IAAM,OAAWd,EAAE,KAAK,GAAM,GACxCmpB,EAAKnpB,EAAE,KAAK,GAAM,EAClBA,EAAE,KAAO,MACTuxB,EAASzwB,EAAGd,EAAG,EAAEmpB,GAEnB,IAAKvpB,EAAI,EAAGA,EAAI,GAAIA,IAClBS,EAAE,EAAET,GAAY,IAAPkB,EAAElB,GACXS,EAAE,EAAET,EAAE,GAAKkB,EAAElB,IAAI,EAIrB,SAAS+xB,EAASP,EAAGjI,GACnB,IAAIlpB,EAAI,IAAIwoB,WAAW,IAAKvoB,EAAI,IAAIuoB,WAAW,IAG/C,OAFAgJ,EAAUxxB,EAAGmxB,GACbK,EAAUvxB,EAAGipB,GACN4D,EAAiB9sB,EAAG,EAAGC,EAAG,GAGnC,SAAS0xB,EAASR,GAChB,IAAIlxB,EAAI,IAAIuoB,WAAW,IAEvB,OADAgJ,EAAUvxB,EAAGkxB,GACC,EAAPlxB,EAAE,GAGX,SAAS2xB,EAAYxxB,EAAGgB,GACtB,IAAIzB,EACJ,IAAKA,EAAI,EAAGA,EAAI,GAAIA,IAAKS,EAAET,GAAKyB,EAAE,EAAEzB,IAAMyB,EAAE,EAAEzB,EAAE,IAAM,GACtDS,EAAE,KAAO,MAGX,SAASyxB,EAAEzxB,EAAG+wB,EAAGjI,GACf,IAAK,IAAIvpB,EAAI,EAAGA,EAAI,GAAIA,IAAKS,EAAET,GAAKwxB,EAAExxB,GAAKupB,EAAEvpB,GAG/C,SAASmyB,EAAE1xB,EAAG+wB,EAAGjI,GACf,IAAK,IAAIvpB,EAAI,EAAGA,EAAI,GAAIA,IAAKS,EAAET,GAAKwxB,EAAExxB,GAAKupB,EAAEvpB,GAG/C,SAASoyB,EAAE3xB,EAAG+wB,EAAGjI,GACf,IAAImI,EAAGrxB,EACJ+vB,EAAK,EAAIC,EAAK,EAAIC,EAAK,EAAIC,EAAK,EAAIC,EAAK,EAAIC,EAAK,EAAIC,EAAK,EAAIC,EAAK,EACpE0B,EAAK,EAAIC,EAAK,EAAGC,EAAM,EAAGC,EAAM,EAAGC,EAAM,EAAGC,EAAM,EAAGC,EAAM,EAAGC,EAAM,EACrEC,EAAM,EAAGC,EAAM,EAAGC,EAAM,EAAGC,EAAM,EAAGC,EAAM,EAAGC,EAAM,EAAGC,EAAM,EAAGC,EAAM,EACrEC,EAAM,EAAGC,EAAM,EAAGC,EAAM,EAAGC,EAAM,EAAGC,EAAM,EAAGC,EAAM,EAAGC,EAAM,EAC5DC,EAAKrK,EAAE,GACPsK,EAAKtK,EAAE,GACPuK,EAAKvK,EAAE,GACPwK,EAAKxK,EAAE,GACPyK,EAAKzK,EAAE,GACP0K,EAAK1K,EAAE,GACP2K,EAAK3K,EAAE,GACP4K,EAAK5K,EAAE,GACP6K,EAAK7K,EAAE,GACP8K,EAAK9K,EAAE,GACP+K,EAAM/K,EAAE,IACRgL,EAAMhL,EAAE,IACRiL,EAAMjL,EAAE,IACRkL,EAAMlL,EAAE,IACRmL,EAAMnL,EAAE,IACRoL,EAAMpL,EAAE,IAGV6G,IADAsB,EAAIF,EAAE,IACIoC,EACVvD,GAAMqB,EAAImC,EACVvD,GAAMoB,EAAIoC,EACVvD,GAAMmB,EAAIqC,EACVvD,GAAMkB,EAAIsC,EACVvD,GAAMiB,EAAIuC,EACVvD,GAAMgB,EAAIwC,EACVvD,GAAMe,EAAIyC,EACV9B,GAAMX,EAAI0C,EACV9B,GAAMZ,EAAI2C,EACV9B,GAAOb,EAAI4C,EACX9B,GAAOd,EAAI6C,EACX9B,GAAOf,EAAI8C,EACX9B,GAAOhB,EAAI+C,EACX9B,GAAOjB,EAAIgD,EACX9B,GAAOlB,EAAIiD,EAEXtE,IADAqB,EAAIF,EAAE,IACIoC,EACVtD,GAAMoB,EAAImC,EACVtD,GAAMmB,EAAIoC,EACVtD,GAAMkB,EAAIqC,EACVtD,GAAMiB,EAAIsC,EACVtD,GAAMgB,EAAIuC,EACVtD,GAAMe,EAAIwC,EACV7B,GAAMX,EAAIyC,EACV7B,GAAMZ,EAAI0C,EACV7B,GAAOb,EAAI2C,EACX7B,GAAOd,EAAI4C,EACX7B,GAAOf,EAAI6C,EACX7B,GAAOhB,EAAI8C,EACX7B,GAAOjB,EAAI+C,EACX7B,GAAOlB,EAAIgD,EACX7B,GAAOnB,EAAIiD,EAEXrE,IADAoB,EAAIF,EAAE,IACIoC,EACVrD,GAAMmB,EAAImC,EACVrD,GAAMkB,EAAIoC,EACVrD,GAAMiB,EAAIqC,EACVrD,GAAMgB,EAAIsC,EACVrD,GAAMe,EAAIuC,EACV5B,GAAMX,EAAIwC,EACV5B,GAAMZ,EAAIyC,EACV5B,GAAOb,EAAI0C,EACX5B,GAAOd,EAAI2C,EACX5B,GAAOf,EAAI4C,EACX5B,GAAOhB,EAAI6C,EACX5B,GAAOjB,EAAI8C,EACX5B,GAAOlB,EAAI+C,EACX5B,GAAOnB,EAAIgD,EACX5B,GAAOpB,EAAIiD,EAEXpE,IADAmB,EAAIF,EAAE,IACIoC,EACVpD,GAAMkB,EAAImC,EACVpD,GAAMiB,EAAIoC,EACVpD,GAAMgB,EAAIqC,EACVpD,GAAMe,EAAIsC,EACV3B,GAAMX,EAAIuC,EACV3B,GAAMZ,EAAIwC,EACV3B,GAAOb,EAAIyC,EACX3B,GAAOd,EAAI0C,EACX3B,GAAOf,EAAI2C,EACX3B,GAAOhB,EAAI4C,EACX3B,GAAOjB,EAAI6C,EACX3B,GAAOlB,EAAI8C,EACX3B,GAAOnB,EAAI+C,EACX3B,GAAOpB,EAAIgD,EACX3B,GAAOrB,EAAIiD,EAEXnE,IADAkB,EAAIF,EAAE,IACIoC,EACVnD,GAAMiB,EAAImC,EACVnD,GAAMgB,EAAIoC,EACVnD,GAAMe,EAAIqC,EACV1B,GAAMX,EAAIsC,EACV1B,GAAMZ,EAAIuC,EACV1B,GAAOb,EAAIwC,EACX1B,GAAOd,EAAIyC,EACX1B,GAAOf,EAAI0C,EACX1B,GAAOhB,EAAI2C,EACX1B,GAAOjB,EAAI4C,EACX1B,GAAOlB,EAAI6C,EACX1B,GAAOnB,EAAI8C,EACX1B,GAAOpB,EAAI+C,EACX1B,GAAOrB,EAAIgD,EACX1B,GAAOtB,EAAIiD,EAEXlE,IADAiB,EAAIF,EAAE,IACIoC,EACVlD,GAAMgB,EAAImC,EACVlD,GAAMe,EAAIoC,EACVzB,GAAMX,EAAIqC,EACVzB,GAAMZ,EAAIsC,EACVzB,GAAOb,EAAIuC,EACXzB,GAAOd,EAAIwC,EACXzB,GAAOf,EAAIyC,EACXzB,GAAOhB,EAAI0C,EACXzB,GAAOjB,EAAI2C,EACXzB,GAAOlB,EAAI4C,EACXzB,GAAOnB,EAAI6C,EACXzB,GAAOpB,EAAI8C,EACXzB,GAAOrB,EAAI+C,EACXzB,GAAOtB,EAAIgD,EACXzB,GAAOvB,EAAIiD,EAEXjE,IADAgB,EAAIF,EAAE,IACIoC,EACVjD,GAAMe,EAAImC,EACVxB,GAAMX,EAAIoC,EACVxB,GAAMZ,EAAIqC,EACVxB,GAAOb,EAAIsC,EACXxB,GAAOd,EAAIuC,EACXxB,GAAOf,EAAIwC,EACXxB,GAAOhB,EAAIyC,EACXxB,GAAOjB,EAAI0C,EACXxB,GAAOlB,EAAI2C,EACXxB,GAAOnB,EAAI4C,EACXxB,GAAOpB,EAAI6C,EACXxB,GAAOrB,EAAI8C,EACXxB,GAAOtB,EAAI+C,EACXxB,GAAOvB,EAAIgD,EACXxB,GAAOxB,EAAIiD,EAEXhE,IADAe,EAAIF,EAAE,IACIoC,EACVvB,GAAMX,EAAImC,EACVvB,GAAMZ,EAAIoC,EACVvB,GAAOb,EAAIqC,EACXvB,GAAOd,EAAIsC,EACXvB,GAAOf,EAAIuC,EACXvB,GAAOhB,EAAIwC,EACXvB,GAAOjB,EAAIyC,EACXvB,GAAOlB,EAAI0C,EACXvB,GAAOnB,EAAI2C,EACXvB,GAAOpB,EAAI4C,EACXvB,GAAOrB,EAAI6C,EACXvB,GAAOtB,EAAI8C,EACXvB,GAAOvB,EAAI+C,EACXvB,GAAOxB,EAAIgD,EACXvB,GAAOzB,EAAIiD,EAEXtC,IADAX,EAAIF,EAAE,IACIoC,EACVtB,GAAMZ,EAAImC,EACVtB,GAAOb,EAAIoC,EACXtB,GAAOd,EAAIqC,EACXtB,GAAOf,EAAIsC,EACXtB,GAAOhB,EAAIuC,EACXtB,GAAOjB,EAAIwC,EACXtB,GAAOlB,EAAIyC,EACXtB,GAAOnB,EAAI0C,EACXtB,GAAOpB,EAAI2C,EACXtB,GAAOrB,EAAI4C,EACXtB,GAAOtB,EAAI6C,EACXtB,GAAOvB,EAAI8C,EACXtB,GAAOxB,EAAI+C,EACXtB,GAAOzB,EAAIgD,EACXtB,GAAO1B,EAAIiD,EAEXrC,IADAZ,EAAIF,EAAE,IACIoC,EACVrB,GAAOb,EAAImC,EACXrB,GAAOd,EAAIoC,EACXrB,GAAOf,EAAIqC,EACXrB,GAAOhB,EAAIsC,EACXrB,GAAOjB,EAAIuC,EACXrB,GAAOlB,EAAIwC,EACXrB,GAAOnB,EAAIyC,EACXrB,GAAOpB,EAAI0C,EACXrB,GAAOrB,EAAI2C,EACXrB,GAAOtB,EAAI4C,EACXrB,GAAOvB,EAAI6C,EACXrB,GAAOxB,EAAI8C,EACXrB,GAAOzB,EAAI+C,EACXrB,GAAO1B,EAAIgD,EACXrB,GAAO3B,EAAIiD,EAEXpC,IADAb,EAAIF,EAAE,KACKoC,EACXpB,GAAOd,EAAImC,EACXpB,GAAOf,EAAIoC,EACXpB,GAAOhB,EAAIqC,EACXpB,GAAOjB,EAAIsC,EACXpB,GAAOlB,EAAIuC,EACXpB,GAAOnB,EAAIwC,EACXpB,GAAOpB,EAAIyC,EACXpB,GAAOrB,EAAI0C,EACXpB,GAAOtB,EAAI2C,EACXpB,GAAOvB,EAAI4C,EACXpB,GAAOxB,EAAI6C,EACXpB,GAAOzB,EAAI8C,EACXpB,GAAO1B,EAAI+C,EACXpB,GAAO3B,EAAIgD,EACXpB,GAAO5B,EAAIiD,EAEXnC,IADAd,EAAIF,EAAE,KACKoC,EACXnB,GAAOf,EAAImC,EACXnB,GAAOhB,EAAIoC,EACXnB,GAAOjB,EAAIqC,EACXnB,GAAOlB,EAAIsC,EACXnB,GAAOnB,EAAIuC,EACXnB,GAAOpB,EAAIwC,EACXnB,GAAOrB,EAAIyC,EACXnB,GAAOtB,EAAI0C,EACXnB,GAAOvB,EAAI2C,EACXnB,GAAOxB,EAAI4C,EACXnB,GAAOzB,EAAI6C,EACXnB,GAAO1B,EAAI8C,EACXnB,GAAO3B,EAAI+C,EACXnB,GAAO5B,EAAIgD,EACXnB,GAAO7B,EAAIiD,EAEXlC,IADAf,EAAIF,EAAE,KACKoC,EACXlB,GAAOhB,EAAImC,EACXlB,GAAOjB,EAAIoC,EACXlB,GAAOlB,EAAIqC,EACXlB,GAAOnB,EAAIsC,EACXlB,GAAOpB,EAAIuC,EACXlB,GAAOrB,EAAIwC,EACXlB,GAAOtB,EAAIyC,EACXlB,GAAOvB,EAAI0C,EACXlB,GAAOxB,EAAI2C,EACXlB,GAAOzB,EAAI4C,EACXlB,GAAO1B,EAAI6C,EACXlB,GAAO3B,EAAI8C,EACXlB,GAAO5B,EAAI+C,EACXlB,GAAO7B,EAAIgD,EACXlB,GAAO9B,EAAIiD,EAEXjC,IADAhB,EAAIF,EAAE,KACKoC,EACXjB,GAAOjB,EAAImC,EACXjB,GAAOlB,EAAIoC,EACXjB,GAAOnB,EAAIqC,EACXjB,GAAOpB,EAAIsC,EACXjB,GAAOrB,EAAIuC,EACXjB,GAAOtB,EAAIwC,EACXjB,GAAOvB,EAAIyC,EACXjB,GAAOxB,EAAI0C,EACXjB,GAAOzB,EAAI2C,EACXjB,GAAO1B,EAAI4C,EACXjB,GAAO3B,EAAI6C,EACXjB,GAAO5B,EAAI8C,EACXjB,GAAO7B,EAAI+C,EACXjB,GAAO9B,EAAIgD,EACXjB,GAAO/B,EAAIiD,EAEXhC,IADAjB,EAAIF,EAAE,KACKoC,EACXhB,GAAOlB,EAAImC,EACXhB,GAAOnB,EAAIoC,EACXhB,GAAOpB,EAAIqC,EACXhB,GAAOrB,EAAIsC,EACXhB,GAAOtB,EAAIuC,EACXhB,GAAOvB,EAAIwC,EACXhB,GAAOxB,EAAIyC,EACXhB,GAAOzB,EAAI0C,EACXhB,GAAO1B,EAAI2C,EACXhB,GAAO3B,EAAI4C,EACXhB,GAAO5B,EAAI6C,EACXhB,GAAO7B,EAAI8C,EACXhB,GAAO9B,EAAI+C,EACXhB,GAAO/B,EAAIgD,EACXhB,GAAOhC,EAAIiD,EAEX/B,IADAlB,EAAIF,EAAE,KACKoC,EAkBXvD,GAAO,IAhBPyC,GAAOpB,EAAIoC,GAiBXxD,GAAO,IAhBPyC,GAAOrB,EAAIqC,GAiBXxD,GAAO,IAhBPyC,GAAOtB,EAAIsC,GAiBXxD,GAAO,IAhBPyC,GAAOvB,EAAIuC,GAiBXxD,GAAO,IAhBPyC,GAAOxB,EAAIwC,GAiBXxD,GAAO,IAhBPyC,GAAOzB,EAAIyC,GAiBXxD,GAAO,IAhBPyC,GAAO1B,EAAI0C,GAiBX/B,GAAO,IAhBPgB,GAAO3B,EAAI2C,GAiBX/B,GAAO,IAhBPgB,GAAO5B,EAAI4C,GAiBX/B,GAAO,IAhBPgB,GAAO7B,EAAI6C,GAiBX/B,GAAO,IAhBPgB,GAAO9B,EAAI8C,GAiBX/B,GAAO,IAhBPgB,GAAO/B,EAAI+C,GAiBX/B,GAAO,IAhBPgB,GAAOhC,EAAIgD,GAiBX/B,GAAO,IAhBPgB,GAAOjC,EAAIiD,GAqBsCvE,GAAjDsB,GAnBAtB,GAAO,IAhBPyC,GAAOnB,EAAImC,KAkCXxzB,EAAI,GACU,OAAgD,OAAzCA,EAAI2N,KAAK+T,MAAM2P,EAAI,QACSrB,GAAjDqB,EAAKrB,EAAKhwB,EAAI,OAAgD,OAAzCA,EAAI2N,KAAK+T,MAAM2P,EAAI,QACSpB,GAAjDoB,EAAKpB,EAAKjwB,EAAI,OAAgD,OAAzCA,EAAI2N,KAAK+T,MAAM2P,EAAI,QACSnB,GAAjDmB,EAAKnB,EAAKlwB,EAAI,OAAgD,OAAzCA,EAAI2N,KAAK+T,MAAM2P,EAAI,QACSlB,GAAjDkB,EAAKlB,EAAKnwB,EAAI,OAAgD,OAAzCA,EAAI2N,KAAK+T,MAAM2P,EAAI,QACSjB,GAAjDiB,EAAKjB,EAAKpwB,EAAI,OAAgD,OAAzCA,EAAI2N,KAAK+T,MAAM2P,EAAI,QACShB,GAAjDgB,EAAKhB,EAAKrwB,EAAI,OAAgD,OAAzCA,EAAI2N,KAAK+T,MAAM2P,EAAI,QACSf,GAAjDe,EAAKf,EAAKtwB,EAAI,OAAgD,OAAzCA,EAAI2N,KAAK+T,MAAM2P,EAAI,QACSW,GAAjDX,EAAKW,EAAKhyB,EAAI,OAAgD,OAAzCA,EAAI2N,KAAK+T,MAAM2P,EAAI,QACSY,GAAjDZ,EAAKY,EAAKjyB,EAAI,OAAgD,OAAzCA,EAAI2N,KAAK+T,MAAM2P,EAAI,QACQa,GAAhDb,EAAIa,EAAMlyB,EAAI,OAAgD,OAAzCA,EAAI2N,KAAK+T,MAAM2P,EAAI,QACQc,GAAhDd,EAAIc,EAAMnyB,EAAI,OAAgD,OAAzCA,EAAI2N,KAAK+T,MAAM2P,EAAI,QACQe,GAAhDf,EAAIe,EAAMpyB,EAAI,OAAgD,OAAzCA,EAAI2N,KAAK+T,MAAM2P,EAAI,QACQgB,GAAhDhB,EAAIgB,EAAMryB,EAAI,OAAgD,OAAzCA,EAAI2N,KAAK+T,MAAM2P,EAAI,QACQiB,GAAhDjB,EAAIiB,EAAMtyB,EAAI,OAAgD,OAAzCA,EAAI2N,KAAK+T,MAAM2P,EAAI,QACQkB,GAAhDlB,EAAIkB,EAAMvyB,EAAI,OAAgD,OAAzCA,EAAI2N,KAAK+T,MAAM2P,EAAI,QAKStB,GAAjDsB,GAJAtB,GAAM/vB,EAAE,EAAI,IAAMA,EAAE,KAGpBA,EAAI,GACU,OAAgD,OAAzCA,EAAI2N,KAAK+T,MAAM2P,EAAI,QACSrB,GAAjDqB,EAAKrB,EAAKhwB,EAAI,OAAgD,OAAzCA,EAAI2N,KAAK+T,MAAM2P,EAAI,QACSpB,GAAjDoB,EAAKpB,EAAKjwB,EAAI,OAAgD,OAAzCA,EAAI2N,KAAK+T,MAAM2P,EAAI,QACSnB,GAAjDmB,EAAKnB,EAAKlwB,EAAI,OAAgD,OAAzCA,EAAI2N,KAAK+T,MAAM2P,EAAI,QACSlB,GAAjDkB,EAAKlB,EAAKnwB,EAAI,OAAgD,OAAzCA,EAAI2N,KAAK+T,MAAM2P,EAAI,QACSjB,GAAjDiB,EAAKjB,EAAKpwB,EAAI,OAAgD,OAAzCA,EAAI2N,KAAK+T,MAAM2P,EAAI,QACShB,GAAjDgB,EAAKhB,EAAKrwB,EAAI,OAAgD,OAAzCA,EAAI2N,KAAK+T,MAAM2P,EAAI,QACSf,GAAjDe,EAAKf,EAAKtwB,EAAI,OAAgD,OAAzCA,EAAI2N,KAAK+T,MAAM2P,EAAI,QACSW,GAAjDX,EAAKW,EAAKhyB,EAAI,OAAgD,OAAzCA,EAAI2N,KAAK+T,MAAM2P,EAAI,QACSY,GAAjDZ,EAAKY,EAAKjyB,EAAI,OAAgD,OAAzCA,EAAI2N,KAAK+T,MAAM2P,EAAI,QACQa,GAAhDb,EAAIa,EAAMlyB,EAAI,OAAgD,OAAzCA,EAAI2N,KAAK+T,MAAM2P,EAAI,QACQc,GAAhDd,EAAIc,EAAMnyB,EAAI,OAAgD,OAAzCA,EAAI2N,KAAK+T,MAAM2P,EAAI,QACQe,GAAhDf,EAAIe,EAAMpyB,EAAI,OAAgD,OAAzCA,EAAI2N,KAAK+T,MAAM2P,EAAI,QACQgB,GAAhDhB,EAAIgB,EAAMryB,EAAI,OAAgD,OAAzCA,EAAI2N,KAAK+T,MAAM2P,EAAI,QACQiB,GAAhDjB,EAAIiB,EAAMtyB,EAAI,OAAgD,OAAzCA,EAAI2N,KAAK+T,MAAM2P,EAAI,QACQkB,GAAhDlB,EAAIkB,EAAMvyB,EAAI,OAAgD,OAAzCA,EAAI2N,KAAK+T,MAAM2P,EAAI,QACxCtB,GAAM/vB,EAAE,EAAI,IAAMA,EAAE,GAEpBI,EAAG,GAAK2vB,EACR3vB,EAAG,GAAK4vB,EACR5vB,EAAG,GAAK6vB,EACR7vB,EAAG,GAAK8vB,EACR9vB,EAAG,GAAK+vB,EACR/vB,EAAG,GAAKgwB,EACRhwB,EAAG,GAAKiwB,EACRjwB,EAAG,GAAKkwB,EACRlwB,EAAG,GAAK4xB,EACR5xB,EAAG,GAAK6xB,EACR7xB,EAAE,IAAM8xB,EACR9xB,EAAE,IAAM+xB,EACR/xB,EAAE,IAAMgyB,EACRhyB,EAAE,IAAMiyB,EACRjyB,EAAE,IAAMkyB,EACRlyB,EAAE,IAAMmyB,EAGV,SAASgC,EAAEn0B,EAAG+wB,GACZY,EAAE3xB,EAAG+wB,EAAGA,GAGV,SAASqD,EAASp0B,EAAGT,GACnB,IACIwxB,EADAnxB,EAAIwrB,IAER,IAAK2F,EAAI,EAAGA,EAAI,GAAIA,IAAKnxB,EAAEmxB,GAAKxxB,EAAEwxB,GAClC,IAAKA,EAAI,IAAKA,GAAK,EAAGA,IACpBoD,EAAEv0B,EAAGA,GACI,IAANmxB,GAAiB,IAANA,GAASY,EAAE/xB,EAAGA,EAAGL,GAEjC,IAAKwxB,EAAI,EAAGA,EAAI,GAAIA,IAAK/wB,EAAE+wB,GAAKnxB,EAAEmxB,GAGpC,SAASsD,EAAQr0B,EAAGT,GAClB,IACIwxB,EADAnxB,EAAIwrB,IAER,IAAK2F,EAAI,EAAGA,EAAI,GAAIA,IAAKnxB,EAAEmxB,GAAKxxB,EAAEwxB,GAClC,IAAKA,EAAI,IAAKA,GAAK,EAAGA,IAClBoD,EAAEv0B,EAAGA,GACI,IAANmxB,GAASY,EAAE/xB,EAAGA,EAAGL,GAExB,IAAKwxB,EAAI,EAAGA,EAAI,GAAIA,IAAK/wB,EAAE+wB,GAAKnxB,EAAEmxB,GAGpC,SAASuD,EAAkBnD,EAAGnwB,EAAGK,GAC/B,IAC8BhB,EAAGd,EAD7B8vB,EAAI,IAAIjH,WAAW,IACnB+D,EAAI,IAAIb,aAAa,IACrByF,EAAI3F,IAAMtC,EAAIsC,IAAMxrB,EAAIwrB,IACxBvrB,EAAIurB,IAAMlnB,EAAIknB,IAAM3oB,EAAI2oB,IAC5B,IAAK7rB,EAAI,EAAGA,EAAI,GAAIA,IAAK8vB,EAAE9vB,GAAKyB,EAAEzB,GAIlC,IAHA8vB,EAAE,IAAW,IAANruB,EAAE,IAAS,GAClBquB,EAAE,IAAI,IACNmC,EAAYrF,EAAE9qB,GACT9B,EAAI,EAAGA,EAAI,GAAIA,IAClBupB,EAAEvpB,GAAG4sB,EAAE5sB,GACPM,EAAEN,GAAGwxB,EAAExxB,GAAGK,EAAEL,GAAG,EAGjB,IADAwxB,EAAE,GAAGlxB,EAAE,GAAG,EACLN,EAAE,IAAKA,GAAG,IAAKA,EAElB2xB,EAASH,EAAEjI,EADXzoB,EAAGgvB,EAAE9vB,IAAI,MAAQ,EAAFA,GAAM,GAErB2xB,EAAStxB,EAAEC,EAAEQ,GACboxB,EAAEvtB,EAAE6sB,EAAEnxB,GACN8xB,EAAEX,EAAEA,EAAEnxB,GACN6xB,EAAE7xB,EAAEkpB,EAAEjpB,GACN6xB,EAAE5I,EAAEA,EAAEjpB,GACNs0B,EAAEt0B,EAAEqE,GACJiwB,EAAE1xB,EAAEsuB,GACJY,EAAEZ,EAAEnxB,EAAEmxB,GACNY,EAAE/xB,EAAEkpB,EAAE5kB,GACNutB,EAAEvtB,EAAE6sB,EAAEnxB,GACN8xB,EAAEX,EAAEA,EAAEnxB,GACNu0B,EAAErL,EAAEiI,GACJW,EAAE9xB,EAAEC,EAAE4C,GACNkvB,EAAEZ,EAAEnxB,EAAEgsB,GACN6F,EAAEV,EAAEA,EAAElxB,GACN8xB,EAAE/xB,EAAEA,EAAEmxB,GACNY,EAAEZ,EAAElxB,EAAE4C,GACNkvB,EAAE9xB,EAAEipB,EAAEqD,GACNgI,EAAErL,EAAE5kB,GACJgtB,EAASH,EAAEjI,EAAEzoB,GACb6wB,EAAStxB,EAAEC,EAAEQ,GAEf,IAAKd,EAAI,EAAGA,EAAI,GAAIA,IAClB4sB,EAAE5sB,EAAE,IAAIwxB,EAAExxB,GACV4sB,EAAE5sB,EAAE,IAAIK,EAAEL,GACV4sB,EAAE5sB,EAAE,IAAIupB,EAAEvpB,GACV4sB,EAAE5sB,EAAE,IAAIM,EAAEN,GAEZ,IAAIg1B,EAAMpI,EAAEqI,SAAS,IACjBC,EAAMtI,EAAEqI,SAAS,IAIrB,OAHAJ,EAASG,EAAIA,GACb5C,EAAE8C,EAAIA,EAAIF,GACVnD,EAAUD,EAAEsD,GACL,EAGT,SAASC,EAAuBvD,EAAGnwB,GACjC,OAAOszB,EAAkBnD,EAAGnwB,EAAGyqB,GAGjC,SAASkJ,EAAmBpI,EAAGJ,GAE7B,OADAZ,EAAYY,EAAG,IACRuI,EAAuBnI,EAAGJ,GAGnC,SAASyI,EAAoB/H,EAAGN,EAAGJ,GACjC,IAAI7qB,EAAI,IAAI8mB,WAAW,IAEvB,OADAkM,EAAkBhzB,EAAG6qB,EAAGI,GACjBwC,EAAqBlC,EAAGrB,EAAIlqB,EAAG2tB,GA33BxCS,EAASvuB,UAAU0zB,OAAS,SAASl1B,EAAGyvB,EAAMtb,GA2B5C,IA1BA,IACI6b,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAItwB,EAChCk1B,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAFpCC,EAAQ1wB,KAAKwrB,IAAM,EAAI,KAIvBmF,EAAK3wB,KAAKsnB,EAAE,GACZsJ,EAAK5wB,KAAKsnB,EAAE,GACZuJ,EAAK7wB,KAAKsnB,EAAE,GACZwJ,EAAK9wB,KAAKsnB,EAAE,GACZyJ,EAAK/wB,KAAKsnB,EAAE,GACZ0J,EAAKhxB,KAAKsnB,EAAE,GACZ2J,EAAKjxB,KAAKsnB,EAAE,GACZ4J,EAAKlxB,KAAKsnB,EAAE,GACZ6J,EAAKnxB,KAAKsnB,EAAE,GACZ8J,EAAKpxB,KAAKsnB,EAAE,GAEZ+J,EAAKrxB,KAAKzE,EAAE,GACZ+1B,EAAKtxB,KAAKzE,EAAE,GACZg2B,EAAKvxB,KAAKzE,EAAE,GACZi2B,EAAKxxB,KAAKzE,EAAE,GACZk2B,EAAKzxB,KAAKzE,EAAE,GACZm2B,EAAK1xB,KAAKzE,EAAE,GACZo2B,EAAK3xB,KAAKzE,EAAE,GACZq2B,EAAK5xB,KAAKzE,EAAE,GACZs2B,EAAK7xB,KAAKzE,EAAE,GACZu2B,EAAK9xB,KAAKzE,EAAE,GAETyT,GAAS,IAcdghB,EAFAl1B,EAAI,EAGJk1B,IAdmDW,GAAmC,MAAtF9F,EAAkB,IAAbhwB,EAAEyvB,EAAM,IAA0B,IAAbzvB,EAAEyvB,EAAM,KAAc,IAcrC+G,EACXrB,IAdmDY,GAAmC,MAA3B/F,IAAO,IAAlEC,EAAkB,IAAbjwB,EAAEyvB,EAAM,IAA0B,IAAbzvB,EAAEyvB,EAAM,KAAc,IAAgC,KAcpE,EAAIwH,GAChB9B,IAdmDa,GAAmC,MAA3B/F,IAAO,IAAlEC,EAAkB,IAAblwB,EAAEyvB,EAAM,IAA0B,IAAbzvB,EAAEyvB,EAAM,KAAc,IAAgC,KAcpE,EAAIuH,GAChB7B,IAdmDc,GAAmC,MAA3B/F,IAAQ,GAAnEC,EAAkB,IAAbnwB,EAAEyvB,EAAM,IAA0B,IAAbzvB,EAAEyvB,EAAM,KAAc,IAAgC,KAcpE,EAAIsH,GAEhB92B,GADAk1B,IAdmDe,GAAmC,MAA3B/F,IAAQ,GAAnEC,EAAkB,IAAbpwB,EAAEyvB,EAAM,IAA0B,IAAbzvB,EAAEyvB,EAAM,KAAc,IAA+B,MAcnE,EAAIqH,MACJ,GAAK3B,GAAM,KACvBA,IAfAgB,GAAQ/F,IAAQ,EAAM,OAeV,EAAIyG,GAChB1B,IAfmDiB,GAAmC,MAA3BhG,IAAO,IAAlEC,EAAkB,IAAbrwB,EAAEyvB,EAAK,KAA2B,IAAbzvB,EAAEyvB,EAAK,MAAe,IAAgC,KAepE,EAAImH,GAChBzB,IAfmDkB,GAAmC,MAA3BhG,IAAO,IAAlEC,EAAkB,IAAbtwB,EAAEyvB,EAAK,KAA2B,IAAbzvB,EAAEyvB,EAAK,MAAe,IAAgC,KAepE,EAAIkH,GAChBxB,IAfmDmB,GAAmC,MAA3BhG,IAAQ,GAAnEC,EAAkB,IAAbvwB,EAAEyvB,EAAK,KAA2B,IAAbzvB,EAAEyvB,EAAK,MAAe,IAAgC,KAepE,EAAIiH,GAIhBtB,EAFAn1B,IADAk1B,IAfAoB,GAAQhG,IAAO,EAAMsF,IAeT,EAAIY,MACH,GAGbrB,GAAMU,EAAKW,EACXrB,GAAMW,EAAKS,EACXpB,GAAMY,GAAM,EAAIiB,GAChB7B,GAAMa,GAAM,EAAIe,GAEhB/2B,GADAm1B,GAAMc,GAAM,EAAIa,MACJ,GAAK3B,GAAM,KACvBA,GAAMe,GAAM,EAAIW,GAChB1B,GAAMgB,GAAM,EAAIS,GAChBzB,GAAMiB,GAAM,EAAIO,GAChBxB,GAAMkB,GAAM,EAAIK,GAEhB12B,IADAm1B,GAAMmB,GAAM,EAAIG,MACH,GAAKtB,GAAM,KAExBC,EAAKp1B,EACLo1B,GAAMS,EAAKY,EACXrB,GAAMU,EAAKU,EACXpB,GAAMW,EAAKQ,EACXnB,GAAMY,GAAM,EAAIgB,GAEhBh3B,GADAo1B,GAAMa,GAAM,EAAIc,MACJ,GAAK3B,GAAM,KACvBA,GAAMc,GAAM,EAAIY,GAChB1B,GAAMe,GAAM,EAAIU,GAChBzB,GAAMgB,GAAM,EAAIQ,GAChBxB,GAAMiB,GAAM,EAAIM,GAIhBtB,EAFAr1B,IADAo1B,GAAMkB,GAAM,EAAII,MACH,GAGbrB,GAAMQ,EAAKa,EACXrB,GAAMS,EAAKW,EACXpB,GAAMU,EAAKS,EACXnB,GAAMW,EAAKO,EAEXv2B,GADAq1B,GAAMY,GAAM,EAAIe,MACJ,GAAK3B,GAAM,KACvBA,GAAMa,GAAM,EAAIa,GAChB1B,GAAMc,GAAM,EAAIW,GAChBzB,GAAMe,GAAM,EAAIS,GAChBxB,GAAMgB,GAAM,EAAIO,GAIhBtB,EAFAt1B,IADAq1B,GAAMiB,GAAM,EAAIK,MACH,GAGbrB,GAAMO,EAAKc,EACXrB,GAAMQ,EAAKY,EACXpB,GAAMS,EAAKU,EACXnB,GAAMU,EAAKQ,EAEXx2B,GADAs1B,GAAMW,EAAKM,KACC,GAAKjB,GAAM,KACvBA,GAAMY,GAAM,EAAIc,GAChB1B,GAAMa,GAAM,EAAIY,GAChBzB,GAAMc,GAAM,EAAIU,GAChBxB,GAAMe,GAAM,EAAIQ,GAIhBtB,EAFAv1B,IADAs1B,GAAMgB,GAAM,EAAIM,MACH,GAGbrB,GAAMM,EAAKe,EACXrB,GAAMO,EAAKa,EACXpB,GAAMQ,EAAKW,EACXnB,GAAMS,EAAKS,EAEXz2B,GADAu1B,GAAMU,EAAKO,KACC,GAAKjB,GAAM,KACvBA,GAAMW,EAAKK,EACXhB,GAAMY,GAAM,EAAIa,GAChBzB,GAAMa,GAAM,EAAIW,GAChBxB,GAAMc,GAAM,EAAIS,GAIhBtB,EAFAx1B,IADAu1B,GAAMe,GAAM,EAAIO,MACH,GAGbrB,GAAMK,EAAKgB,EACXrB,GAAMM,EAAKc,EACXpB,GAAMO,EAAKY,EACXnB,GAAMQ,EAAKU,EAEX12B,GADAw1B,GAAMS,EAAKQ,KACC,GAAKjB,GAAM,KACvBA,GAAMU,EAAKM,EACXhB,GAAMW,EAAKI,EACXf,GAAMY,GAAM,EAAIY,GAChBxB,GAAMa,GAAM,EAAIU,GAIhBtB,EAFAz1B,IADAw1B,GAAMc,GAAM,EAAIQ,MACH,GAGbrB,GAAMI,EAAKiB,EACXrB,GAAMK,EAAKe,EACXpB,GAAMM,EAAKa,EACXnB,GAAMO,EAAKW,EAEX32B,GADAy1B,GAAMQ,EAAKS,KACC,GAAKjB,GAAM,KACvBA,GAAMS,EAAKO,EACXhB,GAAMU,EAAKK,EACXf,GAAMW,EAAKG,EACXd,GAAMY,GAAM,EAAIW,GAIhBtB,EAFA11B,IADAy1B,GAAMa,GAAM,EAAIS,MACH,GAGbrB,GAAMG,EAAKkB,EACXrB,GAAMI,EAAKgB,EACXpB,GAAMK,EAAKc,EACXnB,GAAMM,EAAKY,EAEX52B,GADA01B,GAAMO,EAAKU,KACC,GAAKjB,GAAM,KACvBA,GAAMQ,EAAKQ,EACXhB,GAAMS,EAAKM,EACXf,GAAMU,EAAKI,EACXd,GAAMW,EAAKE,EAIXZ,EAFA31B,IADA01B,GAAMY,GAAM,EAAIU,MACH,GAGbrB,GAAME,EAAKmB,EACXrB,GAAMG,EAAKiB,EACXpB,GAAMI,EAAKe,EACXnB,GAAMK,EAAKa,EAEX72B,GADA21B,GAAMM,EAAKW,KACC,GAAKjB,GAAM,KACvBA,GAAMO,EAAKS,EACXhB,GAAMQ,EAAKO,EACXf,GAAMS,EAAKK,EACXd,GAAMU,EAAKG,EAUXX,EAJAX,EAAS,MADTl1B,GADAA,IAFAA,IADA21B,GAAMW,EAAKC,KACE,KAED,GAAKv2B,EAAM,IAhILk1B,GAAM,MAiIT,GAMfY,EAHAX,GADAn1B,KAAW,GAKX+1B,EA5GkBX,GAAM,KA6GxBY,EA/FkBX,GAAM,KAgGxBY,EAlFkBX,GAAM,KAmFxBY,EArEkBX,GAAM,KAsExBY,EAxDkBX,GAAM,KAyDxBY,EA3CkBX,GAAM,KA4CxBY,EA9BkBX,GAAM,KA+BxBY,EAjBkBX,GAAM,KAmBxBnG,GAAQ,GACRtb,GAAS,GAEXhP,KAAKsnB,EAAE,GAAKqJ,EACZ3wB,KAAKsnB,EAAE,GAAKsJ,EACZ5wB,KAAKsnB,EAAE,GAAKuJ,EACZ7wB,KAAKsnB,EAAE,GAAKwJ,EACZ9wB,KAAKsnB,EAAE,GAAKyJ,EACZ/wB,KAAKsnB,EAAE,GAAK0J,EACZhxB,KAAKsnB,EAAE,GAAK2J,EACZjxB,KAAKsnB,EAAE,GAAK4J,EACZlxB,KAAKsnB,EAAE,GAAK6J,EACZnxB,KAAKsnB,EAAE,GAAK8J,GAGdxG,EAASvuB,UAAU8N,OAAS,SAAS4nB,EAAKC,GACxC,IACIl3B,EAAGm3B,EAAMt0B,EAAGlD,EADZ+nB,EAAI,IAAI6I,YAAY,IAGxB,GAAIrrB,KAAKurB,SAAU,CAGjB,IAFA9wB,EAAIuF,KAAKurB,SACTvrB,KAAK4X,OAAOnd,KAAO,EACZA,EAAI,GAAIA,IAAKuF,KAAK4X,OAAOnd,GAAK,EACrCuF,KAAKwrB,IAAM,EACXxrB,KAAK+vB,OAAO/vB,KAAK4X,OAAQ,EAAG,IAK9B,IAFA9c,EAAIkF,KAAKsnB,EAAE,KAAO,GAClBtnB,KAAKsnB,EAAE,IAAM,KACR7sB,EAAI,EAAGA,EAAI,GAAIA,IAClBuF,KAAKsnB,EAAE7sB,IAAMK,EACbA,EAAIkF,KAAKsnB,EAAE7sB,KAAO,GAClBuF,KAAKsnB,EAAE7sB,IAAM,KAaf,IAXAuF,KAAKsnB,EAAE,IAAW,EAAJxsB,EACdA,EAAIkF,KAAKsnB,EAAE,KAAO,GAClBtnB,KAAKsnB,EAAE,IAAM,KACbtnB,KAAKsnB,EAAE,IAAMxsB,EACbA,EAAIkF,KAAKsnB,EAAE,KAAO,GAClBtnB,KAAKsnB,EAAE,IAAM,KACbtnB,KAAKsnB,EAAE,IAAMxsB,EAEb0nB,EAAE,GAAKxiB,KAAKsnB,EAAE,GAAK,EACnBxsB,EAAI0nB,EAAE,KAAO,GACbA,EAAE,IAAM,KACH/nB,EAAI,EAAGA,EAAI,GAAIA,IAClB+nB,EAAE/nB,GAAKuF,KAAKsnB,EAAE7sB,GAAKK,EACnBA,EAAI0nB,EAAE/nB,KAAO,GACb+nB,EAAE/nB,IAAM,KAKV,IAHA+nB,EAAE,IAAM,KAERyP,GAAY,EAAJn3B,GAAS,EACZL,EAAI,EAAGA,EAAI,GAAIA,IAAK+nB,EAAE/nB,IAAMw3B,EAEjC,IADAA,GAAQA,EACHx3B,EAAI,EAAGA,EAAI,GAAIA,IAAKuF,KAAKsnB,EAAE7sB,GAAMuF,KAAKsnB,EAAE7sB,GAAKw3B,EAAQzP,EAAE/nB,GAa5D,IAXAuF,KAAKsnB,EAAE,GAAoE,OAA7DtnB,KAAKsnB,EAAE,GAActnB,KAAKsnB,EAAE,IAAM,IAChDtnB,KAAKsnB,EAAE,GAAoE,OAA7DtnB,KAAKsnB,EAAE,KAAQ,EAAMtnB,KAAKsnB,EAAE,IAAM,IAChDtnB,KAAKsnB,EAAE,GAAoE,OAA7DtnB,KAAKsnB,EAAE,KAAQ,EAAMtnB,KAAKsnB,EAAE,IAAO,GACjDtnB,KAAKsnB,EAAE,GAAoE,OAA7DtnB,KAAKsnB,EAAE,KAAQ,EAAMtnB,KAAKsnB,EAAE,IAAO,GACjDtnB,KAAKsnB,EAAE,GAAoE,OAA7DtnB,KAAKsnB,EAAE,KAAO,GAAOtnB,KAAKsnB,EAAE,IAAO,EAAMtnB,KAAKsnB,EAAE,IAAM,IACpEtnB,KAAKsnB,EAAE,GAAoE,OAA7DtnB,KAAKsnB,EAAE,KAAQ,EAAMtnB,KAAKsnB,EAAE,IAAM,IAChDtnB,KAAKsnB,EAAE,GAAoE,OAA7DtnB,KAAKsnB,EAAE,KAAQ,EAAMtnB,KAAKsnB,EAAE,IAAO,GACjDtnB,KAAKsnB,EAAE,GAAoE,OAA7DtnB,KAAKsnB,EAAE,KAAQ,EAAMtnB,KAAKsnB,EAAE,IAAO,GAEjD3pB,EAAIqC,KAAKsnB,EAAE,GAAKtnB,KAAKsrB,IAAI,GACzBtrB,KAAKsnB,EAAE,GAAS,MAAJ3pB,EACPlD,EAAI,EAAGA,EAAI,EAAGA,IACjBkD,GAAOqC,KAAKsnB,EAAE7sB,GAAKuF,KAAKsrB,IAAI7wB,GAAM,IAAMkD,IAAM,IAAO,EACrDqC,KAAKsnB,EAAE7sB,GAAS,MAAJkD,EAGdo0B,EAAIC,EAAQ,GAAMhyB,KAAKsnB,EAAE,KAAO,EAAK,IACrCyK,EAAIC,EAAQ,GAAMhyB,KAAKsnB,EAAE,KAAO,EAAK,IACrCyK,EAAIC,EAAQ,GAAMhyB,KAAKsnB,EAAE,KAAO,EAAK,IACrCyK,EAAIC,EAAQ,GAAMhyB,KAAKsnB,EAAE,KAAO,EAAK,IACrCyK,EAAIC,EAAQ,GAAMhyB,KAAKsnB,EAAE,KAAO,EAAK,IACrCyK,EAAIC,EAAQ,GAAMhyB,KAAKsnB,EAAE,KAAO,EAAK,IACrCyK,EAAIC,EAAQ,GAAMhyB,KAAKsnB,EAAE,KAAO,EAAK,IACrCyK,EAAIC,EAAQ,GAAMhyB,KAAKsnB,EAAE,KAAO,EAAK,IACrCyK,EAAIC,EAAQ,GAAMhyB,KAAKsnB,EAAE,KAAO,EAAK,IACrCyK,EAAIC,EAAQ,GAAMhyB,KAAKsnB,EAAE,KAAO,EAAK,IACrCyK,EAAIC,EAAO,IAAOhyB,KAAKsnB,EAAE,KAAO,EAAK,IACrCyK,EAAIC,EAAO,IAAOhyB,KAAKsnB,EAAE,KAAO,EAAK,IACrCyK,EAAIC,EAAO,IAAOhyB,KAAKsnB,EAAE,KAAO,EAAK,IACrCyK,EAAIC,EAAO,IAAOhyB,KAAKsnB,EAAE,KAAO,EAAK,IACrCyK,EAAIC,EAAO,IAAOhyB,KAAKsnB,EAAE,KAAO,EAAK,IACrCyK,EAAIC,EAAO,IAAOhyB,KAAKsnB,EAAE,KAAO,EAAK,KAGvCsD,EAASvuB,UAAUsvB,OAAS,SAAS9wB,EAAGyvB,EAAMtb,GAC5C,IAAIvU,EAAGy3B,EAEP,GAAIlyB,KAAKurB,SAAU,CAIjB,KAHA2G,EAAQ,GAAKlyB,KAAKurB,UACPvc,IACTkjB,EAAOljB,GACJvU,EAAI,EAAGA,EAAIy3B,EAAMz3B,IACpBuF,KAAK4X,OAAO5X,KAAKurB,SAAW9wB,GAAKI,EAAEyvB,EAAK7vB,GAI1C,GAHAuU,GAASkjB,EACT5H,GAAQ4H,EACRlyB,KAAKurB,UAAY2G,EACblyB,KAAKurB,SAAW,GAClB,OACFvrB,KAAK+vB,OAAO/vB,KAAK4X,OAAQ,EAAG,IAC5B5X,KAAKurB,SAAW,EAUlB,GAPIvc,GAAS,KACXkjB,EAAOljB,EAASA,EAAQ,GACxBhP,KAAK+vB,OAAOl1B,EAAGyvB,EAAM4H,GACrB5H,GAAQ4H,EACRljB,GAASkjB,GAGPljB,EAAO,CACT,IAAKvU,EAAI,EAAGA,EAAIuU,EAAOvU,IACrBuF,KAAK4X,OAAO5X,KAAKurB,SAAW9wB,GAAKI,EAAEyvB,EAAK7vB,GAC1CuF,KAAKurB,UAAYvc,IAikBrB,IAAImjB,EAAqBrG,EACrBsG,EAA0BrG,EAc9B,IAAIsG,EAAI,CACN,WAAY,WAAY,WAAY,UACpC,WAAY,WAAY,WAAY,WACpC,UAAY,WAAY,WAAY,WACpC,WAAY,WAAY,WAAY,WACpC,WAAY,WAAY,UAAY,WACpC,UAAY,WAAY,WAAY,WACpC,WAAY,WAAY,WAAY,UACpC,WAAY,UAAY,WAAY,WACpC,WAAY,WAAY,WAAY,UACpC,UAAY,WAAY,UAAY,WACpC,UAAY,WAAY,WAAY,WACpC,WAAY,WAAY,WAAY,WACpC,WAAY,WAAY,WAAY,UACpC,WAAY,WAAY,WAAY,WACpC,WAAY,WAAY,WAAY,WACpC,UAAY,WAAY,UAAY,UACpC,UAAY,WAAY,UAAY,WACpC,WAAY,WAAY,WAAY,WACpC,WAAY,WAAY,WAAY,WACpC,WAAY,WAAY,WAAY,UACpC,WAAY,WAAY,WAAY,WACpC,WAAY,WAAY,WAAY,UACpC,WAAY,WAAY,WAAY,WACpC,WAAY,WAAY,UAAY,UACpC,UAAY,WAAY,UAAY,WACpC,UAAY,WAAY,UAAY,WACpC,UAAY,WAAY,WAAY,WACpC,WAAY,WAAY,WAAY,WACpC,WAAY,WAAY,WAAY,WACpC,WAAY,WAAY,WAAY,UACpC,WAAY,UAAY,WAAY,WACpC,WAAY,WAAY,WAAY,WACpC,WAAY,WAAY,WAAY,UACpC,WAAY,WAAY,WAAY,WACpC,UAAY,WAAY,UAAY,WACpC,UAAY,WAAY,UAAY,UACpC,UAAY,UAAY,UAAY,WACpC,WAAY,UAAY,WAAY,WACpC,WAAY,WAAY,WAAY,WACpC,WAAY,UAAY,WAAY,YAGtC,SAASC,EAAqBC,EAAIC,EAAI33B,EAAGqB,GAyBvC,IAxBA,IACIu2B,EAAKC,EAAKC,EAAKC,EAAKC,EAAKC,EAAKC,EAAKC,EACnCC,EAAKC,EAAKC,EAAKC,EAAKC,EAAKC,EAAKC,EAAKC,EACnCC,EAAIC,EAAIj5B,EAAG8xB,EAAGjF,EAAG5sB,EAAGuxB,EAAGjI,EAAGlpB,EAAGC,EAH7B44B,EAAK,IAAIC,WAAW,IAAKC,EAAK,IAAID,WAAW,IAK7CE,EAAMvB,EAAG,GACTwB,EAAMxB,EAAG,GACTyB,EAAMzB,EAAG,GACT0B,EAAM1B,EAAG,GACT2B,EAAM3B,EAAG,GACT4B,EAAM5B,EAAG,GACT6B,EAAM7B,EAAG,GACT8B,EAAM9B,EAAG,GAET+B,EAAM9B,EAAG,GACT+B,EAAM/B,EAAG,GACTgC,EAAMhC,EAAG,GACTiC,EAAMjC,EAAG,GACTkC,EAAMlC,EAAG,GACTmC,EAAMnC,EAAG,GACToC,EAAMpC,EAAG,GACTqC,EAAMrC,EAAG,GAETvM,EAAM,EACH/pB,GAAK,KAAK,CACf,IAAKzB,EAAI,EAAGA,EAAI,GAAIA,IAClB8xB,EAAI,EAAI9xB,EAAIwrB,EACZ0N,EAAGl5B,GAAMI,EAAE0xB,EAAE,IAAM,GAAO1xB,EAAE0xB,EAAE,IAAM,GAAO1xB,EAAE0xB,EAAE,IAAM,EAAK1xB,EAAE0xB,EAAE,GAC9DsH,EAAGp5B,GAAMI,EAAE0xB,EAAE,IAAM,GAAO1xB,EAAE0xB,EAAE,IAAM,GAAO1xB,EAAE0xB,EAAE,IAAM,EAAK1xB,EAAE0xB,EAAE,GAEhE,IAAK9xB,EAAI,EAAGA,EAAI,GAAIA,IA+HlB,GA9HAg4B,EAAMqB,EACNpB,EAAMqB,EACNpB,EAAMqB,EACNpB,EAAMqB,EACNpB,EAAMqB,EACNpB,EAAMqB,EACNpB,EAAMqB,EACAC,EAENpB,EAAMqB,EACNpB,EAAMqB,EACNpB,EAAMqB,EACNpB,EAAMqB,EACNpB,EAAMqB,EACNpB,EAAMqB,EACNpB,EAAMqB,EACAC,EAMN5I,EAAQ,OAFRvxB,EAAIm6B,GAEY7Q,EAAItpB,IAAM,GAC1BI,EAAQ,OAJRwsB,EAAI+M,GAIYt5B,EAAIusB,IAAM,GAM1B2E,GAAS,OAFTvxB,GAAMg6B,IAAQ,GAAOR,GAAO,KAAcQ,IAAQ,GAAOR,GAAO,KAAcA,IAAQ,EAAYQ,GAAO,KAExF1Q,GAAKtpB,IAAM,GAC5BI,GAAS,OAJTwsB,GAAM4M,IAAQ,GAAOQ,GAAO,KAAcR,IAAQ,GAAOQ,GAAO,KAAcA,IAAQ,EAAYR,GAAO,KAIxFn5B,GAAKusB,IAAM,GAM5B2E,GAAS,OAFTvxB,EAAKg6B,EAAMC,GAASD,EAAME,GAET5Q,GAAKtpB,IAAM,GAC5BI,GAAS,OAJTwsB,EAAK4M,EAAMC,GAASD,EAAME,GAITr5B,GAAKusB,IAAM,GAM5B2E,GAAS,OAFTvxB,EAAI23B,EAAI,EAAF53B,EAAI,IAEOupB,GAAKtpB,IAAM,GAC5BI,GAAS,OAJTwsB,EAAI+K,EAAI,EAAF53B,IAIWM,GAAKusB,IAAM,GAG5BA,EAAIqM,EAAGl5B,EAAE,IAGQupB,IAFjBtpB,EAAIm5B,EAAGp5B,EAAE,OAEmB,GAC5BK,GAAS,MAAJwsB,EAAYvsB,GAAKusB,IAAM,GAG5BxsB,IADAkpB,IAHAiI,GAAS,MAAJvxB,KAGM,MACA,GAUXuxB,EAAQ,OAFRvxB,EAJAg5B,EAAS,MAAJzH,EAAajI,GAAK,IAMPA,EAAItpB,IAAM,GAC1BI,EAAQ,OAJRwsB,EAJAmM,EAAS,MAAJ34B,GAFLC,GAAKD,IAAM,KAEY,IAQPC,EAAIusB,IAAM,GAM1B2E,GAAS,OAFTvxB,GAAM45B,IAAQ,GAAOR,GAAO,IAAcA,IAAQ,EAAYQ,GAAO,KAAmBR,IAAQ,EAAYQ,GAAO,KAElGtQ,GAAKtpB,IAAM,GAC5BI,GAAS,OAJTwsB,GAAMwM,IAAQ,GAAOQ,GAAO,IAAcA,IAAQ,EAAYR,GAAO,KAAmBQ,IAAQ,EAAYR,GAAO,KAIlG/4B,GAAKusB,IAAM,GAMXtD,IAFjBtpB,EAAK45B,EAAMC,EAAQD,EAAME,EAAQD,EAAMC,KAEX,GAC5B15B,GAAS,OAJTwsB,EAAKwM,EAAMC,EAAQD,EAAME,EAAQD,EAAMC,GAItBj5B,GAAKusB,IAAM,GAM5B0L,EAAW,OAHXl4B,IADAkpB,IAHAiI,GAAS,MAAJvxB,KAGM,MACA,KACXK,GAAKD,IAAM,KAEgB,GAC3B04B,EAAW,MAAJvH,EAAejI,GAAK,GAM3BiI,EAAQ,OAFRvxB,EAAI04B,GAEYpP,EAAItpB,IAAM,GAC1BI,EAAQ,OAJRwsB,EAAIsL,GAIY73B,EAAIusB,IAAM,GAKTtD,IAFjBtpB,EAAIg5B,KAEwB,GAC5B54B,GAAS,OAJTwsB,EAAImM,GAIa14B,GAAKusB,IAAM,GAS5ByM,EAAMtB,EACNuB,EAAMtB,EACNuB,EAAMtB,EACNuB,EANAtB,EAAW,OAHX93B,IADAkpB,IAHAiI,GAAS,MAAJvxB,KAGM,MACA,KACXK,GAAKD,IAAM,KAEgB,GAO3Bq5B,EAAMtB,EACNuB,EAAMtB,EACNuB,EAAMtB,EACNe,EAAMd,EAENuB,EAAMtB,EACNuB,EAAMtB,EACNuB,EAAMtB,EACNuB,EAdAtB,EAAW,MAAJnH,EAAejI,GAAK,GAe3B2Q,EAAMtB,EACNuB,EAAMtB,EACNuB,EAAMtB,EACNe,EAAMd,EAEF/4B,EAAE,IAAO,GACX,IAAK8xB,EAAI,EAAGA,EAAI,GAAIA,IAElBjF,EAAIqM,EAAGpH,GAGPN,EAAQ,OAFRvxB,EAAIm5B,EAAGtH,IAESvI,EAAItpB,IAAM,GAC1BI,EAAQ,MAAJwsB,EAAYvsB,EAAIusB,IAAM,GAE1BA,EAAIqM,GAAIpH,EAAE,GAAG,IAGbN,GAAS,OAFTvxB,EAAIm5B,GAAItH,EAAE,GAAG,KAEIvI,GAAKtpB,IAAM,GAC5BI,GAAS,MAAJwsB,EAAYvsB,GAAKusB,IAAM,GAG5BmM,EAAKE,GAAIpH,EAAE,GAAG,IAKdN,GAAS,OAFTvxB,IAFAg5B,EAAKG,GAAItH,EAAE,GAAG,OAED,EAAMkH,GAAM,KAAaC,IAAO,EAAMD,GAAM,KAAaC,IAAO,EAAMD,GAAM,KAExEzP,GAAKtpB,IAAM,GAC5BI,GAAS,OAJTwsB,GAAMmM,IAAO,EAAMC,GAAM,KAAaD,IAAO,EAAMC,GAAM,IAAYD,IAAO,GAI3D14B,GAAKusB,IAAM,GAG5BmM,EAAKE,GAAIpH,EAAE,IAAI,IAKEvI,IAFjBtpB,IAFAg5B,EAAKG,GAAItH,EAAE,IAAI,OAEF,GAAOkH,GAAM,KAAcA,IAAO,GAAYC,GAAM,IAAmBA,IAAO,EAAMD,GAAM,OAE3E,GAC5B34B,GAAS,OAJTwsB,GAAMmM,IAAO,GAAOC,GAAM,KAAcA,IAAO,GAAYD,GAAM,GAAkBA,IAAO,GAIzE14B,GAAKusB,IAAM,GAI5BvsB,IADAD,IADAkpB,IAHAiI,GAAS,MAAJvxB,KAGM,MACA,MACA,GAEXi5B,EAAGpH,GAAU,MAAJzxB,EAAeC,GAAK,GAC7B84B,EAAGtH,GAAU,MAAJN,EAAejI,GAAK,GASnCiI,EAAQ,OAFRvxB,EAAI45B,GAEYtQ,EAAItpB,IAAM,GAC1BI,EAAQ,OAJRwsB,EAAIwM,GAIY/4B,EAAIusB,IAAM,GAE1BA,EAAIiL,EAAG,GAGUvO,IAFjBtpB,EAAI83B,EAAG,MAEqB,GAC5B13B,GAAS,MAAJwsB,EAAYvsB,GAAKusB,IAAM,GAI5BvsB,IADAD,IADAkpB,IAHAiI,GAAS,MAAJvxB,KAGM,MACA,MACA,GAEX63B,EAAG,GAAKuB,EAAW,MAAJh5B,EAAeC,GAAK,GACnCy3B,EAAG,GAAK8B,EAAW,MAAJrI,EAAejI,GAAK,GAKnCiI,EAAQ,OAFRvxB,EAAI65B,GAEYvQ,EAAItpB,IAAM,GAC1BI,EAAQ,OAJRwsB,EAAIyM,GAIYh5B,EAAIusB,IAAM,GAE1BA,EAAIiL,EAAG,GAGUvO,IAFjBtpB,EAAI83B,EAAG,MAEqB,GAC5B13B,GAAS,MAAJwsB,EAAYvsB,GAAKusB,IAAM,GAI5BvsB,IADAD,IADAkpB,IAHAiI,GAAS,MAAJvxB,KAGM,MACA,MACA,GAEX63B,EAAG,GAAKwB,EAAW,MAAJj5B,EAAeC,GAAK,GACnCy3B,EAAG,GAAK+B,EAAW,MAAJtI,EAAejI,GAAK,GAKnCiI,EAAQ,OAFRvxB,EAAI85B,GAEYxQ,EAAItpB,IAAM,GAC1BI,EAAQ,OAJRwsB,EAAI0M,GAIYj5B,EAAIusB,IAAM,GAE1BA,EAAIiL,EAAG,GAGUvO,IAFjBtpB,EAAI83B,EAAG,MAEqB,GAC5B13B,GAAS,MAAJwsB,EAAYvsB,GAAKusB,IAAM,GAI5BvsB,IADAD,IADAkpB,IAHAiI,GAAS,MAAJvxB,KAGM,MACA,MACA,GAEX63B,EAAG,GAAKyB,EAAW,MAAJl5B,EAAeC,GAAK,GACnCy3B,EAAG,GAAKgC,EAAW,MAAJvI,EAAejI,GAAK,GAKnCiI,EAAQ,OAFRvxB,EAAI+5B,GAEYzQ,EAAItpB,IAAM,GAC1BI,EAAQ,OAJRwsB,EAAI2M,GAIYl5B,EAAIusB,IAAM,GAE1BA,EAAIiL,EAAG,GAGUvO,IAFjBtpB,EAAI83B,EAAG,MAEqB,GAC5B13B,GAAS,MAAJwsB,EAAYvsB,GAAKusB,IAAM,GAI5BvsB,IADAD,IADAkpB,IAHAiI,GAAS,MAAJvxB,KAGM,MACA,MACA,GAEX63B,EAAG,GAAK0B,EAAW,MAAJn5B,EAAeC,GAAK,GACnCy3B,EAAG,GAAKiC,EAAW,MAAJxI,EAAejI,GAAK,GAKnCiI,EAAQ,OAFRvxB,EAAIg6B,GAEY1Q,EAAItpB,IAAM,GAC1BI,EAAQ,OAJRwsB,EAAI4M,GAIYn5B,EAAIusB,IAAM,GAE1BA,EAAIiL,EAAG,GAGUvO,IAFjBtpB,EAAI83B,EAAG,MAEqB,GAC5B13B,GAAS,MAAJwsB,EAAYvsB,GAAKusB,IAAM,GAI5BvsB,IADAD,IADAkpB,IAHAiI,GAAS,MAAJvxB,KAGM,MACA,MACA,GAEX63B,EAAG,GAAK2B,EAAW,MAAJp5B,EAAeC,GAAK,GACnCy3B,EAAG,GAAKkC,EAAW,MAAJzI,EAAejI,GAAK,GAKnCiI,EAAQ,OAFRvxB,EAAIi6B,GAEY3Q,EAAItpB,IAAM,GAC1BI,EAAQ,OAJRwsB,EAAI6M,GAIYp5B,EAAIusB,IAAM,GAE1BA,EAAIiL,EAAG,GAGUvO,IAFjBtpB,EAAI83B,EAAG,MAEqB,GAC5B13B,GAAS,MAAJwsB,EAAYvsB,GAAKusB,IAAM,GAI5BvsB,IADAD,IADAkpB,IAHAiI,GAAS,MAAJvxB,KAGM,MACA,MACA,GAEX63B,EAAG,GAAK4B,EAAW,MAAJr5B,EAAeC,GAAK,GACnCy3B,EAAG,GAAKmC,EAAW,MAAJ1I,EAAejI,GAAK,GAKnCiI,EAAQ,OAFRvxB,EAAIk6B,GAEY5Q,EAAItpB,IAAM,GAC1BI,EAAQ,OAJRwsB,EAAI8M,GAIYr5B,EAAIusB,IAAM,GAE1BA,EAAIiL,EAAG,GAGUvO,IAFjBtpB,EAAI83B,EAAG,MAEqB,GAC5B13B,GAAS,MAAJwsB,EAAYvsB,GAAKusB,IAAM,GAI5BvsB,IADAD,IADAkpB,IAHAiI,GAAS,MAAJvxB,KAGM,MACA,MACA,GAEX63B,EAAG,GAAK6B,EAAW,MAAJt5B,EAAeC,GAAK,GACnCy3B,EAAG,GAAKoC,EAAW,MAAJ3I,EAAejI,GAAK,GAKnCiI,EAAQ,OAFRvxB,EAAIm6B,GAEY7Q,EAAItpB,IAAM,GAC1BI,EAAQ,OAJRwsB,EAAI+M,GAIYt5B,EAAIusB,IAAM,GAE1BA,EAAIiL,EAAG,GAGUvO,IAFjBtpB,EAAI83B,EAAG,MAEqB,GAC5B13B,GAAS,MAAJwsB,EAAYvsB,GAAKusB,IAAM,GAI5BvsB,IADAD,IADAkpB,IAHAiI,GAAS,MAAJvxB,KAGM,MACA,MACA,GAEX63B,EAAG,GAAK8B,EAAW,MAAJv5B,EAAeC,GAAK,GACnCy3B,EAAG,GAAKqC,EAAW,MAAJ5I,EAAejI,GAAK,GAEnCiC,GAAO,IACP/pB,GAAK,IAGP,OAAOA,EAGT,SAAS44B,EAAY/R,EAAKloB,EAAGqB,GAC3B,IAGIzB,EAHA83B,EAAK,IAAIqB,WAAW,GACpBpB,EAAK,IAAIoB,WAAW,GACpBvM,EAAI,IAAI/D,WAAW,KAChBU,EAAI9nB,EAuBX,IArBAq2B,EAAG,GAAK,WACRA,EAAG,GAAK,WACRA,EAAG,GAAK,WACRA,EAAG,GAAK,WACRA,EAAG,GAAK,WACRA,EAAG,GAAK,WACRA,EAAG,GAAK,UACRA,EAAG,GAAK,WAERC,EAAG,GAAK,WACRA,EAAG,GAAK,WACRA,EAAG,GAAK,WACRA,EAAG,GAAK,WACRA,EAAG,GAAK,WACRA,EAAG,GAAK,UACRA,EAAG,GAAK,WACRA,EAAG,GAAK,UAERF,EAAqBC,EAAIC,EAAI33B,EAAGqB,GAChCA,GAAK,IAEAzB,EAAI,EAAGA,EAAIyB,EAAGzB,IAAK4sB,EAAE5sB,GAAKI,EAAEmpB,EAAE9nB,EAAEzB,GAQrC,IAPA4sB,EAAEnrB,GAAK,IAGPmrB,GADAnrB,EAAI,IAAI,KAAKA,EAAE,IAAI,EAAE,IACjB,GAAK,EACTkrB,EAAKC,EAAGnrB,EAAE,EAAK8nB,EAAI,UAAc,EAAGA,GAAK,GACzCsO,EAAqBC,EAAIC,EAAInL,EAAGnrB,GAE3BzB,EAAI,EAAGA,EAAI,EAAGA,IAAK2sB,EAAKrE,EAAK,EAAEtoB,EAAG83B,EAAG93B,GAAI+3B,EAAG/3B,IAEjD,OAAO,EAGT,SAAS4G,EAAI9E,EAAG8vB,GACd,IAAIJ,EAAI3F,IAAMtC,EAAIsC,IAAMxrB,EAAIwrB,IACxBvrB,EAAIurB,IAAMlnB,EAAIknB,IAAM3oB,EAAI2oB,IACxB9D,EAAI8D,IAAMgB,EAAIhB,IAAM3qB,EAAI2qB,IAE5BsG,EAAEX,EAAG1vB,EAAE,GAAIA,EAAE,IACbqwB,EAAEjxB,EAAG0wB,EAAE,GAAIA,EAAE,IACbQ,EAAEZ,EAAGA,EAAGtwB,GACRgxB,EAAE3I,EAAGznB,EAAE,GAAIA,EAAE,IACbowB,EAAEhxB,EAAG0wB,EAAE,GAAIA,EAAE,IACbQ,EAAE7I,EAAGA,EAAGroB,GACRkxB,EAAE/xB,EAAGyB,EAAE,GAAI8vB,EAAE,IACbQ,EAAE/xB,EAAGA,EAAGksB,GACR6F,EAAE9xB,EAAGwB,EAAE,GAAI8vB,EAAE,IACbM,EAAE5xB,EAAGA,EAAGA,GACR6xB,EAAExtB,EAAG4kB,EAAGiI,GACRW,EAAEjvB,EAAG5C,EAAGD,GACR6xB,EAAEnK,EAAGznB,EAAGD,GACR6xB,EAAErF,EAAGtD,EAAGiI,GAERY,EAAEtwB,EAAE,GAAI6C,EAAGzB,GACXkvB,EAAEtwB,EAAE,GAAI+qB,EAAG9E,GACXqK,EAAEtwB,EAAE,GAAIimB,EAAG7kB,GACXkvB,EAAEtwB,EAAE,GAAI6C,EAAGkoB,GAGb,SAASyN,EAAMx4B,EAAG8vB,EAAGrI,GACnB,IAAIvpB,EACJ,IAAKA,EAAI,EAAGA,EAAI,EAAGA,IACjB2xB,EAAS7vB,EAAE9B,GAAI4xB,EAAE5xB,GAAIupB,GAIzB,SAASgR,GAAKz5B,EAAGgB,GACf,IAAI04B,EAAK3O,IAAM4O,EAAK5O,IAAM6O,EAAK7O,IAC/BgJ,EAAS6F,EAAI54B,EAAE,IACfswB,EAAEoI,EAAI14B,EAAE,GAAI44B,GACZtI,EAAEqI,EAAI34B,EAAE,GAAI44B,GACZ7I,EAAU/wB,EAAG25B,GACb35B,EAAE,KAAOkxB,EAASwI,IAAO,EAG3B,SAASG,GAAW74B,EAAG8vB,EAAG7vB,GACxB,IAAIwnB,EAAGvpB,EAKP,IAJAuxB,EAASzvB,EAAE,GAAIqqB,GACfoF,EAASzvB,EAAE,GAAIsqB,GACfmF,EAASzvB,EAAE,GAAIsqB,GACfmF,EAASzvB,EAAE,GAAIqqB,GACVnsB,EAAI,IAAKA,GAAK,IAAKA,EAEtBs6B,EAAMx4B,EAAG8vB,EADTrI,EAAKxnB,EAAG/B,EAAE,EAAG,KAAS,EAAFA,GAAQ,GAE5B4G,EAAIgrB,EAAG9vB,GACP8E,EAAI9E,EAAGA,GACPw4B,EAAMx4B,EAAG8vB,EAAGrI,GAIhB,SAASqR,GAAW94B,EAAGC,GACrB,IAAI6vB,EAAI,CAAC/F,IAAMA,IAAMA,IAAMA,KAC3B0F,EAASK,EAAE,GAAIpF,GACf+E,EAASK,EAAE,GAAInF,GACf8E,EAASK,EAAE,GAAIxF,GACfgG,EAAER,EAAE,GAAIpF,EAAGC,GACXkO,GAAW74B,EAAG8vB,EAAG7vB,GAGnB,SAAS84B,GAAoBC,EAAIC,EAAIC,GACnC,IAEIh7B,EAFAM,EAAI,IAAIuoB,WAAW,IACnB/mB,EAAI,CAAC+pB,IAAMA,IAAMA,IAAMA,KAY3B,IATKmP,GAAQhP,EAAY+O,EAAI,IAC7BV,EAAY/5B,EAAGy6B,EAAI,IACnBz6B,EAAE,IAAM,IACRA,EAAE,KAAO,IACTA,EAAE,KAAO,GAETs6B,GAAW94B,EAAGxB,GACdi6B,GAAKO,EAAIh5B,GAEJ9B,EAAI,EAAGA,EAAI,GAAIA,IAAK+6B,EAAG/6B,EAAE,IAAM86B,EAAG96B,GACvC,OAAO,EAGT,IAAIi7B,GAAI,IAAIlP,aAAa,CAAC,IAAM,IAAM,IAAM,GAAM,GAAM,GAAM,GAAM,GAAM,IAAM,IAAM,IAAM,IAAM,IAAM,IAAM,IAAM,GAAM,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,KAEvK,SAASmP,GAAKp6B,EAAG8rB,GACf,IAAIuO,EAAOn7B,EAAG8xB,EAAGxE,EACjB,IAAKttB,EAAI,GAAIA,GAAK,KAAMA,EAAG,CAEzB,IADAm7B,EAAQ,EACHrJ,EAAI9xB,EAAI,GAAIstB,EAAIttB,EAAI,GAAI8xB,EAAIxE,IAAKwE,EACpClF,EAAEkF,IAAMqJ,EAAQ,GAAKvO,EAAE5sB,GAAKi7B,GAAEnJ,GAAK9xB,EAAI,KACvCm7B,EAAQntB,KAAK+T,OAAO6K,EAAEkF,GAAK,KAAO,KAClClF,EAAEkF,IAAc,IAARqJ,EAEVvO,EAAEkF,IAAMqJ,EACRvO,EAAE5sB,GAAK,EAGT,IADAm7B,EAAQ,EACHrJ,EAAI,EAAGA,EAAI,GAAIA,IAClBlF,EAAEkF,IAAMqJ,GAASvO,EAAE,KAAO,GAAKqO,GAAEnJ,GACjCqJ,EAAQvO,EAAEkF,IAAM,EAChBlF,EAAEkF,IAAM,IAEV,IAAKA,EAAI,EAAGA,EAAI,GAAIA,IAAKlF,EAAEkF,IAAMqJ,EAAQF,GAAEnJ,GAC3C,IAAK9xB,EAAI,EAAGA,EAAI,GAAIA,IAClB4sB,EAAE5sB,EAAE,IAAM4sB,EAAE5sB,IAAM,EAClBc,EAAEd,GAAY,IAAP4sB,EAAE5sB,GAIb,SAASo7B,GAAOt6B,GACd,IAA8Bd,EAA1B4sB,EAAI,IAAIb,aAAa,IACzB,IAAK/rB,EAAI,EAAGA,EAAI,GAAIA,IAAK4sB,EAAE5sB,GAAKc,EAAEd,GAClC,IAAKA,EAAI,EAAGA,EAAI,GAAIA,IAAKc,EAAEd,GAAK,EAChCk7B,GAAKp6B,EAAG8rB,GAIV,SAASyO,GAAYC,EAAIl7B,EAAGqB,EAAGs5B,GAC7B,IACI/6B,EAAG8xB,EADHxxB,EAAI,IAAIuoB,WAAW,IAAKgE,EAAI,IAAIhE,WAAW,IAAK/nB,EAAI,IAAI+nB,WAAW,IAC7D+D,EAAI,IAAIb,aAAa,IAC3BjqB,EAAI,CAAC+pB,IAAMA,IAAMA,IAAMA,KAE3BwO,EAAY/5B,EAAGy6B,EAAI,IACnBz6B,EAAE,IAAM,IACRA,EAAE,KAAO,IACTA,EAAE,KAAO,GAET,IAAIi7B,EAAQ95B,EAAI,GAChB,IAAKzB,EAAI,EAAGA,EAAIyB,EAAGzB,IAAKs7B,EAAG,GAAKt7B,GAAKI,EAAEJ,GACvC,IAAKA,EAAI,EAAGA,EAAI,GAAIA,IAAKs7B,EAAG,GAAKt7B,GAAKM,EAAE,GAAKN,GAO7C,IALAq6B,EAAYv5B,EAAGw6B,EAAGrG,SAAS,IAAKxzB,EAAE,IAClC25B,GAAOt6B,GACP85B,GAAW94B,EAAGhB,GACdy5B,GAAKe,EAAIx5B,GAEJ9B,EAAI,GAAIA,EAAI,GAAIA,IAAKs7B,EAAGt7B,GAAK+6B,EAAG/6B,GAIrC,IAHAq6B,EAAYxN,EAAGyO,EAAI75B,EAAI,IACvB25B,GAAOvO,GAEF7sB,EAAI,EAAGA,EAAI,GAAIA,IAAK4sB,EAAE5sB,GAAK,EAChC,IAAKA,EAAI,EAAGA,EAAI,GAAIA,IAAK4sB,EAAE5sB,GAAKc,EAAEd,GAClC,IAAKA,EAAI,EAAGA,EAAI,GAAIA,IAClB,IAAK8xB,EAAI,EAAGA,EAAI,GAAIA,IAClBlF,EAAE5sB,EAAE8xB,IAAMjF,EAAE7sB,GAAKM,EAAEwxB,GAKvB,OADAoJ,GAAKI,EAAGrG,SAAS,IAAKrI,GACf2O,EAyCT,SAASC,GAAiBp7B,EAAGk7B,EAAI75B,EAAGq5B,GAClC,IAAI96B,EACAkB,EAAI,IAAI2nB,WAAW,IAAKgE,EAAI,IAAIhE,WAAW,IAC3C/mB,EAAI,CAAC+pB,IAAMA,IAAMA,IAAMA,KACvB+F,EAAI,CAAC/F,IAAMA,IAAMA,IAAMA,KAE3B,GAAIpqB,EAAI,GAAI,OAAQ,EAEpB,GA9CF,SAAmBX,EAAGgB,GACpB,IAAIZ,EAAI2qB,IAAM4P,EAAM5P,IAAM6P,EAAM7P,IAC5B8P,EAAM9P,IAAM+P,EAAO/P,IAAMgQ,EAAOhQ,IAChCiQ,EAAOjQ,IA2BX,OAzBA0F,EAASzwB,EAAE,GAAIsrB,GACf6F,EAAYnxB,EAAE,GAAIgB,GAClB8yB,EAAE8G,EAAK56B,EAAE,IACTsxB,EAAEuJ,EAAKD,EAAKpP,GACZ6F,EAAEuJ,EAAKA,EAAK56B,EAAE,IACdoxB,EAAEyJ,EAAK76B,EAAE,GAAI66B,GAEb/G,EAAEgH,EAAMD,GACR/G,EAAEiH,EAAMD,GACRxJ,EAAE0J,EAAMD,EAAMD,GACdxJ,EAAElxB,EAAG46B,EAAMJ,GACXtJ,EAAElxB,EAAGA,EAAGy6B,GAER7G,EAAQ5zB,EAAGA,GACXkxB,EAAElxB,EAAGA,EAAGw6B,GACRtJ,EAAElxB,EAAGA,EAAGy6B,GACRvJ,EAAElxB,EAAGA,EAAGy6B,GACRvJ,EAAEtxB,EAAE,GAAII,EAAGy6B,GAEX/G,EAAE6G,EAAK36B,EAAE,IACTsxB,EAAEqJ,EAAKA,EAAKE,GACR5J,EAAS0J,EAAKC,IAAMtJ,EAAEtxB,EAAE,GAAIA,EAAE,GAAI4rB,GAEtCkI,EAAE6G,EAAK36B,EAAE,IACTsxB,EAAEqJ,EAAKA,EAAKE,GACR5J,EAAS0J,EAAKC,IAAc,GAE5B1J,EAASlxB,EAAE,MAASgB,EAAE,KAAK,GAAIqwB,EAAErxB,EAAE,GAAIqrB,EAAKrrB,EAAE,IAElDsxB,EAAEtxB,EAAE,GAAIA,EAAE,GAAIA,EAAE,IACT,GAWHi7B,CAAUnK,EAAGkJ,GAAK,OAAQ,EAE9B,IAAK96B,EAAI,EAAGA,EAAIyB,EAAGzB,IAAKI,EAAEJ,GAAKs7B,EAAGt7B,GAClC,IAAKA,EAAI,EAAGA,EAAI,GAAIA,IAAKI,EAAEJ,EAAE,IAAM86B,EAAG96B,GAUtC,GATAq6B,EAAYxN,EAAGzsB,EAAGqB,GAClB25B,GAAOvO,GACP8N,GAAW74B,EAAG8vB,EAAG/E,GAEjB+N,GAAWhJ,EAAG0J,EAAGrG,SAAS,KAC1BruB,EAAI9E,EAAG8vB,GACP2I,GAAKr5B,EAAGY,GAERL,GAAK,GACD0rB,EAAiBmO,EAAI,EAAGp6B,EAAG,GAAI,CACjC,IAAKlB,EAAI,EAAGA,EAAIyB,EAAGzB,IAAKI,EAAEJ,GAAK,EAC/B,OAAQ,EAGV,IAAKA,EAAI,EAAGA,EAAIyB,EAAGzB,IAAKI,EAAEJ,GAAKs7B,EAAGt7B,EAAI,IACtC,OAAOyB,EAkFT,SAASu6B,GAAa1O,EAAG7rB,GACvB,GAhF8B,KAgF1B6rB,EAAEnrB,OAAsC,MAAM,IAAI4N,MAAM,gBAC5D,GAhFgC,KAgF5BtO,EAAEU,OAAwC,MAAM,IAAI4N,MAAM,kBAQhE,SAASksB,KACP,IAAK,IAAIj8B,EAAI,EAAGA,EAAIuC,UAAUJ,OAAQnC,IACpC,KAAMuC,UAAUvC,aAAc6oB,YAC5B,MAAM,IAAIqT,UAAU,mCAI1B,SAASC,GAAQ5Q,GACf,IAAK,IAAIvrB,EAAI,EAAGA,EAAIurB,EAAIppB,OAAQnC,IAAKurB,EAAIvrB,GAAK,EA/EhD6T,EAAKuoB,SAAW,CACd5M,qBAAsBA,EACtBU,kBAAmBA,EACnBF,cAAeA,EACfL,0BAA2BA,EAC3BI,sBAAuBA,EACvBiB,mBAAoBA,EACpBG,0BAA2BA,EAC3BjE,iBAAkBA,EAClBC,iBAAkBA,EAClBkE,iBAAkBA,EAClBC,sBAAuBA,EACvByD,kBAAmBA,EACnBI,uBAAwBA,EACxBE,oBAAqBA,EACrBqC,mBAAoBA,EACpB2E,WAxsBF,SAAoBh8B,EAAGD,EAAGE,EAAGmB,EAAGurB,EAAGJ,GACjC,IAAIU,EAAI,IAAIzE,WAAW,IAEvB,OADAwM,EAAoB/H,EAAGN,EAAGJ,GACnB8K,EAAmBr3B,EAAGD,EAAGE,EAAGmB,EAAG6rB,IAssBtCgP,gBAnsBF,SAAyBl8B,EAAGC,EAAGC,EAAGmB,EAAGurB,EAAGJ,GACtC,IAAIU,EAAI,IAAIzE,WAAW,IAEvB,OADAwM,EAAoB/H,EAAGN,EAAGJ,GACnB+K,EAAwBv3B,EAAGC,EAAGC,EAAGmB,EAAG6rB,IAisB3C8H,mBAAoBA,EACpBiF,YAAaA,EACbgB,YAAaA,GACbR,oBAAqBA,GACrBW,iBAAkBA,GAElBe,0BA1C8B,GA2C9BC,4BA1CgC,GA2ChCC,2BA1C+B,GA2C/BC,8BA1CkC,GA2ClCC,wBA1C4B,GA2C5BC,8BA1CkC,GA2ClCC,0BA1C8B,GA2C9BC,0BA1C8B,GA2C9BC,yBA1C6B,GA2C7BC,sBAlDgC,GAmDhCC,qBAlD+B,GAmD/BC,wBAlDkC,GAmDlCC,kBA1CsB,GA2CtBC,2BA1C+B,GA2C/BC,2BA1C+B,GA2C/BC,sBA1C0B,GA2C1BC,kBA1CsB,GA4CtB1R,GAAIA,EACJS,EAAGA,EACH2O,EAAGA,GACHpJ,UAAWA,EACXI,YAAaA,EACbG,EAAGA,EACHF,EAAGA,EACH0C,EAAGA,EACHzC,EAAGA,EACH2C,QAASA,EACTluB,IAAKA,EACL2qB,SAAUA,EACV2J,KAAMA,GACNP,WAAYA,GACZC,WAAYA,IA0Bd/mB,EAAK2pB,YAAc,SAAS/7B,GAC1B,IAAI8nB,EAAI,IAAIV,WAAWpnB,GAEvB,OADAuqB,EAAYzC,EAAG9nB,GACR8nB,GAGT1V,EAAKO,UAAY,SAASpE,EAAKkE,EAAO3S,GACpC06B,GAAgBjsB,EAAKkE,EAAO3S,GAC5By6B,GAAaz6B,EAAK2S,GAGlB,IAFA,IAAI9T,EAAI,IAAIyoB,WA3GmB,GA2GqB7Y,EAAI7N,QACpD9B,EAAI,IAAIwoB,WAAWzoB,EAAE+B,QAChBnC,EAAI,EAAGA,EAAIgQ,EAAI7N,OAAQnC,IAAKI,EAAEJ,EA7GR,IA6GwCgQ,EAAIhQ,GAE3E,OADAqxB,EAAiBhxB,EAAGD,EAAGA,EAAE+B,OAAQ+R,EAAO3S,GACjClB,EAAE40B,SA9GyB,KAiHpCphB,EAAKO,UAAUI,KAAO,SAASipB,EAAKvpB,EAAO3S,GACzC06B,GAAgBwB,EAAKvpB,EAAO3S,GAC5By6B,GAAaz6B,EAAK2S,GAGlB,IAFA,IAAI7T,EAAI,IAAIwoB,WApHsB,GAoHqB4U,EAAIt7B,QACvD/B,EAAI,IAAIyoB,WAAWxoB,EAAE8B,QAChBnC,EAAI,EAAGA,EAAIy9B,EAAIt7B,OAAQnC,IAAKK,EAAEL,EAtHL,IAsHwCy9B,EAAIz9B,GAC9E,OAAIK,EAAE8B,OAAS,IAC2C,IAAtDmvB,EAAsBlxB,EAAGC,EAAGA,EAAE8B,OAAQ+R,EAAO3S,GADvB,KAEnBnB,EAAE60B,SA1HsB,KA6HjCphB,EAAKO,UAAUspB,UA/HiB,GAgIhC7pB,EAAKO,UAAUE,YA/HmB,GAgIlCT,EAAKO,UAAUC,eA9HqB,GAgIpCR,EAAK8pB,WAAa,SAASl8B,EAAGK,GAE5B,GADAm6B,GAAgBx6B,EAAGK,GA/He,KAgI9BL,EAAEU,OAA0C,MAAM,IAAI4N,MAAM,cAChE,GAlI4B,KAkIxBjO,EAAEK,OAAoC,MAAM,IAAI4N,MAAM,cAC1D,IAAI6hB,EAAI,IAAI/I,WAnIgB,IAqI5B,OADAkM,EAAkBnD,EAAGnwB,EAAGK,GACjB8vB,GAGT/d,EAAK8pB,WAAW1f,KAAO,SAASxc,GAE9B,GADAw6B,GAAgBx6B,GAxIkB,KAyI9BA,EAAEU,OAA0C,MAAM,IAAI4N,MAAM,cAChE,IAAI6hB,EAAI,IAAI/I,WA3IgB,IA6I5B,OADAsM,EAAuBvD,EAAGnwB,GACnBmwB,GAGT/d,EAAK8pB,WAAWC,aA/IoB,GAgJpC/pB,EAAK8pB,WAAWE,mBAjJc,GAmJ9BhqB,EAAK4pB,IAAM,SAASztB,EAAKkE,EAAO4pB,EAAWC,GACzC,IAAIzQ,EAAIzZ,EAAK4pB,IAAIO,OAAOF,EAAWC,GACnC,OAAOlqB,EAAKO,UAAUpE,EAAKkE,EAAOoZ,IAGpCzZ,EAAK4pB,IAAIO,OAAS,SAASF,EAAWC,GACpC9B,GAAgB6B,EAAWC,GAzE7B,SAAyBjD,EAAIC,GAC3B,GA/E8B,KA+E1BD,EAAG34B,OAAsC,MAAM,IAAI4N,MAAM,uBAC7D,GA/E8B,KA+E1BgrB,EAAG54B,OAAsC,MAAM,IAAI4N,MAAM,uBAwE7DkuB,CAAgBH,EAAWC,GAC3B,IAAIzQ,EAAI,IAAIzE,WAvJiB,IAyJ7B,OADAwM,EAAoB/H,EAAGwQ,EAAWC,GAC3BzQ,GAGTzZ,EAAK4pB,IAAIS,MAAQrqB,EAAKO,UAEtBP,EAAK4pB,IAAIjpB,KAAO,SAASxE,EAAKkE,EAAO4pB,EAAWC,GAC9C,IAAIzQ,EAAIzZ,EAAK4pB,IAAIO,OAAOF,EAAWC,GACnC,OAAOlqB,EAAKO,UAAUI,KAAKxE,EAAKkE,EAAOoZ,IAGzCzZ,EAAK4pB,IAAIjpB,KAAK0pB,MAAQrqB,EAAKO,UAAUI,KAErCX,EAAK4pB,IAAIU,QAAU,WACjB,IAAIrD,EAAK,IAAIjS,WAxKiB,IAyK1BkS,EAAK,IAAIlS,WAxKiB,IA0K9B,OADAuM,EAAmB0F,EAAIC,GAChB,CAAC+C,UAAWhD,EAAIiD,UAAWhD,IAGpClnB,EAAK4pB,IAAIU,QAAQC,cAAgB,SAASL,GAExC,GADA9B,GAAgB8B,GA9Kc,KA+K1BA,EAAU57B,OACZ,MAAM,IAAI4N,MAAM,uBAClB,IAAI+qB,EAAK,IAAIjS,WAlLiB,IAoL9B,OADAsM,EAAuB2F,EAAIiD,GACpB,CAACD,UAAWhD,EAAIiD,UAAW,IAAIlV,WAAWkV,KAGnDlqB,EAAK4pB,IAAIY,gBAvLuB,GAwLhCxqB,EAAK4pB,IAAIa,gBAvLuB,GAwLhCzqB,EAAK4pB,IAAIc,gBAvLsB,GAwL/B1qB,EAAK4pB,IAAInpB,YA/LyB,GAgMlCT,EAAK4pB,IAAIppB,eAAiBR,EAAKO,UAAUC,eAEzCR,EAAK2qB,KAAO,SAASxuB,EAAK+tB,GAExB,GADA9B,GAAgBjsB,EAAK+tB,GAtLU,KAuL3BA,EAAU57B,OACZ,MAAM,IAAI4N,MAAM,uBAClB,IAAI0uB,EAAY,IAAI5V,WA3LE,GA2L2B7Y,EAAI7N,QAErD,OADAk5B,GAAYoD,EAAWzuB,EAAKA,EAAI7N,OAAQ47B,GACjCU,GAGT5qB,EAAK2qB,KAAKhqB,KAAO,SAASiqB,EAAWX,GAEnC,GADA7B,GAAgBwC,EAAWX,GAhMI,KAiM3BA,EAAU37B,OACZ,MAAM,IAAI4N,MAAM,uBAClB,IAAI2uB,EAAM,IAAI7V,WAAW4V,EAAUt8B,QAC/Bw8B,EAAOnD,GAAiBkD,EAAKD,EAAWA,EAAUt8B,OAAQ27B,GAC9D,GAAIa,EAAO,EAAG,OAAO,KAErB,IADA,IAAIv+B,EAAI,IAAIyoB,WAAW8V,GACd3+B,EAAI,EAAGA,EAAII,EAAE+B,OAAQnC,IAAKI,EAAEJ,GAAK0+B,EAAI1+B,GAC9C,OAAOI,GAGTyT,EAAK2qB,KAAKI,SAAW,SAAS5uB,EAAK+tB,GAGjC,IAFA,IAAIU,EAAY5qB,EAAK2qB,KAAKxuB,EAAK+tB,GAC3Bc,EAAM,IAAIhW,WA9MQ,IA+Mb7oB,EAAI,EAAGA,EAAI6+B,EAAI18B,OAAQnC,IAAK6+B,EAAI7+B,GAAKy+B,EAAUz+B,GACxD,OAAO6+B,GAGThrB,EAAK2qB,KAAKI,SAASE,OAAS,SAAS9uB,EAAK6uB,EAAKf,GAE7C,GADA7B,GAAgBjsB,EAAK6uB,EAAKf,GApNJ,KAqNlBe,EAAI18B,OACN,MAAM,IAAI4N,MAAM,sBAClB,GAtN+B,KAsN3B+tB,EAAU37B,OACZ,MAAM,IAAI4N,MAAM,uBAClB,IAEI/P,EAFAs7B,EAAK,IAAIzS,WAzNS,GAyNsB7Y,EAAI7N,QAC5C/B,EAAI,IAAIyoB,WA1NU,GA0NqB7Y,EAAI7N,QAE/C,IAAKnC,EAAI,EAAGA,EA5NU,GA4NaA,IAAKs7B,EAAGt7B,GAAK6+B,EAAI7+B,GACpD,IAAKA,EAAI,EAAGA,EAAIgQ,EAAI7N,OAAQnC,IAAKs7B,EAAGt7B,EA7Nd,IA6NqCgQ,EAAIhQ,GAC/D,OAAQw7B,GAAiBp7B,EAAGk7B,EAAIA,EAAGn5B,OAAQ27B,IAAc,GAG3DjqB,EAAK2qB,KAAKL,QAAU,WAClB,IAAIrD,EAAK,IAAIjS,WAjOkB,IAkO3BkS,EAAK,IAAIlS,WAjOkB,IAmO/B,OADAgS,GAAoBC,EAAIC,GACjB,CAAC+C,UAAWhD,EAAIiD,UAAWhD,IAGpClnB,EAAK2qB,KAAKL,QAAQC,cAAgB,SAASL,GAEzC,GADA9B,GAAgB8B,GAvOe,KAwO3BA,EAAU57B,OACZ,MAAM,IAAI4N,MAAM,uBAElB,IADA,IAAI+qB,EAAK,IAAIjS,WA3OkB,IA4OtB7oB,EAAI,EAAGA,EAAI86B,EAAG34B,OAAQnC,IAAK86B,EAAG96B,GAAK+9B,EAAU,GAAG/9B,GACzD,MAAO,CAAC89B,UAAWhD,EAAIiD,UAAW,IAAIlV,WAAWkV,KAGnDlqB,EAAK2qB,KAAKL,QAAQY,SAAW,SAASC,GAEpC,GADA/C,GAAgB+C,GA/OU,KAgPtBA,EAAK78B,OACP,MAAM,IAAI4N,MAAM,iBAGlB,IAFA,IAAI+qB,EAAK,IAAIjS,WApPkB,IAqP3BkS,EAAK,IAAIlS,WApPkB,IAqPtB7oB,EAAI,EAAGA,EAAI,GAAIA,IAAK+6B,EAAG/6B,GAAKg/B,EAAKh/B,GAE1C,OADA66B,GAAoBC,EAAIC,GAAI,GACrB,CAAC+C,UAAWhD,EAAIiD,UAAWhD,IAGpClnB,EAAK2qB,KAAKH,gBA3PuB,GA4PjCxqB,EAAK2qB,KAAKF,gBA3PuB,GA4PjCzqB,EAAK2qB,KAAKS,WA3PkB,GA4P5BprB,EAAK2qB,KAAKU,gBA/Pc,GAiQxBrrB,EAAKlB,KAAO,SAAS3C,GACnBisB,GAAgBjsB,GAChB,IAAI6c,EAAI,IAAIhE,WA/PU,IAiQtB,OADAwR,EAAYxN,EAAG7c,EAAKA,EAAI7N,QACjB0qB,GAGThZ,EAAKlB,KAAKwsB,WApQc,GAsQxBtrB,EAAKirB,OAAS,SAASlS,EAAGI,GAGxB,OAFAiP,GAAgBrP,EAAGI,GAEF,IAAbJ,EAAEzqB,QAA6B,IAAb6qB,EAAE7qB,SACpByqB,EAAEzqB,SAAW6qB,EAAE7qB,QACkB,IAA7B2qB,EAAGF,EAAG,EAAGI,EAAG,EAAGJ,EAAEzqB,UAG3B0R,EAAKurB,QAAU,SAASn4B,GACtB+kB,EAAc/kB,GAGhB,WAGE,IAAIo4B,EAAyB,oBAATC,KAAwBA,KAAKD,QAAUC,KAAKC,SAAY,KAC5E,GAAIF,GAAUA,EAAOG,gBAAiB,CAGpC3rB,EAAKurB,SAAQ,SAASxS,EAAGnrB,GACvB,IAAIzB,EAAG0xB,EAAI,IAAI7I,WAAWpnB,GAC1B,IAAKzB,EAAI,EAAGA,EAAIyB,EAAGzB,GAHT,MAIRq/B,EAAOG,gBAAgB9N,EAAEuD,SAASj1B,EAAGA,EAAIgO,KAAKiJ,IAAIxV,EAAIzB,EAJ9C,SAMV,IAAKA,EAAI,EAAGA,EAAIyB,EAAGzB,IAAK4sB,EAAE5sB,GAAK0xB,EAAE1xB,GACjCm8B,GAAQzK,WAIV2N,EAAS,EAAQ,MACHA,EAAO7B,aACnB3pB,EAAKurB,SAAQ,SAASxS,EAAGnrB,GACvB,IAAIzB,EAAG0xB,EAAI2N,EAAO7B,YAAY/7B,GAC9B,IAAKzB,EAAI,EAAGA,EAAIyB,EAAGzB,IAAK4sB,EAAE5sB,GAAK0xB,EAAE1xB,GACjCm8B,GAAQzK,MAtBhB,GA1zEA,CAs1EoC3xB,EAAOD,QAAUC,EAAOD,QAAWw/B,KAAKzrB,KAAOyrB,KAAKzrB,MAAQ,K,gBCt1EhG9T,EAAOD,QAAU,EAAQ,IAA4B2/B,S,6BCArD,yEAIe,MAAMC,UAA6B,IAChD,YAAYhZ,EAAiB7c,GAC3B,IAAOhE,aAAe65B,EAAqB75B,aAC3C,IAAOR,IAAMq6B,EAAqBr6B,IAElC,YAAgBwE,GAChBA,EAAQgK,KAAO,EACftM,MAAMmf,EAAS7c,M", "file": "pusher.js", "sourcesContent": [" \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 15);\n", "import base64encode from '../base64';\nimport Util from '../util';\n\n/** Merges multiple objects into the target argument.\n *\n * For properties that are plain Objects, performs a deep-merge. For the\n * rest it just copies the value of the property.\n *\n * To extend prototypes use it as following:\n *   Pusher.Util.extend(Target.prototype, Base.prototype)\n *\n * You can also use it to merge objects without altering them:\n *   Pusher.Util.extend({}, object1, object2)\n *\n * @param  {Object} target\n * @return {Object} the target argument\n */\nexport function extend<T>(target: any, ...sources: any[]): T {\n  for (var i = 0; i < sources.length; i++) {\n    var extensions = sources[i];\n    for (var property in extensions) {\n      if (\n        extensions[property] &&\n        extensions[property].constructor &&\n        extensions[property].constructor === Object\n      ) {\n        target[property] = extend(target[property] || {}, extensions[property]);\n      } else {\n        target[property] = extensions[property];\n      }\n    }\n  }\n  return target;\n}\n\nexport function stringify(): string {\n  var m = ['Pusher'];\n  for (var i = 0; i < arguments.length; i++) {\n    if (typeof arguments[i] === 'string') {\n      m.push(arguments[i]);\n    } else {\n      m.push(safeJSONStringify(arguments[i]));\n    }\n  }\n  return m.join(' : ');\n}\n\nexport function arrayIndexOf(array: any[], item: any): number {\n  // MSIE doesn't have array.indexOf\n  var nativeIndexOf = Array.prototype.indexOf;\n  if (array === null) {\n    return -1;\n  }\n  if (nativeIndexOf && array.indexOf === nativeIndexOf) {\n    return array.indexOf(item);\n  }\n  for (var i = 0, l = array.length; i < l; i++) {\n    if (array[i] === item) {\n      return i;\n    }\n  }\n  return -1;\n}\n\n/** Applies a function f to all properties of an object.\n *\n * Function f gets 3 arguments passed:\n * - element from the object\n * - key of the element\n * - reference to the object\n *\n * @param {Object} object\n * @param {Function} f\n */\nexport function objectApply(object: any, f: Function) {\n  for (var key in object) {\n    if (Object.prototype.hasOwnProperty.call(object, key)) {\n      f(object[key], key, object);\n    }\n  }\n}\n\n/** Return a list of objects own proerty keys\n *\n * @param {Object} object\n * @returns {Array}\n */\nexport function keys(object: any): string[] {\n  var keys = [];\n  objectApply(object, function(_, key) {\n    keys.push(key);\n  });\n  return keys;\n}\n\n/** Return a list of object's own property values\n *\n * @param {Object} object\n * @returns {Array}\n */\nexport function values(object: any): any[] {\n  var values = [];\n  objectApply(object, function(value) {\n    values.push(value);\n  });\n  return values;\n}\n\n/** Applies a function f to all elements of an array.\n *\n * Function f gets 3 arguments passed:\n * - element from the array\n * - index of the element\n * - reference to the array\n *\n * @param {Array} array\n * @param {Function} f\n */\nexport function apply(array: any[], f: Function, context?: any) {\n  for (var i = 0; i < array.length; i++) {\n    f.call(context || global, array[i], i, array);\n  }\n}\n\n/** Maps all elements of the array and returns the result.\n *\n * Function f gets 4 arguments passed:\n * - element from the array\n * - index of the element\n * - reference to the source array\n * - reference to the destination array\n *\n * @param {Array} array\n * @param {Function} f\n */\nexport function map(array: any[], f: Function): any[] {\n  var result = [];\n  for (var i = 0; i < array.length; i++) {\n    result.push(f(array[i], i, array, result));\n  }\n  return result;\n}\n\n/** Maps all elements of the object and returns the result.\n *\n * Function f gets 4 arguments passed:\n * - element from the object\n * - key of the element\n * - reference to the source object\n * - reference to the destination object\n *\n * @param {Object} object\n * @param {Function} f\n */\nexport function mapObject(object: any, f: Function): any {\n  var result = {};\n  objectApply(object, function(value, key) {\n    result[key] = f(value);\n  });\n  return result;\n}\n\n/** Filters elements of the array using a test function.\n *\n * Function test gets 4 arguments passed:\n * - element from the array\n * - index of the element\n * - reference to the source array\n * - reference to the destination array\n *\n * @param {Array} array\n * @param {Function} f\n */\nexport function filter(array: any[], test: Function): any[] {\n  test =\n    test ||\n    function(value) {\n      return !!value;\n    };\n\n  var result = [];\n  for (var i = 0; i < array.length; i++) {\n    if (test(array[i], i, array, result)) {\n      result.push(array[i]);\n    }\n  }\n  return result;\n}\n\n/** Filters properties of the object using a test function.\n *\n * Function test gets 4 arguments passed:\n * - element from the object\n * - key of the element\n * - reference to the source object\n * - reference to the destination object\n *\n * @param {Object} object\n * @param {Function} f\n */\nexport function filterObject(object: Object, test: Function) {\n  var result = {};\n  objectApply(object, function(value, key) {\n    if ((test && test(value, key, object, result)) || Boolean(value)) {\n      result[key] = value;\n    }\n  });\n  return result;\n}\n\n/** Flattens an object into a two-dimensional array.\n *\n * @param  {Object} object\n * @return {Array} resulting array of [key, value] pairs\n */\nexport function flatten(object: Object): any[] {\n  var result = [];\n  objectApply(object, function(value, key) {\n    result.push([key, value]);\n  });\n  return result;\n}\n\n/** Checks whether any element of the array passes the test.\n *\n * Function test gets 3 arguments passed:\n * - element from the array\n * - index of the element\n * - reference to the source array\n *\n * @param {Array} array\n * @param {Function} f\n */\nexport function any(array: any[], test: Function): boolean {\n  for (var i = 0; i < array.length; i++) {\n    if (test(array[i], i, array)) {\n      return true;\n    }\n  }\n  return false;\n}\n\n/** Checks whether all elements of the array pass the test.\n *\n * Function test gets 3 arguments passed:\n * - element from the array\n * - index of the element\n * - reference to the source array\n *\n * @param {Array} array\n * @param {Function} f\n */\nexport function all(array: any[], test: Function): boolean {\n  for (var i = 0; i < array.length; i++) {\n    if (!test(array[i], i, array)) {\n      return false;\n    }\n  }\n  return true;\n}\n\nexport function encodeParamsObject(data): string {\n  return mapObject(data, function(value) {\n    if (typeof value === 'object') {\n      value = safeJSONStringify(value);\n    }\n    return encodeURIComponent(base64encode(value.toString()));\n  });\n}\n\nexport function buildQueryString(data: any): string {\n  var params = filterObject(data, function(value) {\n    return value !== undefined;\n  });\n\n  var query = map(\n    flatten(encodeParamsObject(params)),\n    Util.method('join', '=')\n  ).join('&');\n\n  return query;\n}\n\n/**\n * See https://github.com/douglascrockford/JSON-js/blob/master/cycle.js\n *\n * Remove circular references from an object. Required for JSON.stringify in\n * React Native, which tends to blow up a lot.\n *\n * @param  {any} object\n * @return {any}        Decycled object\n */\nexport function decycleObject(object: any): any {\n  var objects = [],\n    paths = [];\n\n  return (function derez(value, path) {\n    var i, name, nu;\n\n    switch (typeof value) {\n      case 'object':\n        if (!value) {\n          return null;\n        }\n        for (i = 0; i < objects.length; i += 1) {\n          if (objects[i] === value) {\n            return { $ref: paths[i] };\n          }\n        }\n\n        objects.push(value);\n        paths.push(path);\n\n        if (Object.prototype.toString.apply(value) === '[object Array]') {\n          nu = [];\n          for (i = 0; i < value.length; i += 1) {\n            nu[i] = derez(value[i], path + '[' + i + ']');\n          }\n        } else {\n          nu = {};\n          for (name in value) {\n            if (Object.prototype.hasOwnProperty.call(value, name)) {\n              nu[name] = derez(\n                value[name],\n                path + '[' + JSON.stringify(name) + ']'\n              );\n            }\n          }\n        }\n        return nu;\n      case 'number':\n      case 'string':\n      case 'boolean':\n        return value;\n    }\n  })(object, '$');\n}\n\n/**\n * Provides a cross-browser and cross-platform way to safely stringify objects\n * into JSON. This is particularly necessary for ReactNative, where circular JSON\n * structures throw an exception.\n *\n * @param  {any}    source The object to stringify\n * @return {string}        The serialized output.\n */\nexport function safeJSONStringify(source: any): string {\n  try {\n    return JSON.stringify(source);\n  } catch (e) {\n    return JSON.stringify(decycleObject(source));\n  }\n}\n", "import { stringify } from './utils/collections';\nimport Pusher from './pusher';\n\nclass Logger {\n  debug(...args: any[]) {\n    this.log(this.globalLog, args);\n  }\n\n  warn(...args: any[]) {\n    this.log(this.globalLogWarn, args);\n  }\n\n  error(...args: any[]) {\n    this.log(this.globalLogError, args);\n  }\n\n  private globalLog = (message: string) => {\n    if (global.console && global.console.log) {\n      global.console.log(message);\n    }\n  };\n\n  private globalLogWarn(message: string) {\n    if (global.console && global.console.warn) {\n      global.console.warn(message);\n    } else {\n      this.globalLog(message);\n    }\n  }\n\n  private globalLogError(message: string) {\n    if (global.console && global.console.error) {\n      global.console.error(message);\n    } else {\n      this.globalLogWarn(message);\n    }\n  }\n\n  private log(\n    defaultLoggingFunction: (message: string) => void,\n    ...args: any[]\n  ) {\n    var message = stringify.apply(this, arguments);\n    if (Pusher.log) {\n      Pusher.log(message);\n    } else if (Pusher.logToConsole) {\n      const log = defaultLoggingFunction.bind(this);\n      log(message);\n    }\n  }\n}\n\nexport default new Logger();\n", "import * as Collections from './utils/collections';\nimport TimedCallback from './utils/timers/timed_callback';\nimport { OneOffTimer, PeriodicTimer } from './utils/timers';\n\nvar Util = {\n  now(): number {\n    if (Date.now) {\n      return Date.now();\n    } else {\n      return new Date().valueOf();\n    }\n  },\n\n  defer(callback: TimedCallback): OneOffTimer {\n    return new OneOffTimer(0, callback);\n  },\n\n  /** Builds a function that will proxy a method call to its first argument.\n   *\n   * Allows partial application of arguments, so additional arguments are\n   * prepended to the argument list.\n   *\n   * @param  {String} name method name\n   * @return {Function} proxy function\n   */\n  method(name: string, ...args: any[]): Function {\n    var boundArguments = Array.prototype.slice.call(arguments, 1);\n    return function(object) {\n      return object[name].apply(object, boundArguments.concat(arguments));\n    };\n  }\n};\n\nexport default Util;\n", "import * as Collections from '../utils/collections';\nimport Callback from './callback';\nimport Metadata from '../channels/metadata';\nimport CallbackRegistry from './callback_registry';\n\n/** Manages callback bindings and event emitting.\n *\n * @param Function failThrough called when no listeners are bound to an event\n */\nexport default class Dispatcher {\n  callbacks: CallbackRegistry;\n  global_callbacks: Function[];\n  failThrough: Function;\n\n  constructor(failThrough?: Function) {\n    this.callbacks = new CallbackRegistry();\n    this.global_callbacks = [];\n    this.failThrough = failThrough;\n  }\n\n  bind(eventName: string, callback: Function, context?: any) {\n    this.callbacks.add(eventName, callback, context);\n    return this;\n  }\n\n  bind_global(callback: Function) {\n    this.global_callbacks.push(callback);\n    return this;\n  }\n\n  unbind(eventName?: string, callback?: Function, context?: any) {\n    this.callbacks.remove(eventName, callback, context);\n    return this;\n  }\n\n  unbind_global(callback?: Function) {\n    if (!callback) {\n      this.global_callbacks = [];\n      return this;\n    }\n\n    this.global_callbacks = Collections.filter(\n      this.global_callbacks || [],\n      c => c !== callback\n    );\n\n    return this;\n  }\n\n  unbind_all() {\n    this.unbind();\n    this.unbind_global();\n    return this;\n  }\n\n  emit(eventName: string, data?: any, metadata?: Metadata): Dispatcher {\n    for (var i = 0; i < this.global_callbacks.length; i++) {\n      this.global_callbacks[i](eventName, data);\n    }\n\n    var callbacks = this.callbacks.get(eventName);\n    var args = [];\n\n    if (metadata) {\n      // if there's a metadata argument, we need to call the callback with both\n      // data and metadata regardless of whether data is undefined\n      args.push(data, metadata);\n    } else if (data) {\n      // metadata is undefined, so we only need to call the callback with data\n      // if data exists\n      args.push(data);\n    }\n\n    if (callbacks && callbacks.length > 0) {\n      for (var i = 0; i < callbacks.length; i++) {\n        callbacks[i].fn.apply(callbacks[i].context || global, args);\n      }\n    } else if (this.failThrough) {\n      this.failThrough(eventName, data);\n    }\n\n    return this;\n  }\n}\n", "import Timer from './abstract_timer';\nimport TimedCallback from './timed_callback';\nimport { Delay } from './scheduling';\n\n// We need to bind clear functions this way to avoid exceptions on IE8\nfunction clearTimeout(timer) {\n  global.clearTimeout(timer);\n}\nfunction clearInterval(timer) {\n  global.clearInterval(timer);\n}\n\n/** Cross-browser compatible one-off timer abstraction.\n *\n * @param {Number} delay\n * @param {Function} callback\n */\nexport class OneOffTimer extends Timer {\n  constructor(delay: Delay, callback: TimedCallback) {\n    super(setTimeout, clearTimeout, delay, function(timer) {\n      callback();\n      return null;\n    });\n  }\n}\n\n/** Cross-browser compatible periodic timer abstraction.\n *\n * @param {Number} delay\n * @param {Function} callback\n */\nexport class PeriodicTimer extends Timer {\n  constructor(delay: Delay, callback: TimedCallback) {\n    super(setInterval, clearInterval, delay, function(timer) {\n      callback();\n      return timer;\n    });\n  }\n}\n", "import {\n  ChannelAuthorizationOptions,\n  UserAuthenticationOptions\n} from './auth/options';\nimport { AuthTransport } from './config';\n\nexport interface DefaultConfig {\n  VERSION: string;\n  PROTOCOL: number;\n  wsPort: number;\n  wssPort: number;\n  wsPath: string;\n  httpHost: string;\n  httpPort: number;\n  httpsPort: number;\n  httpPath: string;\n  stats_host: string;\n  authEndpoint: string;\n  authTransport: AuthTransport;\n  activityTimeout: number;\n  pongTimeout: number;\n  unavailableTimeout: number;\n  userAuthentication: UserAuthenticationOptions;\n  channelAuthorization: ChannelAuthorizationOptions;\n\n  cdn_http?: string;\n  cdn_https?: string;\n  dependency_suffix?: string;\n}\n\nvar Defaults: DefaultConfig = {\n  VERSION: VERSION,\n  PROTOCOL: 7,\n\n  wsPort: 80,\n  wssPort: 443,\n  wsPath: '',\n  // DEPRECATED: SockJS fallback parameters\n  httpHost: 'sockjs.pusher.com',\n  httpPort: 80,\n  httpsPort: 443,\n  httpPath: '/pusher',\n  // DEPRECATED: Stats\n  stats_host: 'stats.pusher.com',\n  // DEPRECATED: Other settings\n  authEndpoint: '/pusher/auth',\n  authTransport: 'ajax',\n  activityTimeout: 120000,\n  pongTimeout: 30000,\n  unavailableTimeout: 10000,\n  userAuthentication: {\n    endpoint: '/pusher/user-auth',\n    transport: 'ajax'\n  },\n  channelAuthorization: {\n    endpoint: '/pusher/auth',\n    transport: 'ajax'\n  },\n\n  // CDN configuration\n  cdn_http: CDN_HTTP,\n  cdn_https: CDN_HTTPS,\n  dependency_suffix: DEPENDENCY_SUFFIX\n};\n\nexport default Defaults;\n", "import Defaults from '../defaults';\nimport { default as URLScheme, URLSchemeParams } from './url_scheme';\n\nfunction getGenericURL(\n  baseScheme: string,\n  params: URLSchemeParams,\n  path: string\n): string {\n  var scheme = baseScheme + (params.useTLS ? 's' : '');\n  var host = params.useTLS ? params.hostTLS : params.hostNonTLS;\n  return scheme + '://' + host + path;\n}\n\nfunction getGenericPath(key: string, queryString?: string): string {\n  var path = '/app/' + key;\n  var query =\n    '?protocol=' +\n    Defaults.PROTOCOL +\n    '&client=js' +\n    '&version=' +\n    Defaults.VERSION +\n    (queryString ? '&' + queryString : '');\n  return path + query;\n}\n\nexport var ws: URLScheme = {\n  getInitial: function(key: string, params: URLSchemeParams): string {\n    var path = (params.httpPath || '') + getGenericPath(key, 'flash=false');\n    return getGenericURL('ws', params, path);\n  }\n};\n\nexport var http: URLScheme = {\n  getInitial: function(key: string, params: URLSchemeParams): string {\n    var path = (params.httpPath || '/pusher') + getGenericPath(key);\n    return getGenericURL('http', params, path);\n  }\n};\n\nexport var sockjs: URLScheme = {\n  getInitial: function(key: string, params: URLSchemeParams): string {\n    return getGenericURL('http', params, params.httpPath || '/pusher');\n  },\n  getPath: function(key: string, params: URLSchemeParams): string {\n    return getGenericPath(key);\n  }\n};\n", "import Util from '../util';\nimport * as Collections from '../utils/collections';\nimport { default as EventsDispatcher } from '../events/dispatcher';\nimport Logger from '../logger';\nimport TransportHooks from './transport_hooks';\nimport Socket from '../socket';\nimport Runtime from 'runtime';\nimport Timeline from '../timeline/timeline';\nimport TransportConnectionOptions from './transport_connection_options';\n\n/** Provides universal API for transport connections.\n *\n * Transport connection is a low-level object that wraps a connection method\n * and exposes a simple evented interface for the connection state and\n * messaging. It does not implement Pusher-specific WebSocket protocol.\n *\n * Additionally, it fetches resources needed for transport to work and exposes\n * an interface for querying transport features.\n *\n * States:\n * - new - initial state after constructing the object\n * - initializing - during initialization phase, usually fetching resources\n * - intialized - ready to establish a connection\n * - connection - when connection is being established\n * - open - when connection ready to be used\n * - closed - after connection was closed be either side\n *\n * Emits:\n * - error - after the connection raised an error\n *\n * Options:\n * - useTLS - whether connection should be over TLS\n * - hostTLS - host to connect to when connection is over TLS\n * - hostNonTLS - host to connect to when connection is over TLS\n *\n * @param {String} key application key\n * @param {Object} options\n */\nexport default class TransportConnection extends EventsDispatcher {\n  hooks: TransportHooks;\n  name: string;\n  priority: number;\n  key: string;\n  options: TransportConnectionOptions;\n  state: string;\n  timeline: Timeline;\n  activityTimeout: number;\n  id: number;\n  socket: Socket;\n  beforeOpen: Function;\n  initialize: Function;\n\n  constructor(\n    hooks: TransportHooks,\n    name: string,\n    priority: number,\n    key: string,\n    options: TransportConnectionOptions\n  ) {\n    super();\n    this.initialize = Runtime.transportConnectionInitializer;\n    this.hooks = hooks;\n    this.name = name;\n    this.priority = priority;\n    this.key = key;\n    this.options = options;\n\n    this.state = 'new';\n    this.timeline = options.timeline;\n    this.activityTimeout = options.activityTimeout;\n    this.id = this.timeline.generateUniqueID();\n  }\n\n  /** Checks whether the transport handles activity checks by itself.\n   *\n   * @return {Boolean}\n   */\n  handlesActivityChecks(): boolean {\n    return Boolean(this.hooks.handlesActivityChecks);\n  }\n\n  /** Checks whether the transport supports the ping/pong API.\n   *\n   * @return {Boolean}\n   */\n  supportsPing(): boolean {\n    return Boolean(this.hooks.supportsPing);\n  }\n\n  /** Tries to establish a connection.\n   *\n   * @returns {Boolean} false if transport is in invalid state\n   */\n  connect(): boolean {\n    if (this.socket || this.state !== 'initialized') {\n      return false;\n    }\n\n    var url = this.hooks.urls.getInitial(this.key, this.options);\n    try {\n      this.socket = this.hooks.getSocket(url, this.options);\n    } catch (e) {\n      Util.defer(() => {\n        this.onError(e);\n        this.changeState('closed');\n      });\n      return false;\n    }\n\n    this.bindListeners();\n\n    Logger.debug('Connecting', { transport: this.name, url });\n    this.changeState('connecting');\n    return true;\n  }\n\n  /** Closes the connection.\n   *\n   * @return {Boolean} true if there was a connection to close\n   */\n  close(): boolean {\n    if (this.socket) {\n      this.socket.close();\n      return true;\n    } else {\n      return false;\n    }\n  }\n\n  /** Sends data over the open connection.\n   *\n   * @param {String} data\n   * @return {Boolean} true only when in the \"open\" state\n   */\n  send(data: any): boolean {\n    if (this.state === 'open') {\n      // Workaround for MobileSafari bug (see https://gist.github.com/2052006)\n      Util.defer(() => {\n        if (this.socket) {\n          this.socket.send(data);\n        }\n      });\n      return true;\n    } else {\n      return false;\n    }\n  }\n\n  /** Sends a ping if the connection is open and transport supports it. */\n  ping() {\n    if (this.state === 'open' && this.supportsPing()) {\n      this.socket.ping();\n    }\n  }\n\n  private onOpen() {\n    if (this.hooks.beforeOpen) {\n      this.hooks.beforeOpen(\n        this.socket,\n        this.hooks.urls.getPath(this.key, this.options)\n      );\n    }\n    this.changeState('open');\n    this.socket.onopen = undefined;\n  }\n\n  private onError(error) {\n    this.emit('error', { type: 'WebSocketError', error: error });\n    this.timeline.error(this.buildTimelineMessage({ error: error.toString() }));\n  }\n\n  private onClose(closeEvent?: any) {\n    if (closeEvent) {\n      this.changeState('closed', {\n        code: closeEvent.code,\n        reason: closeEvent.reason,\n        wasClean: closeEvent.wasClean\n      });\n    } else {\n      this.changeState('closed');\n    }\n    this.unbindListeners();\n    this.socket = undefined;\n  }\n\n  private onMessage(message) {\n    this.emit('message', message);\n  }\n\n  private onActivity() {\n    this.emit('activity');\n  }\n\n  private bindListeners() {\n    this.socket.onopen = () => {\n      this.onOpen();\n    };\n    this.socket.onerror = error => {\n      this.onError(error);\n    };\n    this.socket.onclose = closeEvent => {\n      this.onClose(closeEvent);\n    };\n    this.socket.onmessage = message => {\n      this.onMessage(message);\n    };\n\n    if (this.supportsPing()) {\n      this.socket.onactivity = () => {\n        this.onActivity();\n      };\n    }\n  }\n\n  private unbindListeners() {\n    if (this.socket) {\n      this.socket.onopen = undefined;\n      this.socket.onerror = undefined;\n      this.socket.onclose = undefined;\n      this.socket.onmessage = undefined;\n      if (this.supportsPing()) {\n        this.socket.onactivity = undefined;\n      }\n    }\n  }\n\n  private changeState(state: string, params?: any) {\n    this.state = state;\n    this.timeline.info(\n      this.buildTimelineMessage({\n        state: state,\n        params: params\n      })\n    );\n    this.emit(state, params);\n  }\n\n  buildTimelineMessage(message): any {\n    return Collections.extend({ cid: this.id }, message);\n  }\n}\n", "import Factory from '../utils/factory';\nimport TransportHooks from './transport_hooks';\nimport TransportConnection from './transport_connection';\nimport TransportConnectionOptions from './transport_connection_options';\n\n/** Provides interface for transport connection instantiation.\n *\n * Takes transport-specific hooks as the only argument, which allow checking\n * for transport support and creating its connections.\n *\n * Supported hooks: * - file - the name of the file to be fetched during initialization\n * - urls - URL scheme to be used by transport\n * - handlesActivityCheck - true when the transport handles activity checks\n * - supportsPing - true when the transport has a ping/activity API\n * - isSupported - tells whether the transport is supported in the environment\n * - getSocket - creates a WebSocket-compatible transport socket\n *\n * See transports.js for specific implementations.\n *\n * @param {Object} hooks object containing all needed transport hooks\n */\nexport default class Transport {\n  hooks: TransportHooks;\n\n  constructor(hooks: TransportHooks) {\n    this.hooks = hooks;\n  }\n\n  /** Returns whether the transport is supported in the environment.\n   *\n   * @param {Object} envronment te environment details (encryption, settings)\n   * @returns {Boolean} true when the transport is supported\n   */\n  isSupported(environment: any): boolean {\n    return this.hooks.isSupported(environment);\n  }\n\n  /** Creates a transport connection.\n   *\n   * @param {String} name\n   * @param {Number} priority\n   * @param {String} key the application key\n   * @param {Object} options\n   * @returns {TransportConnection}\n   */\n  createConnection(\n    name: string,\n    priority: number,\n    key: string,\n    options: any\n  ): TransportConnection {\n    return new TransportConnection(this.hooks, name, priority, key, options);\n  }\n}\n", "import * as URLSchemes from 'core/transports/url_schemes';\nimport URLScheme from 'core/transports/url_scheme';\nimport Transport from 'core/transports/transport';\nimport Util from 'core/util';\nimport * as Collections from 'core/utils/collections';\nimport TransportHooks from 'core/transports/transport_hooks';\nimport TransportsTable from 'core/transports/transports_table';\nimport Runtime from 'runtime';\n\n/** WebSocket transport.\n *\n * Uses native WebSocket implementation, including MozWebSocket supported by\n * earlier Firefox versions.\n */\nvar WSTransport = new Transport(<TransportHooks>{\n  urls: URLSchemes.ws,\n  handlesActivityChecks: false,\n  supportsPing: false,\n\n  isInitialized: function() {\n    return Boolean(Runtime.getWebSocketAPI());\n  },\n  isSupported: function(): boolean {\n    return Boolean(Runtime.getWebSocketAPI());\n  },\n  getSocket: function(url) {\n    return Runtime.createWebSocket(url);\n  }\n});\n\nvar httpConfiguration = {\n  urls: URLSchemes.http,\n  handlesActivityChecks: false,\n  supportsPing: true,\n  isInitialized: function() {\n    return true;\n  }\n};\n\nexport var streamingConfiguration = Collections.extend(\n  {\n    getSocket: function(url) {\n      return Runtime.HTTPFactory.createStreamingSocket(url);\n    }\n  },\n  httpConfiguration\n);\nexport var pollingConfiguration = Collections.extend(\n  {\n    getSocket: function(url) {\n      return Runtime.HTTPFactory.createPollingSocket(url);\n    }\n  },\n  httpConfiguration\n);\n\nvar xhrConfiguration = {\n  isSupported: function(): boolean {\n    return Runtime.isXHRSupported();\n  }\n};\n\n/** HTTP streaming transport using CORS-enabled XMLHttpRequest. */\nvar XHRStreamingTransport = new Transport(\n  <TransportHooks>(\n    Collections.extend({}, streamingConfiguration, xhrConfiguration)\n  )\n);\n\n/** HTTP long-polling transport using CORS-enabled XMLHttpRequest. */\nvar XHRPollingTransport = new Transport(\n  <TransportHooks>Collections.extend({}, pollingConfiguration, xhrConfiguration)\n);\n\nvar Transports: TransportsTable = {\n  ws: WSTransport,\n  xhr_streaming: XHRStreamingTransport,\n  xhr_polling: XHRPollingTransport\n};\n\nexport default Transports;\n", "import Util from '../util';\nimport * as Collections from '../utils/collections';\nimport TransportManager from './transport_manager';\nimport TransportConnection from './transport_connection';\nimport Transport from './transport';\nimport PingDelayOptions from './ping_delay_options';\n\n/** Creates transport connections monitored by a transport manager.\n *\n * When a transport is closed, it might mean the environment does not support\n * it. It's possible that messages get stuck in an intermediate buffer or\n * proxies terminate inactive connections. To combat these problems,\n * assistants monitor the connection lifetime, report unclean exits and\n * adjust ping timeouts to keep the connection active. The decision to disable\n * a transport is the manager's responsibility.\n *\n * @param {TransportManager} manager\n * @param {TransportConnection} transport\n * @param {Object} options\n */\nexport default class AssistantToTheTransportManager {\n  manager: TransportManager;\n  transport: Transport;\n  minPingDelay: number;\n  maxPingDelay: number;\n  pingDelay: number;\n\n  constructor(\n    manager: TransportManager,\n    transport: Transport,\n    options: PingDelayOptions\n  ) {\n    this.manager = manager;\n    this.transport = transport;\n    this.minPingDelay = options.minPingDelay;\n    this.maxPingDelay = options.maxPingDelay;\n    this.pingDelay = undefined;\n  }\n\n  /** Creates a transport connection.\n   *\n   * This function has the same API as Transport#createConnection.\n   *\n   * @param {String} name\n   * @param {Number} priority\n   * @param {String} key the application key\n   * @param {Object} options\n   * @returns {TransportConnection}\n   */\n  createConnection(\n    name: string,\n    priority: number,\n    key: string,\n    options: Object\n  ): TransportConnection {\n    options = Collections.extend({}, options, {\n      activityTimeout: this.pingDelay\n    });\n    var connection = this.transport.createConnection(\n      name,\n      priority,\n      key,\n      options\n    );\n\n    var openTimestamp = null;\n\n    var onOpen = function() {\n      connection.unbind('open', onOpen);\n      connection.bind('closed', onClosed);\n      openTimestamp = Util.now();\n    };\n    var onClosed = closeEvent => {\n      connection.unbind('closed', onClosed);\n\n      if (closeEvent.code === 1002 || closeEvent.code === 1003) {\n        // we don't want to use transports not obeying the protocol\n        this.manager.reportDeath();\n      } else if (!closeEvent.wasClean && openTimestamp) {\n        // report deaths only for short-living transport\n        var lifespan = Util.now() - openTimestamp;\n        if (lifespan < 2 * this.maxPingDelay) {\n          this.manager.reportDeath();\n          this.pingDelay = Math.max(lifespan / 2, this.minPingDelay);\n        }\n      }\n    };\n\n    connection.bind('open', onOpen);\n    return connection;\n  }\n\n  /** Returns whether the transport is supported in the environment.\n   *\n   * This function has the same API as Transport#isSupported. Might return false\n   * when the manager decides to kill the transport.\n   *\n   * @param {Object} environment the environment details (encryption, settings)\n   * @returns {Boolean} true when the transport is supported\n   */\n  isSupported(environment: string): boolean {\n    return this.manager.isAlive() && this.transport.isSupported(environment);\n  }\n}\n", "import Action from './action';\nimport { PusherEvent } from './message-types';\n/**\n * Provides functions for handling Pusher protocol-specific messages.\n */\n\nconst Protocol = {\n  /**\n   * Decodes a message in a Pusher format.\n   *\n   * The MessageEvent we receive from the transport should contain a pusher event\n   * (https://pusher.com/docs/pusher_protocol#events) serialized as JSON in the\n   * data field\n   *\n   * The pusher event may contain a data field too, and it may also be\n   * serialised as JSON\n   *\n   * Throws errors when messages are not parse'able.\n   *\n   * @param  {MessageEvent} messageEvent\n   * @return {PusherEvent}\n   */\n  decodeMessage: function(messageEvent: MessageEvent): PusherEvent {\n    try {\n      var messageData = JSON.parse(messageEvent.data);\n      var pusherEventData = messageData.data;\n      if (typeof pusherEventData === 'string') {\n        try {\n          pusherEventData = JSON.parse(messageData.data);\n        } catch (e) {}\n      }\n      var pusherEvent: PusherEvent = {\n        event: messageData.event,\n        channel: messageData.channel,\n        data: pusherEventData\n      };\n      if (messageData.user_id) {\n        pusherEvent.user_id = messageData.user_id;\n      }\n      return pusherEvent;\n    } catch (e) {\n      throw { type: 'MessageParseError', error: e, data: messageEvent.data };\n    }\n  },\n\n  /**\n   * Encodes a message to be sent.\n   *\n   * @param  {PusherEvent} event\n   * @return {String}\n   */\n  encodeMessage: function(event: PusherEvent): string {\n    return JSON.stringify(event);\n  },\n\n  /**\n   * Processes a handshake message and returns appropriate actions.\n   *\n   * Returns an object with an 'action' and other action-specific properties.\n   *\n   * There are three outcomes when calling this function. First is a successful\n   * connection attempt, when pusher:connection_established is received, which\n   * results in a 'connected' action with an 'id' property. When passed a\n   * pusher:error event, it returns a result with action appropriate to the\n   * close code and an error. Otherwise, it raises an exception.\n   *\n   * @param {String} message\n   * @result Object\n   */\n  processHandshake: function(messageEvent: MessageEvent): Action {\n    var message = Protocol.decodeMessage(messageEvent);\n\n    if (message.event === 'pusher:connection_established') {\n      if (!message.data.activity_timeout) {\n        throw 'No activity timeout specified in handshake';\n      }\n      return {\n        action: 'connected',\n        id: message.data.socket_id,\n        activityTimeout: message.data.activity_timeout * 1000\n      };\n    } else if (message.event === 'pusher:error') {\n      // From protocol 6 close codes are sent only once, so this only\n      // happens when connection does not support close codes\n      return {\n        action: this.getCloseAction(message.data),\n        error: this.getCloseError(message.data)\n      };\n    } else {\n      throw 'Invalid handshake';\n    }\n  },\n\n  /**\n   * Dispatches the close event and returns an appropriate action name.\n   *\n   * See:\n   * 1. https://developer.mozilla.org/en-US/docs/WebSockets/WebSockets_reference/CloseEvent\n   * 2. http://pusher.com/docs/pusher_protocol\n   *\n   * @param  {CloseEvent} closeEvent\n   * @return {String} close action name\n   */\n  getCloseAction: function(closeEvent): string {\n    if (closeEvent.code < 4000) {\n      // ignore 1000 CLOSE_NORMAL, 1001 CLOSE_GOING_AWAY,\n      //        1005 CLOSE_NO_STATUS, 1006 CLOSE_ABNORMAL\n      // ignore 1007...3999\n      // handle 1002 CLOSE_PROTOCOL_ERROR, 1003 CLOSE_UNSUPPORTED,\n      //        1004 CLOSE_TOO_LARGE\n      if (closeEvent.code >= 1002 && closeEvent.code <= 1004) {\n        return 'backoff';\n      } else {\n        return null;\n      }\n    } else if (closeEvent.code === 4000) {\n      return 'tls_only';\n    } else if (closeEvent.code < 4100) {\n      return 'refused';\n    } else if (closeEvent.code < 4200) {\n      return 'backoff';\n    } else if (closeEvent.code < 4300) {\n      return 'retry';\n    } else {\n      // unknown error\n      return 'refused';\n    }\n  },\n\n  /**\n   * Returns an error or null basing on the close event.\n   *\n   * Null is returned when connection was closed cleanly. Otherwise, an object\n   * with error details is returned.\n   *\n   * @param  {CloseEvent} closeEvent\n   * @return {Object} error object\n   */\n  getCloseError: function(closeEvent): any {\n    if (closeEvent.code !== 1000 && closeEvent.code !== 1001) {\n      return {\n        type: 'PusherError',\n        data: {\n          code: closeEvent.code,\n          message: closeEvent.reason || closeEvent.message\n        }\n      };\n    } else {\n      return null;\n    }\n  }\n};\n\nexport default Protocol;\n", "import * as Collections from '../utils/collections';\nimport { default as EventsDispatcher } from '../events/dispatcher';\nimport Protocol from './protocol/protocol';\nimport { PusherEvent } from './protocol/message-types';\nimport Logger from '../logger';\nimport TransportConnection from '../transports/transport_connection';\nimport Socket from '../socket';\n/**\n * Provides Pusher protocol interface for transports.\n *\n * Emits following events:\n * - message - on received messages\n * - ping - on ping requests\n * - pong - on pong responses\n * - error - when the transport emits an error\n * - closed - after closing the transport\n *\n * It also emits more events when connection closes with a code.\n * See Protocol.getCloseAction to get more details.\n *\n * @param {Number} id\n * @param {AbstractTransport} transport\n */\nexport default class Connection extends EventsDispatcher implements Socket {\n  id: string;\n  transport: TransportConnection;\n  activityTimeout: number;\n\n  constructor(id: string, transport: TransportConnection) {\n    super();\n    this.id = id;\n    this.transport = transport;\n    this.activityTimeout = transport.activityTimeout;\n    this.bindListeners();\n  }\n\n  /** Returns whether used transport handles activity checks by itself\n   *\n   * @returns {Boolean} true if activity checks are handled by the transport\n   */\n  handlesActivityChecks() {\n    return this.transport.handlesActivityChecks();\n  }\n\n  /** Sends raw data.\n   *\n   * @param {String} data\n   */\n  send(data: any): boolean {\n    return this.transport.send(data);\n  }\n\n  /** Sends an event.\n   *\n   * @param {String} name\n   * @param {String} data\n   * @param {String} [channel]\n   * @returns {Boolean} whether message was sent or not\n   */\n  send_event(name: string, data: any, channel?: string): boolean {\n    var event: PusherEvent = { event: name, data: data };\n    if (channel) {\n      event.channel = channel;\n    }\n    Logger.debug('Event sent', event);\n    return this.send(Protocol.encodeMessage(event));\n  }\n\n  /** Sends a ping message to the server.\n   *\n   * Basing on the underlying transport, it might send either transport's\n   * protocol-specific ping or pusher:ping event.\n   */\n  ping() {\n    if (this.transport.supportsPing()) {\n      this.transport.ping();\n    } else {\n      this.send_event('pusher:ping', {});\n    }\n  }\n\n  /** Closes the connection. */\n  close() {\n    this.transport.close();\n  }\n\n  private bindListeners() {\n    var listeners = {\n      message: (messageEvent: MessageEvent) => {\n        var pusherEvent;\n        try {\n          pusherEvent = Protocol.decodeMessage(messageEvent);\n        } catch (e) {\n          this.emit('error', {\n            type: 'MessageParseError',\n            error: e,\n            data: messageEvent.data\n          });\n        }\n\n        if (pusherEvent !== undefined) {\n          Logger.debug('Event recd', pusherEvent);\n\n          switch (pusherEvent.event) {\n            case 'pusher:error':\n              this.emit('error', {\n                type: 'PusherError',\n                data: pusherEvent.data\n              });\n              break;\n            case 'pusher:ping':\n              this.emit('ping');\n              break;\n            case 'pusher:pong':\n              this.emit('pong');\n              break;\n          }\n          this.emit('message', pusherEvent);\n        }\n      },\n      activity: () => {\n        this.emit('activity');\n      },\n      error: error => {\n        this.emit('error', error);\n      },\n      closed: closeEvent => {\n        unbindListeners();\n\n        if (closeEvent && closeEvent.code) {\n          this.handleCloseEvent(closeEvent);\n        }\n\n        this.transport = null;\n        this.emit('closed');\n      }\n    };\n\n    var unbindListeners = () => {\n      Collections.objectApply(listeners, (listener, event) => {\n        this.transport.unbind(event, listener);\n      });\n    };\n\n    Collections.objectApply(listeners, (listener, event) => {\n      this.transport.bind(event, listener);\n    });\n  }\n\n  private handleCloseEvent(closeEvent: any) {\n    var action = Protocol.getCloseAction(closeEvent);\n    var error = Protocol.getCloseError(closeEvent);\n    if (error) {\n      this.emit('error', error);\n    }\n    if (action) {\n      this.emit(action, { action: action, error: error });\n    }\n  }\n}\n", "import Util from '../../util';\nimport * as Collections from '../../utils/collections';\nimport Protocol from '../protocol/protocol';\nimport Connection from '../connection';\nimport TransportConnection from '../../transports/transport_connection';\nimport HandshakePayload from './handshake_payload';\n\n/**\n * Handles Pusher protocol handshakes for transports.\n *\n * Calls back with a result object after handshake is completed. Results\n * always have two fields:\n * - action - string describing action to be taken after the handshake\n * - transport - the transport object passed to the constructor\n *\n * Different actions can set different additional properties on the result.\n * In the case of 'connected' action, there will be a 'connection' property\n * containing a Connection object for the transport. Other actions should\n * carry an 'error' property.\n *\n * @param {AbstractTransport} transport\n * @param {Function} callback\n */\nexport default class Handshake {\n  transport: TransportConnection;\n  callback: (HandshakePayload) => void;\n  onMessage: Function;\n  onClosed: Function;\n\n  constructor(\n    transport: TransportConnection,\n    callback: (HandshakePayload) => void\n  ) {\n    this.transport = transport;\n    this.callback = callback;\n    this.bindListeners();\n  }\n\n  close() {\n    this.unbindListeners();\n    this.transport.close();\n  }\n\n  private bindListeners() {\n    this.onMessage = m => {\n      this.unbindListeners();\n\n      var result;\n      try {\n        result = Protocol.processHandshake(m);\n      } catch (e) {\n        this.finish('error', { error: e });\n        this.transport.close();\n        return;\n      }\n\n      if (result.action === 'connected') {\n        this.finish('connected', {\n          connection: new Connection(result.id, this.transport),\n          activityTimeout: result.activityTimeout\n        });\n      } else {\n        this.finish(result.action, { error: result.error });\n        this.transport.close();\n      }\n    };\n\n    this.onClosed = closeEvent => {\n      this.unbindListeners();\n\n      var action = Protocol.getCloseAction(closeEvent) || 'backoff';\n      var error = Protocol.getCloseError(closeEvent);\n      this.finish(action, { error: error });\n    };\n\n    this.transport.bind('message', this.onMessage);\n    this.transport.bind('closed', this.onClosed);\n  }\n\n  private unbindListeners() {\n    this.transport.unbind('message', this.onMessage);\n    this.transport.unbind('closed', this.onClosed);\n  }\n\n  private finish(action: string, params: any) {\n    this.callback(\n      Collections.extend({ transport: this.transport, action: action }, params)\n    );\n  }\n}\n", "import * as Collections from '../utils/collections';\nimport Util from '../util';\nimport base64encode from '../base64';\nimport Timeline from './timeline';\nimport Runtime from 'runtime';\n\nexport interface TimelineSenderOptions {\n  host?: string;\n  port?: number;\n  path?: string;\n}\n\nexport default class TimelineSender {\n  timeline: Timeline;\n  options: TimelineSenderOptions;\n  host: string;\n\n  constructor(timeline: Timeline, options: TimelineSenderOptions) {\n    this.timeline = timeline;\n    this.options = options || {};\n  }\n\n  send(useTLS: boolean, callback?: Function) {\n    if (this.timeline.isEmpty()) {\n      return;\n    }\n\n    this.timeline.send(\n      Runtime.TimelineTransport.getAgent(this, useTLS),\n      callback\n    );\n  }\n}\n", "/** Error classes used throughout the library. */\n// https://github.com/Microsoft/TypeScript-wiki/blob/master/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\nexport class BadEventName extends Error {\n  constructor(msg?: string) {\n    super(msg);\n\n    Object.setPrototypeOf(this, new.target.prototype);\n  }\n}\n\nexport class BadChannelName extends Error {\n  constructor(msg?: string) {\n    super(msg);\n\n    Object.setPrototypeOf(this, new.target.prototype);\n  }\n}\n\nexport class RequestTimedOut extends Error {\n  constructor(msg?: string) {\n    super(msg);\n\n    Object.setPrototypeOf(this, new.target.prototype);\n  }\n}\nexport class TransportPriorityTooLow extends Error {\n  constructor(msg?: string) {\n    super(msg);\n\n    Object.setPrototypeOf(this, new.target.prototype);\n  }\n}\nexport class TransportClosed extends Error {\n  constructor(msg?: string) {\n    super(msg);\n\n    Object.setPrototypeOf(this, new.target.prototype);\n  }\n}\nexport class UnsupportedFeature extends Error {\n  constructor(msg?: string) {\n    super(msg);\n\n    Object.setPrototypeOf(this, new.target.prototype);\n  }\n}\nexport class UnsupportedTransport extends Error {\n  constructor(msg?: string) {\n    super(msg);\n\n    Object.setPrototypeOf(this, new.target.prototype);\n  }\n}\nexport class UnsupportedStrategy extends Error {\n  constructor(msg?: string) {\n    super(msg);\n\n    Object.setPrototypeOf(this, new.target.prototype);\n  }\n}\nexport class HTTPAuthError extends Error {\n  status: number;\n  constructor(status: number, msg?: string) {\n    super(msg);\n    this.status = status;\n\n    Object.setPrototypeOf(this, new.target.prototype);\n  }\n}\n", "/**\n * A place to store help URLs for error messages etc\n */\n\nconst urlStore = {\n  baseUrl: 'https://pusher.com',\n  urls: {\n    authenticationEndpoint: {\n      path: '/docs/channels/server_api/authenticating_users'\n    },\n    authorizationEndpoint: {\n      path: '/docs/channels/server_api/authorizing-users/'\n    },\n    javascriptQuickStart: {\n      path: '/docs/javascript_quick_start'\n    },\n    triggeringClientEvents: {\n      path: '/docs/client_api_guide/client_events#trigger-events'\n    },\n    encryptedChannelSupport: {\n      fullUrl:\n        'https://github.com/pusher/pusher-js/tree/cc491015371a4bde5743d1c87a0fbac0feb53195#encrypted-channel-support'\n    }\n  }\n};\n\n/** Builds a consistent string with links to pusher documentation\n *\n * @param {string} key - relevant key in the url_store.urls object\n * @return {string} suffix string to append to log message\n */\nconst buildLogSuffix = function(key: string): string {\n  const urlPrefix = 'See:';\n  const urlObj = urlStore.urls[key];\n  if (!urlObj) return '';\n\n  let url;\n  if (urlObj.fullUrl) {\n    url = urlObj.fullUrl;\n  } else if (urlObj.path) {\n    url = urlStore.baseUrl + urlObj.path;\n  }\n\n  if (!url) return '';\n  return `${urlPrefix} ${url}`;\n};\n\nexport default { buildLogSuffix };\n", "import { default as EventsDispatcher } from '../events/dispatcher';\nimport * as Errors from '../errors';\nimport Logger from '../logger';\nimport Pusher from '../pusher';\nimport { PusherEvent } from '../connection/protocol/message-types';\nimport <PERSON>ada<PERSON> from './metadata';\nimport UrlStore from '../utils/url_store';\nimport {\n  ChannelAuthorizationData,\n  ChannelAuthorizationCallback\n} from '../auth/options';\nimport { HTTPAuthError } from '../errors';\n\n/** Provides base public channel interface with an event emitter.\n *\n * Emits:\n * - pusher:subscription_succeeded - after subscribing successfully\n * - other non-internal events\n *\n * @param {String} name\n * @param {Pusher} pusher\n */\nexport default class Channel extends EventsDispatcher {\n  name: string;\n  pusher: Pusher;\n  subscribed: boolean;\n  subscriptionPending: boolean;\n  subscriptionCancelled: boolean;\n  subscriptionCount: null;\n\n  constructor(name: string, pusher: Pusher) {\n    super(function(event, data) {\n      Logger.debug('No callbacks on ' + name + ' for ' + event);\n    });\n\n    this.name = name;\n    this.pusher = pusher;\n    this.subscribed = false;\n    this.subscriptionPending = false;\n    this.subscriptionCancelled = false;\n  }\n\n  /** Skips authorization, since public channels don't require it.\n   *\n   * @param {Function} callback\n   */\n  authorize(socketId: string, callback: ChannelAuthorizationCallback) {\n    return callback(null, { auth: '' });\n  }\n\n  /** Triggers an event */\n  trigger(event: string, data: any) {\n    if (event.indexOf('client-') !== 0) {\n      throw new Errors.BadEventName(\n        \"Event '\" + event + \"' does not start with 'client-'\"\n      );\n    }\n    if (!this.subscribed) {\n      var suffix = UrlStore.buildLogSuffix('triggeringClientEvents');\n      Logger.warn(\n        `Client event triggered before channel 'subscription_succeeded' event . ${suffix}`\n      );\n    }\n    return this.pusher.send_event(event, data, this.name);\n  }\n\n  /** Signals disconnection to the channel. For internal use only. */\n  disconnect() {\n    this.subscribed = false;\n    this.subscriptionPending = false;\n  }\n\n  /** Handles a PusherEvent. For internal use only.\n   *\n   * @param {PusherEvent} event\n   */\n  handleEvent(event: PusherEvent) {\n    var eventName = event.event;\n    var data = event.data;\n    if (eventName === 'pusher_internal:subscription_succeeded') {\n      this.handleSubscriptionSucceededEvent(event);\n    } else if (eventName === 'pusher_internal:subscription_count') {\n      this.handleSubscriptionCountEvent(event);\n    } else if (eventName.indexOf('pusher_internal:') !== 0) {\n      var metadata: Metadata = {};\n      this.emit(eventName, data, metadata);\n    }\n  }\n\n  handleSubscriptionSucceededEvent(event: PusherEvent) {\n    this.subscriptionPending = false;\n    this.subscribed = true;\n    if (this.subscriptionCancelled) {\n      this.pusher.unsubscribe(this.name);\n    } else {\n      this.emit('pusher:subscription_succeeded', event.data);\n    }\n  }\n\n  handleSubscriptionCountEvent(event: PusherEvent) {\n    if (event.data.subscription_count) {\n      this.subscriptionCount = event.data.subscription_count;\n    }\n\n    this.emit('pusher:subscription_count', event.data);\n  }\n\n  /** Sends a subscription request. For internal use only. */\n  subscribe() {\n    if (this.subscribed) {\n      return;\n    }\n    this.subscriptionPending = true;\n    this.subscriptionCancelled = false;\n    this.authorize(\n      this.pusher.connection.socket_id,\n      (error: Error | null, data: ChannelAuthorizationData) => {\n        if (error) {\n          this.subscriptionPending = false;\n          // Why not bind to 'pusher:subscription_error' a level up, and log there?\n          // Binding to this event would cause the warning about no callbacks being\n          // bound (see constructor) to be suppressed, that's not what we want.\n          Logger.error(error.toString());\n          this.emit(\n            'pusher:subscription_error',\n            Object.assign(\n              {},\n              {\n                type: 'AuthError',\n                error: error.message\n              },\n              error instanceof HTTPAuthError ? { status: error.status } : {}\n            )\n          );\n        } else {\n          this.pusher.send_event('pusher:subscribe', {\n            auth: data.auth,\n            channel_data: data.channel_data,\n            channel: this.name\n          });\n        }\n      }\n    );\n  }\n\n  /** Sends an unsubscription request. For internal use only. */\n  unsubscribe() {\n    this.subscribed = false;\n    this.pusher.send_event('pusher:unsubscribe', {\n      channel: this.name\n    });\n  }\n\n  /** Cancels an in progress subscription. For internal use only. */\n  cancelSubscription() {\n    this.subscriptionCancelled = true;\n  }\n\n  /** Reinstates an in progress subscripiton. For internal use only. */\n  reinstateSubscription() {\n    this.subscriptionCancelled = false;\n  }\n}\n", "import Factory from '../utils/factory';\nimport Channel from './channel';\nimport { ChannelAuthorizationCallback } from '../auth/options';\n\n/** Extends public channels to provide private channel interface.\n *\n * @param {String} name\n * @param {Pusher} pusher\n */\nexport default class PrivateChannel extends Channel {\n  /** Authorizes the connection to use the channel.\n   *\n   * @param  {String} socketId\n   * @param  {Function} callback\n   */\n  authorize(socketId: string, callback: ChannelAuthorizationCallback) {\n    return this.pusher.config.channelAuthorizer(\n      {\n        channelName: this.name,\n        socketId: socketId\n      },\n      callback\n    );\n  }\n}\n", "import * as Collections from '../utils/collections';\n\n/** Represents a collection of members of a presence channel. */\nexport default class Members {\n  members: any;\n  count: number;\n  myID: any;\n  me: any;\n\n  constructor() {\n    this.reset();\n  }\n\n  /** Returns member's info for given id.\n   *\n   * Resulting object containts two fields - id and info.\n   *\n   * @param {Number} id\n   * @return {Object} member's info or null\n   */\n  get(id: string): any {\n    if (Object.prototype.hasOwnProperty.call(this.members, id)) {\n      return {\n        id: id,\n        info: this.members[id]\n      };\n    } else {\n      return null;\n    }\n  }\n\n  /** Calls back for each member in unspecified order.\n   *\n   * @param  {Function} callback\n   */\n  each(callback: Function) {\n    Collections.objectApply(this.members, (member, id) => {\n      callback(this.get(id));\n    });\n  }\n\n  /** Updates the id for connected member. For internal use only. */\n  setMyID(id: string) {\n    this.myID = id;\n  }\n\n  /** Handles subscription data. For internal use only. */\n  onSubscription(subscriptionData: any) {\n    this.members = subscriptionData.presence.hash;\n    this.count = subscriptionData.presence.count;\n    this.me = this.get(this.myID);\n  }\n\n  /** Adds a new member to the collection. For internal use only. */\n  addMember(memberData: any) {\n    if (this.get(memberData.user_id) === null) {\n      this.count++;\n    }\n    this.members[memberData.user_id] = memberData.user_info;\n    return this.get(memberData.user_id);\n  }\n\n  /** Adds a member from the collection. For internal use only. */\n  removeMember(memberData: any) {\n    var member = this.get(memberData.user_id);\n    if (member) {\n      delete this.members[memberData.user_id];\n      this.count--;\n    }\n    return member;\n  }\n\n  /** Resets the collection to the initial state. For internal use only. */\n  reset() {\n    this.members = {};\n    this.count = 0;\n    this.myID = null;\n    this.me = null;\n  }\n}\n", "import PrivateChannel from './private_channel';\nimport Logger from '../logger';\nimport Members from './members';\nimport Pusher from '../pusher';\nimport UrlStore from 'core/utils/url_store';\nimport { PusherEvent } from '../connection/protocol/message-types';\nimport Metadata from './metadata';\nimport { ChannelAuthorizationData } from '../auth/options';\n\nexport default class PresenceChannel extends PrivateChannel {\n  members: Members;\n\n  /** Adds presence channel functionality to private channels.\n   *\n   * @param {String} name\n   * @param {Pusher} pusher\n   */\n  constructor(name: string, pusher: Pusher) {\n    super(name, pusher);\n    this.members = new Members();\n  }\n\n  /** Authorizes the connection as a member of the channel.\n   *\n   * @param  {String} socketId\n   * @param  {Function} callback\n   */\n  authorize(socketId: string, callback: Function) {\n    super.authorize(socketId, async (error, authData) => {\n      if (!error) {\n        authData = authData as ChannelAuthorizationData;\n        if (authData.channel_data != null) {\n          var channelData = JSON.parse(authData.channel_data);\n          this.members.setMyID(channelData.user_id);\n        } else {\n          await this.pusher.user.signinDonePromise;\n          if (this.pusher.user.user_data != null) {\n            // If the user is signed in, get the id of the authenticated user\n            // and allow the presence authorization to continue.\n            this.members.setMyID(this.pusher.user.user_data.id);\n          } else {\n            let suffix = UrlStore.buildLogSuffix('authorizationEndpoint');\n            Logger.error(\n              `Invalid auth response for channel '${this.name}', ` +\n                `expected 'channel_data' field. ${suffix}, ` +\n                `or the user should be signed in.`\n            );\n            callback('Invalid auth response');\n            return;\n          }\n        }\n      }\n      callback(error, authData);\n    });\n  }\n\n  /** Handles presence and subscription events. For internal use only.\n   *\n   * @param {PusherEvent} event\n   */\n  handleEvent(event: PusherEvent) {\n    var eventName = event.event;\n    if (eventName.indexOf('pusher_internal:') === 0) {\n      this.handleInternalEvent(event);\n    } else {\n      var data = event.data;\n      var metadata: Metadata = {};\n      if (event.user_id) {\n        metadata.user_id = event.user_id;\n      }\n      this.emit(eventName, data, metadata);\n    }\n  }\n  handleInternalEvent(event: PusherEvent) {\n    var eventName = event.event;\n    var data = event.data;\n    switch (eventName) {\n      case 'pusher_internal:subscription_succeeded':\n        this.handleSubscriptionSucceededEvent(event);\n        break;\n      case 'pusher_internal:subscription_count':\n        this.handleSubscriptionCountEvent(event);\n        break;\n      case 'pusher_internal:member_added':\n        var addedMember = this.members.addMember(data);\n        this.emit('pusher:member_added', addedMember);\n        break;\n      case 'pusher_internal:member_removed':\n        var removedMember = this.members.removeMember(data);\n        if (removedMember) {\n          this.emit('pusher:member_removed', removedMember);\n        }\n        break;\n    }\n  }\n\n  handleSubscriptionSucceededEvent(event: PusherEvent) {\n    this.subscriptionPending = false;\n    this.subscribed = true;\n    if (this.subscriptionCancelled) {\n      this.pusher.unsubscribe(this.name);\n    } else {\n      this.members.onSubscription(event.data);\n      this.emit('pusher:subscription_succeeded', this.members);\n    }\n  }\n\n  /** Resets the channel state, including members map. For internal use only. */\n  disconnect() {\n    this.members.reset();\n    super.disconnect();\n  }\n}\n", "import PrivateChannel from './private_channel';\nimport * as Errors from '../errors';\nimport Logger from '../logger';\nimport Pusher from '../pusher';\nimport { decode as encodeUTF8 } from '@stablelib/utf8';\nimport { decode as decodeBase64 } from '@stablelib/base64';\nimport Dispatcher from '../events/dispatcher';\nimport { PusherEvent } from '../connection/protocol/message-types';\nimport {\n  ChannelAuthorizationData,\n  ChannelAuthorizationCallback\n} from '../auth/options';\nimport * as nacl from 'tweetnacl';\n\n/** Extends private channels to provide encrypted channel interface.\n *\n * @param {String} name\n * @param {Pusher} pusher\n */\nexport default class EncryptedChannel extends PrivateChannel {\n  key: Uint8Array = null;\n  nacl: nacl;\n\n  constructor(name: string, pusher: Pusher, nacl: nacl) {\n    super(name, pusher);\n    this.nacl = nacl;\n  }\n\n  /** Authorizes the connection to use the channel.\n   *\n   * @param  {String} socketId\n   * @param  {Function} callback\n   */\n  authorize(socketId: string, callback: ChannelAuthorizationCallback) {\n    super.authorize(\n      socketId,\n      (error: Error | null, authData: ChannelAuthorizationData) => {\n        if (error) {\n          callback(error, authData);\n          return;\n        }\n        let sharedSecret = authData['shared_secret'];\n        if (!sharedSecret) {\n          callback(\n            new Error(\n              `No shared_secret key in auth payload for encrypted channel: ${this.name}`\n            ),\n            null\n          );\n          return;\n        }\n        this.key = decodeBase64(sharedSecret);\n        delete authData['shared_secret'];\n        callback(null, authData);\n      }\n    );\n  }\n\n  trigger(event: string, data: any): boolean {\n    throw new Errors.UnsupportedFeature(\n      'Client events are not currently supported for encrypted channels'\n    );\n  }\n\n  /** Handles an event. For internal use only.\n   *\n   * @param {PusherEvent} event\n   */\n  handleEvent(event: PusherEvent) {\n    var eventName = event.event;\n    var data = event.data;\n    if (\n      eventName.indexOf('pusher_internal:') === 0 ||\n      eventName.indexOf('pusher:') === 0\n    ) {\n      super.handleEvent(event);\n      return;\n    }\n    this.handleEncryptedEvent(eventName, data);\n  }\n\n  private handleEncryptedEvent(event: string, data: any): void {\n    if (!this.key) {\n      Logger.debug(\n        'Received encrypted event before key has been retrieved from the authEndpoint'\n      );\n      return;\n    }\n    if (!data.ciphertext || !data.nonce) {\n      Logger.error(\n        'Unexpected format for encrypted event, expected object with `ciphertext` and `nonce` fields, got: ' +\n          data\n      );\n      return;\n    }\n    let cipherText = decodeBase64(data.ciphertext);\n    if (cipherText.length < this.nacl.secretbox.overheadLength) {\n      Logger.error(\n        `Expected encrypted event ciphertext length to be ${this.nacl.secretbox.overheadLength}, got: ${cipherText.length}`\n      );\n      return;\n    }\n    let nonce = decodeBase64(data.nonce);\n    if (nonce.length < this.nacl.secretbox.nonceLength) {\n      Logger.error(\n        `Expected encrypted event nonce length to be ${this.nacl.secretbox.nonceLength}, got: ${nonce.length}`\n      );\n      return;\n    }\n\n    let bytes = this.nacl.secretbox.open(cipherText, nonce, this.key);\n    if (bytes === null) {\n      Logger.debug(\n        'Failed to decrypt an event, probably because it was encrypted with a different key. Fetching a new key from the authEndpoint...'\n      );\n      // Try a single time to retrieve a new auth key and decrypt the event with it\n      // If this fails, a new key will be requested when a new message is received\n      this.authorize(this.pusher.connection.socket_id, (error, authData) => {\n        if (error) {\n          Logger.error(\n            `Failed to make a request to the authEndpoint: ${authData}. Unable to fetch new key, so dropping encrypted event`\n          );\n          return;\n        }\n        bytes = this.nacl.secretbox.open(cipherText, nonce, this.key);\n        if (bytes === null) {\n          Logger.error(\n            `Failed to decrypt event with new key. Dropping encrypted event`\n          );\n          return;\n        }\n        this.emit(event, this.getDataToEmit(bytes));\n        return;\n      });\n      return;\n    }\n    this.emit(event, this.getDataToEmit(bytes));\n  }\n\n  // Try and parse the decrypted bytes as JSON. If we can't parse it, just\n  // return the utf-8 string\n  getDataToEmit(bytes: Uint8Array): string {\n    let raw = encodeUTF8(bytes);\n    try {\n      return JSON.parse(raw);\n    } catch {\n      return raw;\n    }\n  }\n}\n", "import { default as EventsDispatcher } from '../events/dispatcher';\nimport { OneOffTimer as Timer } from '../utils/timers';\nimport { Config } from '../config';\nimport Logger from '../logger';\nimport HandshakePayload from './handshake/handshake_payload';\nimport Connection from './connection';\nimport Strategy from '../strategies/strategy';\nimport StrategyRunner from '../strategies/strategy_runner';\nimport * as Collections from '../utils/collections';\nimport Timeline from '../timeline/timeline';\nimport ConnectionManagerOptions from './connection_manager_options';\nimport Runtime from 'runtime';\n\nimport {\n  ErrorCallbacks,\n  HandshakeCallbacks,\n  ConnectionCallbacks\n} from './callbacks';\nimport Action from './protocol/action';\n\n/** Manages connection to <PERSON>usher.\n *\n * Uses a strategy (currently only default), timers and network availability\n * info to establish a connection and export its state. In case of failures,\n * manages reconnection attempts.\n *\n * Exports state changes as following events:\n * - \"state_change\", { previous: p, current: state }\n * - state\n *\n * States:\n * - initialized - initial state, never transitioned to\n * - connecting - connection is being established\n * - connected - connection has been fully established\n * - disconnected - on requested disconnection\n * - unavailable - after connection timeout or when there's no network\n * - failed - when the connection strategy is not supported\n *\n * Options:\n * - unavailableTimeout - time to transition to unavailable state\n * - activityTimeout - time after which ping message should be sent\n * - pongTimeout - time for Pusher to respond with pong before reconnecting\n *\n * @param {String} key application key\n * @param {Object} options\n */\nexport default class ConnectionManager extends EventsDispatcher {\n  key: string;\n  options: ConnectionManagerOptions;\n  state: string;\n  connection: Connection;\n  usingTLS: boolean;\n  timeline: Timeline;\n  socket_id: string;\n  unavailableTimer: Timer;\n  activityTimer: Timer;\n  retryTimer: Timer;\n  activityTimeout: number;\n  strategy: Strategy;\n  runner: StrategyRunner;\n  errorCallbacks: ErrorCallbacks;\n  handshakeCallbacks: HandshakeCallbacks;\n  connectionCallbacks: ConnectionCallbacks;\n\n  constructor(key: string, options: ConnectionManagerOptions) {\n    super();\n    this.state = 'initialized';\n    this.connection = null;\n\n    this.key = key;\n    this.options = options;\n    this.timeline = this.options.timeline;\n    this.usingTLS = this.options.useTLS;\n\n    this.errorCallbacks = this.buildErrorCallbacks();\n    this.connectionCallbacks = this.buildConnectionCallbacks(\n      this.errorCallbacks\n    );\n    this.handshakeCallbacks = this.buildHandshakeCallbacks(this.errorCallbacks);\n\n    var Network = Runtime.getNetwork();\n\n    Network.bind('online', () => {\n      this.timeline.info({ netinfo: 'online' });\n      if (this.state === 'connecting' || this.state === 'unavailable') {\n        this.retryIn(0);\n      }\n    });\n    Network.bind('offline', () => {\n      this.timeline.info({ netinfo: 'offline' });\n      if (this.connection) {\n        this.sendActivityCheck();\n      }\n    });\n\n    this.updateStrategy();\n  }\n\n  /** Establishes a connection to Pusher.\n   *\n   * Does nothing when connection is already established. See top-level doc\n   * to find events emitted on connection attempts.\n   */\n  connect() {\n    if (this.connection || this.runner) {\n      return;\n    }\n    if (!this.strategy.isSupported()) {\n      this.updateState('failed');\n      return;\n    }\n    this.updateState('connecting');\n    this.startConnecting();\n    this.setUnavailableTimer();\n  }\n\n  /** Sends raw data.\n   *\n   * @param {String} data\n   */\n  send(data) {\n    if (this.connection) {\n      return this.connection.send(data);\n    } else {\n      return false;\n    }\n  }\n\n  /** Sends an event.\n   *\n   * @param {String} name\n   * @param {String} data\n   * @param {String} [channel]\n   * @returns {Boolean} whether message was sent or not\n   */\n  send_event(name: string, data: any, channel?: string) {\n    if (this.connection) {\n      return this.connection.send_event(name, data, channel);\n    } else {\n      return false;\n    }\n  }\n\n  /** Closes the connection. */\n  disconnect() {\n    this.disconnectInternally();\n    this.updateState('disconnected');\n  }\n\n  isUsingTLS() {\n    return this.usingTLS;\n  }\n\n  private startConnecting() {\n    var callback = (error, handshake) => {\n      if (error) {\n        this.runner = this.strategy.connect(0, callback);\n      } else {\n        if (handshake.action === 'error') {\n          this.emit('error', {\n            type: 'HandshakeError',\n            error: handshake.error\n          });\n          this.timeline.error({ handshakeError: handshake.error });\n        } else {\n          this.abortConnecting(); // we don't support switching connections yet\n          this.handshakeCallbacks[handshake.action](handshake);\n        }\n      }\n    };\n    this.runner = this.strategy.connect(0, callback);\n  }\n\n  private abortConnecting() {\n    if (this.runner) {\n      this.runner.abort();\n      this.runner = null;\n    }\n  }\n\n  private disconnectInternally() {\n    this.abortConnecting();\n    this.clearRetryTimer();\n    this.clearUnavailableTimer();\n    if (this.connection) {\n      var connection = this.abandonConnection();\n      connection.close();\n    }\n  }\n\n  private updateStrategy() {\n    this.strategy = this.options.getStrategy({\n      key: this.key,\n      timeline: this.timeline,\n      useTLS: this.usingTLS\n    });\n  }\n\n  private retryIn(delay) {\n    this.timeline.info({ action: 'retry', delay: delay });\n    if (delay > 0) {\n      this.emit('connecting_in', Math.round(delay / 1000));\n    }\n    this.retryTimer = new Timer(delay || 0, () => {\n      this.disconnectInternally();\n      this.connect();\n    });\n  }\n\n  private clearRetryTimer() {\n    if (this.retryTimer) {\n      this.retryTimer.ensureAborted();\n      this.retryTimer = null;\n    }\n  }\n\n  private setUnavailableTimer() {\n    this.unavailableTimer = new Timer(this.options.unavailableTimeout, () => {\n      this.updateState('unavailable');\n    });\n  }\n\n  private clearUnavailableTimer() {\n    if (this.unavailableTimer) {\n      this.unavailableTimer.ensureAborted();\n    }\n  }\n\n  private sendActivityCheck() {\n    this.stopActivityCheck();\n    this.connection.ping();\n    // wait for pong response\n    this.activityTimer = new Timer(this.options.pongTimeout, () => {\n      this.timeline.error({ pong_timed_out: this.options.pongTimeout });\n      this.retryIn(0);\n    });\n  }\n\n  private resetActivityCheck() {\n    this.stopActivityCheck();\n    // send ping after inactivity\n    if (this.connection && !this.connection.handlesActivityChecks()) {\n      this.activityTimer = new Timer(this.activityTimeout, () => {\n        this.sendActivityCheck();\n      });\n    }\n  }\n\n  private stopActivityCheck() {\n    if (this.activityTimer) {\n      this.activityTimer.ensureAborted();\n    }\n  }\n\n  private buildConnectionCallbacks(\n    errorCallbacks: ErrorCallbacks\n  ): ConnectionCallbacks {\n    return Collections.extend<ConnectionCallbacks>({}, errorCallbacks, {\n      message: message => {\n        // includes pong messages from server\n        this.resetActivityCheck();\n        this.emit('message', message);\n      },\n      ping: () => {\n        this.send_event('pusher:pong', {});\n      },\n      activity: () => {\n        this.resetActivityCheck();\n      },\n      error: error => {\n        // just emit error to user - socket will already be closed by browser\n        this.emit('error', error);\n      },\n      closed: () => {\n        this.abandonConnection();\n        if (this.shouldRetry()) {\n          this.retryIn(1000);\n        }\n      }\n    });\n  }\n\n  private buildHandshakeCallbacks(\n    errorCallbacks: ErrorCallbacks\n  ): HandshakeCallbacks {\n    return Collections.extend<HandshakeCallbacks>({}, errorCallbacks, {\n      connected: (handshake: HandshakePayload) => {\n        this.activityTimeout = Math.min(\n          this.options.activityTimeout,\n          handshake.activityTimeout,\n          handshake.connection.activityTimeout || Infinity\n        );\n        this.clearUnavailableTimer();\n        this.setConnection(handshake.connection);\n        this.socket_id = this.connection.id;\n        this.updateState('connected', { socket_id: this.socket_id });\n      }\n    });\n  }\n\n  private buildErrorCallbacks(): ErrorCallbacks {\n    let withErrorEmitted = callback => {\n      return (result: Action | HandshakePayload) => {\n        if (result.error) {\n          this.emit('error', { type: 'WebSocketError', error: result.error });\n        }\n        callback(result);\n      };\n    };\n\n    return {\n      tls_only: withErrorEmitted(() => {\n        this.usingTLS = true;\n        this.updateStrategy();\n        this.retryIn(0);\n      }),\n      refused: withErrorEmitted(() => {\n        this.disconnect();\n      }),\n      backoff: withErrorEmitted(() => {\n        this.retryIn(1000);\n      }),\n      retry: withErrorEmitted(() => {\n        this.retryIn(0);\n      })\n    };\n  }\n\n  private setConnection(connection) {\n    this.connection = connection;\n    for (var event in this.connectionCallbacks) {\n      this.connection.bind(event, this.connectionCallbacks[event]);\n    }\n    this.resetActivityCheck();\n  }\n\n  private abandonConnection() {\n    if (!this.connection) {\n      return;\n    }\n    this.stopActivityCheck();\n    for (var event in this.connectionCallbacks) {\n      this.connection.unbind(event, this.connectionCallbacks[event]);\n    }\n    var connection = this.connection;\n    this.connection = null;\n    return connection;\n  }\n\n  private updateState(newState: string, data?: any) {\n    var previousState = this.state;\n    this.state = newState;\n    if (previousState !== newState) {\n      var newStateDescription = newState;\n      if (newStateDescription === 'connected') {\n        newStateDescription += ' with new socket ID ' + data.socket_id;\n      }\n      Logger.debug(\n        'State changed',\n        previousState + ' -> ' + newStateDescription\n      );\n      this.timeline.info({ state: newState, params: data });\n      this.emit('state_change', { previous: previousState, current: newState });\n      this.emit(newState, data);\n    }\n  }\n\n  private shouldRetry(): boolean {\n    return this.state === 'connecting' || this.state === 'connected';\n  }\n}\n", "import Channel from './channel';\nimport * as Collections from '../utils/collections';\nimport ChannelTable from './channel_table';\nimport Factory from '../utils/factory';\nimport Pusher from '../pusher';\nimport Logger from '../logger';\nimport * as Errors from '../errors';\nimport urlStore from '../utils/url_store';\n\n/** Handles a channel map. */\nexport default class Channels {\n  channels: ChannelTable;\n\n  constructor() {\n    this.channels = {};\n  }\n\n  /** Creates or retrieves an existing channel by its name.\n   *\n   * @param {String} name\n   * @param {Pusher} pusher\n   * @return {Channel}\n   */\n  add(name: string, pusher: Pusher) {\n    if (!this.channels[name]) {\n      this.channels[name] = createChannel(name, pusher);\n    }\n    return this.channels[name];\n  }\n\n  /** Returns a list of all channels\n   *\n   * @return {Array}\n   */\n  all(): Channel[] {\n    return Collections.values(this.channels);\n  }\n\n  /** Finds a channel by its name.\n   *\n   * @param {String} name\n   * @return {Channel} channel or null if it doesn't exist\n   */\n  find(name: string) {\n    return this.channels[name];\n  }\n\n  /** Removes a channel from the map.\n   *\n   * @param {String} name\n   */\n  remove(name: string) {\n    var channel = this.channels[name];\n    delete this.channels[name];\n    return channel;\n  }\n\n  /** Proxies disconnection signal to all channels. */\n  disconnect() {\n    Collections.objectApply(this.channels, function(channel) {\n      channel.disconnect();\n    });\n  }\n}\n\nfunction createChannel(name: string, pusher: Pusher): Channel {\n  if (name.indexOf('private-encrypted-') === 0) {\n    if (pusher.config.nacl) {\n      return Factory.createEncryptedChannel(name, pusher, pusher.config.nacl);\n    }\n    let errMsg =\n      'Tried to subscribe to a private-encrypted- channel but no nacl implementation available';\n    let suffix = urlStore.buildLogSuffix('encryptedChannelSupport');\n    throw new Errors.UnsupportedFeature(`${errMsg}. ${suffix}`);\n  } else if (name.indexOf('private-') === 0) {\n    return Factory.createPrivateChannel(name, pusher);\n  } else if (name.indexOf('presence-') === 0) {\n    return Factory.createPresenceChannel(name, pusher);\n  } else if (name.indexOf('#') === 0) {\n    throw new Errors.BadChannelName(\n      'Cannot create a channel with name \"' + name + '\".'\n    );\n  } else {\n    return Factory.createChannel(name, pusher);\n  }\n}\n", "import AssistantToTheTransportManager from '../transports/assistant_to_the_transport_manager';\nimport PingDelayOptions from '../transports/ping_delay_options';\nimport Transport from '../transports/transport';\nimport TransportManager from '../transports/transport_manager';\nimport Handshake from '../connection/handshake';\nimport TransportConnection from '../transports/transport_connection';\nimport SocketHooks from '../http/socket_hooks';\nimport HTTPSocket from '../http/http_socket';\n\nimport Timeline from '../timeline/timeline';\nimport {\n  default as TimelineSender,\n  TimelineSenderOptions\n} from '../timeline/timeline_sender';\nimport PresenceChannel from '../channels/presence_channel';\nimport PrivateChannel from '../channels/private_channel';\nimport EncryptedChannel from '../channels/encrypted_channel';\nimport Channel from '../channels/channel';\nimport ConnectionManager from '../connection/connection_manager';\nimport ConnectionManagerOptions from '../connection/connection_manager_options';\nimport Ajax from '../http/ajax';\nimport Channels from '../channels/channels';\nimport Pusher from '../pusher';\nimport { Config } from '../config';\nimport * as nacl from 'tweetnacl';\n\nvar Factory = {\n  createChannels(): Channels {\n    return new Channels();\n  },\n\n  createConnectionManager(\n    key: string,\n    options: ConnectionManagerOptions\n  ): ConnectionManager {\n    return new ConnectionManager(key, options);\n  },\n\n  createChannel(name: string, pusher: Pusher): Channel {\n    return new Channel(name, pusher);\n  },\n\n  createPrivateChannel(name: string, pusher: Pusher): PrivateChannel {\n    return new PrivateChannel(name, pusher);\n  },\n\n  createPresenceChannel(name: string, pusher: Pusher): PresenceChannel {\n    return new PresenceChannel(name, pusher);\n  },\n\n  createEncryptedChannel(\n    name: string,\n    pusher: Pusher,\n    nacl: nacl\n  ): EncryptedChannel {\n    return new EncryptedChannel(name, pusher, nacl);\n  },\n\n  createTimelineSender(timeline: Timeline, options: TimelineSenderOptions) {\n    return new TimelineSender(timeline, options);\n  },\n\n  createHandshake(\n    transport: TransportConnection,\n    callback: (HandshakePayload) => void\n  ): Handshake {\n    return new Handshake(transport, callback);\n  },\n\n  createAssistantToTheTransportManager(\n    manager: TransportManager,\n    transport: Transport,\n    options: PingDelayOptions\n  ): AssistantToTheTransportManager {\n    return new AssistantToTheTransportManager(manager, transport, options);\n  }\n};\n\nexport default Factory;\n", "import AssistantToTheTransportManager from './assistant_to_the_transport_manager';\nimport Transport from './transport';\nimport PingDelayOptions from './ping_delay_options';\nimport Factory from '../utils/factory';\n\nexport interface TransportManagerOptions extends PingDelayOptions {\n  lives?: number;\n}\n\n/** Keeps track of the number of lives left for a transport.\n *\n * In the beginning of a session, transports may be assigned a number of\n * lives. When an AssistantToTheTransportManager instance reports a transport\n * connection closed uncleanly, the transport loses a life. When the number\n * of lives drops to zero, the transport gets disabled by its manager.\n *\n * @param {Object} options\n */\nexport default class TransportManager {\n  options: TransportManagerOptions;\n  livesLeft: number;\n\n  constructor(options: TransportManagerOptions) {\n    this.options = options || {};\n    this.livesLeft = this.options.lives || Infinity;\n  }\n\n  /** Creates a assistant for the transport.\n   *\n   * @param {Transport} transport\n   * @returns {AssistantToTheTransportManager}\n   */\n  getAssistant(transport: Transport): AssistantToTheTransportManager {\n    return Factory.createAssistantToTheTransportManager(this, transport, {\n      minPingDelay: this.options.minPingDelay,\n      maxPingDelay: this.options.maxPingDelay\n    });\n  }\n\n  /** Returns whether the transport has any lives left.\n   *\n   * @returns {Boolean}\n   */\n  isAlive(): boolean {\n    return this.livesLeft > 0;\n  }\n\n  /** Takes one life from the transport. */\n  reportDeath() {\n    this.livesLeft -= 1;\n  }\n}\n", "import * as Collections from '../utils/collections';\nimport Util from '../util';\nimport { OneOffTimer as Timer } from '../utils/timers';\nimport Strategy from './strategy';\nimport StrategyOptions from './strategy_options';\n\n/** Loops through strategies with optional timeouts.\n *\n * Options:\n * - loop - whether it should loop through the substrategy list\n * - timeout - initial timeout for a single substrategy\n * - timeoutLimit - maximum timeout\n *\n * @param {Strategy[]} strategies\n * @param {Object} options\n */\nexport default class SequentialStrategy implements Strategy {\n  strategies: Strategy[];\n  loop: boolean;\n  failFast: boolean;\n  timeout: number;\n  timeoutLimit: number;\n\n  constructor(strategies: Strategy[], options: StrategyOptions) {\n    this.strategies = strategies;\n    this.loop = Boolean(options.loop);\n    this.failFast = Boolean(options.failFast);\n    this.timeout = options.timeout;\n    this.timeoutLimit = options.timeoutLimit;\n  }\n\n  isSupported(): boolean {\n    return Collections.any(this.strategies, Util.method('isSupported'));\n  }\n\n  connect(minPriority: number, callback: Function) {\n    var strategies = this.strategies;\n    var current = 0;\n    var timeout = this.timeout;\n    var runner = null;\n\n    var tryNextStrategy = (error, handshake) => {\n      if (handshake) {\n        callback(null, handshake);\n      } else {\n        current = current + 1;\n        if (this.loop) {\n          current = current % strategies.length;\n        }\n\n        if (current < strategies.length) {\n          if (timeout) {\n            timeout = timeout * 2;\n            if (this.timeoutLimit) {\n              timeout = Math.min(timeout, this.timeoutLimit);\n            }\n          }\n          runner = this.tryStrategy(\n            strategies[current],\n            minPriority,\n            { timeout, failFast: this.failFast },\n            tryNextStrategy\n          );\n        } else {\n          callback(true);\n        }\n      }\n    };\n\n    runner = this.tryStrategy(\n      strategies[current],\n      minPriority,\n      { timeout: timeout, failFast: this.failFast },\n      tryNextStrategy\n    );\n\n    return {\n      abort: function() {\n        runner.abort();\n      },\n      forceMinPriority: function(p) {\n        minPriority = p;\n        if (runner) {\n          runner.forceMinPriority(p);\n        }\n      }\n    };\n  }\n\n  private tryStrategy(\n    strategy: Strategy,\n    minPriority: number,\n    options: StrategyOptions,\n    callback: Function\n  ) {\n    var timer = null;\n    var runner = null;\n\n    if (options.timeout > 0) {\n      timer = new Timer(options.timeout, function() {\n        runner.abort();\n        callback(true);\n      });\n    }\n\n    runner = strategy.connect(minPriority, function(error, handshake) {\n      if (error && timer && timer.isRunning() && !options.failFast) {\n        // advance to the next strategy after the timeout\n        return;\n      }\n      if (timer) {\n        timer.ensureAborted();\n      }\n      callback(error, handshake);\n    });\n\n    return {\n      abort: function() {\n        if (timer) {\n          timer.ensureAborted();\n        }\n        runner.abort();\n      },\n      forceMinPriority: function(p) {\n        runner.forceMinPriority(p);\n      }\n    };\n  }\n}\n", "import * as Collections from '../utils/collections';\nimport Util from '../util';\nimport Strategy from './strategy';\n\n/** Launches all substrategies and emits prioritized connected transports.\n *\n * @param {Array} strategies\n */\nexport default class BestConnectedEverStrategy implements Strategy {\n  strategies: Strategy[];\n\n  constructor(strategies: Strategy[]) {\n    this.strategies = strategies;\n  }\n\n  isSupported(): boolean {\n    return Collections.any(this.strategies, Util.method('isSupported'));\n  }\n\n  connect(minPriority: number, callback: Function) {\n    return connect(this.strategies, minPriority, function(i, runners) {\n      return function(error, handshake) {\n        runners[i].error = error;\n        if (error) {\n          if (allRunnersFailed(runners)) {\n            callback(true);\n          }\n          return;\n        }\n        Collections.apply(runners, function(runner) {\n          runner.forceMinPriority(handshake.transport.priority);\n        });\n        callback(null, handshake);\n      };\n    });\n  }\n}\n\n/** Connects to all strategies in parallel.\n *\n * Callback builder should be a function that takes two arguments: index\n * and a list of runners. It should return another function that will be\n * passed to the substrategy with given index. Runners can be aborted using\n * abortRunner(s) functions from this class.\n *\n * @param  {Array} strategies\n * @param  {Function} callbackBuilder\n * @return {Object} strategy runner\n */\nfunction connect(\n  strategies: Strategy[],\n  minPriority: number,\n  callbackBuilder: Function\n) {\n  var runners = Collections.map(strategies, function(strategy, i, _, rs) {\n    return strategy.connect(minPriority, callbackBuilder(i, rs));\n  });\n  return {\n    abort: function() {\n      Collections.apply(runners, abortRunner);\n    },\n    forceMinPriority: function(p) {\n      Collections.apply(runners, function(runner) {\n        runner.forceMinPriority(p);\n      });\n    }\n  };\n}\n\nfunction allRunnersFailed(runners): boolean {\n  return Collections.all(runners, function(runner) {\n    return Boolean(runner.error);\n  });\n}\n\nfunction abortRunner(runner) {\n  if (!runner.error && !runner.aborted) {\n    runner.abort();\n    runner.aborted = true;\n  }\n}\n", "import Util from '../util';\nimport Runtime from 'runtime';\nimport Strategy from './strategy';\nimport SequentialStrategy from './sequential_strategy';\nimport StrategyOptions from './strategy_options';\nimport TransportStrategy from './transport_strategy';\nimport Timeline from '../timeline/timeline';\nimport * as Collections from '../utils/collections';\n\nexport interface TransportStrategyDictionary {\n  [key: string]: TransportStrategy;\n}\n\n/** Caches the last successful transport and, after the first few attempts,\n *  uses the cached transport for subsequent attempts.\n *\n * @param {Strategy} strategy\n * @param {Object} transports\n * @param {Object} options\n */\nexport default class WebSocketPrioritizedCachedStrategy implements Strategy {\n  strategy: Strategy;\n  transports: TransportStrategyDictionary;\n  ttl: number;\n  usingTLS: boolean;\n  timeline: Timeline;\n\n  constructor(\n    strategy: Strategy,\n    transports: TransportStrategyDictionary,\n    options: StrategyOptions\n  ) {\n    this.strategy = strategy;\n    this.transports = transports;\n    this.ttl = options.ttl || 1800 * 1000;\n    this.usingTLS = options.useTLS;\n    this.timeline = options.timeline;\n  }\n\n  isSupported(): boolean {\n    return this.strategy.isSupported();\n  }\n\n  connect(minPriority: number, callback: Function) {\n    var usingTLS = this.usingTLS;\n    var info = fetchTransportCache(usingTLS);\n    var cacheSkipCount = info && info.cacheSkipCount ? info.cacheSkipCount : 0;\n\n    var strategies = [this.strategy];\n    if (info && info.timestamp + this.ttl >= Util.now()) {\n      var transport = this.transports[info.transport];\n      if (transport) {\n        if (['ws', 'wss'].includes(info.transport) || cacheSkipCount > 3) {\n          this.timeline.info({\n            cached: true,\n            transport: info.transport,\n            latency: info.latency\n          });\n          strategies.push(\n            new SequentialStrategy([transport], {\n              timeout: info.latency * 2 + 1000,\n              failFast: true\n            })\n          );\n        } else {\n          cacheSkipCount++;\n        }\n      }\n    }\n\n    var startTimestamp = Util.now();\n    var runner = strategies\n      .pop()\n      .connect(minPriority, function cb(error, handshake) {\n        if (error) {\n          flushTransportCache(usingTLS);\n          if (strategies.length > 0) {\n            startTimestamp = Util.now();\n            runner = strategies.pop().connect(minPriority, cb);\n          } else {\n            callback(error);\n          }\n        } else {\n          storeTransportCache(\n            usingTLS,\n            handshake.transport.name,\n            Util.now() - startTimestamp,\n            cacheSkipCount\n          );\n          callback(null, handshake);\n        }\n      });\n\n    return {\n      abort: function() {\n        runner.abort();\n      },\n      forceMinPriority: function(p) {\n        minPriority = p;\n        if (runner) {\n          runner.forceMinPriority(p);\n        }\n      }\n    };\n  }\n}\n\nfunction getTransportCacheKey(usingTLS: boolean): string {\n  return 'pusherTransport' + (usingTLS ? 'TLS' : 'NonTLS');\n}\n\nfunction fetchTransportCache(usingTLS: boolean): any {\n  var storage = Runtime.getLocalStorage();\n  if (storage) {\n    try {\n      var serializedCache = storage[getTransportCacheKey(usingTLS)];\n      if (serializedCache) {\n        return JSON.parse(serializedCache);\n      }\n    } catch (e) {\n      flushTransportCache(usingTLS);\n    }\n  }\n  return null;\n}\n\nfunction storeTransportCache(\n  usingTLS: boolean,\n  transport: TransportStrategy,\n  latency: number,\n  cacheSkipCount: number\n) {\n  var storage = Runtime.getLocalStorage();\n  if (storage) {\n    try {\n      storage[getTransportCacheKey(usingTLS)] = Collections.safeJSONStringify({\n        timestamp: Util.now(),\n        transport: transport,\n        latency: latency,\n        cacheSkipCount: cacheSkipCount\n      });\n    } catch (e) {\n      // catch over quota exceptions raised by localStorage\n    }\n  }\n}\n\nfunction flushTransportCache(usingTLS: boolean) {\n  var storage = Runtime.getLocalStorage();\n  if (storage) {\n    try {\n      delete storage[getTransportCacheKey(usingTLS)];\n    } catch (e) {\n      // catch exceptions raised by localStorage\n    }\n  }\n}\n", "import { OneOffTimer as Timer } from '../utils/timers';\nimport Strategy from './strategy';\nimport StrategyOptions from './strategy_options';\n\n/** Runs substrategy after specified delay.\n *\n * Options:\n * - delay - time in miliseconds to delay the substrategy attempt\n *\n * @param {Strategy} strategy\n * @param {Object} options\n */\nexport default class DelayedStrategy implements Strategy {\n  strategy: Strategy;\n  options: { delay: number };\n\n  constructor(strategy: Strategy, { delay: number }) {\n    this.strategy = strategy;\n    this.options = { delay: number };\n  }\n\n  isSupported(): boolean {\n    return this.strategy.isSupported();\n  }\n\n  connect(minPriority: number, callback: Function) {\n    var strategy = this.strategy;\n    var runner;\n    var timer = new Timer(this.options.delay, function() {\n      runner = strategy.connect(minPriority, callback);\n    });\n\n    return {\n      abort: function() {\n        timer.ensureAborted();\n        if (runner) {\n          runner.abort();\n        }\n      },\n      forceMinPriority: function(p) {\n        minPriority = p;\n        if (runner) {\n          runner.forceMinPriority(p);\n        }\n      }\n    };\n  }\n}\n", "import Strategy from './strategy';\nimport StrategyRunner from './strategy_runner';\n\n/** Proxies method calls to one of substrategies basing on the test function.\n *\n * @param {Function} test\n * @param {Strategy} trueBranch strategy used when test returns true\n * @param {Strategy} falseBranch strategy used when test returns false\n */\nexport default class IfStrategy implements Strategy {\n  test: () => boolean;\n  trueBranch: Strategy;\n  falseBranch: Strategy;\n\n  constructor(\n    test: () => boolean,\n    trueBranch: Strategy,\n    falseBranch: Strategy\n  ) {\n    this.test = test;\n    this.trueBranch = trueBranch;\n    this.falseBranch = falseBranch;\n  }\n\n  isSupported(): boolean {\n    var branch = this.test() ? this.trueBranch : this.falseBranch;\n    return branch.isSupported();\n  }\n\n  connect(minPriority: number, callback: Function): StrategyRunner {\n    var branch = this.test() ? this.trueBranch : this.falseBranch;\n    return branch.connect(minPriority, callback);\n  }\n}\n", "import Strategy from './strategy';\nimport StrategyRunner from './strategy_runner';\n\n/** Launches the substrategy and terminates on the first open connection.\n *\n * @param {Strategy} strategy\n */\nexport default class FirstConnectedStrategy implements Strategy {\n  strategy: Strategy;\n\n  constructor(strategy: Strategy) {\n    this.strategy = strategy;\n  }\n\n  isSupported(): boolean {\n    return this.strategy.isSupported();\n  }\n\n  connect(minPriority: number, callback: Function): StrategyRunner {\n    var runner = this.strategy.connect(minPriority, function(error, handshake) {\n      if (handshake) {\n        runner.abort();\n      }\n      callback(error, handshake);\n    });\n    return runner;\n  }\n}\n", "import * as Collections from 'core/utils/collections';\nimport TransportManager from 'core/transports/transport_manager';\nimport Strategy from 'core/strategies/strategy';\nimport SequentialStrategy from 'core/strategies/sequential_strategy';\nimport BestConnectedEverStrategy from 'core/strategies/best_connected_ever_strategy';\nimport WebSocketPrioritizedCachedStrategy, {\n  TransportStrategyDictionary\n} from 'core/strategies/websocket_prioritized_cached_strategy';\nimport DelayedStrategy from 'core/strategies/delayed_strategy';\nimport IfStrategy from 'core/strategies/if_strategy';\nimport FirstConnectedStrategy from 'core/strategies/first_connected_strategy';\nimport { Config } from 'core/config';\nimport StrategyOptions from 'core/strategies/strategy_options';\n\nfunction testSupportsStrategy(strategy: Strategy) {\n  return function() {\n    return strategy.isSupported();\n  };\n}\n\nvar getDefaultStrategy = function(\n  config: Config,\n  baseOptions: StrategyOptions,\n  defineTransport: Function\n): Strategy {\n  var definedTransports = <TransportStrategyDictionary>{};\n\n  function defineTransportStrategy(\n    name: string,\n    type: string,\n    priority: number,\n    options: StrategyOptions,\n    manager?: TransportManager\n  ) {\n    var transport = defineTransport(\n      config,\n      name,\n      type,\n      priority,\n      options,\n      manager\n    );\n\n    definedTransports[name] = transport;\n\n    return transport;\n  }\n\n  var ws_options: StrategyOptions = Object.assign({}, baseOptions, {\n    hostNonTLS: config.wsHost + ':' + config.wsPort,\n    hostTLS: config.wsHost + ':' + config.wssPort,\n    httpPath: config.wsPath\n  });\n  var wss_options: StrategyOptions = Collections.extend({}, ws_options, {\n    useTLS: true\n  });\n  var http_options: StrategyOptions = Object.assign({}, baseOptions, {\n    hostNonTLS: config.httpHost + ':' + config.httpPort,\n    hostTLS: config.httpHost + ':' + config.httpsPort,\n    httpPath: config.httpPath\n  });\n  var timeouts = {\n    loop: true,\n    timeout: 15000,\n    timeoutLimit: 60000\n  };\n\n  var ws_manager = new TransportManager({\n    minPingDelay: 10000,\n    maxPingDelay: config.activityTimeout\n  });\n  var streaming_manager = new TransportManager({\n    lives: 2,\n    minPingDelay: 10000,\n    maxPingDelay: config.activityTimeout\n  });\n\n  var ws_transport = defineTransportStrategy(\n    'ws',\n    'ws',\n    3,\n    ws_options,\n    ws_manager\n  );\n  var wss_transport = defineTransportStrategy(\n    'wss',\n    'ws',\n    3,\n    wss_options,\n    ws_manager\n  );\n  var xhr_streaming_transport = defineTransportStrategy(\n    'xhr_streaming',\n    'xhr_streaming',\n    1,\n    http_options,\n    streaming_manager\n  );\n  var xhr_polling_transport = defineTransportStrategy(\n    'xhr_polling',\n    'xhr_polling',\n    1,\n    http_options\n  );\n\n  var ws_loop = new SequentialStrategy([ws_transport], timeouts);\n  var wss_loop = new SequentialStrategy([wss_transport], timeouts);\n  var streaming_loop = new SequentialStrategy(\n    [xhr_streaming_transport],\n    timeouts\n  );\n  var polling_loop = new SequentialStrategy([xhr_polling_transport], timeouts);\n\n  var http_loop = new SequentialStrategy(\n    [\n      new IfStrategy(\n        testSupportsStrategy(streaming_loop),\n        new BestConnectedEverStrategy([\n          streaming_loop,\n          new DelayedStrategy(polling_loop, { delay: 4000 })\n        ]),\n        polling_loop\n      )\n    ],\n    timeouts\n  );\n\n  var wsStrategy;\n  if (baseOptions.useTLS) {\n    wsStrategy = new BestConnectedEverStrategy([\n      ws_loop,\n      new DelayedStrategy(http_loop, { delay: 2000 })\n    ]);\n  } else {\n    wsStrategy = new BestConnectedEverStrategy([\n      ws_loop,\n      new DelayedStrategy(wss_loop, { delay: 2000 }),\n      new DelayedStrategy(http_loop, { delay: 5000 })\n    ]);\n  }\n\n  return new WebSocketPrioritizedCachedStrategy(\n    new FirstConnectedStrategy(\n      new IfStrategy(testSupportsStrategy(ws_transport), wsStrategy, http_loop)\n    ),\n    definedTransports,\n    {\n      ttl: 1800000,\n      timeline: baseOptions.timeline,\n      useTLS: baseOptions.useTLS\n    }\n  );\n};\n\nexport default getDefaultStrategy;\n", "import Runtime from 'runtime';\nimport RequestHooks from './request_hooks';\nimport <PERSON> from './ajax';\nimport { default as EventsDispatcher } from '../events/dispatcher';\n\nconst MAX_BUFFER_LENGTH = 256 * 1024;\n\nexport default class HTTPRequest extends EventsDispatcher {\n  hooks: RequestHooks;\n  method: string;\n  url: string;\n  position: number;\n  xhr: Ajax;\n  unloader: Function;\n\n  constructor(hooks: RequestHooks, method: string, url: string) {\n    super();\n    this.hooks = hooks;\n    this.method = method;\n    this.url = url;\n  }\n\n  start(payload?: any) {\n    this.position = 0;\n    this.xhr = this.hooks.getRequest(this);\n\n    this.unloader = () => {\n      this.close();\n    };\n    Runtime.addUnloadListener(this.unloader);\n\n    this.xhr.open(this.method, this.url, true);\n\n    if (this.xhr.setRequestHeader) {\n      this.xhr.setRequestHeader('Content-Type', 'application/json'); // ReactNative doesn't set this header by default.\n    }\n    this.xhr.send(payload);\n  }\n\n  close() {\n    if (this.unloader) {\n      Runtime.removeUnloadListener(this.unloader);\n      this.unloader = null;\n    }\n    if (this.xhr) {\n      this.hooks.abortRequest(this.xhr);\n      this.xhr = null;\n    }\n  }\n\n  onChunk(status: number, data: any) {\n    while (true) {\n      var chunk = this.advanceBuffer(data);\n      if (chunk) {\n        this.emit('chunk', { status: status, data: chunk });\n      } else {\n        break;\n      }\n    }\n    if (this.isBufferTooLong(data)) {\n      this.emit('buffer_too_long');\n    }\n  }\n\n  private advanceBuffer(buffer: any[]): any {\n    var unreadData = buffer.slice(this.position);\n    var endOfLinePosition = unreadData.indexOf('\\n');\n\n    if (endOfLinePosition !== -1) {\n      this.position += endOfLinePosition + 1;\n      return unreadData.slice(0, endOfLinePosition);\n    } else {\n      // chunk is not finished yet, don't move the buffer pointer\n      return null;\n    }\n  }\n\n  private isBufferTooLong(buffer: any): boolean {\n    return this.position === buffer.length && buffer.length > MAX_BUFFER_LENGTH;\n  }\n}\n", "enum State {\n  CONNECTING = 0,\n  OPEN = 1,\n  CLOSED = 3\n}\n\nexport default State;\n", "import URLLocation from './url_location';\nimport State from './state';\nimport Socket from '../socket';\nimport SocketHooks from './socket_hooks';\nimport Util from '../util';\nimport Ajax from './ajax';\nimport HTTPRequest from './http_request';\nimport Runtime from 'runtime';\n\nvar autoIncrement = 1;\n\nclass HTTPSocket implements Socket {\n  hooks: SocketHooks;\n  session: string;\n  location: URLLocation;\n  readyState: State;\n  stream: HTTPRequest;\n\n  onopen: () => void;\n  onerror: (error: any) => void;\n  onclose: (closeEvent: any) => void;\n  onmessage: (message: any) => void;\n  onactivity: () => void;\n\n  constructor(hooks: SocketHooks, url: string) {\n    this.hooks = hooks;\n    this.session = randomNumber(1000) + '/' + randomString(8);\n    this.location = getLocation(url);\n    this.readyState = State.CONNECTING;\n    this.openStream();\n  }\n\n  send(payload: any) {\n    return this.sendRaw(JSON.stringify([payload]));\n  }\n\n  ping() {\n    this.hooks.sendHeartbeat(this);\n  }\n\n  close(code: any, reason: any) {\n    this.onClose(code, reason, true);\n  }\n\n  /** For internal use only */\n  sendRaw(payload: any): boolean {\n    if (this.readyState === State.OPEN) {\n      try {\n        Runtime.createSocketRequest(\n          'POST',\n          getUniqueURL(getSendURL(this.location, this.session))\n        ).start(payload);\n        return true;\n      } catch (e) {\n        return false;\n      }\n    } else {\n      return false;\n    }\n  }\n\n  /** For internal use only */\n  reconnect() {\n    this.closeStream();\n    this.openStream();\n  }\n\n  /** For internal use only */\n  onClose(code, reason, wasClean) {\n    this.closeStream();\n    this.readyState = State.CLOSED;\n    if (this.onclose) {\n      this.onclose({\n        code: code,\n        reason: reason,\n        wasClean: wasClean\n      });\n    }\n  }\n\n  private onChunk(chunk) {\n    if (chunk.status !== 200) {\n      return;\n    }\n    if (this.readyState === State.OPEN) {\n      this.onActivity();\n    }\n\n    var payload;\n    var type = chunk.data.slice(0, 1);\n    switch (type) {\n      case 'o':\n        payload = JSON.parse(chunk.data.slice(1) || '{}');\n        this.onOpen(payload);\n        break;\n      case 'a':\n        payload = JSON.parse(chunk.data.slice(1) || '[]');\n        for (var i = 0; i < payload.length; i++) {\n          this.onEvent(payload[i]);\n        }\n        break;\n      case 'm':\n        payload = JSON.parse(chunk.data.slice(1) || 'null');\n        this.onEvent(payload);\n        break;\n      case 'h':\n        this.hooks.onHeartbeat(this);\n        break;\n      case 'c':\n        payload = JSON.parse(chunk.data.slice(1) || '[]');\n        this.onClose(payload[0], payload[1], true);\n        break;\n    }\n  }\n\n  private onOpen(options) {\n    if (this.readyState === State.CONNECTING) {\n      if (options && options.hostname) {\n        this.location.base = replaceHost(this.location.base, options.hostname);\n      }\n      this.readyState = State.OPEN;\n\n      if (this.onopen) {\n        this.onopen();\n      }\n    } else {\n      this.onClose(1006, 'Server lost session', true);\n    }\n  }\n\n  private onEvent(event) {\n    if (this.readyState === State.OPEN && this.onmessage) {\n      this.onmessage({ data: event });\n    }\n  }\n\n  private onActivity() {\n    if (this.onactivity) {\n      this.onactivity();\n    }\n  }\n\n  private onError(error) {\n    if (this.onerror) {\n      this.onerror(error);\n    }\n  }\n\n  private openStream() {\n    this.stream = Runtime.createSocketRequest(\n      'POST',\n      getUniqueURL(this.hooks.getReceiveURL(this.location, this.session))\n    );\n\n    this.stream.bind('chunk', chunk => {\n      this.onChunk(chunk);\n    });\n    this.stream.bind('finished', status => {\n      this.hooks.onFinished(this, status);\n    });\n    this.stream.bind('buffer_too_long', () => {\n      this.reconnect();\n    });\n\n    try {\n      this.stream.start();\n    } catch (error) {\n      Util.defer(() => {\n        this.onError(error);\n        this.onClose(1006, 'Could not start streaming', false);\n      });\n    }\n  }\n\n  private closeStream() {\n    if (this.stream) {\n      this.stream.unbind_all();\n      this.stream.close();\n      this.stream = null;\n    }\n  }\n}\n\nfunction getLocation(url): URLLocation {\n  var parts = /([^\\?]*)\\/*(\\??.*)/.exec(url);\n  return {\n    base: parts[1],\n    queryString: parts[2]\n  };\n}\n\nfunction getSendURL(url: URLLocation, session: string): string {\n  return url.base + '/' + session + '/xhr_send';\n}\n\nfunction getUniqueURL(url: string): string {\n  var separator = url.indexOf('?') === -1 ? '?' : '&';\n  return url + separator + 't=' + +new Date() + '&n=' + autoIncrement++;\n}\n\nfunction replaceHost(url: string, hostname: string): string {\n  var urlParts = /(https?:\\/\\/)([^\\/:]+)((\\/|:)?.*)/.exec(url);\n  return urlParts[1] + hostname + urlParts[3];\n}\n\nfunction randomNumber(max: number): number {\n  return Runtime.randomInt(max);\n}\n\nfunction randomString(length: number): string {\n  var result = [];\n\n  for (var i = 0; i < length; i++) {\n    result.push(randomNumber(32).toString(32));\n  }\n\n  return result.join('');\n}\n\nexport default HTTPSocket;\n", "import SocketHooks from './socket_hooks';\nimport HTTPSocket from './http_socket';\n\nvar hooks: SocketHooks = {\n  getReceiveURL: function(url, session) {\n    return url.base + '/' + session + '/xhr_streaming' + url.queryString;\n  },\n  onHeartbeat: function(socket) {\n    socket.sendRaw('[]');\n  },\n  sendHeartbeat: function(socket) {\n    socket.sendRaw('[]');\n  },\n  onFinished: function(socket, status) {\n    socket.onClose(1006, 'Connection interrupted (' + status + ')', false);\n  }\n};\n\nexport default hooks;\n", "import SocketHooks from './socket_hooks';\nimport URLLocation from './url_location';\nimport HTTPSocket from './http_socket';\n\nvar hooks: SocketHooks = {\n  getReceiveURL: function(url: URLLocation, session: string): string {\n    return url.base + '/' + session + '/xhr' + url.queryString;\n  },\n  onHeartbeat: function() {\n    // next HTTP request will reset server's activity timer\n  },\n  sendHeartbeat: function(socket) {\n    socket.sendRaw('[]');\n  },\n  onFinished: function(socket, status) {\n    if (status === 200) {\n      socket.reconnect();\n    } else {\n      socket.onClose(1006, 'Connection interrupted (' + status + ')', false);\n    }\n  }\n};\n\nexport default hooks;\n", "import HTTPRequest from 'core/http/http_request';\nimport <PERSON>questHooks from 'core/http/request_hooks';\nimport Ajax from 'core/http/ajax';\nimport Runtime from 'runtime';\n\nvar hooks: RequestHooks = {\n  getRequest: function(socket: HTTPRequest): Ajax {\n    var Constructor = Runtime.getXHRAPI();\n    var xhr = new Constructor();\n    xhr.onreadystatechange = xhr.onprogress = function() {\n      switch (xhr.readyState) {\n        case 3:\n          if (xhr.responseText && xhr.responseText.length > 0) {\n            socket.onChunk(xhr.status, xhr.responseText);\n          }\n          break;\n        case 4:\n          // this happens only on errors, never after calling close\n          if (xhr.responseText && xhr.responseText.length > 0) {\n            socket.onChunk(xhr.status, xhr.responseText);\n          }\n          socket.emit('finished', xhr.status);\n          socket.close();\n          break;\n      }\n    };\n    return xhr;\n  },\n  abortRequest: function(xhr: Ajax) {\n    xhr.onreadystatechange = null;\n    xhr.abort();\n  }\n};\n\nexport default hooks;\n", "import * as Collections from 'core/utils/collections';\nimport Transports from 'isomorphic/transports/transports';\nimport TimelineSender from 'core/timeline/timeline_sender';\nimport Ajax from 'core/http/ajax';\nimport getDefaultStrategy from './default_strategy';\nimport TransportsTable from 'core/transports/transports_table';\nimport transportConnectionInitializer from './transports/transport_connection_initializer';\nimport HTTPFactory from './http/http';\n\nvar Isomorphic: any = {\n  getDefaultStrategy,\n  Transports: <TransportsTable>Transports,\n  transportConnectionInitializer,\n  HTTPFactory,\n\n  setup(PusherClass): void {\n    PusherClass.ready();\n  },\n\n  getLocalStorage(): any {\n    return undefined;\n  },\n\n  getClientFeatures(): any[] {\n    return Collections.keys(\n      Collections.filterObject({ ws: Transports.ws }, function(t) {\n        return t.isSupported({});\n      })\n    );\n  },\n\n  getProtocol(): string {\n    return 'http:';\n  },\n\n  isXHRSupported(): boolean {\n    return true;\n  },\n\n  createSocketRequest(method: string, url: string) {\n    if (this.isXHRSupported()) {\n      return this.HTTPFactory.createXHR(method, url);\n    } else {\n      throw 'Cross-origin HTTP requests are not supported';\n    }\n  },\n\n  createXHR(): Ajax {\n    var Constructor = this.getXHRAPI();\n    return new Constructor();\n  },\n\n  createWebSocket(url: string): any {\n    var Constructor = this.getWebSocketAPI();\n    return new Constructor(url);\n  },\n\n  addUnloadListener(listener: any) {},\n  removeUnloadListener(listener: any) {}\n};\n\nexport default Isomorphic;\n", "/** Initializes the transport.\n *\n * Fetches resources if needed and then transitions to initialized.\n */\nexport default function() {\n  var self = this;\n\n  self.timeline.info(\n    self.buildTimelineMessage({\n      transport: self.name + (self.options.useTLS ? 's' : '')\n    })\n  );\n\n  if (self.hooks.isInitialized()) {\n    self.changeState('initialized');\n  } else {\n    self.onClose();\n  }\n}\n", "import HTTPRequest from 'core/http/http_request';\nimport HTTPSocket from 'core/http/http_socket';\nimport SocketHooks from 'core/http/socket_hooks';\nimport RequestHooks from 'core/http/request_hooks';\nimport streamingHooks from 'core/http/http_streaming_socket';\nimport pollingHooks from 'core/http/http_polling_socket';\nimport xhrHooks from './http_xhr_request';\nimport HTTPFactory from 'core/http/http_factory';\n\nvar HTTP: HTTPFactory = {\n  createStreamingSocket(url: string): HTTPSocket {\n    return this.createSocket(streamingHooks, url);\n  },\n\n  createPollingSocket(url: string): HTTPSocket {\n    return this.createSocket(pollingHooks, url);\n  },\n\n  createSocket(hooks: SocketHooks, url: string): HTTPSocket {\n    return new HTTPSocket(hooks, url);\n  },\n\n  createXHR(method: string, url: string): HTTPRequest {\n    return this.createRequest(xhrHooks, method, url);\n  },\n\n  createRequest(hooks: RequestHooks, method: string, url: string): HTTPRequest {\n    return new HTTPRequest(hooks, method, url);\n  }\n};\n\nexport default HTTP;\n", "import { default as NativeNetInfo } from '@react-native-community/netinfo';\nimport EventsDispatcher from 'core/events/dispatcher';\nimport Util from 'core/util';\nimport Reachability from 'core/reachability';\n\nfunction hasOnlineConnectionState(connectionState): boolean {\n  return connectionState.type.toLowerCase() !== 'none';\n}\n\nexport class NetInfo extends EventsDispatcher implements Reachability {\n  online: boolean;\n\n  constructor() {\n    super();\n    this.online = true;\n\n    NativeNetInfo.fetch().then(connectionState => {\n      this.online = hasOnlineConnectionState(connectionState);\n    });\n\n    NativeNetInfo.addEventListener(connectionState => {\n      var isNowOnline = hasOnlineConnectionState(connectionState);\n\n      // React Native counts the switch from Wi-Fi to Cellular\n      // as a state change. Return if current and previous states\n      // are both online/offline\n      if (this.online === isNowOnline) return;\n      this.online = isNowOnline;\n      if (this.online) {\n        this.emit('online');\n      } else {\n        this.emit('offline');\n      }\n    });\n  }\n\n  isOnline(): boolean {\n    return this.online;\n  }\n}\n\nexport var Network = new NetInfo();\n", "export enum AuthRequestType {\n  UserAuthentication = 'user-authentication',\n  ChannelAuthorization = 'channel-authorization'\n}\n\nexport interface ChannelAuthorizationData {\n  auth: string;\n  channel_data?: string;\n  shared_secret?: string;\n}\n\nexport type ChannelAuthorizationCallback = (\n  error: Error | null,\n  authData: ChannelAuthorizationData | null\n) => void;\n\nexport interface ChannelAuthorizationRequestParams {\n  socketId: string;\n  channelName: string;\n}\n\nexport interface ChannelAuthorizationHandler {\n  (\n    params: ChannelAuthorizationRequestParams,\n    callback: ChannelAuthorizationCallback\n  ): void;\n}\n\nexport interface UserAuthenticationData {\n  auth: string;\n  user_data: string;\n}\n\nexport type UserAuthenticationCallback = (\n  error: Error | null,\n  authData: UserAuthenticationData | null\n) => void;\n\nexport interface UserAuthenticationRequestParams {\n  socketId: string;\n}\n\nexport interface UserAuthenticationHandler {\n  (\n    params: UserAuthenticationRequestParams,\n    callback: UserAuthenticationCallback\n  ): void;\n}\n\nexport type AuthTransportCallback =\n  | ChannelAuthorizationCallback\n  | UserAuthenticationCallback;\n\nexport interface AuthOptionsT<AuthHandler> {\n  transport: 'ajax' | 'jsonp';\n  endpoint: string;\n  params?: any;\n  headers?: any;\n  paramsProvider?: () => any;\n  headersProvider?: () => any;\n  customHandler?: AuthHandler;\n}\n\nexport declare type UserAuthenticationOptions = AuthOptionsT<\n  UserAuthenticationHandler\n>;\nexport declare type ChannelAuthorizationOptions = AuthOptionsT<\n  ChannelAuthorizationHandler\n>;\n\nexport interface InternalAuthOptions {\n  transport: 'ajax' | 'jsonp';\n  endpoint: string;\n  params?: any;\n  headers?: any;\n  paramsProvider?: () => any;\n  headersProvider?: () => any;\n}\n", "import TimelineSender from 'core/timeline/timeline_sender';\nimport * as Collections from 'core/utils/collections';\nimport Util from 'core/util';\nimport Runtime from 'runtime';\nimport { AuthTransport } from 'core/auth/auth_transports';\nimport AbstractRuntime from 'runtimes/interface';\nimport UrlStore from 'core/utils/url_store';\nimport {\n  AuthRequestType,\n  AuthTransportCallback,\n  InternalAuthOptions\n} from 'core/auth/options';\nimport { HTTPAuthError } from 'core/errors';\n\nconst ajax: AuthTransport = function(\n  context: AbstractRuntime,\n  query: string,\n  authOptions: InternalAuthOptions,\n  authRequestType: AuthRequestType,\n  callback: AuthTransportCallback\n) {\n  const xhr = Runtime.createXHR();\n  xhr.open('POST', authOptions.endpoint, true);\n\n  // add request headers\n  xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');\n  for (var headerName in authOptions.headers) {\n    xhr.setRequestHeader(headerName, authOptions.headers[headerName]);\n  }\n  if (authOptions.headersProvider != null) {\n    let dynamicHeaders = authOptions.headersProvider();\n    for (var headerName in dynamicHeaders) {\n      xhr.setRequestHeader(headerName, dynamicHeaders[headerName]);\n    }\n  }\n\n  xhr.onreadystatechange = function() {\n    if (xhr.readyState === 4) {\n      if (xhr.status === 200) {\n        let data;\n        let parsed = false;\n\n        try {\n          data = JSON.parse(xhr.responseText);\n          parsed = true;\n        } catch (e) {\n          callback(\n            new HTTPAuthError(\n              200,\n              `JSON returned from ${authRequestType.toString()} endpoint was invalid, yet status code was 200. Data was: ${\n                xhr.responseText\n              }`\n            ),\n            null\n          );\n        }\n\n        if (parsed) {\n          // prevents double execution.\n          callback(null, data);\n        }\n      } else {\n        let suffix = '';\n        switch (authRequestType) {\n          case AuthRequestType.UserAuthentication:\n            suffix = UrlStore.buildLogSuffix('authenticationEndpoint');\n            break;\n          case AuthRequestType.ChannelAuthorization:\n            suffix = `Clients must be authorized to join private or presence channels. ${UrlStore.buildLogSuffix(\n              'authorizationEndpoint'\n            )}`;\n            break;\n        }\n        callback(\n          new HTTPAuthError(\n            xhr.status,\n            `Unable to retrieve auth string from ${authRequestType.toString()} endpoint - ` +\n              `received status: ${xhr.status} from ${authOptions.endpoint}. ${suffix}`\n          ),\n          null\n        );\n      }\n    }\n  };\n\n  xhr.send(query);\n  return xhr;\n};\n\nexport default ajax;\n", "import Logger from 'core/logger';\nimport TimelineSender from 'core/timeline/timeline_sender';\nimport * as Collections from 'core/utils/collections';\nimport Util from 'core/util';\nimport Runtime from 'runtime';\nimport TimelineTransport from 'core/timeline/timeline_transport';\n\nvar getAgent = function(sender: TimelineSender, useTLS: boolean) {\n  return function(data: any, callback: Function) {\n    var scheme = 'http' + (useTLS ? 's' : '') + '://';\n    var url =\n      scheme + (sender.host || sender.options.host) + sender.options.path;\n    var query = Collections.buildQueryString(data);\n\n    url += '/' + 2 + '?' + query;\n\n    var xhr = Runtime.createXHR();\n    xhr.open('GET', url, true);\n\n    xhr.onreadystatechange = function() {\n      if (xhr.readyState === 4) {\n        let { status, responseText } = xhr;\n        if (status !== 200) {\n          Logger.debug(\n            `TimelineSender Error: received ${status} from stats.pusher.com`\n          );\n          return;\n        }\n\n        try {\n          var { host } = JSON.parse(responseText);\n        } catch (e) {\n          Logger.debug(`TimelineSenderError: invalid response ${responseText}`);\n        }\n        if (host) {\n          sender.host = host;\n        }\n      }\n    };\n\n    xhr.send();\n  };\n};\n\nvar xhr = {\n  name: 'xhr',\n  getAgent\n};\n\nexport default xhr;\n", "import Isomorphic from 'isomorphic/runtime';\nimport Runtime from '../interface';\nimport { Network } from './net_info';\nimport xhrAuth from 'isomorphic/auth/xhr_auth';\nimport { AuthTransports } from 'core/auth/auth_transports';\nimport xhrTimeline from 'isomorphic/timeline/xhr_timeline';\n\n// Very verbose but until unavoidable until\n// TypeScript 2.1, when spread attributes will be\n// supported\nconst {\n  getDefaultStrategy,\n  Transports,\n  setup,\n  getProtocol,\n  isXHRSupported,\n  getLocalStorage,\n  createXHR,\n  createWebSocket,\n  addUnloadListener,\n  removeUnloadListener,\n  transportConnectionInitializer,\n  createSocketRequest,\n  HTTPFactory\n} = Isomorphic;\n\nconst ReactNative: Runtime = {\n  getDefaultStrategy,\n  Transports,\n  setup,\n  getProtocol,\n  isXHRSupported,\n  getLocalStorage,\n  createXHR,\n  createWebSocket,\n  addUnloadListener,\n  removeUnloadListener,\n  transportConnectionInitializer,\n  createSocketRequest,\n  HTTPFactory,\n\n  TimelineTransport: xhrTimeline,\n\n  getAuthorizers(): AuthTransports {\n    return { ajax: xhrAuth };\n  },\n\n  getWebSocketAPI() {\n    return WebSocket;\n  },\n\n  getXHRAPI() {\n    return XMLHttpRequest;\n  },\n\n  getNetwork() {\n    return Network;\n  },\n\n  randomInt(max: number): number {\n    return Math.floor(Math.random() * max);\n  }\n};\n\nexport default ReactNative;\n", "enum TimelineLevel {\n  ERROR = 3,\n  INFO = 6,\n  DEBUG = 7\n}\n\nexport default TimelineLevel;\n", "import * as Collections from '../utils/collections';\nimport Util from '../util';\nimport { default as Level } from './level';\n\nexport interface TimelineOptions {\n  level?: Level;\n  limit?: number;\n  version?: string;\n  cluster?: string;\n  features?: string[];\n  params?: any;\n}\n\nexport default class Timeline {\n  key: string;\n  session: number;\n  events: any[];\n  options: TimelineOptions;\n  sent: number;\n  uniqueID: number;\n\n  constructor(key: string, session: number, options: TimelineOptions) {\n    this.key = key;\n    this.session = session;\n    this.events = [];\n    this.options = options || {};\n    this.sent = 0;\n    this.uniqueID = 0;\n  }\n\n  log(level, event) {\n    if (level <= this.options.level) {\n      this.events.push(\n        Collections.extend({}, event, { timestamp: Util.now() })\n      );\n      if (this.options.limit && this.events.length > this.options.limit) {\n        this.events.shift();\n      }\n    }\n  }\n\n  error(event) {\n    this.log(Level.ERROR, event);\n  }\n\n  info(event) {\n    this.log(Level.INFO, event);\n  }\n\n  debug(event) {\n    this.log(Level.DEBUG, event);\n  }\n\n  isEmpty() {\n    return this.events.length === 0;\n  }\n\n  send(sendfn, callback) {\n    var data = Collections.extend(\n      {\n        session: this.session,\n        bundle: this.sent + 1,\n        key: this.key,\n        lib: 'js',\n        version: this.options.version,\n        cluster: this.options.cluster,\n        features: this.options.features,\n        timeline: this.events\n      },\n      this.options.params\n    );\n\n    this.events = [];\n    sendfn(data, (error, result) => {\n      if (!error) {\n        this.sent++;\n      }\n      if (callback) {\n        callback(error, result);\n      }\n    });\n\n    return true;\n  }\n\n  generateUniqueID(): number {\n    this.uniqueID++;\n    return this.uniqueID;\n  }\n}\n", "import Factory from '../utils/factory';\nimport Util from '../util';\nimport * as Errors from '../errors';\nimport * as Collections from '../utils/collections';\nimport Strategy from './strategy';\nimport Transport from '../transports/transport';\nimport StrategyOptions from './strategy_options';\nimport Handshake from '../connection/handshake';\n\n/** Provides a strategy interface for transports.\n *\n * @param {String} name\n * @param {Number} priority\n * @param {Class} transport\n * @param {Object} options\n */\nexport default class TransportStrategy implements Strategy {\n  name: string;\n  priority: number;\n  transport: Transport;\n  options: StrategyOptions;\n\n  constructor(\n    name: string,\n    priority: number,\n    transport: Transport,\n    options: StrategyOptions\n  ) {\n    this.name = name;\n    this.priority = priority;\n    this.transport = transport;\n    this.options = options || {};\n  }\n\n  /** Returns whether the transport is supported in the browser.\n   *\n   * @returns {Boolean}\n   */\n  isSupported(): boolean {\n    return this.transport.isSupported({\n      useTLS: this.options.useTLS\n    });\n  }\n\n  /** Launches a connection attempt and returns a strategy runner.\n   *\n   * @param  {Function} callback\n   * @return {Object} strategy runner\n   */\n  connect(minPriority: number, callback: Function) {\n    if (!this.isSupported()) {\n      return failAttempt(new Errors.UnsupportedStrategy(), callback);\n    } else if (this.priority < minPriority) {\n      return failAttempt(new Errors.TransportPriorityTooLow(), callback);\n    }\n\n    var connected = false;\n    var transport = this.transport.createConnection(\n      this.name,\n      this.priority,\n      this.options.key,\n      this.options\n    );\n    var handshake = null;\n\n    var onInitialized = function() {\n      transport.unbind('initialized', onInitialized);\n      transport.connect();\n    };\n    var onOpen = function() {\n      handshake = Factory.createHandshake(transport, function(result) {\n        connected = true;\n        unbindListeners();\n        callback(null, result);\n      });\n    };\n    var onError = function(error) {\n      unbindListeners();\n      callback(error);\n    };\n    var onClosed = function() {\n      unbindListeners();\n      var serializedTransport;\n\n      // The reason for this try/catch block is that on React Native\n      // the WebSocket object is circular. Therefore transport.socket will\n      // throw errors upon stringification. Collections.safeJSONStringify\n      // discards circular references when serializing.\n      serializedTransport = Collections.safeJSONStringify(transport);\n      callback(new Errors.TransportClosed(serializedTransport));\n    };\n\n    var unbindListeners = function() {\n      transport.unbind('initialized', onInitialized);\n      transport.unbind('open', onOpen);\n      transport.unbind('error', onError);\n      transport.unbind('closed', onClosed);\n    };\n\n    transport.bind('initialized', onInitialized);\n    transport.bind('open', onOpen);\n    transport.bind('error', onError);\n    transport.bind('closed', onClosed);\n\n    // connect will be called automatically after initialization\n    transport.initialize();\n\n    return {\n      abort: () => {\n        if (connected) {\n          return;\n        }\n        unbindListeners();\n        if (handshake) {\n          handshake.close();\n        } else {\n          transport.close();\n        }\n      },\n      forceMinPriority: p => {\n        if (connected) {\n          return;\n        }\n        if (this.priority < p) {\n          if (handshake) {\n            handshake.close();\n          } else {\n            transport.close();\n          }\n        }\n      }\n    };\n  }\n}\n\nfunction failAttempt(error: Error, callback: Function) {\n  Util.defer(function() {\n    callback(error);\n  });\n  return {\n    abort: function() {},\n    forceMinPriority: function() {}\n  };\n}\n", "import * as Collections from '../utils/collections';\nimport Util from '../util';\nimport TransportManager from '../transports/transport_manager';\nimport * as Errors from '../errors';\nimport Strategy from './strategy';\nimport TransportStrategy from './transport_strategy';\nimport StrategyOptions from '../strategies/strategy_options';\nimport { Config } from '../config';\nimport Runtime from 'runtime';\n\nconst { Transports } = Runtime;\n\nexport var defineTransport = function(\n  config: Config,\n  name: string,\n  type: string,\n  priority: number,\n  options: StrategyOptions,\n  manager?: TransportManager\n): Strategy {\n  var transportClass = Transports[type];\n  if (!transportClass) {\n    throw new Errors.UnsupportedTransport(type);\n  }\n\n  var enabled =\n    (!config.enabledTransports ||\n      Collections.arrayIndexOf(config.enabledTransports, name) !== -1) &&\n    (!config.disabledTransports ||\n      Collections.arrayIndexOf(config.disabledTransports, name) === -1);\n\n  var transport;\n  if (enabled) {\n    options = Object.assign(\n      { ignoreNullOrigin: config.ignoreNullOrigin },\n      options\n    );\n\n    transport = new TransportStrategy(\n      name,\n      priority,\n      manager ? manager.getAssistant(transportClass) : transportClass,\n      options\n    );\n  } else {\n    transport = UnsupportedStrategy;\n  }\n\n  return transport;\n};\n\nvar UnsupportedStrategy: Strategy = {\n  isSupported: function() {\n    return false;\n  },\n  connect: function(_, callback) {\n    var deferred = Util.defer(function() {\n      callback(new Errors.UnsupportedStrategy());\n    });\n    return {\n      abort: function() {\n        deferred.ensureAborted();\n      },\n      forceMinPriority: function() {}\n    };\n  }\n};\n", "import {\n  UserAuthenticationCallback,\n  InternalAuthOptions,\n  UserAuthenticationHandler,\n  UserAuthenticationRequestParams,\n  AuthRequestType\n} from './options';\n\nimport Runtime from 'runtime';\n\nconst composeChannelQuery = (\n  params: UserAuthenticationRequestParams,\n  authOptions: InternalAuthOptions\n) => {\n  var query = 'socket_id=' + encodeURIComponent(params.socketId);\n\n  for (var key in authOptions.params) {\n    query +=\n      '&' +\n      encodeURIComponent(key) +\n      '=' +\n      encodeURIComponent(authOptions.params[key]);\n  }\n\n  if (authOptions.paramsProvider != null) {\n    let dynamicParams = authOptions.paramsProvider();\n    for (var key in dynamicParams) {\n      query +=\n        '&' +\n        encodeURIComponent(key) +\n        '=' +\n        encodeURIComponent(dynamicParams[key]);\n    }\n  }\n\n  return query;\n};\n\nconst UserAuthenticator = (\n  authOptions: InternalAuthOptions\n): UserAuthenticationHandler => {\n  if (typeof Runtime.getAuthorizers()[authOptions.transport] === 'undefined') {\n    throw `'${authOptions.transport}' is not a recognized auth transport`;\n  }\n\n  return (\n    params: UserAuthenticationRequestParams,\n    callback: UserAuthenticationCallback\n  ) => {\n    const query = composeChannelQuery(params, authOptions);\n\n    Runtime.getAuthorizers()[authOptions.transport](\n      Runtime,\n      query,\n      authOptions,\n      AuthRequestType.UserAuthentication,\n      callback\n    );\n  };\n};\n\nexport default UserAuthenticator;\n", "import {\n  AuthRequestType,\n  InternalAuthOptions,\n  ChannelAuthorizationHandler,\n  ChannelAuthorizationRequestParams,\n  ChannelAuthorizationCallback\n} from './options';\n\nimport Runtime from 'runtime';\n\nconst composeChannelQuery = (\n  params: ChannelAuthorizationRequestParams,\n  authOptions: InternalAuthOptions\n) => {\n  var query = 'socket_id=' + encodeURIComponent(params.socketId);\n\n  query += '&channel_name=' + encodeURIComponent(params.channelName);\n\n  for (var key in authOptions.params) {\n    query +=\n      '&' +\n      encodeURIComponent(key) +\n      '=' +\n      encodeURIComponent(authOptions.params[key]);\n  }\n\n  if (authOptions.paramsProvider != null) {\n    let dynamicParams = authOptions.paramsProvider();\n    for (var key in dynamicParams) {\n      query +=\n        '&' +\n        encodeURIComponent(key) +\n        '=' +\n        encodeURIComponent(dynamicParams[key]);\n    }\n  }\n\n  return query;\n};\n\nconst ChannelAuthorizer = (\n  authOptions: InternalAuthOptions\n): ChannelAuthorizationHandler => {\n  if (typeof Runtime.getAuthorizers()[authOptions.transport] === 'undefined') {\n    throw `'${authOptions.transport}' is not a recognized auth transport`;\n  }\n\n  return (\n    params: ChannelAuthorizationRequestParams,\n    callback: ChannelAuthorizationCallback\n  ) => {\n    const query = composeChannelQuery(params, authOptions);\n\n    Runtime.getAuthorizers()[authOptions.transport](\n      Runtime,\n      query,\n      authOptions,\n      AuthRequestType.ChannelAuthorization,\n      callback\n    );\n  };\n};\n\nexport default ChannelAuthorizer;\n", "import { Options } from './options';\nimport Defaults from './defaults';\nimport {\n  Channel<PERSON>uth<PERSON><PERSON><PERSON><PERSON><PERSON>,\n  UserAuthenticationHandler,\n  ChannelAuthorizationOptions\n} from './auth/options';\nimport UserAuthenticator from './auth/user_authenticator';\nimport ChannelAuthorizer from './auth/channel_authorizer';\nimport { ChannelAuthorizerProxy } from './auth/deprecated_channel_authorizer';\nimport Runtime from 'runtime';\nimport * as nacl from 'tweetnacl';\n\nexport type AuthTransport = 'ajax' | 'jsonp';\nexport type Transport =\n  | 'ws'\n  | 'wss'\n  | 'xhr_streaming'\n  | 'xhr_polling'\n  | 'sockjs';\n\nexport interface Config {\n  // these are all 'required' config parameters, it's not necessary for the user\n  // to set them, but they have configured defaults.\n  activityTimeout: number;\n  enableStats: boolean;\n  httpHost: string;\n  httpPath: string;\n  httpPort: number;\n  httpsPort: number;\n  pongTimeout: number;\n  statsHost: string;\n  unavailableTimeout: number;\n  useTLS: boolean;\n  wsHost: string;\n  wsPath: string;\n  wsPort: number;\n  wssPort: number;\n  userAuthenticator: UserAuthenticationHandler;\n  channelAuthorizer: ChannelAuthorizationHandler;\n\n  // these are all optional parameters or overrrides. The customer can set these\n  // but it's not strictly necessary\n  forceTLS?: boolean;\n  cluster?: string;\n  disabledTransports?: Transport[];\n  enabledTransports?: Transport[];\n  ignoreNullOrigin?: boolean;\n  nacl?: nacl;\n  timelineParams?: any;\n}\n\n// getConfig mainly sets the defaults for the options that are not provided\nexport function getConfig(opts: Options, pusher): Config {\n  let config: Config = {\n    activityTimeout: opts.activityTimeout || Defaults.activityTimeout,\n    cluster: opts.cluster,\n    httpPath: opts.httpPath || Defaults.httpPath,\n    httpPort: opts.httpPort || Defaults.httpPort,\n    httpsPort: opts.httpsPort || Defaults.httpsPort,\n    pongTimeout: opts.pongTimeout || Defaults.pongTimeout,\n    statsHost: opts.statsHost || Defaults.stats_host,\n    unavailableTimeout: opts.unavailableTimeout || Defaults.unavailableTimeout,\n    wsPath: opts.wsPath || Defaults.wsPath,\n    wsPort: opts.wsPort || Defaults.wsPort,\n    wssPort: opts.wssPort || Defaults.wssPort,\n\n    enableStats: getEnableStatsConfig(opts),\n    httpHost: getHttpHost(opts),\n    useTLS: shouldUseTLS(opts),\n    wsHost: getWebsocketHost(opts),\n\n    userAuthenticator: buildUserAuthenticator(opts),\n    channelAuthorizer: buildChannelAuthorizer(opts, pusher)\n  };\n\n  if ('disabledTransports' in opts)\n    config.disabledTransports = opts.disabledTransports;\n  if ('enabledTransports' in opts)\n    config.enabledTransports = opts.enabledTransports;\n  if ('ignoreNullOrigin' in opts)\n    config.ignoreNullOrigin = opts.ignoreNullOrigin;\n  if ('timelineParams' in opts) config.timelineParams = opts.timelineParams;\n  if ('nacl' in opts) {\n    config.nacl = opts.nacl;\n  }\n\n  return config;\n}\n\nfunction getHttpHost(opts: Options): string {\n  if (opts.httpHost) {\n    return opts.httpHost;\n  }\n  if (opts.cluster) {\n    return `sockjs-${opts.cluster}.pusher.com`;\n  }\n  return Defaults.httpHost;\n}\n\nfunction getWebsocketHost(opts: Options): string {\n  if (opts.wsHost) {\n    return opts.wsHost;\n  }\n  return getWebsocketHostFromCluster(opts.cluster);\n}\n\nfunction getWebsocketHostFromCluster(cluster: string): string {\n  return `ws-${cluster}.pusher.com`;\n}\n\nfunction shouldUseTLS(opts: Options): boolean {\n  if (Runtime.getProtocol() === 'https:') {\n    return true;\n  } else if (opts.forceTLS === false) {\n    return false;\n  }\n  return true;\n}\n\n// if enableStats is set take the value\n// if disableStats is set take the inverse\n// otherwise default to false\nfunction getEnableStatsConfig(opts: Options): boolean {\n  if ('enableStats' in opts) {\n    return opts.enableStats;\n  }\n  if ('disableStats' in opts) {\n    return !opts.disableStats;\n  }\n  return false;\n}\n\nfunction buildUserAuthenticator(opts: Options): UserAuthenticationHandler {\n  const userAuthentication = {\n    ...Defaults.userAuthentication,\n    ...opts.userAuthentication\n  };\n  if (\n    'customHandler' in userAuthentication &&\n    userAuthentication['customHandler'] != null\n  ) {\n    return userAuthentication['customHandler'];\n  }\n\n  return UserAuthenticator(userAuthentication);\n}\n\nfunction buildChannelAuth(opts: Options, pusher): ChannelAuthorizationOptions {\n  let channelAuthorization: ChannelAuthorizationOptions;\n  if ('channelAuthorization' in opts) {\n    channelAuthorization = {\n      ...Defaults.channelAuthorization,\n      ...opts.channelAuthorization\n    };\n  } else {\n    channelAuthorization = {\n      transport: opts.authTransport || Defaults.authTransport,\n      endpoint: opts.authEndpoint || Defaults.authEndpoint\n    };\n    if ('auth' in opts) {\n      if ('params' in opts.auth) channelAuthorization.params = opts.auth.params;\n      if ('headers' in opts.auth)\n        channelAuthorization.headers = opts.auth.headers;\n    }\n    if ('authorizer' in opts)\n      channelAuthorization.customHandler = ChannelAuthorizerProxy(\n        pusher,\n        channelAuthorization,\n        opts.authorizer\n      );\n  }\n  return channelAuthorization;\n}\n\nfunction buildChannelAuthorizer(\n  opts: Options,\n  pusher\n): ChannelAuthorizationHandler {\n  const channelAuthorization = buildChannelAuth(opts, pusher);\n  if (\n    'customHandler' in channelAuthorization &&\n    channelAuthorization['customHandler'] != null\n  ) {\n    return channelAuthorization['customHandler'];\n  }\n\n  return ChannelAuthorizer(channelAuthorization);\n}\n", "import Channel from '../channels/channel';\nimport {\n  ChannelAuthorizationCallback,\n  ChannelAuthorizationHandler,\n  ChannelAuthorizationRequestParams,\n  InternalAuthOptions\n} from './options';\n\nexport interface DeprecatedChannelAuthorizer {\n  authorize(socketId: string, callback: ChannelAuthorizationCallback): void;\n}\n\nexport interface ChannelAuthorizerGenerator {\n  (\n    channel: Channel,\n    options: DeprecatedAuthorizerOptions\n  ): DeprecatedChannelAuthorizer;\n}\n\nexport interface DeprecatedAuthOptions {\n  params?: any;\n  headers?: any;\n}\n\nexport interface DeprecatedAuthorizerOptions {\n  authTransport: 'ajax' | 'jsonp';\n  authEndpoint: string;\n  auth?: DeprecatedAuthOptions;\n}\n\nexport const ChannelAuthorizerProxy = (\n  pusher,\n  authOptions: InternalAuthOptions,\n  channelAuthorizerGenerator: ChannelAuthorizerGenerator\n): ChannelAuthorizationHandler => {\n  const deprecatedAuthorizerOptions: DeprecatedAuthorizerOptions = {\n    authTransport: authOptions.transport,\n    authEndpoint: authOptions.endpoint,\n    auth: {\n      params: authOptions.params,\n      headers: authOptions.headers\n    }\n  };\n  return (\n    params: ChannelAuthorizationRequestParams,\n    callback: ChannelAuthorizationCallback\n  ) => {\n    const channel = pusher.channel(params.channelName);\n    // This line creates a new channel authorizer every time.\n    // In the past, this was only done once per channel and reused.\n    // We can do that again if we want to keep this behavior intact.\n    const channelAuthorizer: DeprecatedChannelAuthorizer = channelAuthorizerGenerator(\n      channel,\n      deprecatedAuthorizerOptions\n    );\n    channelAuthorizer.authorize(params.socketId, callback);\n  };\n};\n", "import Logger from './logger';\nimport Pusher from './pusher';\nimport EventsDispatcher from './events/dispatcher';\n\nexport default class WatchlistFacade extends EventsDispatcher {\n  private pusher: Pusher;\n\n  public constructor(pusher: Pusher) {\n    super(function(eventName, data) {\n      Logger.debug(`No callbacks on watchlist events for ${eventName}`);\n    });\n\n    this.pusher = pusher;\n    this.bindWatchlistInternalEvent();\n  }\n\n  handleEvent(pusherEvent) {\n    pusherEvent.data.events.forEach(watchlistEvent => {\n      this.emit(watchlistEvent.name, watchlistEvent);\n    });\n  }\n\n  private bindWatchlistInternalEvent() {\n    this.pusher.connection.bind('message', pusherEvent => {\n      var eventName = pusherEvent.event;\n      if (eventName === 'pusher_internal:watchlist_events') {\n        this.handleEvent(pusherEvent);\n      }\n    });\n  }\n}\n", "function flatPromise() {\n  let resolve, reject;\n  const promise = new Promise((res, rej) => {\n    resolve = res;\n    reject = rej;\n  });\n  return { promise, resolve, reject };\n}\n\nexport default flatPromise;\n", "import Pusher from './pusher';\nimport Logger from './logger';\nimport {\n  UserAuthenticationData,\n  UserAuthenticationCallback\n} from './auth/options';\nimport Channel from './channels/channel';\nimport WatchlistFacade from './watchlist';\nimport EventsDispatcher from './events/dispatcher';\nimport flatPromise from './utils/flat_promise';\n\nexport default class UserFacade extends EventsDispatcher {\n  pusher: Pusher;\n  signin_requested: boolean = false;\n  user_data: any = null;\n  serverToUserChannel: Channel = null;\n  signinDonePromise: Promise<any> = null;\n  watchlist: WatchlistFacade;\n  private _signinDoneResolve: Function = null;\n\n  public constructor(pusher: Pusher) {\n    super(function(eventName, data) {\n      Logger.debug('No callbacks on user for ' + eventName);\n    });\n    this.pusher = pusher;\n    this.pusher.connection.bind('state_change', ({ previous, current }) => {\n      if (previous !== 'connected' && current === 'connected') {\n        this._signin();\n      }\n      if (previous === 'connected' && current !== 'connected') {\n        this._cleanup();\n        this._newSigninPromiseIfNeeded();\n      }\n    });\n\n    this.watchlist = new WatchlistFacade(pusher);\n\n    this.pusher.connection.bind('message', event => {\n      var eventName = event.event;\n      if (eventName === 'pusher:signin_success') {\n        this._onSigninSuccess(event.data);\n      }\n      if (\n        this.serverToUserChannel &&\n        this.serverToUserChannel.name === event.channel\n      ) {\n        this.serverToUserChannel.handleEvent(event);\n      }\n    });\n  }\n\n  public signin() {\n    if (this.signin_requested) {\n      return;\n    }\n\n    this.signin_requested = true;\n    this._signin();\n  }\n\n  private _signin() {\n    if (!this.signin_requested) {\n      return;\n    }\n\n    this._newSigninPromiseIfNeeded();\n\n    if (this.pusher.connection.state !== 'connected') {\n      // Signin will be attempted when the connection is connected\n      return;\n    }\n\n    this.pusher.config.userAuthenticator(\n      {\n        socketId: this.pusher.connection.socket_id\n      },\n      this._onAuthorize\n    );\n  }\n\n  private _onAuthorize: UserAuthenticationCallback = (\n    err,\n    authData: UserAuthenticationData\n  ) => {\n    if (err) {\n      Logger.warn(`Error during signin: ${err}`);\n      this._cleanup();\n      return;\n    }\n\n    this.pusher.send_event('pusher:signin', {\n      auth: authData.auth,\n      user_data: authData.user_data\n    });\n\n    // Later when we get pusher:singin_success event, the user will be marked as signed in\n  };\n\n  private _onSigninSuccess(data: any) {\n    try {\n      this.user_data = JSON.parse(data.user_data);\n    } catch (e) {\n      Logger.error(`Failed parsing user data after signin: ${data.user_data}`);\n      this._cleanup();\n      return;\n    }\n\n    if (typeof this.user_data.id !== 'string' || this.user_data.id === '') {\n      Logger.error(\n        `user_data doesn't contain an id. user_data: ${this.user_data}`\n      );\n      this._cleanup();\n      return;\n    }\n\n    // Signin succeeded\n    this._signinDoneResolve();\n    this._subscribeChannels();\n  }\n\n  private _subscribeChannels() {\n    const ensure_subscribed = channel => {\n      if (channel.subscriptionPending && channel.subscriptionCancelled) {\n        channel.reinstateSubscription();\n      } else if (\n        !channel.subscriptionPending &&\n        this.pusher.connection.state === 'connected'\n      ) {\n        channel.subscribe();\n      }\n    };\n\n    this.serverToUserChannel = new Channel(\n      `#server-to-user-${this.user_data.id}`,\n      this.pusher\n    );\n    this.serverToUserChannel.bind_global((eventName, data) => {\n      if (\n        eventName.indexOf('pusher_internal:') === 0 ||\n        eventName.indexOf('pusher:') === 0\n      ) {\n        // ignore internal events\n        return;\n      }\n      this.emit(eventName, data);\n    });\n    ensure_subscribed(this.serverToUserChannel);\n  }\n\n  private _cleanup() {\n    this.user_data = null;\n    if (this.serverToUserChannel) {\n      this.serverToUserChannel.unbind_all();\n      this.serverToUserChannel.disconnect();\n      this.serverToUserChannel = null;\n    }\n\n    if (this.signin_requested) {\n      // If signin is in progress and cleanup is called,\n      // Mark the current signin process as done.\n      this._signinDoneResolve();\n    }\n  }\n\n  private _newSigninPromiseIfNeeded() {\n    if (!this.signin_requested) {\n      return;\n    }\n\n    // If there is a promise and it is not resolved, return without creating a new one.\n    if (this.signinDonePromise && !(this.signinDonePromise as any).done) {\n      return;\n    }\n\n    // This promise is never rejected.\n    // It gets resolved when the signin process is done whether it failed or succeeded\n    const { promise, resolve, reject: _ } = flatPromise();\n    (promise as any).done = false;\n    const setDone = () => {\n      (promise as any).done = true;\n    };\n    promise.then(setDone).catch(setDone);\n    this.signinDonePromise = promise;\n    this._signinDoneResolve = resolve;\n  }\n}\n", "import AbstractRuntime from '../runtimes/interface';\nimport Runtime from 'runtime';\nimport Util from './util';\nimport * as Collections from './utils/collections';\nimport Channels from './channels/channels';\nimport Channel from './channels/channel';\nimport { default as EventsDispatcher } from './events/dispatcher';\nimport Timeline from './timeline/timeline';\nimport TimelineSender from './timeline/timeline_sender';\nimport TimelineLevel from './timeline/level';\nimport { defineTransport } from './strategies/strategy_builder';\nimport ConnectionManager from './connection/connection_manager';\nimport ConnectionManagerOptions from './connection/connection_manager_options';\nimport { PeriodicTimer } from './utils/timers';\nimport Defaults from './defaults';\nimport * as DefaultConfig from './config';\nimport Logger from './logger';\nimport Factory from './utils/factory';\nimport UrlStore from 'core/utils/url_store';\nimport { Options, validateOptions } from './options';\nimport { Config, getConfig } from './config';\nimport StrategyOptions from './strategies/strategy_options';\nimport UserFacade from './user';\n\nexport default class Pusher {\n  /*  STATIC PROPERTIES */\n  static instances: Pusher[] = [];\n  static isReady: boolean = false;\n  static logToConsole: boolean = false;\n\n  // for jsonp\n  static Runtime: AbstractRuntime = Runtime;\n  static ScriptReceivers: any = (<any>Runtime).ScriptReceivers;\n  static DependenciesReceivers: any = (<any>Runtime).DependenciesReceivers;\n  static auth_callbacks: any = (<any>Runtime).auth_callbacks;\n\n  static ready() {\n    Pusher.isReady = true;\n    for (var i = 0, l = Pusher.instances.length; i < l; i++) {\n      Pusher.instances[i].connect();\n    }\n  }\n\n  static log: (message: any) => void;\n\n  private static getClientFeatures(): string[] {\n    return Collections.keys(\n      Collections.filterObject({ ws: Runtime.Transports.ws }, function(t) {\n        return t.isSupported({});\n      })\n    );\n  }\n\n  /* INSTANCE PROPERTIES */\n  key: string;\n  config: Config;\n  channels: Channels;\n  global_emitter: EventsDispatcher;\n  sessionID: number;\n  timeline: Timeline;\n  timelineSender: TimelineSender;\n  connection: ConnectionManager;\n  timelineSenderTimer: PeriodicTimer;\n  user: UserFacade;\n  constructor(app_key: string, options: Options) {\n    checkAppKey(app_key);\n    validateOptions(options);\n    this.key = app_key;\n    this.config = getConfig(options, this);\n\n    this.channels = Factory.createChannels();\n    this.global_emitter = new EventsDispatcher();\n    this.sessionID = Runtime.randomInt(1000000000);\n\n    this.timeline = new Timeline(this.key, this.sessionID, {\n      cluster: this.config.cluster,\n      features: Pusher.getClientFeatures(),\n      params: this.config.timelineParams || {},\n      limit: 50,\n      level: TimelineLevel.INFO,\n      version: Defaults.VERSION\n    });\n    if (this.config.enableStats) {\n      this.timelineSender = Factory.createTimelineSender(this.timeline, {\n        host: this.config.statsHost,\n        path: '/timeline/v2/' + Runtime.TimelineTransport.name\n      });\n    }\n\n    var getStrategy = (options: StrategyOptions) => {\n      return Runtime.getDefaultStrategy(this.config, options, defineTransport);\n    };\n\n    this.connection = Factory.createConnectionManager(this.key, {\n      getStrategy: getStrategy,\n      timeline: this.timeline,\n      activityTimeout: this.config.activityTimeout,\n      pongTimeout: this.config.pongTimeout,\n      unavailableTimeout: this.config.unavailableTimeout,\n      useTLS: Boolean(this.config.useTLS)\n    });\n\n    this.connection.bind('connected', () => {\n      this.subscribeAll();\n      if (this.timelineSender) {\n        this.timelineSender.send(this.connection.isUsingTLS());\n      }\n    });\n\n    this.connection.bind('message', event => {\n      var eventName = event.event;\n      var internal = eventName.indexOf('pusher_internal:') === 0;\n      if (event.channel) {\n        var channel = this.channel(event.channel);\n        if (channel) {\n          channel.handleEvent(event);\n        }\n      }\n      // Emit globally [deprecated]\n      if (!internal) {\n        this.global_emitter.emit(event.event, event.data);\n      }\n    });\n    this.connection.bind('connecting', () => {\n      this.channels.disconnect();\n    });\n    this.connection.bind('disconnected', () => {\n      this.channels.disconnect();\n    });\n    this.connection.bind('error', err => {\n      Logger.warn(err);\n    });\n\n    Pusher.instances.push(this);\n    this.timeline.info({ instances: Pusher.instances.length });\n\n    this.user = new UserFacade(this);\n\n    if (Pusher.isReady) {\n      this.connect();\n    }\n  }\n\n  channel(name: string): Channel {\n    return this.channels.find(name);\n  }\n\n  allChannels(): Channel[] {\n    return this.channels.all();\n  }\n\n  connect() {\n    this.connection.connect();\n\n    if (this.timelineSender) {\n      if (!this.timelineSenderTimer) {\n        var usingTLS = this.connection.isUsingTLS();\n        var timelineSender = this.timelineSender;\n        this.timelineSenderTimer = new PeriodicTimer(60000, function() {\n          timelineSender.send(usingTLS);\n        });\n      }\n    }\n  }\n\n  disconnect() {\n    this.connection.disconnect();\n\n    if (this.timelineSenderTimer) {\n      this.timelineSenderTimer.ensureAborted();\n      this.timelineSenderTimer = null;\n    }\n  }\n\n  bind(event_name: string, callback: Function, context?: any): Pusher {\n    this.global_emitter.bind(event_name, callback, context);\n    return this;\n  }\n\n  unbind(event_name?: string, callback?: Function, context?: any): Pusher {\n    this.global_emitter.unbind(event_name, callback, context);\n    return this;\n  }\n\n  bind_global(callback: Function): Pusher {\n    this.global_emitter.bind_global(callback);\n    return this;\n  }\n\n  unbind_global(callback?: Function): Pusher {\n    this.global_emitter.unbind_global(callback);\n    return this;\n  }\n\n  unbind_all(callback?: Function): Pusher {\n    this.global_emitter.unbind_all();\n    return this;\n  }\n\n  subscribeAll() {\n    var channelName;\n    for (channelName in this.channels.channels) {\n      if (this.channels.channels.hasOwnProperty(channelName)) {\n        this.subscribe(channelName);\n      }\n    }\n  }\n\n  subscribe(channel_name: string) {\n    var channel = this.channels.add(channel_name, this);\n    if (channel.subscriptionPending && channel.subscriptionCancelled) {\n      channel.reinstateSubscription();\n    } else if (\n      !channel.subscriptionPending &&\n      this.connection.state === 'connected'\n    ) {\n      channel.subscribe();\n    }\n    return channel;\n  }\n\n  unsubscribe(channel_name: string) {\n    var channel = this.channels.find(channel_name);\n    if (channel && channel.subscriptionPending) {\n      channel.cancelSubscription();\n    } else {\n      channel = this.channels.remove(channel_name);\n      if (channel && channel.subscribed) {\n        channel.unsubscribe();\n      }\n    }\n  }\n\n  send_event(event_name: string, data: any, channel?: string) {\n    return this.connection.send_event(event_name, data, channel);\n  }\n\n  shouldUseTLS(): boolean {\n    return this.config.useTLS;\n  }\n\n  signin() {\n    this.user.signin();\n  }\n}\n\nfunction checkAppKey(key) {\n  if (key === null || key === undefined) {\n    throw 'You must pass your app key when you instantiate Pusher.';\n  }\n}\n\nRuntime.setup(Pusher);\n", "var g;\n\n// This works in non-strict mode\ng = (function() {\n\treturn this;\n})();\n\ntry {\n\t// This works if eval is allowed (see CSP)\n\tg = g || new Function(\"return this\")();\n} catch (e) {\n\t// This works if the window reference is available\n\tif (typeof window === \"object\") g = window;\n}\n\n// g can still be undefined, but nothing to do about it...\n// We return undefined, instead of nothing here, so it's\n// easier to handle this case. if(!global) { ...}\n\nmodule.exports = g;\n", "import ConnectionManager from './connection/connection_manager';\nimport {\n  ChannelAuthorizationOptions,\n  UserAuthenticationOptions\n} from './auth/options';\nimport {\n  ChannelAuthorizerGenerator,\n  DeprecatedAuthOptions\n} from './auth/deprecated_channel_authorizer';\nimport { AuthTransport, Transport } from './config';\nimport * as nacl from 'tweetnacl';\nimport Logger from './logger';\n\nexport interface Options {\n  activityTimeout?: number;\n\n  auth?: DeprecatedAuthOptions; // DEPRECATED use channelAuthorization instead\n  authEndpoint?: string; // DEPRECATED use channelAuthorization instead\n  authTransport?: AuthTransport; // DEPRECATED use channelAuthorization instead\n  authorizer?: ChannelAuthorizerGenerator; // DEPRECATED use channelAuthorization instead\n\n  channelAuthorization?: ChannelAuthorizationOptions;\n  userAuthentication?: UserAuthenticationOptions;\n\n  cluster: string;\n  enableStats?: boolean;\n  disableStats?: boolean;\n  disabledTransports?: Transport[];\n  enabledTransports?: Transport[];\n  forceTLS?: boolean;\n  httpHost?: string;\n  httpPath?: string;\n  httpPort?: number;\n  httpsPort?: number;\n  ignoreNullOrigin?: boolean;\n  nacl?: nacl;\n  pongTimeout?: number;\n  statsHost?: string;\n  timelineParams?: any;\n  unavailableTimeout?: number;\n  wsHost?: string;\n  wsPath?: string;\n  wsPort?: number;\n  wssPort?: number;\n}\n\nexport function validateOptions(options) {\n  if (options == null) {\n    throw 'You must pass an options object';\n  }\n  if (options.cluster == null) {\n    throw 'Options object must provide a cluster';\n  }\n  if ('disableStats' in options) {\n    Logger.warn(\n      'The disableStats option is deprecated in favor of enableStats'\n    );\n  }\n}\n", "// Copyright (C) 2016 <PERSON>\n// MIT License. See LICENSE file for details.\n\n/**\n * Package base64 implements Base64 encoding and decoding.\n */\n\n// Invalid character used in decoding to indicate\n// that the character to decode is out of range of\n// alphabet and cannot be decoded.\nconst INVALID_BYTE = 256;\n\n/**\n * Implements standard Base64 encoding.\n *\n * Operates in constant time.\n */\nexport class Coder {\n    // TODO(dchest): methods to encode chunk-by-chunk.\n\n    constructor(private _padding<PERSON>haracter = \"=\") { }\n\n    encodedLength(length: number): number {\n        if (!this._padding<PERSON>haracter) {\n            return (length * 8 + 5) / 6 | 0;\n        }\n        return (length + 2) / 3 * 4 | 0;\n    }\n\n    encode(data: Uint8Array): string {\n        let out = \"\";\n\n        let i = 0;\n        for (; i < data.length - 2; i += 3) {\n            let c = (data[i] << 16) | (data[i + 1] << 8) | (data[i + 2]);\n            out += this._encodeByte((c >>> 3 * 6) & 63);\n            out += this._encodeByte((c >>> 2 * 6) & 63);\n            out += this._encodeByte((c >>> 1 * 6) & 63);\n            out += this._encodeByte((c >>> 0 * 6) & 63);\n        }\n\n        const left = data.length - i;\n        if (left > 0) {\n            let c = (data[i] << 16) | (left === 2 ? data[i + 1] << 8 : 0);\n            out += this._encodeByte((c >>> 3 * 6) & 63);\n            out += this._encodeByte((c >>> 2 * 6) & 63);\n            if (left === 2) {\n                out += this._encodeByte((c >>> 1 * 6) & 63);\n            } else {\n                out += this._paddingCharacter || \"\";\n            }\n            out += this._paddingCharacter || \"\";\n        }\n\n        return out;\n    }\n\n    maxDecodedLength(length: number): number {\n        if (!this._paddingCharacter) {\n            return (length * 6 + 7) / 8 | 0;\n        }\n        return length / 4 * 3 | 0;\n    }\n\n    decodedLength(s: string): number {\n        return this.maxDecodedLength(s.length - this._getPaddingLength(s));\n    }\n\n    decode(s: string): Uint8Array {\n        if (s.length === 0) {\n            return new Uint8Array(0);\n        }\n        const paddingLength = this._getPaddingLength(s);\n        const length = s.length - paddingLength;\n        const out = new Uint8Array(this.maxDecodedLength(length));\n        let op = 0;\n        let i = 0;\n        let haveBad = 0;\n        let v0 = 0, v1 = 0, v2 = 0, v3 = 0;\n        for (; i < length - 4; i += 4) {\n            v0 = this._decodeChar(s.charCodeAt(i + 0));\n            v1 = this._decodeChar(s.charCodeAt(i + 1));\n            v2 = this._decodeChar(s.charCodeAt(i + 2));\n            v3 = this._decodeChar(s.charCodeAt(i + 3));\n            out[op++] = (v0 << 2) | (v1 >>> 4);\n            out[op++] = (v1 << 4) | (v2 >>> 2);\n            out[op++] = (v2 << 6) | v3;\n            haveBad |= v0 & INVALID_BYTE;\n            haveBad |= v1 & INVALID_BYTE;\n            haveBad |= v2 & INVALID_BYTE;\n            haveBad |= v3 & INVALID_BYTE;\n        }\n        if (i < length - 1) {\n            v0 = this._decodeChar(s.charCodeAt(i));\n            v1 = this._decodeChar(s.charCodeAt(i + 1));\n            out[op++] = (v0 << 2) | (v1 >>> 4);\n            haveBad |= v0 & INVALID_BYTE;\n            haveBad |= v1 & INVALID_BYTE;\n        }\n        if (i < length - 2) {\n            v2 = this._decodeChar(s.charCodeAt(i + 2));\n            out[op++] = (v1 << 4) | (v2 >>> 2);\n            haveBad |= v2 & INVALID_BYTE;\n        }\n        if (i < length - 3) {\n            v3 = this._decodeChar(s.charCodeAt(i + 3));\n            out[op++] = (v2 << 6) | v3;\n            haveBad |= v3 & INVALID_BYTE;\n        }\n        if (haveBad !== 0) {\n            throw new Error(\"Base64Coder: incorrect characters for decoding\");\n        }\n        return out;\n    }\n\n    // Standard encoding have the following encoded/decoded ranges,\n    // which we need to convert between.\n    //\n    // ABCDEFGHIJKLMNOPQRSTUVWXYZ abcdefghijklmnopqrstuvwxyz 0123456789  +   /\n    // Index:   0 - 25                    26 - 51              52 - 61   62  63\n    // ASCII:  65 - 90                    97 - 122             48 - 57   43  47\n    //\n\n    // Encode 6 bits in b into a new character.\n    protected _encodeByte(b: number): string {\n        // Encoding uses constant time operations as follows:\n        //\n        // 1. Define comparison of A with B using (A - B) >>> 8:\n        //          if A > B, then result is positive integer\n        //          if A <= B, then result is 0\n        //\n        // 2. Define selection of C or 0 using bitwise AND: X & C:\n        //          if X == 0, then result is 0\n        //          if X != 0, then result is C\n        //\n        // 3. Start with the smallest comparison (b >= 0), which is always\n        //    true, so set the result to the starting ASCII value (65).\n        //\n        // 4. Continue comparing b to higher ASCII values, and selecting\n        //    zero if comparison isn't true, otherwise selecting a value\n        //    to add to result, which:\n        //\n        //          a) undoes the previous addition\n        //          b) provides new value to add\n        //\n        let result = b;\n        // b >= 0\n        result += 65;\n        // b > 25\n        result += ((25 - b) >>> 8) & ((0 - 65) - 26 + 97);\n        // b > 51\n        result += ((51 - b) >>> 8) & ((26 - 97) - 52 + 48);\n        // b > 61\n        result += ((61 - b) >>> 8) & ((52 - 48) - 62 + 43);\n        // b > 62\n        result += ((62 - b) >>> 8) & ((62 - 43) - 63 + 47);\n\n        return String.fromCharCode(result);\n    }\n\n    // Decode a character code into a byte.\n    // Must return 256 if character is out of alphabet range.\n    protected _decodeChar(c: number): number {\n        // Decoding works similar to encoding: using the same comparison\n        // function, but now it works on ranges: result is always incremented\n        // by value, but this value becomes zero if the range is not\n        // satisfied.\n        //\n        // Decoding starts with invalid value, 256, which is then\n        // subtracted when the range is satisfied. If none of the ranges\n        // apply, the function returns 256, which is then checked by\n        // the caller to throw error.\n        let result = INVALID_BYTE; // start with invalid character\n\n        // c == 43 (c > 42 and c < 44)\n        result += (((42 - c) & (c - 44)) >>> 8) & (-INVALID_BYTE + c - 43 + 62);\n        // c == 47 (c > 46 and c < 48)\n        result += (((46 - c) & (c - 48)) >>> 8) & (-INVALID_BYTE + c - 47 + 63);\n        // c > 47 and c < 58\n        result += (((47 - c) & (c - 58)) >>> 8) & (-INVALID_BYTE + c - 48 + 52);\n        // c > 64 and c < 91\n        result += (((64 - c) & (c - 91)) >>> 8) & (-INVALID_BYTE + c - 65 + 0);\n        // c > 96 and c < 123\n        result += (((96 - c) & (c - 123)) >>> 8) & (-INVALID_BYTE + c - 97 + 26);\n\n        return result;\n    }\n\n    private _getPaddingLength(s: string): number {\n        let paddingLength = 0;\n        if (this._paddingCharacter) {\n            for (let i = s.length - 1; i >= 0; i--) {\n                if (s[i] !== this._paddingCharacter) {\n                    break;\n                }\n                paddingLength++;\n            }\n            if (s.length < 4 || paddingLength > 2) {\n                throw new Error(\"Base64Coder: incorrect padding\");\n            }\n        }\n        return paddingLength;\n    }\n\n}\n\nconst stdCoder = new Coder();\n\nexport function encode(data: Uint8Array): string {\n    return stdCoder.encode(data);\n}\n\nexport function decode(s: string): Uint8Array {\n    return stdCoder.decode(s);\n}\n\n/**\n * Implements URL-safe Base64 encoding.\n * (Same as Base64, but '+' is replaced with '-', and '/' with '_').\n *\n * Operates in constant time.\n */\nexport class URLSafeCoder extends Coder {\n    // URL-safe encoding have the following encoded/decoded ranges:\n    //\n    // ABCDEFGHIJKLMNOPQRSTUVWXYZ abcdefghijklmnopqrstuvwxyz 0123456789  -   _\n    // Index:   0 - 25                    26 - 51              52 - 61   62  63\n    // ASCII:  65 - 90                    97 - 122             48 - 57   45  95\n    //\n\n    protected _encodeByte(b: number): string {\n        let result = b;\n        // b >= 0\n        result += 65;\n        // b > 25\n        result += ((25 - b) >>> 8) & ((0 - 65) - 26 + 97);\n        // b > 51\n        result += ((51 - b) >>> 8) & ((26 - 97) - 52 + 48);\n        // b > 61\n        result += ((61 - b) >>> 8) & ((52 - 48) - 62 + 45);\n        // b > 62\n        result += ((62 - b) >>> 8) & ((62 - 45) - 63 + 95);\n\n        return String.fromCharCode(result);\n    }\n\n    protected _decodeChar(c: number): number {\n        let result = INVALID_BYTE;\n\n        // c == 45 (c > 44 and c < 46)\n        result += (((44 - c) & (c - 46)) >>> 8) & (-INVALID_BYTE + c - 45 + 62);\n        // c == 95 (c > 94 and c < 96)\n        result += (((94 - c) & (c - 96)) >>> 8) & (-INVALID_BYTE + c - 95 + 63);\n        // c > 47 and c < 58\n        result += (((47 - c) & (c - 58)) >>> 8) & (-INVALID_BYTE + c - 48 + 52);\n        // c > 64 and c < 91\n        result += (((64 - c) & (c - 91)) >>> 8) & (-INVALID_BYTE + c - 65 + 0);\n        // c > 96 and c < 123\n        result += (((96 - c) & (c - 123)) >>> 8) & (-INVALID_BYTE + c - 97 + 26);\n\n        return result;\n    }\n}\n\nconst urlSafeCoder = new URLSafeCoder();\n\nexport function encodeURLSafe(data: Uint8Array): string {\n    return urlSafeCoder.encode(data);\n}\n\nexport function decodeURLSafe(s: string): Uint8Array {\n    return urlSafeCoder.decode(s);\n}\n\n\nexport const encodedLength = (length: number) =>\n    stdCoder.encodedLength(length);\n\nexport const maxDecodedLength = (length: number) =>\n    stdCoder.maxDecodedLength(length);\n\nexport const decodedLength = (s: string) =>\n    stdCoder.decodedLength(s);\n", "import TimedCallback from './timed_callback';\nimport { Delay, Scheduler, Canceller } from './scheduling';\n\nabstract class Timer {\n  protected clear: Canceller;\n  protected timer: number | void;\n\n  constructor(\n    set: Scheduler,\n    clear: <PERSON><PERSON><PERSON>,\n    delay: Delay,\n    callback: TimedCallback\n  ) {\n    this.clear = clear;\n    this.timer = set(() => {\n      if (this.timer) {\n        this.timer = callback(this.timer);\n      }\n    }, delay);\n  }\n\n  /** Returns whether the timer is still running.\n   *\n   * @return {Boolean}\n   */\n  isRunning(): boolean {\n    return this.timer !== null;\n  }\n\n  /** Aborts a timer when it's running. */\n  ensureAborted() {\n    if (this.timer) {\n      this.clear(this.timer);\n      this.timer = null;\n    }\n  }\n}\n\nexport default Timer;\n", "module.exports = require(\"@react-native-community/netinfo\");", "export default function encode(s: any): string {\n  return btoa(utob(s));\n}\n\nvar fromCharCode = String.fromCharCode;\n\nvar b64chars =\n  'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\nvar b64tab = {};\n\nfor (var i = 0, l = b64chars.length; i < l; i++) {\n  b64tab[b64chars.charAt(i)] = i;\n}\n\nvar cb_utob = function(c) {\n  var cc = c.charCodeAt(0);\n  return cc < 0x80\n    ? c\n    : cc < 0x800\n    ? fromCharCode(0xc0 | (cc >>> 6)) + fromCharCode(0x80 | (cc & 0x3f))\n    : fromCharCode(0xe0 | ((cc >>> 12) & 0x0f)) +\n      fromCharCode(0x80 | ((cc >>> 6) & 0x3f)) +\n      fromCharCode(0x80 | (cc & 0x3f));\n};\n\nvar utob = function(u) {\n  return u.replace(/[^\\x00-\\x7F]/g, cb_utob);\n};\n\nvar cb_encode = function(ccc) {\n  var padlen = [0, 2, 1][ccc.length % 3];\n  var ord =\n    (ccc.charCodeAt(0) << 16) |\n    ((ccc.length > 1 ? ccc.charCodeAt(1) : 0) << 8) |\n    (ccc.length > 2 ? ccc.charCodeAt(2) : 0);\n  var chars = [\n    b64chars.charAt(ord >>> 18),\n    b64chars.charAt((ord >>> 12) & 63),\n    padlen >= 2 ? '=' : b64chars.charAt((ord >>> 6) & 63),\n    padlen >= 1 ? '=' : b64chars.charAt(ord & 63)\n  ];\n  return chars.join('');\n};\n\nvar btoa =\n  global.btoa ||\n  function(b) {\n    return b.replace(/[\\s\\S]{1,3}/g, cb_encode);\n  };\n", "import Callback from './callback';\nimport * as Collections from '../utils/collections';\nimport CallbackTable from './callback_table';\n\nexport default class CallbackRegistry {\n  _callbacks: CallbackTable;\n\n  constructor() {\n    this._callbacks = {};\n  }\n\n  get(name: string): Callback[] {\n    return this._callbacks[prefix(name)];\n  }\n\n  add(name: string, callback: Function, context: any) {\n    var prefixedEventName = prefix(name);\n    this._callbacks[prefixedEventName] =\n      this._callbacks[prefixedEventName] || [];\n    this._callbacks[prefixedEventName].push({\n      fn: callback,\n      context: context\n    });\n  }\n\n  remove(name?: string, callback?: Function, context?: any) {\n    if (!name && !callback && !context) {\n      this._callbacks = {};\n      return;\n    }\n\n    var names = name ? [prefix(name)] : Collections.keys(this._callbacks);\n\n    if (callback || context) {\n      this.removeCallback(names, callback, context);\n    } else {\n      this.removeAllCallbacks(names);\n    }\n  }\n\n  private removeCallback(names: string[], callback: Function, context: any) {\n    Collections.apply(\n      names,\n      function(name) {\n        this._callbacks[name] = Collections.filter(\n          this._callbacks[name] || [],\n          function(binding) {\n            return (\n              (callback && callback !== binding.fn) ||\n              (context && context !== binding.context)\n            );\n          }\n        );\n        if (this._callbacks[name].length === 0) {\n          delete this._callbacks[name];\n        }\n      },\n      this\n    );\n  }\n\n  private removeAllCallbacks(names: string[]) {\n    Collections.apply(\n      names,\n      function(name) {\n        delete this._callbacks[name];\n      },\n      this\n    );\n  }\n}\n\nfunction prefix(name: string): string {\n  return '_' + name;\n}\n", "// Copyright (C) 2016 <PERSON>\n// MIT License. See LICENSE file for details.\n\n/**\n * Package utf8 implements UTF-8 encoding and decoding.\n */\n\nconst INVALID_UTF16 = \"utf8: invalid string\";\nconst INVALID_UTF8 = \"utf8: invalid source encoding\";\n\n/**\n * Encodes the given string into UTF-8 byte array.\n * Throws if the source string has invalid UTF-16 encoding.\n */\nexport function encode(s: string): Uint8Array {\n    // Calculate result length and allocate output array.\n    // encodedLength() also validates string and throws errors,\n    // so we don't need repeat validation here.\n    const arr = new Uint8Array(encodedLength(s));\n\n    let pos = 0;\n    for (let i = 0; i < s.length; i++) {\n        let c = s.charCodeAt(i);\n        if (c < 0x80) {\n            arr[pos++] = c;\n        } else if (c < 0x800) {\n            arr[pos++] = 0xc0 | c >> 6;\n            arr[pos++] = 0x80 | c & 0x3f;\n        } else if (c < 0xd800) {\n            arr[pos++] = 0xe0 | c >> 12;\n            arr[pos++] = 0x80 | (c >> 6) & 0x3f;\n            arr[pos++] = 0x80 | c & 0x3f;\n        } else {\n            i++; // get one more character\n            c = (c & 0x3ff) << 10;\n            c |= s.charCodeAt(i) & 0x3ff;\n            c += 0x10000;\n\n            arr[pos++] = 0xf0 | c >> 18;\n            arr[pos++] = 0x80 | (c >> 12) & 0x3f;\n            arr[pos++] = 0x80 | (c >> 6) & 0x3f;\n            arr[pos++] = 0x80 | c & 0x3f;\n        }\n    }\n    return arr;\n}\n\n/**\n * Returns the number of bytes required to encode the given string into UTF-8.\n * Throws if the source string has invalid UTF-16 encoding.\n */\nexport function encodedLength(s: string): number {\n    let result = 0;\n    for (let i = 0; i < s.length; i++) {\n        const c = s.charCodeAt(i);\n        if (c < 0x80) {\n            result += 1;\n        } else if (c < 0x800) {\n            result += 2;\n        } else if (c < 0xd800) {\n            result += 3;\n        } else if (c <= 0xdfff) {\n            if (i >= s.length - 1) {\n                throw new Error(INVALID_UTF16);\n            }\n            i++; // \"eat\" next character\n            result += 4;\n        } else {\n            throw new Error(INVALID_UTF16);\n        }\n    }\n    return result;\n}\n\n/**\n * Decodes the given byte array from UTF-8 into a string.\n * Throws if encoding is invalid.\n */\nexport function decode(arr: Uint8Array): string {\n    const chars: string[] = [];\n    for (let i = 0; i < arr.length; i++) {\n        let b = arr[i];\n\n        if (b & 0x80) {\n            let min;\n            if (b < 0xe0) {\n                // Need 1 more byte.\n                if (i >= arr.length) {\n                    throw new Error(INVALID_UTF8);\n                }\n                const n1 = arr[++i];\n                if ((n1 & 0xc0) !== 0x80) {\n                    throw new Error(INVALID_UTF8);\n                }\n                b = (b & 0x1f) << 6 | (n1 & 0x3f);\n                min = 0x80;\n            } else if (b < 0xf0) {\n                // Need 2 more bytes.\n                if (i >= arr.length - 1) {\n                    throw new Error(INVALID_UTF8);\n                }\n                const n1 = arr[++i];\n                const n2 = arr[++i];\n                if ((n1 & 0xc0) !== 0x80 || (n2 & 0xc0) !== 0x80) {\n                    throw new Error(INVALID_UTF8);\n                }\n                b = (b & 0x0f) << 12 | (n1 & 0x3f) << 6 | (n2 & 0x3f);\n                min = 0x800;\n            } else if (b < 0xf8) {\n                // Need 3 more bytes.\n                if (i >= arr.length - 2) {\n                    throw new Error(INVALID_UTF8);\n                }\n                const n1 = arr[++i];\n                const n2 = arr[++i];\n                const n3 = arr[++i];\n                if ((n1 & 0xc0) !== 0x80 || (n2 & 0xc0) !== 0x80 || (n3 & 0xc0) !== 0x80) {\n                    throw new Error(INVALID_UTF8);\n                }\n                b = (b & 0x0f) << 18 | (n1 & 0x3f) << 12 | (n2 & 0x3f) << 6 | (n3 & 0x3f);\n                min = 0x10000;\n            } else {\n                throw new Error(INVALID_UTF8);\n            }\n\n            if (b < min || (b >= 0xd800 && b <= 0xdfff)) {\n                throw new Error(INVALID_UTF8);\n            }\n\n            if (b >= 0x10000) {\n                // Surrogate pair.\n                if (b > 0x10ffff) {\n                    throw new Error(INVALID_UTF8);\n                }\n                b -= 0x10000;\n                chars.push(String.fromCharCode(0xd800 | (b >> 10)));\n                b = 0xdc00 | (b & 0x3ff);\n            }\n        }\n\n        chars.push(String.fromCharCode(b));\n    }\n    return chars.join(\"\");\n}\n", "(function(nacl) {\n'use strict';\n\n// Ported in 2014 by <PERSON> and <PERSON>.\n// Public domain.\n//\n// Implementation derived from TweetNaCl version 20140427.\n// See for details: http://tweetnacl.cr.yp.to/\n\nvar gf = function(init) {\n  var i, r = new Float64Array(16);\n  if (init) for (i = 0; i < init.length; i++) r[i] = init[i];\n  return r;\n};\n\n//  Pluggable, initialized in high-level API below.\nvar randombytes = function(/* x, n */) { throw new Error('no PRNG'); };\n\nvar _0 = new Uint8Array(16);\nvar _9 = new Uint8Array(32); _9[0] = 9;\n\nvar gf0 = gf(),\n    gf1 = gf([1]),\n    _121665 = gf([0xdb41, 1]),\n    D = gf([0x78a3, 0x1359, 0x4dca, 0x75eb, 0xd8ab, 0x4141, 0x0a4d, 0x0070, 0xe898, 0x7779, 0x4079, 0x8cc7, 0xfe73, 0x2b6f, 0x6cee, 0x5203]),\n    D2 = gf([0xf159, 0x26b2, 0x9b94, 0xebd6, 0xb156, 0x8283, 0x149a, 0x00e0, 0xd130, 0xeef3, 0x80f2, 0x198e, 0xfce7, 0x56df, 0xd9dc, 0x2406]),\n    X = gf([0xd51a, 0x8f25, 0x2d60, 0xc956, 0xa7b2, 0x9525, 0xc760, 0x692c, 0xdc5c, 0xfdd6, 0xe231, 0xc0a4, 0x53fe, 0xcd6e, 0x36d3, 0x2169]),\n    Y = gf([0x6658, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666]),\n    I = gf([0xa0b0, 0x4a0e, 0x1b27, 0xc4ee, 0xe478, 0xad2f, 0x1806, 0x2f43, 0xd7a7, 0x3dfb, 0x0099, 0x2b4d, 0xdf0b, 0x4fc1, 0x2480, 0x2b83]);\n\nfunction ts64(x, i, h, l) {\n  x[i]   = (h >> 24) & 0xff;\n  x[i+1] = (h >> 16) & 0xff;\n  x[i+2] = (h >>  8) & 0xff;\n  x[i+3] = h & 0xff;\n  x[i+4] = (l >> 24)  & 0xff;\n  x[i+5] = (l >> 16)  & 0xff;\n  x[i+6] = (l >>  8)  & 0xff;\n  x[i+7] = l & 0xff;\n}\n\nfunction vn(x, xi, y, yi, n) {\n  var i,d = 0;\n  for (i = 0; i < n; i++) d |= x[xi+i]^y[yi+i];\n  return (1 & ((d - 1) >>> 8)) - 1;\n}\n\nfunction crypto_verify_16(x, xi, y, yi) {\n  return vn(x,xi,y,yi,16);\n}\n\nfunction crypto_verify_32(x, xi, y, yi) {\n  return vn(x,xi,y,yi,32);\n}\n\nfunction core_salsa20(o, p, k, c) {\n  var j0  = c[ 0] & 0xff | (c[ 1] & 0xff)<<8 | (c[ 2] & 0xff)<<16 | (c[ 3] & 0xff)<<24,\n      j1  = k[ 0] & 0xff | (k[ 1] & 0xff)<<8 | (k[ 2] & 0xff)<<16 | (k[ 3] & 0xff)<<24,\n      j2  = k[ 4] & 0xff | (k[ 5] & 0xff)<<8 | (k[ 6] & 0xff)<<16 | (k[ 7] & 0xff)<<24,\n      j3  = k[ 8] & 0xff | (k[ 9] & 0xff)<<8 | (k[10] & 0xff)<<16 | (k[11] & 0xff)<<24,\n      j4  = k[12] & 0xff | (k[13] & 0xff)<<8 | (k[14] & 0xff)<<16 | (k[15] & 0xff)<<24,\n      j5  = c[ 4] & 0xff | (c[ 5] & 0xff)<<8 | (c[ 6] & 0xff)<<16 | (c[ 7] & 0xff)<<24,\n      j6  = p[ 0] & 0xff | (p[ 1] & 0xff)<<8 | (p[ 2] & 0xff)<<16 | (p[ 3] & 0xff)<<24,\n      j7  = p[ 4] & 0xff | (p[ 5] & 0xff)<<8 | (p[ 6] & 0xff)<<16 | (p[ 7] & 0xff)<<24,\n      j8  = p[ 8] & 0xff | (p[ 9] & 0xff)<<8 | (p[10] & 0xff)<<16 | (p[11] & 0xff)<<24,\n      j9  = p[12] & 0xff | (p[13] & 0xff)<<8 | (p[14] & 0xff)<<16 | (p[15] & 0xff)<<24,\n      j10 = c[ 8] & 0xff | (c[ 9] & 0xff)<<8 | (c[10] & 0xff)<<16 | (c[11] & 0xff)<<24,\n      j11 = k[16] & 0xff | (k[17] & 0xff)<<8 | (k[18] & 0xff)<<16 | (k[19] & 0xff)<<24,\n      j12 = k[20] & 0xff | (k[21] & 0xff)<<8 | (k[22] & 0xff)<<16 | (k[23] & 0xff)<<24,\n      j13 = k[24] & 0xff | (k[25] & 0xff)<<8 | (k[26] & 0xff)<<16 | (k[27] & 0xff)<<24,\n      j14 = k[28] & 0xff | (k[29] & 0xff)<<8 | (k[30] & 0xff)<<16 | (k[31] & 0xff)<<24,\n      j15 = c[12] & 0xff | (c[13] & 0xff)<<8 | (c[14] & 0xff)<<16 | (c[15] & 0xff)<<24;\n\n  var x0 = j0, x1 = j1, x2 = j2, x3 = j3, x4 = j4, x5 = j5, x6 = j6, x7 = j7,\n      x8 = j8, x9 = j9, x10 = j10, x11 = j11, x12 = j12, x13 = j13, x14 = j14,\n      x15 = j15, u;\n\n  for (var i = 0; i < 20; i += 2) {\n    u = x0 + x12 | 0;\n    x4 ^= u<<7 | u>>>(32-7);\n    u = x4 + x0 | 0;\n    x8 ^= u<<9 | u>>>(32-9);\n    u = x8 + x4 | 0;\n    x12 ^= u<<13 | u>>>(32-13);\n    u = x12 + x8 | 0;\n    x0 ^= u<<18 | u>>>(32-18);\n\n    u = x5 + x1 | 0;\n    x9 ^= u<<7 | u>>>(32-7);\n    u = x9 + x5 | 0;\n    x13 ^= u<<9 | u>>>(32-9);\n    u = x13 + x9 | 0;\n    x1 ^= u<<13 | u>>>(32-13);\n    u = x1 + x13 | 0;\n    x5 ^= u<<18 | u>>>(32-18);\n\n    u = x10 + x6 | 0;\n    x14 ^= u<<7 | u>>>(32-7);\n    u = x14 + x10 | 0;\n    x2 ^= u<<9 | u>>>(32-9);\n    u = x2 + x14 | 0;\n    x6 ^= u<<13 | u>>>(32-13);\n    u = x6 + x2 | 0;\n    x10 ^= u<<18 | u>>>(32-18);\n\n    u = x15 + x11 | 0;\n    x3 ^= u<<7 | u>>>(32-7);\n    u = x3 + x15 | 0;\n    x7 ^= u<<9 | u>>>(32-9);\n    u = x7 + x3 | 0;\n    x11 ^= u<<13 | u>>>(32-13);\n    u = x11 + x7 | 0;\n    x15 ^= u<<18 | u>>>(32-18);\n\n    u = x0 + x3 | 0;\n    x1 ^= u<<7 | u>>>(32-7);\n    u = x1 + x0 | 0;\n    x2 ^= u<<9 | u>>>(32-9);\n    u = x2 + x1 | 0;\n    x3 ^= u<<13 | u>>>(32-13);\n    u = x3 + x2 | 0;\n    x0 ^= u<<18 | u>>>(32-18);\n\n    u = x5 + x4 | 0;\n    x6 ^= u<<7 | u>>>(32-7);\n    u = x6 + x5 | 0;\n    x7 ^= u<<9 | u>>>(32-9);\n    u = x7 + x6 | 0;\n    x4 ^= u<<13 | u>>>(32-13);\n    u = x4 + x7 | 0;\n    x5 ^= u<<18 | u>>>(32-18);\n\n    u = x10 + x9 | 0;\n    x11 ^= u<<7 | u>>>(32-7);\n    u = x11 + x10 | 0;\n    x8 ^= u<<9 | u>>>(32-9);\n    u = x8 + x11 | 0;\n    x9 ^= u<<13 | u>>>(32-13);\n    u = x9 + x8 | 0;\n    x10 ^= u<<18 | u>>>(32-18);\n\n    u = x15 + x14 | 0;\n    x12 ^= u<<7 | u>>>(32-7);\n    u = x12 + x15 | 0;\n    x13 ^= u<<9 | u>>>(32-9);\n    u = x13 + x12 | 0;\n    x14 ^= u<<13 | u>>>(32-13);\n    u = x14 + x13 | 0;\n    x15 ^= u<<18 | u>>>(32-18);\n  }\n   x0 =  x0 +  j0 | 0;\n   x1 =  x1 +  j1 | 0;\n   x2 =  x2 +  j2 | 0;\n   x3 =  x3 +  j3 | 0;\n   x4 =  x4 +  j4 | 0;\n   x5 =  x5 +  j5 | 0;\n   x6 =  x6 +  j6 | 0;\n   x7 =  x7 +  j7 | 0;\n   x8 =  x8 +  j8 | 0;\n   x9 =  x9 +  j9 | 0;\n  x10 = x10 + j10 | 0;\n  x11 = x11 + j11 | 0;\n  x12 = x12 + j12 | 0;\n  x13 = x13 + j13 | 0;\n  x14 = x14 + j14 | 0;\n  x15 = x15 + j15 | 0;\n\n  o[ 0] = x0 >>>  0 & 0xff;\n  o[ 1] = x0 >>>  8 & 0xff;\n  o[ 2] = x0 >>> 16 & 0xff;\n  o[ 3] = x0 >>> 24 & 0xff;\n\n  o[ 4] = x1 >>>  0 & 0xff;\n  o[ 5] = x1 >>>  8 & 0xff;\n  o[ 6] = x1 >>> 16 & 0xff;\n  o[ 7] = x1 >>> 24 & 0xff;\n\n  o[ 8] = x2 >>>  0 & 0xff;\n  o[ 9] = x2 >>>  8 & 0xff;\n  o[10] = x2 >>> 16 & 0xff;\n  o[11] = x2 >>> 24 & 0xff;\n\n  o[12] = x3 >>>  0 & 0xff;\n  o[13] = x3 >>>  8 & 0xff;\n  o[14] = x3 >>> 16 & 0xff;\n  o[15] = x3 >>> 24 & 0xff;\n\n  o[16] = x4 >>>  0 & 0xff;\n  o[17] = x4 >>>  8 & 0xff;\n  o[18] = x4 >>> 16 & 0xff;\n  o[19] = x4 >>> 24 & 0xff;\n\n  o[20] = x5 >>>  0 & 0xff;\n  o[21] = x5 >>>  8 & 0xff;\n  o[22] = x5 >>> 16 & 0xff;\n  o[23] = x5 >>> 24 & 0xff;\n\n  o[24] = x6 >>>  0 & 0xff;\n  o[25] = x6 >>>  8 & 0xff;\n  o[26] = x6 >>> 16 & 0xff;\n  o[27] = x6 >>> 24 & 0xff;\n\n  o[28] = x7 >>>  0 & 0xff;\n  o[29] = x7 >>>  8 & 0xff;\n  o[30] = x7 >>> 16 & 0xff;\n  o[31] = x7 >>> 24 & 0xff;\n\n  o[32] = x8 >>>  0 & 0xff;\n  o[33] = x8 >>>  8 & 0xff;\n  o[34] = x8 >>> 16 & 0xff;\n  o[35] = x8 >>> 24 & 0xff;\n\n  o[36] = x9 >>>  0 & 0xff;\n  o[37] = x9 >>>  8 & 0xff;\n  o[38] = x9 >>> 16 & 0xff;\n  o[39] = x9 >>> 24 & 0xff;\n\n  o[40] = x10 >>>  0 & 0xff;\n  o[41] = x10 >>>  8 & 0xff;\n  o[42] = x10 >>> 16 & 0xff;\n  o[43] = x10 >>> 24 & 0xff;\n\n  o[44] = x11 >>>  0 & 0xff;\n  o[45] = x11 >>>  8 & 0xff;\n  o[46] = x11 >>> 16 & 0xff;\n  o[47] = x11 >>> 24 & 0xff;\n\n  o[48] = x12 >>>  0 & 0xff;\n  o[49] = x12 >>>  8 & 0xff;\n  o[50] = x12 >>> 16 & 0xff;\n  o[51] = x12 >>> 24 & 0xff;\n\n  o[52] = x13 >>>  0 & 0xff;\n  o[53] = x13 >>>  8 & 0xff;\n  o[54] = x13 >>> 16 & 0xff;\n  o[55] = x13 >>> 24 & 0xff;\n\n  o[56] = x14 >>>  0 & 0xff;\n  o[57] = x14 >>>  8 & 0xff;\n  o[58] = x14 >>> 16 & 0xff;\n  o[59] = x14 >>> 24 & 0xff;\n\n  o[60] = x15 >>>  0 & 0xff;\n  o[61] = x15 >>>  8 & 0xff;\n  o[62] = x15 >>> 16 & 0xff;\n  o[63] = x15 >>> 24 & 0xff;\n}\n\nfunction core_hsalsa20(o,p,k,c) {\n  var j0  = c[ 0] & 0xff | (c[ 1] & 0xff)<<8 | (c[ 2] & 0xff)<<16 | (c[ 3] & 0xff)<<24,\n      j1  = k[ 0] & 0xff | (k[ 1] & 0xff)<<8 | (k[ 2] & 0xff)<<16 | (k[ 3] & 0xff)<<24,\n      j2  = k[ 4] & 0xff | (k[ 5] & 0xff)<<8 | (k[ 6] & 0xff)<<16 | (k[ 7] & 0xff)<<24,\n      j3  = k[ 8] & 0xff | (k[ 9] & 0xff)<<8 | (k[10] & 0xff)<<16 | (k[11] & 0xff)<<24,\n      j4  = k[12] & 0xff | (k[13] & 0xff)<<8 | (k[14] & 0xff)<<16 | (k[15] & 0xff)<<24,\n      j5  = c[ 4] & 0xff | (c[ 5] & 0xff)<<8 | (c[ 6] & 0xff)<<16 | (c[ 7] & 0xff)<<24,\n      j6  = p[ 0] & 0xff | (p[ 1] & 0xff)<<8 | (p[ 2] & 0xff)<<16 | (p[ 3] & 0xff)<<24,\n      j7  = p[ 4] & 0xff | (p[ 5] & 0xff)<<8 | (p[ 6] & 0xff)<<16 | (p[ 7] & 0xff)<<24,\n      j8  = p[ 8] & 0xff | (p[ 9] & 0xff)<<8 | (p[10] & 0xff)<<16 | (p[11] & 0xff)<<24,\n      j9  = p[12] & 0xff | (p[13] & 0xff)<<8 | (p[14] & 0xff)<<16 | (p[15] & 0xff)<<24,\n      j10 = c[ 8] & 0xff | (c[ 9] & 0xff)<<8 | (c[10] & 0xff)<<16 | (c[11] & 0xff)<<24,\n      j11 = k[16] & 0xff | (k[17] & 0xff)<<8 | (k[18] & 0xff)<<16 | (k[19] & 0xff)<<24,\n      j12 = k[20] & 0xff | (k[21] & 0xff)<<8 | (k[22] & 0xff)<<16 | (k[23] & 0xff)<<24,\n      j13 = k[24] & 0xff | (k[25] & 0xff)<<8 | (k[26] & 0xff)<<16 | (k[27] & 0xff)<<24,\n      j14 = k[28] & 0xff | (k[29] & 0xff)<<8 | (k[30] & 0xff)<<16 | (k[31] & 0xff)<<24,\n      j15 = c[12] & 0xff | (c[13] & 0xff)<<8 | (c[14] & 0xff)<<16 | (c[15] & 0xff)<<24;\n\n  var x0 = j0, x1 = j1, x2 = j2, x3 = j3, x4 = j4, x5 = j5, x6 = j6, x7 = j7,\n      x8 = j8, x9 = j9, x10 = j10, x11 = j11, x12 = j12, x13 = j13, x14 = j14,\n      x15 = j15, u;\n\n  for (var i = 0; i < 20; i += 2) {\n    u = x0 + x12 | 0;\n    x4 ^= u<<7 | u>>>(32-7);\n    u = x4 + x0 | 0;\n    x8 ^= u<<9 | u>>>(32-9);\n    u = x8 + x4 | 0;\n    x12 ^= u<<13 | u>>>(32-13);\n    u = x12 + x8 | 0;\n    x0 ^= u<<18 | u>>>(32-18);\n\n    u = x5 + x1 | 0;\n    x9 ^= u<<7 | u>>>(32-7);\n    u = x9 + x5 | 0;\n    x13 ^= u<<9 | u>>>(32-9);\n    u = x13 + x9 | 0;\n    x1 ^= u<<13 | u>>>(32-13);\n    u = x1 + x13 | 0;\n    x5 ^= u<<18 | u>>>(32-18);\n\n    u = x10 + x6 | 0;\n    x14 ^= u<<7 | u>>>(32-7);\n    u = x14 + x10 | 0;\n    x2 ^= u<<9 | u>>>(32-9);\n    u = x2 + x14 | 0;\n    x6 ^= u<<13 | u>>>(32-13);\n    u = x6 + x2 | 0;\n    x10 ^= u<<18 | u>>>(32-18);\n\n    u = x15 + x11 | 0;\n    x3 ^= u<<7 | u>>>(32-7);\n    u = x3 + x15 | 0;\n    x7 ^= u<<9 | u>>>(32-9);\n    u = x7 + x3 | 0;\n    x11 ^= u<<13 | u>>>(32-13);\n    u = x11 + x7 | 0;\n    x15 ^= u<<18 | u>>>(32-18);\n\n    u = x0 + x3 | 0;\n    x1 ^= u<<7 | u>>>(32-7);\n    u = x1 + x0 | 0;\n    x2 ^= u<<9 | u>>>(32-9);\n    u = x2 + x1 | 0;\n    x3 ^= u<<13 | u>>>(32-13);\n    u = x3 + x2 | 0;\n    x0 ^= u<<18 | u>>>(32-18);\n\n    u = x5 + x4 | 0;\n    x6 ^= u<<7 | u>>>(32-7);\n    u = x6 + x5 | 0;\n    x7 ^= u<<9 | u>>>(32-9);\n    u = x7 + x6 | 0;\n    x4 ^= u<<13 | u>>>(32-13);\n    u = x4 + x7 | 0;\n    x5 ^= u<<18 | u>>>(32-18);\n\n    u = x10 + x9 | 0;\n    x11 ^= u<<7 | u>>>(32-7);\n    u = x11 + x10 | 0;\n    x8 ^= u<<9 | u>>>(32-9);\n    u = x8 + x11 | 0;\n    x9 ^= u<<13 | u>>>(32-13);\n    u = x9 + x8 | 0;\n    x10 ^= u<<18 | u>>>(32-18);\n\n    u = x15 + x14 | 0;\n    x12 ^= u<<7 | u>>>(32-7);\n    u = x12 + x15 | 0;\n    x13 ^= u<<9 | u>>>(32-9);\n    u = x13 + x12 | 0;\n    x14 ^= u<<13 | u>>>(32-13);\n    u = x14 + x13 | 0;\n    x15 ^= u<<18 | u>>>(32-18);\n  }\n\n  o[ 0] = x0 >>>  0 & 0xff;\n  o[ 1] = x0 >>>  8 & 0xff;\n  o[ 2] = x0 >>> 16 & 0xff;\n  o[ 3] = x0 >>> 24 & 0xff;\n\n  o[ 4] = x5 >>>  0 & 0xff;\n  o[ 5] = x5 >>>  8 & 0xff;\n  o[ 6] = x5 >>> 16 & 0xff;\n  o[ 7] = x5 >>> 24 & 0xff;\n\n  o[ 8] = x10 >>>  0 & 0xff;\n  o[ 9] = x10 >>>  8 & 0xff;\n  o[10] = x10 >>> 16 & 0xff;\n  o[11] = x10 >>> 24 & 0xff;\n\n  o[12] = x15 >>>  0 & 0xff;\n  o[13] = x15 >>>  8 & 0xff;\n  o[14] = x15 >>> 16 & 0xff;\n  o[15] = x15 >>> 24 & 0xff;\n\n  o[16] = x6 >>>  0 & 0xff;\n  o[17] = x6 >>>  8 & 0xff;\n  o[18] = x6 >>> 16 & 0xff;\n  o[19] = x6 >>> 24 & 0xff;\n\n  o[20] = x7 >>>  0 & 0xff;\n  o[21] = x7 >>>  8 & 0xff;\n  o[22] = x7 >>> 16 & 0xff;\n  o[23] = x7 >>> 24 & 0xff;\n\n  o[24] = x8 >>>  0 & 0xff;\n  o[25] = x8 >>>  8 & 0xff;\n  o[26] = x8 >>> 16 & 0xff;\n  o[27] = x8 >>> 24 & 0xff;\n\n  o[28] = x9 >>>  0 & 0xff;\n  o[29] = x9 >>>  8 & 0xff;\n  o[30] = x9 >>> 16 & 0xff;\n  o[31] = x9 >>> 24 & 0xff;\n}\n\nfunction crypto_core_salsa20(out,inp,k,c) {\n  core_salsa20(out,inp,k,c);\n}\n\nfunction crypto_core_hsalsa20(out,inp,k,c) {\n  core_hsalsa20(out,inp,k,c);\n}\n\nvar sigma = new Uint8Array([101, 120, 112, 97, 110, 100, 32, 51, 50, 45, 98, 121, 116, 101, 32, 107]);\n            // \"expand 32-byte k\"\n\nfunction crypto_stream_salsa20_xor(c,cpos,m,mpos,b,n,k) {\n  var z = new Uint8Array(16), x = new Uint8Array(64);\n  var u, i;\n  for (i = 0; i < 16; i++) z[i] = 0;\n  for (i = 0; i < 8; i++) z[i] = n[i];\n  while (b >= 64) {\n    crypto_core_salsa20(x,z,k,sigma);\n    for (i = 0; i < 64; i++) c[cpos+i] = m[mpos+i] ^ x[i];\n    u = 1;\n    for (i = 8; i < 16; i++) {\n      u = u + (z[i] & 0xff) | 0;\n      z[i] = u & 0xff;\n      u >>>= 8;\n    }\n    b -= 64;\n    cpos += 64;\n    mpos += 64;\n  }\n  if (b > 0) {\n    crypto_core_salsa20(x,z,k,sigma);\n    for (i = 0; i < b; i++) c[cpos+i] = m[mpos+i] ^ x[i];\n  }\n  return 0;\n}\n\nfunction crypto_stream_salsa20(c,cpos,b,n,k) {\n  var z = new Uint8Array(16), x = new Uint8Array(64);\n  var u, i;\n  for (i = 0; i < 16; i++) z[i] = 0;\n  for (i = 0; i < 8; i++) z[i] = n[i];\n  while (b >= 64) {\n    crypto_core_salsa20(x,z,k,sigma);\n    for (i = 0; i < 64; i++) c[cpos+i] = x[i];\n    u = 1;\n    for (i = 8; i < 16; i++) {\n      u = u + (z[i] & 0xff) | 0;\n      z[i] = u & 0xff;\n      u >>>= 8;\n    }\n    b -= 64;\n    cpos += 64;\n  }\n  if (b > 0) {\n    crypto_core_salsa20(x,z,k,sigma);\n    for (i = 0; i < b; i++) c[cpos+i] = x[i];\n  }\n  return 0;\n}\n\nfunction crypto_stream(c,cpos,d,n,k) {\n  var s = new Uint8Array(32);\n  crypto_core_hsalsa20(s,n,k,sigma);\n  var sn = new Uint8Array(8);\n  for (var i = 0; i < 8; i++) sn[i] = n[i+16];\n  return crypto_stream_salsa20(c,cpos,d,sn,s);\n}\n\nfunction crypto_stream_xor(c,cpos,m,mpos,d,n,k) {\n  var s = new Uint8Array(32);\n  crypto_core_hsalsa20(s,n,k,sigma);\n  var sn = new Uint8Array(8);\n  for (var i = 0; i < 8; i++) sn[i] = n[i+16];\n  return crypto_stream_salsa20_xor(c,cpos,m,mpos,d,sn,s);\n}\n\n/*\n* Port of Andrew Moon's Poly1305-donna-16. Public domain.\n* https://github.com/floodyberry/poly1305-donna\n*/\n\nvar poly1305 = function(key) {\n  this.buffer = new Uint8Array(16);\n  this.r = new Uint16Array(10);\n  this.h = new Uint16Array(10);\n  this.pad = new Uint16Array(8);\n  this.leftover = 0;\n  this.fin = 0;\n\n  var t0, t1, t2, t3, t4, t5, t6, t7;\n\n  t0 = key[ 0] & 0xff | (key[ 1] & 0xff) << 8; this.r[0] = ( t0                     ) & 0x1fff;\n  t1 = key[ 2] & 0xff | (key[ 3] & 0xff) << 8; this.r[1] = ((t0 >>> 13) | (t1 <<  3)) & 0x1fff;\n  t2 = key[ 4] & 0xff | (key[ 5] & 0xff) << 8; this.r[2] = ((t1 >>> 10) | (t2 <<  6)) & 0x1f03;\n  t3 = key[ 6] & 0xff | (key[ 7] & 0xff) << 8; this.r[3] = ((t2 >>>  7) | (t3 <<  9)) & 0x1fff;\n  t4 = key[ 8] & 0xff | (key[ 9] & 0xff) << 8; this.r[4] = ((t3 >>>  4) | (t4 << 12)) & 0x00ff;\n  this.r[5] = ((t4 >>>  1)) & 0x1ffe;\n  t5 = key[10] & 0xff | (key[11] & 0xff) << 8; this.r[6] = ((t4 >>> 14) | (t5 <<  2)) & 0x1fff;\n  t6 = key[12] & 0xff | (key[13] & 0xff) << 8; this.r[7] = ((t5 >>> 11) | (t6 <<  5)) & 0x1f81;\n  t7 = key[14] & 0xff | (key[15] & 0xff) << 8; this.r[8] = ((t6 >>>  8) | (t7 <<  8)) & 0x1fff;\n  this.r[9] = ((t7 >>>  5)) & 0x007f;\n\n  this.pad[0] = key[16] & 0xff | (key[17] & 0xff) << 8;\n  this.pad[1] = key[18] & 0xff | (key[19] & 0xff) << 8;\n  this.pad[2] = key[20] & 0xff | (key[21] & 0xff) << 8;\n  this.pad[3] = key[22] & 0xff | (key[23] & 0xff) << 8;\n  this.pad[4] = key[24] & 0xff | (key[25] & 0xff) << 8;\n  this.pad[5] = key[26] & 0xff | (key[27] & 0xff) << 8;\n  this.pad[6] = key[28] & 0xff | (key[29] & 0xff) << 8;\n  this.pad[7] = key[30] & 0xff | (key[31] & 0xff) << 8;\n};\n\npoly1305.prototype.blocks = function(m, mpos, bytes) {\n  var hibit = this.fin ? 0 : (1 << 11);\n  var t0, t1, t2, t3, t4, t5, t6, t7, c;\n  var d0, d1, d2, d3, d4, d5, d6, d7, d8, d9;\n\n  var h0 = this.h[0],\n      h1 = this.h[1],\n      h2 = this.h[2],\n      h3 = this.h[3],\n      h4 = this.h[4],\n      h5 = this.h[5],\n      h6 = this.h[6],\n      h7 = this.h[7],\n      h8 = this.h[8],\n      h9 = this.h[9];\n\n  var r0 = this.r[0],\n      r1 = this.r[1],\n      r2 = this.r[2],\n      r3 = this.r[3],\n      r4 = this.r[4],\n      r5 = this.r[5],\n      r6 = this.r[6],\n      r7 = this.r[7],\n      r8 = this.r[8],\n      r9 = this.r[9];\n\n  while (bytes >= 16) {\n    t0 = m[mpos+ 0] & 0xff | (m[mpos+ 1] & 0xff) << 8; h0 += ( t0                     ) & 0x1fff;\n    t1 = m[mpos+ 2] & 0xff | (m[mpos+ 3] & 0xff) << 8; h1 += ((t0 >>> 13) | (t1 <<  3)) & 0x1fff;\n    t2 = m[mpos+ 4] & 0xff | (m[mpos+ 5] & 0xff) << 8; h2 += ((t1 >>> 10) | (t2 <<  6)) & 0x1fff;\n    t3 = m[mpos+ 6] & 0xff | (m[mpos+ 7] & 0xff) << 8; h3 += ((t2 >>>  7) | (t3 <<  9)) & 0x1fff;\n    t4 = m[mpos+ 8] & 0xff | (m[mpos+ 9] & 0xff) << 8; h4 += ((t3 >>>  4) | (t4 << 12)) & 0x1fff;\n    h5 += ((t4 >>>  1)) & 0x1fff;\n    t5 = m[mpos+10] & 0xff | (m[mpos+11] & 0xff) << 8; h6 += ((t4 >>> 14) | (t5 <<  2)) & 0x1fff;\n    t6 = m[mpos+12] & 0xff | (m[mpos+13] & 0xff) << 8; h7 += ((t5 >>> 11) | (t6 <<  5)) & 0x1fff;\n    t7 = m[mpos+14] & 0xff | (m[mpos+15] & 0xff) << 8; h8 += ((t6 >>>  8) | (t7 <<  8)) & 0x1fff;\n    h9 += ((t7 >>> 5)) | hibit;\n\n    c = 0;\n\n    d0 = c;\n    d0 += h0 * r0;\n    d0 += h1 * (5 * r9);\n    d0 += h2 * (5 * r8);\n    d0 += h3 * (5 * r7);\n    d0 += h4 * (5 * r6);\n    c = (d0 >>> 13); d0 &= 0x1fff;\n    d0 += h5 * (5 * r5);\n    d0 += h6 * (5 * r4);\n    d0 += h7 * (5 * r3);\n    d0 += h8 * (5 * r2);\n    d0 += h9 * (5 * r1);\n    c += (d0 >>> 13); d0 &= 0x1fff;\n\n    d1 = c;\n    d1 += h0 * r1;\n    d1 += h1 * r0;\n    d1 += h2 * (5 * r9);\n    d1 += h3 * (5 * r8);\n    d1 += h4 * (5 * r7);\n    c = (d1 >>> 13); d1 &= 0x1fff;\n    d1 += h5 * (5 * r6);\n    d1 += h6 * (5 * r5);\n    d1 += h7 * (5 * r4);\n    d1 += h8 * (5 * r3);\n    d1 += h9 * (5 * r2);\n    c += (d1 >>> 13); d1 &= 0x1fff;\n\n    d2 = c;\n    d2 += h0 * r2;\n    d2 += h1 * r1;\n    d2 += h2 * r0;\n    d2 += h3 * (5 * r9);\n    d2 += h4 * (5 * r8);\n    c = (d2 >>> 13); d2 &= 0x1fff;\n    d2 += h5 * (5 * r7);\n    d2 += h6 * (5 * r6);\n    d2 += h7 * (5 * r5);\n    d2 += h8 * (5 * r4);\n    d2 += h9 * (5 * r3);\n    c += (d2 >>> 13); d2 &= 0x1fff;\n\n    d3 = c;\n    d3 += h0 * r3;\n    d3 += h1 * r2;\n    d3 += h2 * r1;\n    d3 += h3 * r0;\n    d3 += h4 * (5 * r9);\n    c = (d3 >>> 13); d3 &= 0x1fff;\n    d3 += h5 * (5 * r8);\n    d3 += h6 * (5 * r7);\n    d3 += h7 * (5 * r6);\n    d3 += h8 * (5 * r5);\n    d3 += h9 * (5 * r4);\n    c += (d3 >>> 13); d3 &= 0x1fff;\n\n    d4 = c;\n    d4 += h0 * r4;\n    d4 += h1 * r3;\n    d4 += h2 * r2;\n    d4 += h3 * r1;\n    d4 += h4 * r0;\n    c = (d4 >>> 13); d4 &= 0x1fff;\n    d4 += h5 * (5 * r9);\n    d4 += h6 * (5 * r8);\n    d4 += h7 * (5 * r7);\n    d4 += h8 * (5 * r6);\n    d4 += h9 * (5 * r5);\n    c += (d4 >>> 13); d4 &= 0x1fff;\n\n    d5 = c;\n    d5 += h0 * r5;\n    d5 += h1 * r4;\n    d5 += h2 * r3;\n    d5 += h3 * r2;\n    d5 += h4 * r1;\n    c = (d5 >>> 13); d5 &= 0x1fff;\n    d5 += h5 * r0;\n    d5 += h6 * (5 * r9);\n    d5 += h7 * (5 * r8);\n    d5 += h8 * (5 * r7);\n    d5 += h9 * (5 * r6);\n    c += (d5 >>> 13); d5 &= 0x1fff;\n\n    d6 = c;\n    d6 += h0 * r6;\n    d6 += h1 * r5;\n    d6 += h2 * r4;\n    d6 += h3 * r3;\n    d6 += h4 * r2;\n    c = (d6 >>> 13); d6 &= 0x1fff;\n    d6 += h5 * r1;\n    d6 += h6 * r0;\n    d6 += h7 * (5 * r9);\n    d6 += h8 * (5 * r8);\n    d6 += h9 * (5 * r7);\n    c += (d6 >>> 13); d6 &= 0x1fff;\n\n    d7 = c;\n    d7 += h0 * r7;\n    d7 += h1 * r6;\n    d7 += h2 * r5;\n    d7 += h3 * r4;\n    d7 += h4 * r3;\n    c = (d7 >>> 13); d7 &= 0x1fff;\n    d7 += h5 * r2;\n    d7 += h6 * r1;\n    d7 += h7 * r0;\n    d7 += h8 * (5 * r9);\n    d7 += h9 * (5 * r8);\n    c += (d7 >>> 13); d7 &= 0x1fff;\n\n    d8 = c;\n    d8 += h0 * r8;\n    d8 += h1 * r7;\n    d8 += h2 * r6;\n    d8 += h3 * r5;\n    d8 += h4 * r4;\n    c = (d8 >>> 13); d8 &= 0x1fff;\n    d8 += h5 * r3;\n    d8 += h6 * r2;\n    d8 += h7 * r1;\n    d8 += h8 * r0;\n    d8 += h9 * (5 * r9);\n    c += (d8 >>> 13); d8 &= 0x1fff;\n\n    d9 = c;\n    d9 += h0 * r9;\n    d9 += h1 * r8;\n    d9 += h2 * r7;\n    d9 += h3 * r6;\n    d9 += h4 * r5;\n    c = (d9 >>> 13); d9 &= 0x1fff;\n    d9 += h5 * r4;\n    d9 += h6 * r3;\n    d9 += h7 * r2;\n    d9 += h8 * r1;\n    d9 += h9 * r0;\n    c += (d9 >>> 13); d9 &= 0x1fff;\n\n    c = (((c << 2) + c)) | 0;\n    c = (c + d0) | 0;\n    d0 = c & 0x1fff;\n    c = (c >>> 13);\n    d1 += c;\n\n    h0 = d0;\n    h1 = d1;\n    h2 = d2;\n    h3 = d3;\n    h4 = d4;\n    h5 = d5;\n    h6 = d6;\n    h7 = d7;\n    h8 = d8;\n    h9 = d9;\n\n    mpos += 16;\n    bytes -= 16;\n  }\n  this.h[0] = h0;\n  this.h[1] = h1;\n  this.h[2] = h2;\n  this.h[3] = h3;\n  this.h[4] = h4;\n  this.h[5] = h5;\n  this.h[6] = h6;\n  this.h[7] = h7;\n  this.h[8] = h8;\n  this.h[9] = h9;\n};\n\npoly1305.prototype.finish = function(mac, macpos) {\n  var g = new Uint16Array(10);\n  var c, mask, f, i;\n\n  if (this.leftover) {\n    i = this.leftover;\n    this.buffer[i++] = 1;\n    for (; i < 16; i++) this.buffer[i] = 0;\n    this.fin = 1;\n    this.blocks(this.buffer, 0, 16);\n  }\n\n  c = this.h[1] >>> 13;\n  this.h[1] &= 0x1fff;\n  for (i = 2; i < 10; i++) {\n    this.h[i] += c;\n    c = this.h[i] >>> 13;\n    this.h[i] &= 0x1fff;\n  }\n  this.h[0] += (c * 5);\n  c = this.h[0] >>> 13;\n  this.h[0] &= 0x1fff;\n  this.h[1] += c;\n  c = this.h[1] >>> 13;\n  this.h[1] &= 0x1fff;\n  this.h[2] += c;\n\n  g[0] = this.h[0] + 5;\n  c = g[0] >>> 13;\n  g[0] &= 0x1fff;\n  for (i = 1; i < 10; i++) {\n    g[i] = this.h[i] + c;\n    c = g[i] >>> 13;\n    g[i] &= 0x1fff;\n  }\n  g[9] -= (1 << 13);\n\n  mask = (c ^ 1) - 1;\n  for (i = 0; i < 10; i++) g[i] &= mask;\n  mask = ~mask;\n  for (i = 0; i < 10; i++) this.h[i] = (this.h[i] & mask) | g[i];\n\n  this.h[0] = ((this.h[0]       ) | (this.h[1] << 13)                    ) & 0xffff;\n  this.h[1] = ((this.h[1] >>>  3) | (this.h[2] << 10)                    ) & 0xffff;\n  this.h[2] = ((this.h[2] >>>  6) | (this.h[3] <<  7)                    ) & 0xffff;\n  this.h[3] = ((this.h[3] >>>  9) | (this.h[4] <<  4)                    ) & 0xffff;\n  this.h[4] = ((this.h[4] >>> 12) | (this.h[5] <<  1) | (this.h[6] << 14)) & 0xffff;\n  this.h[5] = ((this.h[6] >>>  2) | (this.h[7] << 11)                    ) & 0xffff;\n  this.h[6] = ((this.h[7] >>>  5) | (this.h[8] <<  8)                    ) & 0xffff;\n  this.h[7] = ((this.h[8] >>>  8) | (this.h[9] <<  5)                    ) & 0xffff;\n\n  f = this.h[0] + this.pad[0];\n  this.h[0] = f & 0xffff;\n  for (i = 1; i < 8; i++) {\n    f = (((this.h[i] + this.pad[i]) | 0) + (f >>> 16)) | 0;\n    this.h[i] = f & 0xffff;\n  }\n\n  mac[macpos+ 0] = (this.h[0] >>> 0) & 0xff;\n  mac[macpos+ 1] = (this.h[0] >>> 8) & 0xff;\n  mac[macpos+ 2] = (this.h[1] >>> 0) & 0xff;\n  mac[macpos+ 3] = (this.h[1] >>> 8) & 0xff;\n  mac[macpos+ 4] = (this.h[2] >>> 0) & 0xff;\n  mac[macpos+ 5] = (this.h[2] >>> 8) & 0xff;\n  mac[macpos+ 6] = (this.h[3] >>> 0) & 0xff;\n  mac[macpos+ 7] = (this.h[3] >>> 8) & 0xff;\n  mac[macpos+ 8] = (this.h[4] >>> 0) & 0xff;\n  mac[macpos+ 9] = (this.h[4] >>> 8) & 0xff;\n  mac[macpos+10] = (this.h[5] >>> 0) & 0xff;\n  mac[macpos+11] = (this.h[5] >>> 8) & 0xff;\n  mac[macpos+12] = (this.h[6] >>> 0) & 0xff;\n  mac[macpos+13] = (this.h[6] >>> 8) & 0xff;\n  mac[macpos+14] = (this.h[7] >>> 0) & 0xff;\n  mac[macpos+15] = (this.h[7] >>> 8) & 0xff;\n};\n\npoly1305.prototype.update = function(m, mpos, bytes) {\n  var i, want;\n\n  if (this.leftover) {\n    want = (16 - this.leftover);\n    if (want > bytes)\n      want = bytes;\n    for (i = 0; i < want; i++)\n      this.buffer[this.leftover + i] = m[mpos+i];\n    bytes -= want;\n    mpos += want;\n    this.leftover += want;\n    if (this.leftover < 16)\n      return;\n    this.blocks(this.buffer, 0, 16);\n    this.leftover = 0;\n  }\n\n  if (bytes >= 16) {\n    want = bytes - (bytes % 16);\n    this.blocks(m, mpos, want);\n    mpos += want;\n    bytes -= want;\n  }\n\n  if (bytes) {\n    for (i = 0; i < bytes; i++)\n      this.buffer[this.leftover + i] = m[mpos+i];\n    this.leftover += bytes;\n  }\n};\n\nfunction crypto_onetimeauth(out, outpos, m, mpos, n, k) {\n  var s = new poly1305(k);\n  s.update(m, mpos, n);\n  s.finish(out, outpos);\n  return 0;\n}\n\nfunction crypto_onetimeauth_verify(h, hpos, m, mpos, n, k) {\n  var x = new Uint8Array(16);\n  crypto_onetimeauth(x,0,m,mpos,n,k);\n  return crypto_verify_16(h,hpos,x,0);\n}\n\nfunction crypto_secretbox(c,m,d,n,k) {\n  var i;\n  if (d < 32) return -1;\n  crypto_stream_xor(c,0,m,0,d,n,k);\n  crypto_onetimeauth(c, 16, c, 32, d - 32, c);\n  for (i = 0; i < 16; i++) c[i] = 0;\n  return 0;\n}\n\nfunction crypto_secretbox_open(m,c,d,n,k) {\n  var i;\n  var x = new Uint8Array(32);\n  if (d < 32) return -1;\n  crypto_stream(x,0,32,n,k);\n  if (crypto_onetimeauth_verify(c, 16,c, 32,d - 32,x) !== 0) return -1;\n  crypto_stream_xor(m,0,c,0,d,n,k);\n  for (i = 0; i < 32; i++) m[i] = 0;\n  return 0;\n}\n\nfunction set25519(r, a) {\n  var i;\n  for (i = 0; i < 16; i++) r[i] = a[i]|0;\n}\n\nfunction car25519(o) {\n  var i, v, c = 1;\n  for (i = 0; i < 16; i++) {\n    v = o[i] + c + 65535;\n    c = Math.floor(v / 65536);\n    o[i] = v - c * 65536;\n  }\n  o[0] += c-1 + 37 * (c-1);\n}\n\nfunction sel25519(p, q, b) {\n  var t, c = ~(b-1);\n  for (var i = 0; i < 16; i++) {\n    t = c & (p[i] ^ q[i]);\n    p[i] ^= t;\n    q[i] ^= t;\n  }\n}\n\nfunction pack25519(o, n) {\n  var i, j, b;\n  var m = gf(), t = gf();\n  for (i = 0; i < 16; i++) t[i] = n[i];\n  car25519(t);\n  car25519(t);\n  car25519(t);\n  for (j = 0; j < 2; j++) {\n    m[0] = t[0] - 0xffed;\n    for (i = 1; i < 15; i++) {\n      m[i] = t[i] - 0xffff - ((m[i-1]>>16) & 1);\n      m[i-1] &= 0xffff;\n    }\n    m[15] = t[15] - 0x7fff - ((m[14]>>16) & 1);\n    b = (m[15]>>16) & 1;\n    m[14] &= 0xffff;\n    sel25519(t, m, 1-b);\n  }\n  for (i = 0; i < 16; i++) {\n    o[2*i] = t[i] & 0xff;\n    o[2*i+1] = t[i]>>8;\n  }\n}\n\nfunction neq25519(a, b) {\n  var c = new Uint8Array(32), d = new Uint8Array(32);\n  pack25519(c, a);\n  pack25519(d, b);\n  return crypto_verify_32(c, 0, d, 0);\n}\n\nfunction par25519(a) {\n  var d = new Uint8Array(32);\n  pack25519(d, a);\n  return d[0] & 1;\n}\n\nfunction unpack25519(o, n) {\n  var i;\n  for (i = 0; i < 16; i++) o[i] = n[2*i] + (n[2*i+1] << 8);\n  o[15] &= 0x7fff;\n}\n\nfunction A(o, a, b) {\n  for (var i = 0; i < 16; i++) o[i] = a[i] + b[i];\n}\n\nfunction Z(o, a, b) {\n  for (var i = 0; i < 16; i++) o[i] = a[i] - b[i];\n}\n\nfunction M(o, a, b) {\n  var v, c,\n     t0 = 0,  t1 = 0,  t2 = 0,  t3 = 0,  t4 = 0,  t5 = 0,  t6 = 0,  t7 = 0,\n     t8 = 0,  t9 = 0, t10 = 0, t11 = 0, t12 = 0, t13 = 0, t14 = 0, t15 = 0,\n    t16 = 0, t17 = 0, t18 = 0, t19 = 0, t20 = 0, t21 = 0, t22 = 0, t23 = 0,\n    t24 = 0, t25 = 0, t26 = 0, t27 = 0, t28 = 0, t29 = 0, t30 = 0,\n    b0 = b[0],\n    b1 = b[1],\n    b2 = b[2],\n    b3 = b[3],\n    b4 = b[4],\n    b5 = b[5],\n    b6 = b[6],\n    b7 = b[7],\n    b8 = b[8],\n    b9 = b[9],\n    b10 = b[10],\n    b11 = b[11],\n    b12 = b[12],\n    b13 = b[13],\n    b14 = b[14],\n    b15 = b[15];\n\n  v = a[0];\n  t0 += v * b0;\n  t1 += v * b1;\n  t2 += v * b2;\n  t3 += v * b3;\n  t4 += v * b4;\n  t5 += v * b5;\n  t6 += v * b6;\n  t7 += v * b7;\n  t8 += v * b8;\n  t9 += v * b9;\n  t10 += v * b10;\n  t11 += v * b11;\n  t12 += v * b12;\n  t13 += v * b13;\n  t14 += v * b14;\n  t15 += v * b15;\n  v = a[1];\n  t1 += v * b0;\n  t2 += v * b1;\n  t3 += v * b2;\n  t4 += v * b3;\n  t5 += v * b4;\n  t6 += v * b5;\n  t7 += v * b6;\n  t8 += v * b7;\n  t9 += v * b8;\n  t10 += v * b9;\n  t11 += v * b10;\n  t12 += v * b11;\n  t13 += v * b12;\n  t14 += v * b13;\n  t15 += v * b14;\n  t16 += v * b15;\n  v = a[2];\n  t2 += v * b0;\n  t3 += v * b1;\n  t4 += v * b2;\n  t5 += v * b3;\n  t6 += v * b4;\n  t7 += v * b5;\n  t8 += v * b6;\n  t9 += v * b7;\n  t10 += v * b8;\n  t11 += v * b9;\n  t12 += v * b10;\n  t13 += v * b11;\n  t14 += v * b12;\n  t15 += v * b13;\n  t16 += v * b14;\n  t17 += v * b15;\n  v = a[3];\n  t3 += v * b0;\n  t4 += v * b1;\n  t5 += v * b2;\n  t6 += v * b3;\n  t7 += v * b4;\n  t8 += v * b5;\n  t9 += v * b6;\n  t10 += v * b7;\n  t11 += v * b8;\n  t12 += v * b9;\n  t13 += v * b10;\n  t14 += v * b11;\n  t15 += v * b12;\n  t16 += v * b13;\n  t17 += v * b14;\n  t18 += v * b15;\n  v = a[4];\n  t4 += v * b0;\n  t5 += v * b1;\n  t6 += v * b2;\n  t7 += v * b3;\n  t8 += v * b4;\n  t9 += v * b5;\n  t10 += v * b6;\n  t11 += v * b7;\n  t12 += v * b8;\n  t13 += v * b9;\n  t14 += v * b10;\n  t15 += v * b11;\n  t16 += v * b12;\n  t17 += v * b13;\n  t18 += v * b14;\n  t19 += v * b15;\n  v = a[5];\n  t5 += v * b0;\n  t6 += v * b1;\n  t7 += v * b2;\n  t8 += v * b3;\n  t9 += v * b4;\n  t10 += v * b5;\n  t11 += v * b6;\n  t12 += v * b7;\n  t13 += v * b8;\n  t14 += v * b9;\n  t15 += v * b10;\n  t16 += v * b11;\n  t17 += v * b12;\n  t18 += v * b13;\n  t19 += v * b14;\n  t20 += v * b15;\n  v = a[6];\n  t6 += v * b0;\n  t7 += v * b1;\n  t8 += v * b2;\n  t9 += v * b3;\n  t10 += v * b4;\n  t11 += v * b5;\n  t12 += v * b6;\n  t13 += v * b7;\n  t14 += v * b8;\n  t15 += v * b9;\n  t16 += v * b10;\n  t17 += v * b11;\n  t18 += v * b12;\n  t19 += v * b13;\n  t20 += v * b14;\n  t21 += v * b15;\n  v = a[7];\n  t7 += v * b0;\n  t8 += v * b1;\n  t9 += v * b2;\n  t10 += v * b3;\n  t11 += v * b4;\n  t12 += v * b5;\n  t13 += v * b6;\n  t14 += v * b7;\n  t15 += v * b8;\n  t16 += v * b9;\n  t17 += v * b10;\n  t18 += v * b11;\n  t19 += v * b12;\n  t20 += v * b13;\n  t21 += v * b14;\n  t22 += v * b15;\n  v = a[8];\n  t8 += v * b0;\n  t9 += v * b1;\n  t10 += v * b2;\n  t11 += v * b3;\n  t12 += v * b4;\n  t13 += v * b5;\n  t14 += v * b6;\n  t15 += v * b7;\n  t16 += v * b8;\n  t17 += v * b9;\n  t18 += v * b10;\n  t19 += v * b11;\n  t20 += v * b12;\n  t21 += v * b13;\n  t22 += v * b14;\n  t23 += v * b15;\n  v = a[9];\n  t9 += v * b0;\n  t10 += v * b1;\n  t11 += v * b2;\n  t12 += v * b3;\n  t13 += v * b4;\n  t14 += v * b5;\n  t15 += v * b6;\n  t16 += v * b7;\n  t17 += v * b8;\n  t18 += v * b9;\n  t19 += v * b10;\n  t20 += v * b11;\n  t21 += v * b12;\n  t22 += v * b13;\n  t23 += v * b14;\n  t24 += v * b15;\n  v = a[10];\n  t10 += v * b0;\n  t11 += v * b1;\n  t12 += v * b2;\n  t13 += v * b3;\n  t14 += v * b4;\n  t15 += v * b5;\n  t16 += v * b6;\n  t17 += v * b7;\n  t18 += v * b8;\n  t19 += v * b9;\n  t20 += v * b10;\n  t21 += v * b11;\n  t22 += v * b12;\n  t23 += v * b13;\n  t24 += v * b14;\n  t25 += v * b15;\n  v = a[11];\n  t11 += v * b0;\n  t12 += v * b1;\n  t13 += v * b2;\n  t14 += v * b3;\n  t15 += v * b4;\n  t16 += v * b5;\n  t17 += v * b6;\n  t18 += v * b7;\n  t19 += v * b8;\n  t20 += v * b9;\n  t21 += v * b10;\n  t22 += v * b11;\n  t23 += v * b12;\n  t24 += v * b13;\n  t25 += v * b14;\n  t26 += v * b15;\n  v = a[12];\n  t12 += v * b0;\n  t13 += v * b1;\n  t14 += v * b2;\n  t15 += v * b3;\n  t16 += v * b4;\n  t17 += v * b5;\n  t18 += v * b6;\n  t19 += v * b7;\n  t20 += v * b8;\n  t21 += v * b9;\n  t22 += v * b10;\n  t23 += v * b11;\n  t24 += v * b12;\n  t25 += v * b13;\n  t26 += v * b14;\n  t27 += v * b15;\n  v = a[13];\n  t13 += v * b0;\n  t14 += v * b1;\n  t15 += v * b2;\n  t16 += v * b3;\n  t17 += v * b4;\n  t18 += v * b5;\n  t19 += v * b6;\n  t20 += v * b7;\n  t21 += v * b8;\n  t22 += v * b9;\n  t23 += v * b10;\n  t24 += v * b11;\n  t25 += v * b12;\n  t26 += v * b13;\n  t27 += v * b14;\n  t28 += v * b15;\n  v = a[14];\n  t14 += v * b0;\n  t15 += v * b1;\n  t16 += v * b2;\n  t17 += v * b3;\n  t18 += v * b4;\n  t19 += v * b5;\n  t20 += v * b6;\n  t21 += v * b7;\n  t22 += v * b8;\n  t23 += v * b9;\n  t24 += v * b10;\n  t25 += v * b11;\n  t26 += v * b12;\n  t27 += v * b13;\n  t28 += v * b14;\n  t29 += v * b15;\n  v = a[15];\n  t15 += v * b0;\n  t16 += v * b1;\n  t17 += v * b2;\n  t18 += v * b3;\n  t19 += v * b4;\n  t20 += v * b5;\n  t21 += v * b6;\n  t22 += v * b7;\n  t23 += v * b8;\n  t24 += v * b9;\n  t25 += v * b10;\n  t26 += v * b11;\n  t27 += v * b12;\n  t28 += v * b13;\n  t29 += v * b14;\n  t30 += v * b15;\n\n  t0  += 38 * t16;\n  t1  += 38 * t17;\n  t2  += 38 * t18;\n  t3  += 38 * t19;\n  t4  += 38 * t20;\n  t5  += 38 * t21;\n  t6  += 38 * t22;\n  t7  += 38 * t23;\n  t8  += 38 * t24;\n  t9  += 38 * t25;\n  t10 += 38 * t26;\n  t11 += 38 * t27;\n  t12 += 38 * t28;\n  t13 += 38 * t29;\n  t14 += 38 * t30;\n  // t15 left as is\n\n  // first car\n  c = 1;\n  v =  t0 + c + 65535; c = Math.floor(v / 65536);  t0 = v - c * 65536;\n  v =  t1 + c + 65535; c = Math.floor(v / 65536);  t1 = v - c * 65536;\n  v =  t2 + c + 65535; c = Math.floor(v / 65536);  t2 = v - c * 65536;\n  v =  t3 + c + 65535; c = Math.floor(v / 65536);  t3 = v - c * 65536;\n  v =  t4 + c + 65535; c = Math.floor(v / 65536);  t4 = v - c * 65536;\n  v =  t5 + c + 65535; c = Math.floor(v / 65536);  t5 = v - c * 65536;\n  v =  t6 + c + 65535; c = Math.floor(v / 65536);  t6 = v - c * 65536;\n  v =  t7 + c + 65535; c = Math.floor(v / 65536);  t7 = v - c * 65536;\n  v =  t8 + c + 65535; c = Math.floor(v / 65536);  t8 = v - c * 65536;\n  v =  t9 + c + 65535; c = Math.floor(v / 65536);  t9 = v - c * 65536;\n  v = t10 + c + 65535; c = Math.floor(v / 65536); t10 = v - c * 65536;\n  v = t11 + c + 65535; c = Math.floor(v / 65536); t11 = v - c * 65536;\n  v = t12 + c + 65535; c = Math.floor(v / 65536); t12 = v - c * 65536;\n  v = t13 + c + 65535; c = Math.floor(v / 65536); t13 = v - c * 65536;\n  v = t14 + c + 65535; c = Math.floor(v / 65536); t14 = v - c * 65536;\n  v = t15 + c + 65535; c = Math.floor(v / 65536); t15 = v - c * 65536;\n  t0 += c-1 + 37 * (c-1);\n\n  // second car\n  c = 1;\n  v =  t0 + c + 65535; c = Math.floor(v / 65536);  t0 = v - c * 65536;\n  v =  t1 + c + 65535; c = Math.floor(v / 65536);  t1 = v - c * 65536;\n  v =  t2 + c + 65535; c = Math.floor(v / 65536);  t2 = v - c * 65536;\n  v =  t3 + c + 65535; c = Math.floor(v / 65536);  t3 = v - c * 65536;\n  v =  t4 + c + 65535; c = Math.floor(v / 65536);  t4 = v - c * 65536;\n  v =  t5 + c + 65535; c = Math.floor(v / 65536);  t5 = v - c * 65536;\n  v =  t6 + c + 65535; c = Math.floor(v / 65536);  t6 = v - c * 65536;\n  v =  t7 + c + 65535; c = Math.floor(v / 65536);  t7 = v - c * 65536;\n  v =  t8 + c + 65535; c = Math.floor(v / 65536);  t8 = v - c * 65536;\n  v =  t9 + c + 65535; c = Math.floor(v / 65536);  t9 = v - c * 65536;\n  v = t10 + c + 65535; c = Math.floor(v / 65536); t10 = v - c * 65536;\n  v = t11 + c + 65535; c = Math.floor(v / 65536); t11 = v - c * 65536;\n  v = t12 + c + 65535; c = Math.floor(v / 65536); t12 = v - c * 65536;\n  v = t13 + c + 65535; c = Math.floor(v / 65536); t13 = v - c * 65536;\n  v = t14 + c + 65535; c = Math.floor(v / 65536); t14 = v - c * 65536;\n  v = t15 + c + 65535; c = Math.floor(v / 65536); t15 = v - c * 65536;\n  t0 += c-1 + 37 * (c-1);\n\n  o[ 0] = t0;\n  o[ 1] = t1;\n  o[ 2] = t2;\n  o[ 3] = t3;\n  o[ 4] = t4;\n  o[ 5] = t5;\n  o[ 6] = t6;\n  o[ 7] = t7;\n  o[ 8] = t8;\n  o[ 9] = t9;\n  o[10] = t10;\n  o[11] = t11;\n  o[12] = t12;\n  o[13] = t13;\n  o[14] = t14;\n  o[15] = t15;\n}\n\nfunction S(o, a) {\n  M(o, a, a);\n}\n\nfunction inv25519(o, i) {\n  var c = gf();\n  var a;\n  for (a = 0; a < 16; a++) c[a] = i[a];\n  for (a = 253; a >= 0; a--) {\n    S(c, c);\n    if(a !== 2 && a !== 4) M(c, c, i);\n  }\n  for (a = 0; a < 16; a++) o[a] = c[a];\n}\n\nfunction pow2523(o, i) {\n  var c = gf();\n  var a;\n  for (a = 0; a < 16; a++) c[a] = i[a];\n  for (a = 250; a >= 0; a--) {\n      S(c, c);\n      if(a !== 1) M(c, c, i);\n  }\n  for (a = 0; a < 16; a++) o[a] = c[a];\n}\n\nfunction crypto_scalarmult(q, n, p) {\n  var z = new Uint8Array(32);\n  var x = new Float64Array(80), r, i;\n  var a = gf(), b = gf(), c = gf(),\n      d = gf(), e = gf(), f = gf();\n  for (i = 0; i < 31; i++) z[i] = n[i];\n  z[31]=(n[31]&127)|64;\n  z[0]&=248;\n  unpack25519(x,p);\n  for (i = 0; i < 16; i++) {\n    b[i]=x[i];\n    d[i]=a[i]=c[i]=0;\n  }\n  a[0]=d[0]=1;\n  for (i=254; i>=0; --i) {\n    r=(z[i>>>3]>>>(i&7))&1;\n    sel25519(a,b,r);\n    sel25519(c,d,r);\n    A(e,a,c);\n    Z(a,a,c);\n    A(c,b,d);\n    Z(b,b,d);\n    S(d,e);\n    S(f,a);\n    M(a,c,a);\n    M(c,b,e);\n    A(e,a,c);\n    Z(a,a,c);\n    S(b,a);\n    Z(c,d,f);\n    M(a,c,_121665);\n    A(a,a,d);\n    M(c,c,a);\n    M(a,d,f);\n    M(d,b,x);\n    S(b,e);\n    sel25519(a,b,r);\n    sel25519(c,d,r);\n  }\n  for (i = 0; i < 16; i++) {\n    x[i+16]=a[i];\n    x[i+32]=c[i];\n    x[i+48]=b[i];\n    x[i+64]=d[i];\n  }\n  var x32 = x.subarray(32);\n  var x16 = x.subarray(16);\n  inv25519(x32,x32);\n  M(x16,x16,x32);\n  pack25519(q,x16);\n  return 0;\n}\n\nfunction crypto_scalarmult_base(q, n) {\n  return crypto_scalarmult(q, n, _9);\n}\n\nfunction crypto_box_keypair(y, x) {\n  randombytes(x, 32);\n  return crypto_scalarmult_base(y, x);\n}\n\nfunction crypto_box_beforenm(k, y, x) {\n  var s = new Uint8Array(32);\n  crypto_scalarmult(s, x, y);\n  return crypto_core_hsalsa20(k, _0, s, sigma);\n}\n\nvar crypto_box_afternm = crypto_secretbox;\nvar crypto_box_open_afternm = crypto_secretbox_open;\n\nfunction crypto_box(c, m, d, n, y, x) {\n  var k = new Uint8Array(32);\n  crypto_box_beforenm(k, y, x);\n  return crypto_box_afternm(c, m, d, n, k);\n}\n\nfunction crypto_box_open(m, c, d, n, y, x) {\n  var k = new Uint8Array(32);\n  crypto_box_beforenm(k, y, x);\n  return crypto_box_open_afternm(m, c, d, n, k);\n}\n\nvar K = [\n  0x428a2f98, 0xd728ae22, 0x71374491, 0x23ef65cd,\n  0xb5c0fbcf, 0xec4d3b2f, 0xe9b5dba5, 0x8189dbbc,\n  0x3956c25b, 0xf348b538, 0x59f111f1, 0xb605d019,\n  0x923f82a4, 0xaf194f9b, 0xab1c5ed5, 0xda6d8118,\n  0xd807aa98, 0xa3030242, 0x12835b01, 0x45706fbe,\n  0x243185be, 0x4ee4b28c, 0x550c7dc3, 0xd5ffb4e2,\n  0x72be5d74, 0xf27b896f, 0x80deb1fe, 0x3b1696b1,\n  0x9bdc06a7, 0x25c71235, 0xc19bf174, 0xcf692694,\n  0xe49b69c1, 0x9ef14ad2, 0xefbe4786, 0x384f25e3,\n  0x0fc19dc6, 0x8b8cd5b5, 0x240ca1cc, 0x77ac9c65,\n  0x2de92c6f, 0x592b0275, 0x4a7484aa, 0x6ea6e483,\n  0x5cb0a9dc, 0xbd41fbd4, 0x76f988da, 0x831153b5,\n  0x983e5152, 0xee66dfab, 0xa831c66d, 0x2db43210,\n  0xb00327c8, 0x98fb213f, 0xbf597fc7, 0xbeef0ee4,\n  0xc6e00bf3, 0x3da88fc2, 0xd5a79147, 0x930aa725,\n  0x06ca6351, 0xe003826f, 0x14292967, 0x0a0e6e70,\n  0x27b70a85, 0x46d22ffc, 0x2e1b2138, 0x5c26c926,\n  0x4d2c6dfc, 0x5ac42aed, 0x53380d13, 0x9d95b3df,\n  0x650a7354, 0x8baf63de, 0x766a0abb, 0x3c77b2a8,\n  0x81c2c92e, 0x47edaee6, 0x92722c85, 0x1482353b,\n  0xa2bfe8a1, 0x4cf10364, 0xa81a664b, 0xbc423001,\n  0xc24b8b70, 0xd0f89791, 0xc76c51a3, 0x0654be30,\n  0xd192e819, 0xd6ef5218, 0xd6990624, 0x5565a910,\n  0xf40e3585, 0x5771202a, 0x106aa070, 0x32bbd1b8,\n  0x19a4c116, 0xb8d2d0c8, 0x1e376c08, 0x5141ab53,\n  0x2748774c, 0xdf8eeb99, 0x34b0bcb5, 0xe19b48a8,\n  0x391c0cb3, 0xc5c95a63, 0x4ed8aa4a, 0xe3418acb,\n  0x5b9cca4f, 0x7763e373, 0x682e6ff3, 0xd6b2b8a3,\n  0x748f82ee, 0x5defb2fc, 0x78a5636f, 0x43172f60,\n  0x84c87814, 0xa1f0ab72, 0x8cc70208, 0x1a6439ec,\n  0x90befffa, 0x23631e28, 0xa4506ceb, 0xde82bde9,\n  0xbef9a3f7, 0xb2c67915, 0xc67178f2, 0xe372532b,\n  0xca273ece, 0xea26619c, 0xd186b8c7, 0x21c0c207,\n  0xeada7dd6, 0xcde0eb1e, 0xf57d4f7f, 0xee6ed178,\n  0x06f067aa, 0x72176fba, 0x0a637dc5, 0xa2c898a6,\n  0x113f9804, 0xbef90dae, 0x1b710b35, 0x131c471b,\n  0x28db77f5, 0x23047d84, 0x32caab7b, 0x40c72493,\n  0x3c9ebe0a, 0x15c9bebc, 0x431d67c4, 0x9c100d4c,\n  0x4cc5d4be, 0xcb3e42b6, 0x597f299c, 0xfc657e2a,\n  0x5fcb6fab, 0x3ad6faec, 0x6c44198c, 0x4a475817\n];\n\nfunction crypto_hashblocks_hl(hh, hl, m, n) {\n  var wh = new Int32Array(16), wl = new Int32Array(16),\n      bh0, bh1, bh2, bh3, bh4, bh5, bh6, bh7,\n      bl0, bl1, bl2, bl3, bl4, bl5, bl6, bl7,\n      th, tl, i, j, h, l, a, b, c, d;\n\n  var ah0 = hh[0],\n      ah1 = hh[1],\n      ah2 = hh[2],\n      ah3 = hh[3],\n      ah4 = hh[4],\n      ah5 = hh[5],\n      ah6 = hh[6],\n      ah7 = hh[7],\n\n      al0 = hl[0],\n      al1 = hl[1],\n      al2 = hl[2],\n      al3 = hl[3],\n      al4 = hl[4],\n      al5 = hl[5],\n      al6 = hl[6],\n      al7 = hl[7];\n\n  var pos = 0;\n  while (n >= 128) {\n    for (i = 0; i < 16; i++) {\n      j = 8 * i + pos;\n      wh[i] = (m[j+0] << 24) | (m[j+1] << 16) | (m[j+2] << 8) | m[j+3];\n      wl[i] = (m[j+4] << 24) | (m[j+5] << 16) | (m[j+6] << 8) | m[j+7];\n    }\n    for (i = 0; i < 80; i++) {\n      bh0 = ah0;\n      bh1 = ah1;\n      bh2 = ah2;\n      bh3 = ah3;\n      bh4 = ah4;\n      bh5 = ah5;\n      bh6 = ah6;\n      bh7 = ah7;\n\n      bl0 = al0;\n      bl1 = al1;\n      bl2 = al2;\n      bl3 = al3;\n      bl4 = al4;\n      bl5 = al5;\n      bl6 = al6;\n      bl7 = al7;\n\n      // add\n      h = ah7;\n      l = al7;\n\n      a = l & 0xffff; b = l >>> 16;\n      c = h & 0xffff; d = h >>> 16;\n\n      // Sigma1\n      h = ((ah4 >>> 14) | (al4 << (32-14))) ^ ((ah4 >>> 18) | (al4 << (32-18))) ^ ((al4 >>> (41-32)) | (ah4 << (32-(41-32))));\n      l = ((al4 >>> 14) | (ah4 << (32-14))) ^ ((al4 >>> 18) | (ah4 << (32-18))) ^ ((ah4 >>> (41-32)) | (al4 << (32-(41-32))));\n\n      a += l & 0xffff; b += l >>> 16;\n      c += h & 0xffff; d += h >>> 16;\n\n      // Ch\n      h = (ah4 & ah5) ^ (~ah4 & ah6);\n      l = (al4 & al5) ^ (~al4 & al6);\n\n      a += l & 0xffff; b += l >>> 16;\n      c += h & 0xffff; d += h >>> 16;\n\n      // K\n      h = K[i*2];\n      l = K[i*2+1];\n\n      a += l & 0xffff; b += l >>> 16;\n      c += h & 0xffff; d += h >>> 16;\n\n      // w\n      h = wh[i%16];\n      l = wl[i%16];\n\n      a += l & 0xffff; b += l >>> 16;\n      c += h & 0xffff; d += h >>> 16;\n\n      b += a >>> 16;\n      c += b >>> 16;\n      d += c >>> 16;\n\n      th = c & 0xffff | d << 16;\n      tl = a & 0xffff | b << 16;\n\n      // add\n      h = th;\n      l = tl;\n\n      a = l & 0xffff; b = l >>> 16;\n      c = h & 0xffff; d = h >>> 16;\n\n      // Sigma0\n      h = ((ah0 >>> 28) | (al0 << (32-28))) ^ ((al0 >>> (34-32)) | (ah0 << (32-(34-32)))) ^ ((al0 >>> (39-32)) | (ah0 << (32-(39-32))));\n      l = ((al0 >>> 28) | (ah0 << (32-28))) ^ ((ah0 >>> (34-32)) | (al0 << (32-(34-32)))) ^ ((ah0 >>> (39-32)) | (al0 << (32-(39-32))));\n\n      a += l & 0xffff; b += l >>> 16;\n      c += h & 0xffff; d += h >>> 16;\n\n      // Maj\n      h = (ah0 & ah1) ^ (ah0 & ah2) ^ (ah1 & ah2);\n      l = (al0 & al1) ^ (al0 & al2) ^ (al1 & al2);\n\n      a += l & 0xffff; b += l >>> 16;\n      c += h & 0xffff; d += h >>> 16;\n\n      b += a >>> 16;\n      c += b >>> 16;\n      d += c >>> 16;\n\n      bh7 = (c & 0xffff) | (d << 16);\n      bl7 = (a & 0xffff) | (b << 16);\n\n      // add\n      h = bh3;\n      l = bl3;\n\n      a = l & 0xffff; b = l >>> 16;\n      c = h & 0xffff; d = h >>> 16;\n\n      h = th;\n      l = tl;\n\n      a += l & 0xffff; b += l >>> 16;\n      c += h & 0xffff; d += h >>> 16;\n\n      b += a >>> 16;\n      c += b >>> 16;\n      d += c >>> 16;\n\n      bh3 = (c & 0xffff) | (d << 16);\n      bl3 = (a & 0xffff) | (b << 16);\n\n      ah1 = bh0;\n      ah2 = bh1;\n      ah3 = bh2;\n      ah4 = bh3;\n      ah5 = bh4;\n      ah6 = bh5;\n      ah7 = bh6;\n      ah0 = bh7;\n\n      al1 = bl0;\n      al2 = bl1;\n      al3 = bl2;\n      al4 = bl3;\n      al5 = bl4;\n      al6 = bl5;\n      al7 = bl6;\n      al0 = bl7;\n\n      if (i%16 === 15) {\n        for (j = 0; j < 16; j++) {\n          // add\n          h = wh[j];\n          l = wl[j];\n\n          a = l & 0xffff; b = l >>> 16;\n          c = h & 0xffff; d = h >>> 16;\n\n          h = wh[(j+9)%16];\n          l = wl[(j+9)%16];\n\n          a += l & 0xffff; b += l >>> 16;\n          c += h & 0xffff; d += h >>> 16;\n\n          // sigma0\n          th = wh[(j+1)%16];\n          tl = wl[(j+1)%16];\n          h = ((th >>> 1) | (tl << (32-1))) ^ ((th >>> 8) | (tl << (32-8))) ^ (th >>> 7);\n          l = ((tl >>> 1) | (th << (32-1))) ^ ((tl >>> 8) | (th << (32-8))) ^ ((tl >>> 7) | (th << (32-7)));\n\n          a += l & 0xffff; b += l >>> 16;\n          c += h & 0xffff; d += h >>> 16;\n\n          // sigma1\n          th = wh[(j+14)%16];\n          tl = wl[(j+14)%16];\n          h = ((th >>> 19) | (tl << (32-19))) ^ ((tl >>> (61-32)) | (th << (32-(61-32)))) ^ (th >>> 6);\n          l = ((tl >>> 19) | (th << (32-19))) ^ ((th >>> (61-32)) | (tl << (32-(61-32)))) ^ ((tl >>> 6) | (th << (32-6)));\n\n          a += l & 0xffff; b += l >>> 16;\n          c += h & 0xffff; d += h >>> 16;\n\n          b += a >>> 16;\n          c += b >>> 16;\n          d += c >>> 16;\n\n          wh[j] = (c & 0xffff) | (d << 16);\n          wl[j] = (a & 0xffff) | (b << 16);\n        }\n      }\n    }\n\n    // add\n    h = ah0;\n    l = al0;\n\n    a = l & 0xffff; b = l >>> 16;\n    c = h & 0xffff; d = h >>> 16;\n\n    h = hh[0];\n    l = hl[0];\n\n    a += l & 0xffff; b += l >>> 16;\n    c += h & 0xffff; d += h >>> 16;\n\n    b += a >>> 16;\n    c += b >>> 16;\n    d += c >>> 16;\n\n    hh[0] = ah0 = (c & 0xffff) | (d << 16);\n    hl[0] = al0 = (a & 0xffff) | (b << 16);\n\n    h = ah1;\n    l = al1;\n\n    a = l & 0xffff; b = l >>> 16;\n    c = h & 0xffff; d = h >>> 16;\n\n    h = hh[1];\n    l = hl[1];\n\n    a += l & 0xffff; b += l >>> 16;\n    c += h & 0xffff; d += h >>> 16;\n\n    b += a >>> 16;\n    c += b >>> 16;\n    d += c >>> 16;\n\n    hh[1] = ah1 = (c & 0xffff) | (d << 16);\n    hl[1] = al1 = (a & 0xffff) | (b << 16);\n\n    h = ah2;\n    l = al2;\n\n    a = l & 0xffff; b = l >>> 16;\n    c = h & 0xffff; d = h >>> 16;\n\n    h = hh[2];\n    l = hl[2];\n\n    a += l & 0xffff; b += l >>> 16;\n    c += h & 0xffff; d += h >>> 16;\n\n    b += a >>> 16;\n    c += b >>> 16;\n    d += c >>> 16;\n\n    hh[2] = ah2 = (c & 0xffff) | (d << 16);\n    hl[2] = al2 = (a & 0xffff) | (b << 16);\n\n    h = ah3;\n    l = al3;\n\n    a = l & 0xffff; b = l >>> 16;\n    c = h & 0xffff; d = h >>> 16;\n\n    h = hh[3];\n    l = hl[3];\n\n    a += l & 0xffff; b += l >>> 16;\n    c += h & 0xffff; d += h >>> 16;\n\n    b += a >>> 16;\n    c += b >>> 16;\n    d += c >>> 16;\n\n    hh[3] = ah3 = (c & 0xffff) | (d << 16);\n    hl[3] = al3 = (a & 0xffff) | (b << 16);\n\n    h = ah4;\n    l = al4;\n\n    a = l & 0xffff; b = l >>> 16;\n    c = h & 0xffff; d = h >>> 16;\n\n    h = hh[4];\n    l = hl[4];\n\n    a += l & 0xffff; b += l >>> 16;\n    c += h & 0xffff; d += h >>> 16;\n\n    b += a >>> 16;\n    c += b >>> 16;\n    d += c >>> 16;\n\n    hh[4] = ah4 = (c & 0xffff) | (d << 16);\n    hl[4] = al4 = (a & 0xffff) | (b << 16);\n\n    h = ah5;\n    l = al5;\n\n    a = l & 0xffff; b = l >>> 16;\n    c = h & 0xffff; d = h >>> 16;\n\n    h = hh[5];\n    l = hl[5];\n\n    a += l & 0xffff; b += l >>> 16;\n    c += h & 0xffff; d += h >>> 16;\n\n    b += a >>> 16;\n    c += b >>> 16;\n    d += c >>> 16;\n\n    hh[5] = ah5 = (c & 0xffff) | (d << 16);\n    hl[5] = al5 = (a & 0xffff) | (b << 16);\n\n    h = ah6;\n    l = al6;\n\n    a = l & 0xffff; b = l >>> 16;\n    c = h & 0xffff; d = h >>> 16;\n\n    h = hh[6];\n    l = hl[6];\n\n    a += l & 0xffff; b += l >>> 16;\n    c += h & 0xffff; d += h >>> 16;\n\n    b += a >>> 16;\n    c += b >>> 16;\n    d += c >>> 16;\n\n    hh[6] = ah6 = (c & 0xffff) | (d << 16);\n    hl[6] = al6 = (a & 0xffff) | (b << 16);\n\n    h = ah7;\n    l = al7;\n\n    a = l & 0xffff; b = l >>> 16;\n    c = h & 0xffff; d = h >>> 16;\n\n    h = hh[7];\n    l = hl[7];\n\n    a += l & 0xffff; b += l >>> 16;\n    c += h & 0xffff; d += h >>> 16;\n\n    b += a >>> 16;\n    c += b >>> 16;\n    d += c >>> 16;\n\n    hh[7] = ah7 = (c & 0xffff) | (d << 16);\n    hl[7] = al7 = (a & 0xffff) | (b << 16);\n\n    pos += 128;\n    n -= 128;\n  }\n\n  return n;\n}\n\nfunction crypto_hash(out, m, n) {\n  var hh = new Int32Array(8),\n      hl = new Int32Array(8),\n      x = new Uint8Array(256),\n      i, b = n;\n\n  hh[0] = 0x6a09e667;\n  hh[1] = 0xbb67ae85;\n  hh[2] = 0x3c6ef372;\n  hh[3] = 0xa54ff53a;\n  hh[4] = 0x510e527f;\n  hh[5] = 0x9b05688c;\n  hh[6] = 0x1f83d9ab;\n  hh[7] = 0x5be0cd19;\n\n  hl[0] = 0xf3bcc908;\n  hl[1] = 0x84caa73b;\n  hl[2] = 0xfe94f82b;\n  hl[3] = 0x5f1d36f1;\n  hl[4] = 0xade682d1;\n  hl[5] = 0x2b3e6c1f;\n  hl[6] = 0xfb41bd6b;\n  hl[7] = 0x137e2179;\n\n  crypto_hashblocks_hl(hh, hl, m, n);\n  n %= 128;\n\n  for (i = 0; i < n; i++) x[i] = m[b-n+i];\n  x[n] = 128;\n\n  n = 256-128*(n<112?1:0);\n  x[n-9] = 0;\n  ts64(x, n-8,  (b / 0x20000000) | 0, b << 3);\n  crypto_hashblocks_hl(hh, hl, x, n);\n\n  for (i = 0; i < 8; i++) ts64(out, 8*i, hh[i], hl[i]);\n\n  return 0;\n}\n\nfunction add(p, q) {\n  var a = gf(), b = gf(), c = gf(),\n      d = gf(), e = gf(), f = gf(),\n      g = gf(), h = gf(), t = gf();\n\n  Z(a, p[1], p[0]);\n  Z(t, q[1], q[0]);\n  M(a, a, t);\n  A(b, p[0], p[1]);\n  A(t, q[0], q[1]);\n  M(b, b, t);\n  M(c, p[3], q[3]);\n  M(c, c, D2);\n  M(d, p[2], q[2]);\n  A(d, d, d);\n  Z(e, b, a);\n  Z(f, d, c);\n  A(g, d, c);\n  A(h, b, a);\n\n  M(p[0], e, f);\n  M(p[1], h, g);\n  M(p[2], g, f);\n  M(p[3], e, h);\n}\n\nfunction cswap(p, q, b) {\n  var i;\n  for (i = 0; i < 4; i++) {\n    sel25519(p[i], q[i], b);\n  }\n}\n\nfunction pack(r, p) {\n  var tx = gf(), ty = gf(), zi = gf();\n  inv25519(zi, p[2]);\n  M(tx, p[0], zi);\n  M(ty, p[1], zi);\n  pack25519(r, ty);\n  r[31] ^= par25519(tx) << 7;\n}\n\nfunction scalarmult(p, q, s) {\n  var b, i;\n  set25519(p[0], gf0);\n  set25519(p[1], gf1);\n  set25519(p[2], gf1);\n  set25519(p[3], gf0);\n  for (i = 255; i >= 0; --i) {\n    b = (s[(i/8)|0] >> (i&7)) & 1;\n    cswap(p, q, b);\n    add(q, p);\n    add(p, p);\n    cswap(p, q, b);\n  }\n}\n\nfunction scalarbase(p, s) {\n  var q = [gf(), gf(), gf(), gf()];\n  set25519(q[0], X);\n  set25519(q[1], Y);\n  set25519(q[2], gf1);\n  M(q[3], X, Y);\n  scalarmult(p, q, s);\n}\n\nfunction crypto_sign_keypair(pk, sk, seeded) {\n  var d = new Uint8Array(64);\n  var p = [gf(), gf(), gf(), gf()];\n  var i;\n\n  if (!seeded) randombytes(sk, 32);\n  crypto_hash(d, sk, 32);\n  d[0] &= 248;\n  d[31] &= 127;\n  d[31] |= 64;\n\n  scalarbase(p, d);\n  pack(pk, p);\n\n  for (i = 0; i < 32; i++) sk[i+32] = pk[i];\n  return 0;\n}\n\nvar L = new Float64Array([0xed, 0xd3, 0xf5, 0x5c, 0x1a, 0x63, 0x12, 0x58, 0xd6, 0x9c, 0xf7, 0xa2, 0xde, 0xf9, 0xde, 0x14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0x10]);\n\nfunction modL(r, x) {\n  var carry, i, j, k;\n  for (i = 63; i >= 32; --i) {\n    carry = 0;\n    for (j = i - 32, k = i - 12; j < k; ++j) {\n      x[j] += carry - 16 * x[i] * L[j - (i - 32)];\n      carry = Math.floor((x[j] + 128) / 256);\n      x[j] -= carry * 256;\n    }\n    x[j] += carry;\n    x[i] = 0;\n  }\n  carry = 0;\n  for (j = 0; j < 32; j++) {\n    x[j] += carry - (x[31] >> 4) * L[j];\n    carry = x[j] >> 8;\n    x[j] &= 255;\n  }\n  for (j = 0; j < 32; j++) x[j] -= carry * L[j];\n  for (i = 0; i < 32; i++) {\n    x[i+1] += x[i] >> 8;\n    r[i] = x[i] & 255;\n  }\n}\n\nfunction reduce(r) {\n  var x = new Float64Array(64), i;\n  for (i = 0; i < 64; i++) x[i] = r[i];\n  for (i = 0; i < 64; i++) r[i] = 0;\n  modL(r, x);\n}\n\n// Note: difference from C - smlen returned, not passed as argument.\nfunction crypto_sign(sm, m, n, sk) {\n  var d = new Uint8Array(64), h = new Uint8Array(64), r = new Uint8Array(64);\n  var i, j, x = new Float64Array(64);\n  var p = [gf(), gf(), gf(), gf()];\n\n  crypto_hash(d, sk, 32);\n  d[0] &= 248;\n  d[31] &= 127;\n  d[31] |= 64;\n\n  var smlen = n + 64;\n  for (i = 0; i < n; i++) sm[64 + i] = m[i];\n  for (i = 0; i < 32; i++) sm[32 + i] = d[32 + i];\n\n  crypto_hash(r, sm.subarray(32), n+32);\n  reduce(r);\n  scalarbase(p, r);\n  pack(sm, p);\n\n  for (i = 32; i < 64; i++) sm[i] = sk[i];\n  crypto_hash(h, sm, n + 64);\n  reduce(h);\n\n  for (i = 0; i < 64; i++) x[i] = 0;\n  for (i = 0; i < 32; i++) x[i] = r[i];\n  for (i = 0; i < 32; i++) {\n    for (j = 0; j < 32; j++) {\n      x[i+j] += h[i] * d[j];\n    }\n  }\n\n  modL(sm.subarray(32), x);\n  return smlen;\n}\n\nfunction unpackneg(r, p) {\n  var t = gf(), chk = gf(), num = gf(),\n      den = gf(), den2 = gf(), den4 = gf(),\n      den6 = gf();\n\n  set25519(r[2], gf1);\n  unpack25519(r[1], p);\n  S(num, r[1]);\n  M(den, num, D);\n  Z(num, num, r[2]);\n  A(den, r[2], den);\n\n  S(den2, den);\n  S(den4, den2);\n  M(den6, den4, den2);\n  M(t, den6, num);\n  M(t, t, den);\n\n  pow2523(t, t);\n  M(t, t, num);\n  M(t, t, den);\n  M(t, t, den);\n  M(r[0], t, den);\n\n  S(chk, r[0]);\n  M(chk, chk, den);\n  if (neq25519(chk, num)) M(r[0], r[0], I);\n\n  S(chk, r[0]);\n  M(chk, chk, den);\n  if (neq25519(chk, num)) return -1;\n\n  if (par25519(r[0]) === (p[31]>>7)) Z(r[0], gf0, r[0]);\n\n  M(r[3], r[0], r[1]);\n  return 0;\n}\n\nfunction crypto_sign_open(m, sm, n, pk) {\n  var i;\n  var t = new Uint8Array(32), h = new Uint8Array(64);\n  var p = [gf(), gf(), gf(), gf()],\n      q = [gf(), gf(), gf(), gf()];\n\n  if (n < 64) return -1;\n\n  if (unpackneg(q, pk)) return -1;\n\n  for (i = 0; i < n; i++) m[i] = sm[i];\n  for (i = 0; i < 32; i++) m[i+32] = pk[i];\n  crypto_hash(h, m, n);\n  reduce(h);\n  scalarmult(p, q, h);\n\n  scalarbase(q, sm.subarray(32));\n  add(p, q);\n  pack(t, p);\n\n  n -= 64;\n  if (crypto_verify_32(sm, 0, t, 0)) {\n    for (i = 0; i < n; i++) m[i] = 0;\n    return -1;\n  }\n\n  for (i = 0; i < n; i++) m[i] = sm[i + 64];\n  return n;\n}\n\nvar crypto_secretbox_KEYBYTES = 32,\n    crypto_secretbox_NONCEBYTES = 24,\n    crypto_secretbox_ZEROBYTES = 32,\n    crypto_secretbox_BOXZEROBYTES = 16,\n    crypto_scalarmult_BYTES = 32,\n    crypto_scalarmult_SCALARBYTES = 32,\n    crypto_box_PUBLICKEYBYTES = 32,\n    crypto_box_SECRETKEYBYTES = 32,\n    crypto_box_BEFORENMBYTES = 32,\n    crypto_box_NONCEBYTES = crypto_secretbox_NONCEBYTES,\n    crypto_box_ZEROBYTES = crypto_secretbox_ZEROBYTES,\n    crypto_box_BOXZEROBYTES = crypto_secretbox_BOXZEROBYTES,\n    crypto_sign_BYTES = 64,\n    crypto_sign_PUBLICKEYBYTES = 32,\n    crypto_sign_SECRETKEYBYTES = 64,\n    crypto_sign_SEEDBYTES = 32,\n    crypto_hash_BYTES = 64;\n\nnacl.lowlevel = {\n  crypto_core_hsalsa20: crypto_core_hsalsa20,\n  crypto_stream_xor: crypto_stream_xor,\n  crypto_stream: crypto_stream,\n  crypto_stream_salsa20_xor: crypto_stream_salsa20_xor,\n  crypto_stream_salsa20: crypto_stream_salsa20,\n  crypto_onetimeauth: crypto_onetimeauth,\n  crypto_onetimeauth_verify: crypto_onetimeauth_verify,\n  crypto_verify_16: crypto_verify_16,\n  crypto_verify_32: crypto_verify_32,\n  crypto_secretbox: crypto_secretbox,\n  crypto_secretbox_open: crypto_secretbox_open,\n  crypto_scalarmult: crypto_scalarmult,\n  crypto_scalarmult_base: crypto_scalarmult_base,\n  crypto_box_beforenm: crypto_box_beforenm,\n  crypto_box_afternm: crypto_box_afternm,\n  crypto_box: crypto_box,\n  crypto_box_open: crypto_box_open,\n  crypto_box_keypair: crypto_box_keypair,\n  crypto_hash: crypto_hash,\n  crypto_sign: crypto_sign,\n  crypto_sign_keypair: crypto_sign_keypair,\n  crypto_sign_open: crypto_sign_open,\n\n  crypto_secretbox_KEYBYTES: crypto_secretbox_KEYBYTES,\n  crypto_secretbox_NONCEBYTES: crypto_secretbox_NONCEBYTES,\n  crypto_secretbox_ZEROBYTES: crypto_secretbox_ZEROBYTES,\n  crypto_secretbox_BOXZEROBYTES: crypto_secretbox_BOXZEROBYTES,\n  crypto_scalarmult_BYTES: crypto_scalarmult_BYTES,\n  crypto_scalarmult_SCALARBYTES: crypto_scalarmult_SCALARBYTES,\n  crypto_box_PUBLICKEYBYTES: crypto_box_PUBLICKEYBYTES,\n  crypto_box_SECRETKEYBYTES: crypto_box_SECRETKEYBYTES,\n  crypto_box_BEFORENMBYTES: crypto_box_BEFORENMBYTES,\n  crypto_box_NONCEBYTES: crypto_box_NONCEBYTES,\n  crypto_box_ZEROBYTES: crypto_box_ZEROBYTES,\n  crypto_box_BOXZEROBYTES: crypto_box_BOXZEROBYTES,\n  crypto_sign_BYTES: crypto_sign_BYTES,\n  crypto_sign_PUBLICKEYBYTES: crypto_sign_PUBLICKEYBYTES,\n  crypto_sign_SECRETKEYBYTES: crypto_sign_SECRETKEYBYTES,\n  crypto_sign_SEEDBYTES: crypto_sign_SEEDBYTES,\n  crypto_hash_BYTES: crypto_hash_BYTES,\n\n  gf: gf,\n  D: D,\n  L: L,\n  pack25519: pack25519,\n  unpack25519: unpack25519,\n  M: M,\n  A: A,\n  S: S,\n  Z: Z,\n  pow2523: pow2523,\n  add: add,\n  set25519: set25519,\n  modL: modL,\n  scalarmult: scalarmult,\n  scalarbase: scalarbase,\n};\n\n/* High-level API */\n\nfunction checkLengths(k, n) {\n  if (k.length !== crypto_secretbox_KEYBYTES) throw new Error('bad key size');\n  if (n.length !== crypto_secretbox_NONCEBYTES) throw new Error('bad nonce size');\n}\n\nfunction checkBoxLengths(pk, sk) {\n  if (pk.length !== crypto_box_PUBLICKEYBYTES) throw new Error('bad public key size');\n  if (sk.length !== crypto_box_SECRETKEYBYTES) throw new Error('bad secret key size');\n}\n\nfunction checkArrayTypes() {\n  for (var i = 0; i < arguments.length; i++) {\n    if (!(arguments[i] instanceof Uint8Array))\n      throw new TypeError('unexpected type, use Uint8Array');\n  }\n}\n\nfunction cleanup(arr) {\n  for (var i = 0; i < arr.length; i++) arr[i] = 0;\n}\n\nnacl.randomBytes = function(n) {\n  var b = new Uint8Array(n);\n  randombytes(b, n);\n  return b;\n};\n\nnacl.secretbox = function(msg, nonce, key) {\n  checkArrayTypes(msg, nonce, key);\n  checkLengths(key, nonce);\n  var m = new Uint8Array(crypto_secretbox_ZEROBYTES + msg.length);\n  var c = new Uint8Array(m.length);\n  for (var i = 0; i < msg.length; i++) m[i+crypto_secretbox_ZEROBYTES] = msg[i];\n  crypto_secretbox(c, m, m.length, nonce, key);\n  return c.subarray(crypto_secretbox_BOXZEROBYTES);\n};\n\nnacl.secretbox.open = function(box, nonce, key) {\n  checkArrayTypes(box, nonce, key);\n  checkLengths(key, nonce);\n  var c = new Uint8Array(crypto_secretbox_BOXZEROBYTES + box.length);\n  var m = new Uint8Array(c.length);\n  for (var i = 0; i < box.length; i++) c[i+crypto_secretbox_BOXZEROBYTES] = box[i];\n  if (c.length < 32) return null;\n  if (crypto_secretbox_open(m, c, c.length, nonce, key) !== 0) return null;\n  return m.subarray(crypto_secretbox_ZEROBYTES);\n};\n\nnacl.secretbox.keyLength = crypto_secretbox_KEYBYTES;\nnacl.secretbox.nonceLength = crypto_secretbox_NONCEBYTES;\nnacl.secretbox.overheadLength = crypto_secretbox_BOXZEROBYTES;\n\nnacl.scalarMult = function(n, p) {\n  checkArrayTypes(n, p);\n  if (n.length !== crypto_scalarmult_SCALARBYTES) throw new Error('bad n size');\n  if (p.length !== crypto_scalarmult_BYTES) throw new Error('bad p size');\n  var q = new Uint8Array(crypto_scalarmult_BYTES);\n  crypto_scalarmult(q, n, p);\n  return q;\n};\n\nnacl.scalarMult.base = function(n) {\n  checkArrayTypes(n);\n  if (n.length !== crypto_scalarmult_SCALARBYTES) throw new Error('bad n size');\n  var q = new Uint8Array(crypto_scalarmult_BYTES);\n  crypto_scalarmult_base(q, n);\n  return q;\n};\n\nnacl.scalarMult.scalarLength = crypto_scalarmult_SCALARBYTES;\nnacl.scalarMult.groupElementLength = crypto_scalarmult_BYTES;\n\nnacl.box = function(msg, nonce, publicKey, secretKey) {\n  var k = nacl.box.before(publicKey, secretKey);\n  return nacl.secretbox(msg, nonce, k);\n};\n\nnacl.box.before = function(publicKey, secretKey) {\n  checkArrayTypes(publicKey, secretKey);\n  checkBoxLengths(publicKey, secretKey);\n  var k = new Uint8Array(crypto_box_BEFORENMBYTES);\n  crypto_box_beforenm(k, publicKey, secretKey);\n  return k;\n};\n\nnacl.box.after = nacl.secretbox;\n\nnacl.box.open = function(msg, nonce, publicKey, secretKey) {\n  var k = nacl.box.before(publicKey, secretKey);\n  return nacl.secretbox.open(msg, nonce, k);\n};\n\nnacl.box.open.after = nacl.secretbox.open;\n\nnacl.box.keyPair = function() {\n  var pk = new Uint8Array(crypto_box_PUBLICKEYBYTES);\n  var sk = new Uint8Array(crypto_box_SECRETKEYBYTES);\n  crypto_box_keypair(pk, sk);\n  return {publicKey: pk, secretKey: sk};\n};\n\nnacl.box.keyPair.fromSecretKey = function(secretKey) {\n  checkArrayTypes(secretKey);\n  if (secretKey.length !== crypto_box_SECRETKEYBYTES)\n    throw new Error('bad secret key size');\n  var pk = new Uint8Array(crypto_box_PUBLICKEYBYTES);\n  crypto_scalarmult_base(pk, secretKey);\n  return {publicKey: pk, secretKey: new Uint8Array(secretKey)};\n};\n\nnacl.box.publicKeyLength = crypto_box_PUBLICKEYBYTES;\nnacl.box.secretKeyLength = crypto_box_SECRETKEYBYTES;\nnacl.box.sharedKeyLength = crypto_box_BEFORENMBYTES;\nnacl.box.nonceLength = crypto_box_NONCEBYTES;\nnacl.box.overheadLength = nacl.secretbox.overheadLength;\n\nnacl.sign = function(msg, secretKey) {\n  checkArrayTypes(msg, secretKey);\n  if (secretKey.length !== crypto_sign_SECRETKEYBYTES)\n    throw new Error('bad secret key size');\n  var signedMsg = new Uint8Array(crypto_sign_BYTES+msg.length);\n  crypto_sign(signedMsg, msg, msg.length, secretKey);\n  return signedMsg;\n};\n\nnacl.sign.open = function(signedMsg, publicKey) {\n  checkArrayTypes(signedMsg, publicKey);\n  if (publicKey.length !== crypto_sign_PUBLICKEYBYTES)\n    throw new Error('bad public key size');\n  var tmp = new Uint8Array(signedMsg.length);\n  var mlen = crypto_sign_open(tmp, signedMsg, signedMsg.length, publicKey);\n  if (mlen < 0) return null;\n  var m = new Uint8Array(mlen);\n  for (var i = 0; i < m.length; i++) m[i] = tmp[i];\n  return m;\n};\n\nnacl.sign.detached = function(msg, secretKey) {\n  var signedMsg = nacl.sign(msg, secretKey);\n  var sig = new Uint8Array(crypto_sign_BYTES);\n  for (var i = 0; i < sig.length; i++) sig[i] = signedMsg[i];\n  return sig;\n};\n\nnacl.sign.detached.verify = function(msg, sig, publicKey) {\n  checkArrayTypes(msg, sig, publicKey);\n  if (sig.length !== crypto_sign_BYTES)\n    throw new Error('bad signature size');\n  if (publicKey.length !== crypto_sign_PUBLICKEYBYTES)\n    throw new Error('bad public key size');\n  var sm = new Uint8Array(crypto_sign_BYTES + msg.length);\n  var m = new Uint8Array(crypto_sign_BYTES + msg.length);\n  var i;\n  for (i = 0; i < crypto_sign_BYTES; i++) sm[i] = sig[i];\n  for (i = 0; i < msg.length; i++) sm[i+crypto_sign_BYTES] = msg[i];\n  return (crypto_sign_open(m, sm, sm.length, publicKey) >= 0);\n};\n\nnacl.sign.keyPair = function() {\n  var pk = new Uint8Array(crypto_sign_PUBLICKEYBYTES);\n  var sk = new Uint8Array(crypto_sign_SECRETKEYBYTES);\n  crypto_sign_keypair(pk, sk);\n  return {publicKey: pk, secretKey: sk};\n};\n\nnacl.sign.keyPair.fromSecretKey = function(secretKey) {\n  checkArrayTypes(secretKey);\n  if (secretKey.length !== crypto_sign_SECRETKEYBYTES)\n    throw new Error('bad secret key size');\n  var pk = new Uint8Array(crypto_sign_PUBLICKEYBYTES);\n  for (var i = 0; i < pk.length; i++) pk[i] = secretKey[32+i];\n  return {publicKey: pk, secretKey: new Uint8Array(secretKey)};\n};\n\nnacl.sign.keyPair.fromSeed = function(seed) {\n  checkArrayTypes(seed);\n  if (seed.length !== crypto_sign_SEEDBYTES)\n    throw new Error('bad seed size');\n  var pk = new Uint8Array(crypto_sign_PUBLICKEYBYTES);\n  var sk = new Uint8Array(crypto_sign_SECRETKEYBYTES);\n  for (var i = 0; i < 32; i++) sk[i] = seed[i];\n  crypto_sign_keypair(pk, sk, true);\n  return {publicKey: pk, secretKey: sk};\n};\n\nnacl.sign.publicKeyLength = crypto_sign_PUBLICKEYBYTES;\nnacl.sign.secretKeyLength = crypto_sign_SECRETKEYBYTES;\nnacl.sign.seedLength = crypto_sign_SEEDBYTES;\nnacl.sign.signatureLength = crypto_sign_BYTES;\n\nnacl.hash = function(msg) {\n  checkArrayTypes(msg);\n  var h = new Uint8Array(crypto_hash_BYTES);\n  crypto_hash(h, msg, msg.length);\n  return h;\n};\n\nnacl.hash.hashLength = crypto_hash_BYTES;\n\nnacl.verify = function(x, y) {\n  checkArrayTypes(x, y);\n  // Zero length arguments are considered not equal.\n  if (x.length === 0 || y.length === 0) return false;\n  if (x.length !== y.length) return false;\n  return (vn(x, 0, y, 0, x.length) === 0) ? true : false;\n};\n\nnacl.setPRNG = function(fn) {\n  randombytes = fn;\n};\n\n(function() {\n  // Initialize PRNG if environment provides CSPRNG.\n  // If not, methods calling randombytes will throw.\n  var crypto = typeof self !== 'undefined' ? (self.crypto || self.msCrypto) : null;\n  if (crypto && crypto.getRandomValues) {\n    // Browsers.\n    var QUOTA = 65536;\n    nacl.setPRNG(function(x, n) {\n      var i, v = new Uint8Array(n);\n      for (i = 0; i < n; i += QUOTA) {\n        crypto.getRandomValues(v.subarray(i, i + Math.min(n - i, QUOTA)));\n      }\n      for (i = 0; i < n; i++) x[i] = v[i];\n      cleanup(v);\n    });\n  } else if (typeof require !== 'undefined') {\n    // Node.js.\n    crypto = require('crypto');\n    if (crypto && crypto.randomBytes) {\n      nacl.setPRNG(function(x, n) {\n        var i, v = crypto.randomBytes(n);\n        for (i = 0; i < n; i++) x[i] = v[i];\n        cleanup(v);\n      });\n    }\n  }\n})();\n\n})(typeof module !== 'undefined' && module.exports ? module.exports : (self.nacl = self.nacl || {}));\n", "module.exports = require('./pusher-with-encryption').default;\n", "import Pusher from './pusher';\nimport { Options, validateOptions } from './options';\nimport * as nacl from 'tweetnacl';\n\nexport default class PusherWithEncryption extends Pusher {\n  constructor(app_key: string, options: Options) {\n    Pusher.logToConsole = PusherWithEncryption.logToConsole;\n    Pusher.log = PusherWithEncryption.log;\n\n    validateOptions(options);\n    options.nacl = nacl;\n    super(app_key, options);\n  }\n}\n"], "sourceRoot": ""}