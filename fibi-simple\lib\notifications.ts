'use client'

import { supabase } from './supabase'

export interface NotificationData {
  title: string
  message: string
  type?: 'info' | 'success' | 'warning' | 'error'
  userId?: string
}

// Проверяем поддержку уведомлений
export const isNotificationSupported = () => {
  return 'Notification' in window && 'serviceWorker' in navigator
}

// Запрашиваем разрешение на уведомления
export const requestNotificationPermission = async (): Promise<boolean> => {
  if (!isNotificationSupported()) {
    return false
  }

  if (Notification.permission === 'granted') {
    return true
  }

  if (Notification.permission === 'denied') {
    return false
  }

  const permission = await Notification.requestPermission()
  return permission === 'granted'
}

// Отправляем браузерное уведомление
export const sendBrowserNotification = (title: string, options?: NotificationOptions) => {
  if (!isNotificationSupported() || Notification.permission !== 'granted') {
    return
  }

  const notification = new Notification(title, {
    icon: '/favicon.ico',
    badge: '/favicon.ico',
    ...options
  })

  // Автоматически закрываем через 5 секунд
  setTimeout(() => {
    notification.close()
  }, 5000)

  return notification
}

// Сохраняем уведомление в базу данных
export const saveNotificationToDatabase = async (data: NotificationData) => {
  try {
    const { error } = await supabase
      .from('notifications')
      .insert({
        user_id: data.userId,
        title: data.title,
        message: data.message,
        type: data.type || 'info'
      })

    if (error) {
      console.error('Error saving notification:', error)
    }
  } catch (error) {
    console.error('Error saving notification:', error)
  }
}

// Отправляем полное уведомление (браузер + база данных)
export const sendNotification = async (data: NotificationData) => {
  // Сохраняем в базу данных
  if (data.userId) {
    await saveNotificationToDatabase(data)
  }

  // Отправляем браузерное уведомление
  sendBrowserNotification(data.title, {
    body: data.message,
    tag: `notification-${Date.now()}`,
    requireInteraction: data.type === 'error'
  })
}

// Получаем уведомления пользователя
export const getUserNotifications = async (userId: string) => {
  try {
    const { data, error } = await supabase
      .from('notifications')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(50)

    if (error) {
      console.error('Error fetching notifications:', error)
      return []
    }

    return data || []
  } catch (error) {
    console.error('Error fetching notifications:', error)
    return []
  }
}

// Отмечаем уведомление как прочитанное
export const markNotificationAsRead = async (notificationId: string) => {
  try {
    const { error } = await supabase
      .from('notifications')
      .update({ read: true })
      .eq('id', notificationId)

    if (error) {
      console.error('Error marking notification as read:', error)
    }
  } catch (error) {
    console.error('Error marking notification as read:', error)
  }
}

// Отмечаем все уведомления как прочитанные
export const markAllNotificationsAsRead = async (userId: string) => {
  try {
    const { error } = await supabase
      .from('notifications')
      .update({ read: true })
      .eq('user_id', userId)
      .eq('read', false)

    if (error) {
      console.error('Error marking all notifications as read:', error)
    }
  } catch (error) {
    console.error('Error marking all notifications as read:', error)
  }
}

// Хук для работы с уведомлениями
export const useNotifications = () => {
  const initializeNotifications = async () => {
    const hasPermission = await requestNotificationPermission()
    
    if (hasPermission) {
      console.log('Уведомления разрешены')
    } else {
      console.log('Уведомления заблокированы')
    }

    return hasPermission
  }

  return {
    initializeNotifications,
    sendNotification,
    getUserNotifications,
    markNotificationAsRead,
    markAllNotificationsAsRead,
    isSupported: isNotificationSupported()
  }
}
