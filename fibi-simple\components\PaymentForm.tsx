'use client'

import { useState } from 'react'
import { loadStripe } from '@stripe/stripe-js'
import { Elements, CardElement, useStripe, useElements } from '@stripe/react-stripe-js'
import { CreditCard, Lock, CheckCircle, AlertCircle } from 'lucide-react'

const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!)

interface PaymentFormProps {
  amount: number
  serviceId: string
  serviceName: string
  onSuccess?: () => void
  onError?: (error: string) => void
}

function CheckoutForm({ amount, serviceId, serviceName, onSuccess, onError }: PaymentFormProps) {
  const stripe = useStripe()
  const elements = useElements()
  const [isProcessing, setIsProcessing] = useState(false)
  const [paymentStatus, setPaymentStatus] = useState<'idle' | 'processing' | 'success' | 'error'>('idle')
  const [errorMessage, setErrorMessage] = useState('')

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault()

    if (!stripe || !elements) {
      return
    }

    setIsProcessing(true)
    setPaymentStatus('processing')
    setErrorMessage('')

    try {
      // Создаем PaymentIntent на сервере
      const response = await fetch('/api/payments/create-payment-intent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount: amount * 100, // Конвертируем в копейки
          serviceId,
          description: `Оплата услуги: ${serviceName}`
        }),
      })

      if (!response.ok) {
        throw new Error('Ошибка создания платежа')
      }

      const { clientSecret } = await response.json()

      // Подтверждаем платеж
      const { error, paymentIntent } = await stripe.confirmCardPayment(clientSecret, {
        payment_method: {
          card: elements.getElement(CardElement)!,
        }
      })

      if (error) {
        setPaymentStatus('error')
        setErrorMessage(error.message || 'Ошибка обработки платежа')
        onError?.(error.message || 'Ошибка обработки платежа')
      } else if (paymentIntent.status === 'succeeded') {
        setPaymentStatus('success')
        onSuccess?.()
      }
    } catch (error) {
      setPaymentStatus('error')
      const message = error instanceof Error ? error.message : 'Неизвестная ошибка'
      setErrorMessage(message)
      onError?.(message)
    } finally {
      setIsProcessing(false)
    }
  }

  if (paymentStatus === 'success') {
    return (
      <div className="text-center p-6">
        <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          Платеж успешно обработан!
        </h3>
        <p className="text-gray-600">
          Услуга "{serviceName}" активирована
        </p>
      </div>
    )
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Данные карты
        </label>
        <div className="border border-gray-300 rounded-md p-3 bg-white">
          <CardElement
            options={{
              style: {
                base: {
                  fontSize: '16px',
                  color: '#424770',
                  '::placeholder': {
                    color: '#aab7c4',
                  },
                },
              },
            }}
          />
        </div>
      </div>

      {paymentStatus === 'error' && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <AlertCircle className="h-5 w-5 text-red-400 mr-2" />
            <div className="text-sm text-red-700">{errorMessage}</div>
          </div>
        </div>
      )}

      <div className="bg-gray-50 rounded-lg p-4">
        <div className="flex justify-between items-center">
          <span className="text-gray-700">Услуга:</span>
          <span className="font-medium">{serviceName}</span>
        </div>
        <div className="flex justify-between items-center mt-2">
          <span className="text-gray-700">Сумма к оплате:</span>
          <span className="text-lg font-bold text-gray-900">
            {amount.toLocaleString('ru-RU')} ₽
          </span>
        </div>
      </div>

      <button
        type="submit"
        disabled={!stripe || isProcessing}
        className="w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
      >
        {isProcessing ? (
          <>
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
            Обработка...
          </>
        ) : (
          <>
            <Lock className="h-4 w-4 mr-2" />
            Оплатить {amount.toLocaleString('ru-RU')} ₽
          </>
        )}
      </button>

      <div className="flex items-center justify-center text-sm text-gray-500">
        <Lock className="h-4 w-4 mr-1" />
        Защищенная оплата через Stripe
      </div>
    </form>
  )
}

export default function PaymentForm(props: PaymentFormProps) {
  return (
    <Elements stripe={stripePromise}>
      <div className="max-w-md mx-auto bg-white rounded-lg shadow-lg p-6">
        <div className="flex items-center mb-6">
          <CreditCard className="h-6 w-6 text-blue-600 mr-2" />
          <h2 className="text-xl font-semibold text-gray-900">Оплата услуги</h2>
        </div>
        <CheckoutForm {...props} />
      </div>
    </Elements>
  )
}
