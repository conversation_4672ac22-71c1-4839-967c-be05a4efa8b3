# 🚀 Полное руководство по настройке Fibi Telecom

## 📋 Обзор

Этот проект включает в себя все современные функции телекоммуникационной компании:
- 💳 Система оплаты Stripe
- 📊 Аналитика и отчеты  
- 💬 Чат поддержки в реальном времени
- 🔔 Push уведомления
- 📧 Email рассылки

## 🔧 Настройка сервисов

### 1. Supabase (База данных и авторизация)

1. Создайте проект на [supabase.com](https://supabase.com)
2. Перейдите в Settings > API
3. Скопируйте URL и anon key
4. Выполните SQL скрипт из `supabase-setup.sql` в SQL Editor

### 2. Stripe (Платежи)

1. Создайте аккаунт на [stripe.com](https://stripe.com)
2. Перейдите в Developers > API keys
3. Скопируйте Publishable key и Secret key
4. Настройте webhook endpoint: `your-domain.com/api/webhooks/stripe`
5. Выберите события: `payment_intent.succeeded`, `payment_intent.payment_failed`

### 3. Pusher (Чат в реальном времени)

1. Создайте аккаунт на [pusher.com](https://pusher.com)
2. Создайте новое приложение
3. Скопируйте app_id, key, secret, cluster
4. Включите client events в настройках

### 4. Email (SMTP)

Для Gmail:
1. Включите 2FA в Google аккаунте
2. Создайте App Password в настройках безопасности
3. Используйте этот пароль в SMTP_PASS

Для других провайдеров:
- **Yandex**: smtp.yandex.ru:587
- **Mail.ru**: smtp.mail.ru:587
- **SendGrid**: smtp.sendgrid.net:587

## 🔐 Переменные окружения

Создайте файл `.env.local`:

```env
# Supabase
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key

# Stripe
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_...
STRIPE_SECRET_KEY=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...

# Email
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Pusher
NEXT_PUBLIC_PUSHER_APP_ID=your-app-id
NEXT_PUBLIC_PUSHER_KEY=your-key
NEXT_PUBLIC_PUSHER_SECRET=your-secret
NEXT_PUBLIC_PUSHER_CLUSTER=eu

# App URL (для email ссылок)
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

## 🗄️ Структура базы данных

После выполнения SQL скрипта у вас будут таблицы:

- `profiles` - профили пользователей
- `contact_messages` - сообщения обратной связи
- `services` - услуги компании
- `user_services` - подписки пользователей
- `payments` - история платежей
- `chat_rooms` - комнаты чата
- `chat_messages` - сообщения чата
- `notifications` - уведомления

## 🚀 Запуск проекта

```bash
# Установка зависимостей
npm install

# Запуск в режиме разработки
npm run dev

# Сборка для продакшена
npm run build
npm start
```

## 👤 Создание администратора

1. Зарегистрируйтесь через интерфейс сайта
2. В Supabase Dashboard выполните:
```sql
UPDATE profiles SET role = 'admin' WHERE email = '<EMAIL>';
```

## 🧪 Тестирование функций

### Платежи
1. Используйте тестовые карты Stripe:
   - `4242 4242 4242 4242` - успешная оплата
   - `4000 0000 0000 0002` - отклоненная карта
2. Проверьте webhook в Stripe Dashboard

### Чат
1. Откройте сайт в двух вкладках
2. Войдите как пользователь в одной, как админ в другой
3. Начните чат и проверьте уведомления

### Email
1. Зарегистрируйте нового пользователя
2. Проверьте приветственное письмо
3. Совершите платеж и проверьте уведомление

### Уведомления
1. Разрешите уведомления в браузере
2. Отправьте сообщение в чат
3. Проверьте push уведомление

## 🔧 Настройка продакшена

### Vercel
1. Подключите GitHub репозиторий
2. Добавьте все переменные окружения
3. Обновите `NEXT_PUBLIC_APP_URL` на ваш домен

### Stripe Webhooks
1. Обновите endpoint URL на продакшен домен
2. Обновите `STRIPE_WEBHOOK_SECRET`

### Email домен
1. Настройте SPF/DKIM записи
2. Используйте корпоративный email

## 📱 Мобильное приложение (React Native)

Для создания мобильного приложения:

```bash
# Создание React Native проекта
npx create-expo-app fibi-mobile
cd fibi-mobile

# Установка зависимостей
npm install @supabase/supabase-js
npm install @stripe/stripe-react-native
npm install pusher-js
```

Основные компоненты для переноса:
- Авторизация
- Личный кабинет
- Чат поддержки
- Push уведомления

## 🔍 Мониторинг и логи

### Supabase
- Мониторинг API запросов
- Логи аутентификации
- Статистика использования

### Stripe
- Dashboard для платежей
- Webhook логи
- Отчеты по доходам

### Vercel
- Логи функций
- Аналитика производительности
- Мониторинг ошибок

## 🆘 Решение проблем

### Ошибки Stripe
- Проверьте webhook endpoint
- Убедитесь в правильности ключей
- Проверьте тестовый режим

### Проблемы с чатом
- Проверьте Pusher credentials
- Убедитесь в правильности каналов
- Проверьте CORS настройки

### Email не отправляется
- Проверьте SMTP настройки
- Убедитесь в правильности пароля
- Проверьте лимиты провайдера

## 📞 Поддержка

Если возникли вопросы:
1. Проверьте логи в консоли браузера
2. Посмотрите Network вкладку в DevTools
3. Проверьте переменные окружения
4. Убедитесь в правильности API ключей

---

**🎉 Готово! Ваш проект полностью настроен и готов к использованию!**
