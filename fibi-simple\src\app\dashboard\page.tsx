'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { useAuth } from '@/hooks/useAuth'
import { Phone, Wifi, Shield, CreditCard, Settings, LogOut, User } from 'lucide-react'

export default function DashboardPage() {
  const { user, profile, loading, signOut } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!loading && !user) {
      router.push('/auth/login')
    }
  }, [user, loading, router])

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!user) {
    return null
  }

  const handleSignOut = async () => {
    await signOut()
    router.push('/')
  }

  const services = [
    {
      icon: Wifi,
      name: 'Интернет',
      status: 'Активен',
      plan: 'Домашний 100 Мбит/с',
      price: '990 ₽/мес',
      statusColor: 'text-green-600'
    },
    {
      icon: Phone,
      name: 'Телефония',
      status: 'Активен',
      plan: 'Безлимитные звонки',
      price: '490 ₽/мес',
      statusColor: 'text-green-600'
    },
    {
      icon: Shield,
      name: 'Безопасность',
      status: 'Не подключен',
      plan: 'Базовая защита',
      price: '290 ₽/мес',
      statusColor: 'text-gray-500'
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <Link href="/" className="flex items-center">
                <Phone className="h-8 w-8 text-blue-600 mr-2" />
                <span className="text-2xl font-bold text-gray-900">Fibi Telecom</span>
              </Link>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-gray-700">
                {profile?.full_name || user.email}
              </span>
              <button
                onClick={handleSignOut}
                className="flex items-center text-gray-600 hover:text-gray-800"
              >
                <LogOut className="h-5 w-5 mr-1" />
                Выйти
              </button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900">Личный кабинет</h1>
            <p className="mt-2 text-gray-600">
              Управляйте вашими услугами и настройками
            </p>
          </div>

          <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
            {/* Profile Card */}
            <div className="lg:col-span-1">
              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="px-4 py-5 sm:p-6">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <User className="h-10 w-10 text-gray-400" />
                    </div>
                    <div className="ml-4">
                      <h3 className="text-lg font-medium text-gray-900">
                        {profile?.full_name || 'Пользователь'}
                      </h3>
                      <p className="text-sm text-gray-500">{user.email}</p>
                      <p className="text-sm text-gray-500">
                        Роль: {profile?.role === 'admin' ? 'Администратор' : 'Пользователь'}
                      </p>
                    </div>
                  </div>
                  <div className="mt-6">
                    <Link
                      href="/dashboard/profile"
                      className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                    >
                      <Settings className="h-4 w-4 mr-2" />
                      Настройки профиля
                    </Link>
                  </div>
                </div>
              </div>
            </div>

            {/* Services */}
            <div className="lg:col-span-2">
              <div className="bg-white shadow rounded-lg">
                <div className="px-4 py-5 sm:p-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-6">
                    Ваши услуги
                  </h3>
                  <div className="space-y-4">
                    {services.map((service, index) => {
                      const IconComponent = service.icon
                      return (
                        <div
                          key={index}
                          className="flex items-center justify-between p-4 border border-gray-200 rounded-lg"
                        >
                          <div className="flex items-center">
                            <IconComponent className="h-8 w-8 text-blue-600 mr-4" />
                            <div>
                              <h4 className="text-lg font-medium text-gray-900">
                                {service.name}
                              </h4>
                              <p className="text-sm text-gray-500">{service.plan}</p>
                              <p className={`text-sm ${service.statusColor}`}>
                                {service.status}
                              </p>
                            </div>
                          </div>
                          <div className="text-right">
                            <p className="text-lg font-semibold text-gray-900">
                              {service.price}
                            </p>
                            <button className="mt-2 text-sm text-blue-600 hover:text-blue-800">
                              Управлять
                            </button>
                          </div>
                        </div>
                      )
                    })}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="mt-8">
            <h2 className="text-xl font-bold text-gray-900 mb-4">Быстрые действия</h2>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
              <Link
                href="/dashboard/billing"
                className="bg-white p-6 rounded-lg shadow hover:shadow-md transition-shadow"
              >
                <CreditCard className="h-8 w-8 text-blue-600 mb-2" />
                <h3 className="text-lg font-medium text-gray-900">Счета</h3>
                <p className="text-sm text-gray-500">Просмотр и оплата счетов</p>
              </Link>

              <Link
                href="/dashboard/support"
                className="bg-white p-6 rounded-lg shadow hover:shadow-md transition-shadow"
              >
                <Phone className="h-8 w-8 text-blue-600 mb-2" />
                <h3 className="text-lg font-medium text-gray-900">Поддержка</h3>
                <p className="text-sm text-gray-500">Связаться с поддержкой</p>
              </Link>

              <Link
                href="/dashboard/services"
                className="bg-white p-6 rounded-lg shadow hover:shadow-md transition-shadow"
              >
                <Settings className="h-8 w-8 text-blue-600 mb-2" />
                <h3 className="text-lg font-medium text-gray-900">Услуги</h3>
                <p className="text-sm text-gray-500">Управление услугами</p>
              </Link>

              {profile?.role === 'admin' && (
                <Link
                  href="/admin"
                  className="bg-red-50 p-6 rounded-lg shadow hover:shadow-md transition-shadow border border-red-200"
                >
                  <Shield className="h-8 w-8 text-red-600 mb-2" />
                  <h3 className="text-lg font-medium text-red-900">Админ-панель</h3>
                  <p className="text-sm text-red-600">Управление системой</p>
                </Link>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
