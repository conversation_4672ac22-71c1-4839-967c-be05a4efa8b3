import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase-server'
import { sendEmail, emailTemplates } from '@/lib/email'
import { z } from 'zod'

const emailSchema = z.object({
  type: z.enum(['welcome', 'payment_success', 'support_ticket', 'newsletter']),
  recipient: z.string().email(),
  data: z.object({
    userName: z.string(),
    serviceName: z.string().optional(),
    amount: z.number().optional(),
    ticketId: z.string().optional(),
    message: z.string().optional(),
    title: z.string().optional(),
    content: z.string().optional()
  })
})

export async function POST(request: NextRequest) {
  try {
    const supabase = createClient()
    
    // Проверяем авторизацию
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Необходима авторизация' },
        { status: 401 }
      )
    }

    // Проверяем права администратора для некоторых типов писем
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    const body = await request.json()
    const { type, recipient, data } = emailSchema.parse(body)

    // Проверяем права для массовых рассылок
    if (type === 'newsletter' && profile?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Недостаточно прав' },
        { status: 403 }
      )
    }

    let emailTemplate
    
    switch (type) {
      case 'welcome':
        emailTemplate = emailTemplates.welcome(data.userName)
        break
        
      case 'payment_success':
        if (!data.serviceName || !data.amount) {
          return NextResponse.json(
            { error: 'Недостаточно данных для письма об оплате' },
            { status: 400 }
          )
        }
        emailTemplate = emailTemplates.paymentSuccess(
          data.userName, 
          data.serviceName, 
          data.amount
        )
        break
        
      case 'support_ticket':
        if (!data.ticketId || !data.message) {
          return NextResponse.json(
            { error: 'Недостаточно данных для письма о тикете' },
            { status: 400 }
          )
        }
        emailTemplate = emailTemplates.supportTicket(
          data.userName, 
          data.ticketId, 
          data.message
        )
        break
        
      case 'newsletter':
        if (!data.title || !data.content) {
          return NextResponse.json(
            { error: 'Недостаточно данных для рассылки' },
            { status: 400 }
          )
        }
        emailTemplate = emailTemplates.newsletter(
          data.userName, 
          data.title, 
          data.content
        )
        break
        
      default:
        return NextResponse.json(
          { error: 'Неизвестный тип письма' },
          { status: 400 }
        )
    }

    // Отправляем email
    const success = await sendEmail({
      to: recipient,
      subject: emailTemplate.subject,
      html: emailTemplate.html
    })

    if (success) {
      // Логируем отправку в базу данных
      await supabase
        .from('email_logs')
        .insert({
          recipient,
          type,
          subject: emailTemplate.subject,
          sent_by: user.id,
          status: 'sent'
        })

      return NextResponse.json({ success: true })
    } else {
      return NextResponse.json(
        { error: 'Ошибка отправки email' },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('Email sending error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Неверные данные', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Ошибка отправки email' },
      { status: 500 }
    )
  }
}
