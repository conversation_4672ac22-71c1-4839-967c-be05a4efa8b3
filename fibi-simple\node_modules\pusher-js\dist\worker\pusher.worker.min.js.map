{"version": 3, "sources": ["webpack://Pusher/webpack/universalModuleDefinition", "webpack://Pusher/webpack/bootstrap", "webpack://Pusher/./node_modules/@stablelib/base64/base64.ts", "webpack://Pusher/./node_modules/@stablelib/utf8/utf8.ts", "webpack://Pusher/./src/core/pusher.js", "webpack://Pusher/./src/core/base64.ts", "webpack://Pusher/./src/core/utils/timers/abstract_timer.ts", "webpack://Pusher/./src/core/utils/timers/index.ts", "webpack://Pusher/./src/core/util.ts", "webpack://Pusher/./src/core/utils/collections.ts", "webpack://Pusher/./src/core/defaults.ts", "webpack://Pusher/./src/core/transports/url_schemes.ts", "webpack://Pusher/./src/core/events/callback_registry.ts", "webpack://Pusher/./src/core/events/dispatcher.ts", "webpack://Pusher/./src/core/logger.ts", "webpack://Pusher/./src/core/transports/transport_connection.ts", "webpack://Pusher/./src/core/transports/transport.ts", "webpack://Pusher/./src/runtimes/isomorphic/transports/transports.ts", "webpack://Pusher/./src/core/transports/assistant_to_the_transport_manager.ts", "webpack://Pusher/./src/core/connection/protocol/protocol.ts", "webpack://Pusher/./src/core/connection/connection.ts", "webpack://Pusher/./src/core/connection/handshake/index.ts", "webpack://Pusher/./src/core/timeline/timeline_sender.ts", "webpack://Pusher/./src/core/errors.ts", "webpack://Pusher/./src/core/utils/url_store.ts", "webpack://Pusher/./src/core/channels/channel.ts", "webpack://Pusher/./src/core/channels/private_channel.ts", "webpack://Pusher/./src/core/channels/members.ts", "webpack://Pusher/./src/core/channels/presence_channel.ts", "webpack://Pusher/./src/core/channels/encrypted_channel.ts", "webpack://Pusher/./src/core/connection/connection_manager.ts", "webpack://Pusher/./src/core/channels/channels.ts", "webpack://Pusher/./src/core/utils/factory.ts", "webpack://Pusher/./src/core/transports/transport_manager.ts", "webpack://Pusher/./src/core/strategies/sequential_strategy.ts", "webpack://Pusher/./src/core/strategies/best_connected_ever_strategy.ts", "webpack://Pusher/./src/core/strategies/websocket_prioritized_cached_strategy.ts", "webpack://Pusher/./src/core/strategies/delayed_strategy.ts", "webpack://Pusher/./src/core/strategies/if_strategy.ts", "webpack://Pusher/./src/core/strategies/first_connected_strategy.ts", "webpack://Pusher/./src/runtimes/isomorphic/default_strategy.ts", "webpack://Pusher/./src/core/http/http_request.ts", "webpack://Pusher/./src/core/http/state.ts", "webpack://Pusher/./src/core/http/http_socket.ts", "webpack://Pusher/./src/core/http/http_streaming_socket.ts", "webpack://Pusher/./src/core/http/http_polling_socket.ts", "webpack://Pusher/./src/runtimes/isomorphic/http/http_xhr_request.ts", "webpack://Pusher/./src/runtimes/isomorphic/runtime.ts", "webpack://Pusher/./src/runtimes/isomorphic/transports/transport_connection_initializer.ts", "webpack://Pusher/./src/runtimes/isomorphic/http/http.ts", "webpack://Pusher/./src/runtimes/worker/net_info.ts", "webpack://Pusher/./src/runtimes/worker/auth/fetch_auth.ts", "webpack://Pusher/./src/runtimes/worker/timeline/fetch_timeline.ts", "webpack://Pusher/./src/runtimes/worker/runtime.ts", "webpack://Pusher/./src/core/timeline/level.ts", "webpack://Pusher/./src/core/timeline/timeline.ts", "webpack://Pusher/./src/core/strategies/transport_strategy.ts", "webpack://Pusher/./src/core/strategies/strategy_builder.ts", "webpack://Pusher/./src/core/auth/options.ts", "webpack://Pusher/./src/core/auth/user_authenticator.ts", "webpack://Pusher/./src/core/auth/channel_authorizer.ts", "webpack://Pusher/./src/core/config.ts", "webpack://Pusher/./src/core/auth/deprecated_channel_authorizer.ts", "webpack://Pusher/./src/core/watchlist.ts", "webpack://Pusher/./src/core/utils/flat_promise.ts", "webpack://Pusher/./src/core/user.ts", "webpack://Pusher/./src/core/pusher.ts", "webpack://Pusher/./src/core/options.ts"], "names": ["root", "factory", "exports", "module", "define", "amd", "this", "installedModules", "__webpack_require__", "moduleId", "i", "l", "modules", "call", "m", "c", "d", "name", "getter", "o", "Object", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "object", "property", "prototype", "hasOwnProperty", "p", "s", "_padding<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "length", "encode", "data", "out", "_encodeByte", "left", "maxDecoded<PERSON><PERSON>th", "decodedLength", "_getPaddingLength", "decode", "Uint8Array", "paddingLength", "op", "haveBad", "v0", "v1", "v2", "v3", "_decodeChar", "charCodeAt", "Error", "b", "result", "String", "fromCharCode", "Coder", "stdCoder", "URLSafeCoder", "urlSafeCoder", "INVALID_UTF8", "arr", "pos", "chars", "min", "n1", "n2", "n3", "push", "join", "default", "b64chars", "b64tab", "char<PERSON>t", "cb_utob", "cc", "utob", "u", "replace", "cb_encode", "ccc", "padlen", "ord", "btoa", "set", "clear", "delay", "callback", "timer", "clearTimeout", "clearInterval", "super", "setTimeout", "setInterval", "now", "Date", "valueOf", "defer", "args", "boundArguments", "Array", "slice", "arguments", "apply", "concat", "extend", "target", "sources", "extensions", "constructor", "stringify", "safeJSONStringify", "arrayIndexOf", "array", "item", "nativeIndexOf", "indexOf", "objectApply", "f", "keys", "_", "context", "map", "filter", "test", "filterObject", "Boolean", "any", "encodeParamsObject", "encodeURIComponent", "toString", "buildQueryString", "params", "undefined", "method", "source", "JSON", "e", "objects", "paths", "derez", "path", "nu", "$ref", "VERSION", "PROTOCOL", "wsPort", "wssPort", "wsPath", "httpHost", "httpPort", "httpsPort", "httpPath", "stats_host", "authEndpoint", "authTransport", "activityTimeout", "pongTimeout", "unavailableTimeout", "userAuthentication", "endpoint", "transport", "channelAuthorization", "cdn_http", "cdn_https", "dependency_suffix", "getGenericURL", "baseScheme", "useTLS", "hostTLS", "hostNonTLS", "getGenericPath", "queryString", "ws", "getInitial", "http", "_callbacks", "prefix", "prefixedEventName", "fn", "names", "removeCallback", "removeAllCallbacks", "binding", "failThrough", "callbacks", "global_callbacks", "eventName", "add", "remove", "unbind", "unbind_global", "metadata", "globalLog", "message", "console", "log", "globalLogWarn", "globalLogError", "warn", "error", "defaultLoggingFunction", "logToConsole", "hooks", "priority", "options", "initialize", "transportConnectionInitializer", "state", "timeline", "id", "generateUniqueID", "handlesActivityChecks", "supportsPing", "socket", "url", "urls", "getSocket", "onError", "changeState", "bindListeners", "debug", "close", "send", "ping", "beforeOpen", "<PERSON><PERSON><PERSON>", "onopen", "emit", "type", "buildTimelineMessage", "closeEvent", "code", "reason", "<PERSON><PERSON><PERSON>", "unbindListeners", "onOpen", "onerror", "onclose", "onClose", "onmessage", "onMessage", "onactivity", "onActivity", "info", "cid", "environment", "isSupported", "WSTransport", "isInitialized", "getWebSocketAPI", "createWebSocket", "httpConfiguration", "streamingConfiguration", "HTTPFactory", "createStreamingSocket", "pollingConfiguration", "createPollingSocket", "xhrConfiguration", "isXHRSupported", "xhr_streaming", "xhr_polling", "manager", "min<PERSON>ing<PERSON>elay", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "ping<PERSON><PERSON><PERSON>", "connection", "createConnection", "openTimestamp", "onClosed", "reportDeath", "lifespan", "Math", "max", "isAlive", "Protocol", "decodeMessage", "messageEvent", "messageData", "parse", "pusherEventData", "pusherEvent", "event", "channel", "user_id", "encodeMessage", "processHandshake", "activity_timeout", "action", "socket_id", "getCloseAction", "getCloseError", "send_event", "listeners", "activity", "closed", "handleCloseEvent", "listener", "finish", "isEmpty", "TimelineTransport", "getAgent", "BadEventName", "msg", "setPrototypeOf", "BadChannelName", "TransportPriorityTooLow", "TransportClosed", "UnsupportedFeature", "UnsupportedTransport", "UnsupportedStrategy", "HTTPAuthError", "status", "urlStore", "baseUrl", "authenticationEndpoint", "authorizationEndpoint", "javascriptQuickStart", "triggeringClientEvents", "encryptedChannelSupport", "fullUrl", "url<PERSON>bj", "pusher", "subscribed", "subscriptionPending", "subscriptionCancelled", "socketId", "auth", "suffix", "handleSubscriptionSucceededEvent", "handleSubscriptionCountEvent", "unsubscribe", "subscription_count", "subscriptionCount", "authorize", "assign", "channel_data", "config", "channelAuthorizer", "channelName", "reset", "members", "member", "myID", "subscriptionData", "presence", "hash", "count", "me", "memberData", "user_info", "authData", "channelData", "setMyID", "user", "signinDonePromise", "user_data", "handleInternalEvent", "addedMember", "addMember", "removedMember", "removeMember", "onSubscription", "disconnect", "nacl", "sharedSecret", "handleEncryptedEvent", "handleEvent", "ciphertext", "nonce", "cipherText", "secretbox", "overheadLength", "non<PERSON><PERSON><PERSON><PERSON>", "bytes", "open", "getDataToEmit", "raw", "usingTLS", "errorCallbacks", "buildErrorCallbacks", "connectionCallbacks", "buildConnectionCallbacks", "handshakeCallbacks", "buildHandshakeCallbacks", "Network", "getNetwork", "netinfo", "retryIn", "sendActivityCheck", "updateStrategy", "runner", "strategy", "updateState", "startConnecting", "setUnavailableTimer", "disconnectInternally", "handshake", "connect", "handshake<PERSON><PERSON><PERSON>", "abortConnecting", "abort", "clearRetryTimer", "clearUnavailableTimer", "abandonConnection", "getStrategy", "round", "retryTimer", "ensureAborted", "unavailableTimer", "stopActivityCheck", "activityTimer", "pong_timed_out", "resetActivity<PERSON>heck", "shouldRetry", "connected", "Infinity", "setConnection", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tls_only", "refused", "backoff", "retry", "newState", "previousState", "newStateDescription", "previous", "current", "channels", "createEncryptedChannel", "errMsg", "createPrivateChannel", "createPresenceChannel", "createChannel", "values", "createChannels", "createConnectionManager", "createTimelineSender", "createHandshake", "createAssistantToTheTransportManager", "livesLeft", "lives", "strategies", "loop", "failFast", "timeout", "timeoutLimit", "minPriority", "tryNextStrategy", "tryStrategy", "forceMinPriority", "isRunning", "callbackBuilder", "runners", "rs", "abort<PERSON><PERSON><PERSON>", "allRunnersFailed", "aborted", "transports", "ttl", "storage", "getLocalStorage", "serializedCache", "getTransportCacheKey", "flushTransportCache", "fetchTransportCache", "cacheSkipCount", "timestamp", "includes", "cached", "latency", "startTimestamp", "pop", "cb", "storeTransportCache", "number", "IfStrategy", "trueBranch", "falseBranch", "FirstConnectedStrategy", "testSupportsStrategy", "baseOptions", "defineTransport", "definedTransports", "defineTransportStrategy", "wsStrategy", "ws_options", "wsHost", "wss_options", "http_options", "timeouts", "ws_manager", "streaming_manager", "ws_transport", "wss_transport", "xhr_streaming_transport", "xhr_polling_transport", "ws_loop", "wss_loop", "streaming_loop", "polling_loop", "http_loop", "payload", "position", "xhr", "getRequest", "unloader", "addUnloadListener", "setRequestHeader", "removeUnloadListener", "abortRequest", "chunk", "advanceBuffer", "isBufferTooLong", "buffer", "unreadData", "endOfLinePosition", "State", "autoIncrement", "getUniqueURL", "separator", "randomNumber", "randomInt", "session", "randomString", "location", "parts", "exec", "base", "getLocation", "readyState", "CONNECTING", "openStream", "sendRaw", "sendHeartbeat", "OPEN", "createSocketRequest", "start", "closeStream", "CLOSED", "onEvent", "onHeartbeat", "hostname", "urlParts", "stream", "getReceiveURL", "onChunk", "onFinished", "reconnect", "unbind_all", "getXHRAPI", "onreadystatechange", "onprogress", "responseText", "getDefaultStrategy", "Transports", "createSocket", "createRequest", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ready", "getClientFeatures", "getProtocol", "createXHR", "query", "authOptions", "authRequestType", "headers", "Headers", "headerName", "headers<PERSON>rovider", "dynamicHeaders", "body", "request", "Request", "credentials", "fetch", "then", "response", "text", "parsedData", "catch", "err", "sender", "host", "json", "setup", "TimelineLevel", "getAuthorizers", "ajax", "WebSocket", "XMLHttpRequest", "floor", "globalThis", "crypto", "getRandomValues", "Uint32Array", "events", "sent", "uniqueID", "level", "limit", "shift", "ERROR", "INFO", "DEBUG", "sendfn", "bundle", "lib", "version", "cluster", "features", "failAttempt", "onInitialized", "serializedTransport", "AuthRequestType", "transportClass", "enabledTransports", "disabledTransports", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getAssistant", "deferred", "params<PERSON>rov<PERSON>", "dynamicParams", "composeChannel<PERSON><PERSON>y", "UserAuthentication", "ChannelAuthorization", "getHttpHost", "opts", "getWebsocketHost", "shouldUseTLS", "forceTLS", "getEnableStatsConfig", "enableStats", "disableStats", "buildUserAuthenticator", "buildChannelAuthorizer", "customHandler", "channelAuthorizerGenerator", "deprecatedAuthorizerOptions", "ChannelAuthorizerProxy", "authorizer", "buildChannelAuth", "bindWatchlistInternalEvent", "for<PERSON>ach", "watchlistEvent", "resolve", "reject", "promise", "Promise", "res", "rej", "signin_requested", "serverToUserChannel", "_signinDoneResolve", "_onAuthorize", "_cleanup", "_signin", "_newSigninPromiseIfNeeded", "watchlist", "_onSigninSuccess", "userAuthenticator", "_subscribeChannels", "bind_global", "reinstateSubscription", "subscribe", "ensure_subscribed", "done", "setDone", "isReady", "instances", "app_key", "check<PERSON><PERSON><PERSON><PERSON>", "validateOptions", "statsHost", "timelineParams", "getConfig", "global_emitter", "sessionID", "timelineSender", "subscribeAll", "isUsingTLS", "internal", "find", "all", "timelineSenderTimer", "event_name", "channel_name", "cancelSubscription", "signin", "Runtime", "ScriptReceivers", "DependenciesReceivers", "auth_callbacks"], "mappings": ";;;;;;;CAAA,SAA2CA,EAAMC,GAC1B,iBAAZC,SAA0C,iBAAXC,OACxCA,OAAOD,QAAUD,IACQ,mBAAXG,QAAyBA,OAAOC,IAC9CD,OAAO,GAAIH,GACe,iBAAZC,QACdA,QAAgB,OAAID,IAEpBD,EAAa,OAAIC,IARnB,CASGK,MAAM,WACT,O,YCTE,IAAIC,EAAmB,GAGvB,SAASC,EAAoBC,GAG5B,GAAGF,EAAiBE,GACnB,OAAOF,EAAiBE,GAAUP,QAGnC,IAAIC,EAASI,EAAiBE,GAAY,CACzCC,EAAGD,EACHE,GAAG,EACHT,QAAS,IAUV,OANAU,EAAQH,GAAUI,KAAKV,EAAOD,QAASC,EAAQA,EAAOD,QAASM,GAG/DL,EAAOQ,GAAI,EAGJR,EAAOD,QA0Df,OArDAM,EAAoBM,EAAIF,EAGxBJ,EAAoBO,EAAIR,EAGxBC,EAAoBQ,EAAI,SAASd,EAASe,EAAMC,GAC3CV,EAAoBW,EAAEjB,EAASe,IAClCG,OAAOC,eAAenB,EAASe,EAAM,CAAEK,YAAY,EAAMC,IAAKL,KAKhEV,EAAoBgB,EAAI,SAAStB,GACX,oBAAXuB,QAA0BA,OAAOC,aAC1CN,OAAOC,eAAenB,EAASuB,OAAOC,YAAa,CAAEC,MAAO,WAE7DP,OAAOC,eAAenB,EAAS,aAAc,CAAEyB,OAAO,KAQvDnB,EAAoBoB,EAAI,SAASD,EAAOE,GAEvC,GADU,EAAPA,IAAUF,EAAQnB,EAAoBmB,IAC/B,EAAPE,EAAU,OAAOF,EACpB,GAAW,EAAPE,GAA8B,iBAAVF,GAAsBA,GAASA,EAAMG,WAAY,OAAOH,EAChF,IAAII,EAAKX,OAAOY,OAAO,MAGvB,GAFAxB,EAAoBgB,EAAEO,GACtBX,OAAOC,eAAeU,EAAI,UAAW,CAAET,YAAY,EAAMK,MAAOA,IACtD,EAAPE,GAA4B,iBAATF,EAAmB,IAAI,IAAIM,KAAON,EAAOnB,EAAoBQ,EAAEe,EAAIE,EAAK,SAASA,GAAO,OAAON,EAAMM,IAAQC,KAAK,KAAMD,IAC9I,OAAOF,GAIRvB,EAAoB2B,EAAI,SAAShC,GAChC,IAAIe,EAASf,GAAUA,EAAO2B,WAC7B,WAAwB,OAAO3B,EAAgB,SAC/C,WAA8B,OAAOA,GAEtC,OADAK,EAAoBQ,EAAEE,EAAQ,IAAKA,GAC5BA,GAIRV,EAAoBW,EAAI,SAASiB,EAAQC,GAAY,OAAOjB,OAAOkB,UAAUC,eAAe1B,KAAKuB,EAAQC,IAGzG7B,EAAoBgC,EAAI,GAIjBhC,EAAoBA,EAAoBiC,EAAI,G,gaCxErD,IAOA,aAGI,WAAoBC,QAAA,IAAAA,MAAA,UAAAA,oBAwLxB,OAtLI,YAAAC,cAAA,SAAcC,GACV,OAAKtC,KAAKoC,mBAGFE,EAAS,GAAK,EAAI,EAAI,GAFT,EAATA,EAAa,GAAK,EAAI,GAKtC,YAAAC,OAAA,SAAOC,GAIH,IAHA,IAAIC,EAAM,GAENrC,EAAI,EACDA,EAAIoC,EAAKF,OAAS,EAAGlC,GAAK,EAAG,CAChC,IAAIK,EAAK+B,EAAKpC,IAAM,GAAOoC,EAAKpC,EAAI,IAAM,EAAMoC,EAAKpC,EAAI,GACzDqC,GAAOzC,KAAK0C,YAAajC,IAAM,GAAS,IACxCgC,GAAOzC,KAAK0C,YAAajC,IAAM,GAAS,IACxCgC,GAAOzC,KAAK0C,YAAajC,IAAM,EAAS,IACxCgC,GAAOzC,KAAK0C,YAAajC,IAAM,EAAS,IAG5C,IAAMkC,EAAOH,EAAKF,OAASlC,EAC3B,GAAIuC,EAAO,EAAG,CACNlC,EAAK+B,EAAKpC,IAAM,IAAgB,IAATuC,EAAaH,EAAKpC,EAAI,IAAM,EAAI,GAC3DqC,GAAOzC,KAAK0C,YAAajC,IAAM,GAAS,IACxCgC,GAAOzC,KAAK0C,YAAajC,IAAM,GAAS,IAEpCgC,GADS,IAATE,EACO3C,KAAK0C,YAAajC,IAAM,EAAS,IAEjCT,KAAKoC,mBAAqB,GAErCK,GAAOzC,KAAKoC,mBAAqB,GAGrC,OAAOK,GAGX,YAAAG,iBAAA,SAAiBN,GACb,OAAKtC,KAAKoC,kBAGHE,EAAS,EAAI,EAAI,GAFH,EAATA,EAAa,GAAK,EAAI,GAKtC,YAAAO,cAAA,SAAcV,GACV,OAAOnC,KAAK4C,iBAAiBT,EAAEG,OAAStC,KAAK8C,kBAAkBX,KAGnE,YAAAY,OAAA,SAAOZ,GACH,GAAiB,IAAbA,EAAEG,OACF,OAAO,IAAIU,WAAW,GAS1B,IAPA,IAAMC,EAAgBjD,KAAK8C,kBAAkBX,GACvCG,EAASH,EAAEG,OAASW,EACpBR,EAAM,IAAIO,WAAWhD,KAAK4C,iBAAiBN,IAC7CY,EAAK,EACL9C,EAAI,EACJ+C,EAAU,EACVC,EAAK,EAAGC,EAAK,EAAGC,EAAK,EAAGC,EAAK,EAC1BnD,EAAIkC,EAAS,EAAGlC,GAAK,EACxBgD,EAAKpD,KAAKwD,YAAYrB,EAAEsB,WAAWrD,EAAI,IACvCiD,EAAKrD,KAAKwD,YAAYrB,EAAEsB,WAAWrD,EAAI,IACvCkD,EAAKtD,KAAKwD,YAAYrB,EAAEsB,WAAWrD,EAAI,IACvCmD,EAAKvD,KAAKwD,YAAYrB,EAAEsB,WAAWrD,EAAI,IACvCqC,EAAIS,KAASE,GAAM,EAAMC,IAAO,EAChCZ,EAAIS,KAASG,GAAM,EAAMC,IAAO,EAChCb,EAAIS,KAASI,GAAM,EAAKC,EACxBJ,GA7ES,IA6EEC,EACXD,GA9ES,IA8EEE,EACXF,GA/ES,IA+EEG,EACXH,GAhFS,IAgFEI,EAmBf,GAjBInD,EAAIkC,EAAS,IACbc,EAAKpD,KAAKwD,YAAYrB,EAAEsB,WAAWrD,IACnCiD,EAAKrD,KAAKwD,YAAYrB,EAAEsB,WAAWrD,EAAI,IACvCqC,EAAIS,KAASE,GAAM,EAAMC,IAAO,EAChCF,GAtFS,IAsFEC,EACXD,GAvFS,IAuFEE,GAEXjD,EAAIkC,EAAS,IACbgB,EAAKtD,KAAKwD,YAAYrB,EAAEsB,WAAWrD,EAAI,IACvCqC,EAAIS,KAASG,GAAM,EAAMC,IAAO,EAChCH,GA5FS,IA4FEG,GAEXlD,EAAIkC,EAAS,IACbiB,EAAKvD,KAAKwD,YAAYrB,EAAEsB,WAAWrD,EAAI,IACvCqC,EAAIS,KAASI,GAAM,EAAKC,EACxBJ,GAjGS,IAiGEI,GAEC,IAAZJ,EACA,MAAM,IAAIO,MAAM,kDAEpB,OAAOjB,GAYD,YAAAC,YAAV,SAAsBiB,GAqBlB,IAAIC,EAASD,EAYb,OAVAC,GAAU,GAEVA,GAAY,GAAKD,IAAO,EAAK,EAE7BC,GAAY,GAAKD,IAAO,GAAK,GAE7BC,GAAY,GAAKD,IAAO,GAAK,GAE7BC,GAAY,GAAKD,IAAO,EAAK,EAEtBE,OAAOC,aAAaF,IAKrB,YAAAJ,YAAV,SAAsB/C,GAUlB,IAAImD,EAlKS,IA+Kb,OAVAA,IAAa,GAAKnD,EAAMA,EAAI,MAAS,GArKxB,IAqK8CA,EAAI,GAAK,GAEpEmD,IAAa,GAAKnD,EAAMA,EAAI,MAAS,GAvKxB,IAuK8CA,EAAI,GAAK,GAEpEmD,IAAa,GAAKnD,EAAMA,EAAI,MAAS,GAzKxB,IAyK8CA,EAAI,GAAK,GAEpEmD,IAAa,GAAKnD,EAAMA,EAAI,MAAS,GA3KxB,IA2K8CA,EAAI,GAAK,EAEpEmD,IAAa,GAAKnD,EAAMA,EAAI,OAAU,GA7KzB,IA6K+CA,EAAI,GAAK,IAKjE,YAAAqC,kBAAR,SAA0BX,GACtB,IAAIc,EAAgB,EACpB,GAAIjD,KAAKoC,kBAAmB,CACxB,IAAK,IAAIhC,EAAI+B,EAAEG,OAAS,EAAGlC,GAAK,GACxB+B,EAAE/B,KAAOJ,KAAKoC,kBADahC,IAI/B6C,IAEJ,GAAId,EAAEG,OAAS,GAAKW,EAAgB,EAChC,MAAM,IAAIS,MAAM,kCAGxB,OAAOT,GAGf,EA3LA,GAAa,EAAAc,QA6Lb,IAAMC,EAAW,IAAID,EAErB,kBAAuBvB,GACnB,OAAOwB,EAASzB,OAAOC,IAG3B,kBAAuBL,GACnB,OAAO6B,EAASjB,OAAOZ,IAS3B,+B,+CAwCA,OAxCkC,OAQpB,YAAAO,YAAV,SAAsBiB,GAClB,IAAIC,EAASD,EAYb,OAVAC,GAAU,GAEVA,GAAY,GAAKD,IAAO,EAAK,EAE7BC,GAAY,GAAKD,IAAO,GAAK,GAE7BC,GAAY,GAAKD,IAAO,GAAK,GAE7BC,GAAY,GAAKD,IAAO,EAAK,GAEtBE,OAAOC,aAAaF,IAGrB,YAAAJ,YAAV,SAAsB/C,GAClB,IAAImD,EA7OS,IA0Pb,OAVAA,IAAa,GAAKnD,EAAMA,EAAI,MAAS,GAhPxB,IAgP8CA,EAAI,GAAK,GAEpEmD,IAAa,GAAKnD,EAAMA,EAAI,MAAS,GAlPxB,IAkP8CA,EAAI,GAAK,GAEpEmD,IAAa,GAAKnD,EAAMA,EAAI,MAAS,GApPxB,IAoP8CA,EAAI,GAAK,GAEpEmD,IAAa,GAAKnD,EAAMA,EAAI,MAAS,GAtPxB,IAsP8CA,EAAI,GAAK,EAEpEmD,IAAa,GAAKnD,EAAMA,EAAI,OAAU,GAxPzB,IAwP+CA,EAAI,GAAK,IAI7E,EAxCA,CAAkCsD,GAArB,EAAAE,eA0Cb,IAAMC,EAAe,IAAID,EAEzB,yBAA8BzB,GAC1B,OAAO0B,EAAa3B,OAAOC,IAG/B,yBAA8BL,GAC1B,OAAO+B,EAAanB,OAAOZ,IAIlB,EAAAE,cAAgB,SAACC,GAC1B,OAAA0B,EAAS3B,cAAcC,IAEd,EAAAM,iBAAmB,SAACN,GAC7B,OAAA0B,EAASpB,iBAAiBN,IAEjB,EAAAO,cAAgB,SAACV,GAC1B,OAAA6B,EAASnB,cAAcV,K,8ECnR3B,IACMgC,EAAe,gCA2CrB,SAAgB9B,EAAcF,GAE1B,IADA,IAAIyB,EAAS,EACJxD,EAAI,EAAGA,EAAI+B,EAAEG,OAAQlC,IAAK,CAC/B,IAAMK,EAAI0B,EAAEsB,WAAWrD,GACvB,GAAIK,EAAI,IACJmD,GAAU,OACP,GAAInD,EAAI,KACXmD,GAAU,OACP,GAAInD,EAAI,MACXmD,GAAU,MACP,MAAInD,GAAK,OAOZ,MAAM,IAAIiD,MA7DA,wBAuDV,GAAItD,GAAK+B,EAAEG,OAAS,EAChB,MAAM,IAAIoB,MAxDJ,wBA0DVtD,IACAwD,GAAU,GAKlB,OAAOA,EAzDX,kBAAuBzB,GAOnB,IAHA,IAAMiC,EAAM,IAAIpB,WAAWX,EAAcF,IAErCkC,EAAM,EACDjE,EAAI,EAAGA,EAAI+B,EAAEG,OAAQlC,IAAK,CAC/B,IAAIK,EAAI0B,EAAEsB,WAAWrD,GACjBK,EAAI,IACJ2D,EAAIC,KAAS5D,EACNA,EAAI,MACX2D,EAAIC,KAAS,IAAO5D,GAAK,EACzB2D,EAAIC,KAAS,IAAW,GAAJ5D,GACbA,EAAI,OACX2D,EAAIC,KAAS,IAAO5D,GAAK,GACzB2D,EAAIC,KAAS,IAAQ5D,GAAK,EAAK,GAC/B2D,EAAIC,KAAS,IAAW,GAAJ5D,IAEpBL,IACAK,GAAS,KAAJA,IAAc,GACnBA,GAAuB,KAAlB0B,EAAEsB,WAAWrD,GAClBK,GAAK,MAEL2D,EAAIC,KAAS,IAAO5D,GAAK,GACzB2D,EAAIC,KAAS,IAAQ5D,GAAK,GAAM,GAChC2D,EAAIC,KAAS,IAAQ5D,GAAK,EAAK,GAC/B2D,EAAIC,KAAS,IAAW,GAAJ5D,GAG5B,OAAO2D,GAOX,kBA2BA,kBAAuBA,GAEnB,IADA,IAAME,EAAkB,GACflE,EAAI,EAAGA,EAAIgE,EAAI9B,OAAQlC,IAAK,CACjC,IAAIuD,EAAIS,EAAIhE,GAEZ,GAAQ,IAAJuD,EAAU,CACV,IAAIY,OAAG,EACP,GAAIZ,EAAI,IAAM,CAEV,GAAIvD,GAAKgE,EAAI9B,OACT,MAAM,IAAIoB,MAAMS,GAGpB,GAAoB,MAAV,KADJK,EAAKJ,IAAMhE,KAEb,MAAM,IAAIsD,MAAMS,GAEpBR,GAAS,GAAJA,IAAa,EAAU,GAALa,EACvBD,EAAM,SACH,GAAIZ,EAAI,IAAM,CAEjB,GAAIvD,GAAKgE,EAAI9B,OAAS,EAClB,MAAM,IAAIoB,MAAMS,GAEpB,IAAMK,EAAKJ,IAAMhE,GACXqE,EAAKL,IAAMhE,GACjB,GAAoB,MAAV,IAALoE,IAAuC,MAAV,IAALC,GACzB,MAAM,IAAIf,MAAMS,GAEpBR,GAAS,GAAJA,IAAa,IAAW,GAALa,IAAc,EAAU,GAALC,EAC3CF,EAAM,SACH,MAAIZ,EAAI,KAcX,MAAM,IAAID,MAAMS,GAZhB,GAAI/D,GAAKgE,EAAI9B,OAAS,EAClB,MAAM,IAAIoB,MAAMS,GAEdK,EAAKJ,IAAMhE,GACXqE,EAAKL,IAAMhE,GADjB,IAEMsE,EAAKN,IAAMhE,GACjB,GAAoB,MAAV,IAALoE,IAAuC,MAAV,IAALC,IAAuC,MAAV,IAALC,GACjD,MAAM,IAAIhB,MAAMS,GAEpBR,GAAS,GAAJA,IAAa,IAAW,GAALa,IAAc,IAAW,GAALC,IAAc,EAAU,GAALC,EAC/DH,EAAM,MAKV,GAAIZ,EAAIY,GAAQZ,GAAK,OAAUA,GAAK,MAChC,MAAM,IAAID,MAAMS,GAGpB,GAAIR,GAAK,MAAS,CAEd,GAAIA,EAAI,QACJ,MAAM,IAAID,MAAMS,GAEpBR,GAAK,MACLW,EAAMK,KAAKd,OAAOC,aAAa,MAAUH,GAAK,KAC9CA,EAAI,MAAc,KAAJA,GAItBW,EAAMK,KAAKd,OAAOC,aAAaH,IAEnC,OAAOW,EAAMM,KAAK,M,gBC7ItB/E,EAAOD,QAAU,EAAQ,GAAYiF,S,oCCSrC,IANA,IAAIf,EAAeD,OAAOC,aAEtBgB,EACF,mEACEC,EAAS,GAEJ,EAAI,EAAG1E,EAAIyE,EAASxC,OAAQ,EAAIjC,EAAG,IAC1C0E,EAAOD,EAASE,OAAO,IAAM,EAG/B,IAAIC,EAAU,SAAUxE,GACtB,IAAIyE,EAAKzE,EAAEgD,WAAW,GACtB,OAAOyB,EAAK,IACRzE,EACAyE,EAAK,KACHpB,EAAa,IAAQoB,IAAO,GAAMpB,EAAa,IAAa,GAALoB,GACvDpB,EAAa,IAASoB,IAAO,GAAM,IACnCpB,EAAa,IAASoB,IAAO,EAAK,IAClCpB,EAAa,IAAa,GAALoB,IAGzBC,EAAO,SAAUC,GACnB,OAAOA,EAAEC,QAAQ,gBAAiBJ,IAGhCK,EAAY,SAAUC,GACxB,IAAIC,EAAS,CAAC,EAAG,EAAG,GAAGD,EAAIjD,OAAS,GAChCmD,EACDF,EAAI9B,WAAW,IAAM,IACpB8B,EAAIjD,OAAS,EAAIiD,EAAI9B,WAAW,GAAK,IAAM,GAC5C8B,EAAIjD,OAAS,EAAIiD,EAAI9B,WAAW,GAAK,GAOxC,MANY,CACVqB,EAASE,OAAOS,IAAQ,IACxBX,EAASE,OAAQS,IAAQ,GAAM,IAC/BD,GAAU,EAAI,IAAMV,EAASE,OAAQS,IAAQ,EAAK,IAClDD,GAAU,EAAI,IAAMV,EAASE,OAAa,GAANS,IAEzBb,KAAK,KAGhBc,EACF,KAAOA,MACP,SAAU/B,GACR,OAAOA,EAAE0B,QAAQ,eAAgBC,ICTtB,MAnCf,MAIE,YACEK,EACAC,EACAC,EACAC,GAEA9F,KAAK4F,MAAQA,EACb5F,KAAK+F,MAAQJ,EAAI,KACX3F,KAAK+F,QACP/F,KAAK+F,MAAQD,EAAS9F,KAAK+F,SAE5BF,GAOL,YACE,OAAsB,OAAf7F,KAAK+F,MAId,gBACM/F,KAAK+F,QACP/F,KAAK4F,MAAM5F,KAAK+F,OAChB/F,KAAK+F,MAAQ,QC5BnB,SAAS,EAAaA,GACpB,KAAOC,aAAaD,GAEtB,SAAS,EAAcA,GACrB,KAAOE,cAAcF,GAQhB,MAAM,UAAoB,EAC/B,YAAYF,EAAcC,GACxBI,MAAMC,WAAY,EAAcN,GAAO,SAAUE,GAE/C,OADAD,IACO,SAUN,MAAM,UAAsB,EACjC,YAAYD,EAAcC,GACxBI,MAAME,YAAa,EAAeP,GAAO,SAAUE,GAEjD,OADAD,IACOC,MC/Bb,IA6Be,EA7BJ,CACTM,IAAG,IACGC,KAAKD,IACAC,KAAKD,OAEL,IAAIC,MAAOC,UAItBC,MAAMV,GACG,IAAI,EAAY,EAAGA,GAW5B,OAAOnF,KAAiB8F,GACtB,IAAIC,EAAiBC,MAAM3E,UAAU4E,MAAMrG,KAAKsG,UAAW,GAC3D,OAAO,SAAU/E,GACf,OAAOA,EAAOnB,GAAMmG,MAAMhF,EAAQ4E,EAAeK,OAAOF,eCXvD,SAASG,EAAUC,KAAgBC,GACxC,IAAK,IAAI9G,EAAI,EAAGA,EAAI8G,EAAQ5E,OAAQlC,IAAK,CACvC,IAAI+G,EAAaD,EAAQ9G,GACzB,IAAK,IAAI2B,KAAYoF,EAEjBA,EAAWpF,IACXoF,EAAWpF,GAAUqF,aACrBD,EAAWpF,GAAUqF,cAAgBtG,OAErCmG,EAAOlF,GAAYiF,EAAOC,EAAOlF,IAAa,GAAIoF,EAAWpF,IAE7DkF,EAAOlF,GAAYoF,EAAWpF,GAIpC,OAAOkF,EAGF,SAASI,IAEd,IADA,IAAI7G,EAAI,CAAC,UACAJ,EAAI,EAAGA,EAAIyG,UAAUvE,OAAQlC,IACR,iBAAjByG,UAAUzG,GACnBI,EAAEmE,KAAKkC,UAAUzG,IAEjBI,EAAEmE,KAAK2C,EAAkBT,UAAUzG,KAGvC,OAAOI,EAAEoE,KAAK,OAGT,SAAS2C,EAAaC,EAAcC,GAEzC,IAAIC,EAAgBf,MAAM3E,UAAU2F,QACpC,GAAc,OAAVH,EACF,OAAQ,EAEV,GAAIE,GAAiBF,EAAMG,UAAYD,EACrC,OAAOF,EAAMG,QAAQF,GAEvB,IAAK,IAAIrH,EAAI,EAAGC,EAAImH,EAAMlF,OAAQlC,EAAIC,EAAGD,IACvC,GAAIoH,EAAMpH,KAAOqH,EACf,OAAOrH,EAGX,OAAQ,EAaH,SAASwH,EAAY9F,EAAa+F,GACvC,IAAK,IAAIlG,KAAOG,EACVhB,OAAOkB,UAAUC,eAAe1B,KAAKuB,EAAQH,IAC/CkG,EAAE/F,EAAOH,GAAMA,EAAKG,GAUnB,SAASgG,EAAKhG,GACnB,IAAIgG,EAAO,GAIX,OAHAF,EAAY9F,GAAQ,SAAUiG,EAAGpG,GAC/BmG,EAAKnD,KAAKhD,MAELmG,EA0BF,SAAShB,EAAMU,EAAcK,EAAaG,GAC/C,IAAK,IAAI5H,EAAI,EAAGA,EAAIoH,EAAMlF,OAAQlC,IAChCyH,EAAEtH,KAAKyH,GAAW,KAAQR,EAAMpH,GAAIA,EAAGoH,GAepC,SAASS,EAAIT,EAAcK,GAEhC,IADA,IAAIjE,EAAS,GACJxD,EAAI,EAAGA,EAAIoH,EAAMlF,OAAQlC,IAChCwD,EAAOe,KAAKkD,EAAEL,EAAMpH,GAAIA,EAAGoH,EAAO5D,IAEpC,OAAOA,EAiCF,SAASsE,EAAOV,EAAcW,GACnCA,EACEA,GACA,SAAU9G,GACR,QAASA,GAIb,IADA,IAAIuC,EAAS,GACJxD,EAAI,EAAGA,EAAIoH,EAAMlF,OAAQlC,IAC5B+H,EAAKX,EAAMpH,GAAIA,EAAGoH,EAAO5D,IAC3BA,EAAOe,KAAK6C,EAAMpH,IAGtB,OAAOwD,EAcF,SAASwE,EAAatG,EAAgBqG,GAC3C,IAAIvE,EAAS,GAMb,OALAgE,EAAY9F,GAAQ,SAAUT,EAAOM,IAC9BwG,GAAQA,EAAK9G,EAAOM,EAAKG,EAAQ8B,IAAYyE,QAAQhH,MACxDuC,EAAOjC,GAAON,MAGXuC,EA0BF,SAAS0E,EAAId,EAAcW,GAChC,IAAK,IAAI/H,EAAI,EAAGA,EAAIoH,EAAMlF,OAAQlC,IAChC,GAAI+H,EAAKX,EAAMpH,GAAIA,EAAGoH,GACpB,OAAO,EAGX,OAAO,EAsBF,SAASe,EAAmB/F,GACjC,OA5GqCqF,EA4Gd,SAAUxG,GAI/B,MAHqB,iBAAVA,IACTA,EAAQiG,EAAkBjG,IAErBmH,oBJ1QoBrG,EI0QYd,EAAMoH,WJzQxC/C,EAAKP,EAAKhD,MADJ,IAAgBA,GI2JzByB,EAAS,GACbgE,EA0GiBpF,GA1GG,SAAUnB,EAAOM,GACnCiC,EAAOjC,GAAOkG,EAAExG,MAEXuC,EALF,IAAgCiE,EACjCjE,EAmHC,SAAS8E,EAAiBlG,GAC/B,IAxDsBV,EAClB8B,EAuDA+E,EAASP,EAAa5F,GAAM,SAAUnB,GACxC,YAAiBuH,IAAVvH,KAQT,OALY4G,GA5DUnG,EA6DZyG,EAAmBI,GA5DzB/E,EAAS,GACbgE,EAAY9F,GAAQ,SAAUT,EAAOM,GACnCiC,EAAOe,KAAK,CAAChD,EAAKN,OAEbuC,GAyDL,EAAKiF,OAAO,OAAQ,MACpBjE,KAAK,KAoEF,SAAS0C,EAAkBwB,GAChC,IACE,OAAOC,KAAK1B,UAAUyB,GACtB,MAAOE,GACP,OAAOD,KAAK1B,WAzDV4B,EAAU,GACZC,EAAQ,GAEH,SAAUC,EAAM9H,EAAO+H,GAC5B,IAAIhJ,EAAGO,EAAM0I,EAEb,cAAehI,GACb,IAAK,SACH,IAAKA,EACH,OAAO,KAET,IAAKjB,EAAI,EAAGA,EAAI6I,EAAQ3G,OAAQlC,GAAK,EACnC,GAAI6I,EAAQ7I,KAAOiB,EACjB,MAAO,CAAEiI,KAAMJ,EAAM9I,IAOzB,GAHA6I,EAAQtE,KAAKtD,GACb6H,EAAMvE,KAAKyE,GAEoC,mBAA3CtI,OAAOkB,UAAUyG,SAAS3B,MAAMzF,GAElC,IADAgI,EAAK,GACAjJ,EAAI,EAAGA,EAAIiB,EAAMiB,OAAQlC,GAAK,EACjCiJ,EAAGjJ,GAAK+I,EAAM9H,EAAMjB,GAAIgJ,EAAO,IAAMhJ,EAAI,UAI3C,IAAKO,KADL0I,EAAK,GACQhI,EACPP,OAAOkB,UAAUC,eAAe1B,KAAKc,EAAOV,KAC9C0I,EAAG1I,GAAQwI,EACT9H,EAAMV,GACNyI,EAAO,IAAML,KAAK1B,UAAU1G,GAAQ,MAK5C,OAAO0I,EACT,IAAK,SACL,IAAK,SACL,IAAK,UACH,OAAOhI,GArCN,CAsD+ByH,EAf3B,OA3CN,IACDG,EACFC,ECxQJ,IAmCe,EAnCe,CAC5BK,QAAS,QACTC,SAAU,EAEVC,OAAQ,GACRC,QAAS,IACTC,OAAQ,GAERC,SAAU,oBACVC,SAAU,GACVC,UAAW,IACXC,SAAU,UAEVC,WAAY,mBAEZC,aAAc,eACdC,cAAe,OACfC,gBAAiB,KACjBC,YAAa,IACbC,mBAAoB,IACpBC,mBAAoB,CAClBC,SAAU,oBACVC,UAAW,QAEbC,qBAAsB,CACpBF,SAAU,eACVC,UAAW,QAIbE,SAAU,uBACVC,UAAW,wBACXC,kBAAmB,IC3DrB,SAASC,EACPC,EACAnC,EACAS,GAIA,OAFa0B,GAAcnC,EAAOoC,OAAS,IAAM,IAEjC,OADLpC,EAAOoC,OAASpC,EAAOqC,QAAUrC,EAAOsC,YACpB7B,EAGjC,SAAS8B,EAAevJ,EAAawJ,GASnC,MARW,QAAUxJ,GAEnB,aACA,EAAS6H,SADT,sBAIA,EAASD,SACR4B,EAAc,IAAMA,EAAc,KAIhC,IAAIC,EAAgB,CACzBC,WAAY,SAAU1J,EAAagH,GAEjC,OAAOkC,EAAc,KAAMlC,GADfA,EAAOoB,UAAY,IAAMmB,EAAevJ,EAAK,kBAKlD2J,EAAkB,CAC3BD,WAAY,SAAU1J,EAAagH,GAEjC,OAAOkC,EAAc,OAAQlC,GADjBA,EAAOoB,UAAY,WAAamB,EAAevJ,MC9BhD,MAAM,EAGnB,cACE3B,KAAKuL,WAAa,GAGpB,IAAI5K,GACF,OAAOX,KAAKuL,WAAWC,EAAO7K,IAGhC,IAAIA,EAAcmF,EAAoBkC,GACpC,IAAIyD,EAAoBD,EAAO7K,GAC/BX,KAAKuL,WAAWE,GACdzL,KAAKuL,WAAWE,IAAsB,GACxCzL,KAAKuL,WAAWE,GAAmB9G,KAAK,CACtC+G,GAAI5F,EACJkC,QAASA,IAIb,OAAOrH,EAAemF,EAAqBkC,GACzC,GAAKrH,GAASmF,GAAakC,EAA3B,CAKA,IAAI2D,EAAQhL,EAAO,CAAC6K,EAAO7K,IAAS,EAAiBX,KAAKuL,YAEtDzF,GAAYkC,EACdhI,KAAK4L,eAAeD,EAAO7F,EAAUkC,GAErChI,KAAK6L,mBAAmBF,QATxB3L,KAAKuL,WAAa,GAad,eAAeI,EAAiB7F,EAAoBkC,GAC1D,EACE2D,GACA,SAAUhL,GACRX,KAAKuL,WAAW5K,GAAQ,EACtBX,KAAKuL,WAAW5K,IAAS,IACzB,SAAUmL,GACR,OACGhG,GAAYA,IAAagG,EAAQJ,IACjC1D,GAAWA,IAAY8D,EAAQ9D,WAID,IAAjChI,KAAKuL,WAAW5K,GAAM2B,eACjBtC,KAAKuL,WAAW5K,KAG3BX,MAII,mBAAmB2L,GACzB,EACEA,GACA,SAAUhL,UACDX,KAAKuL,WAAW5K,KAEzBX,OAKN,SAASwL,EAAO7K,GACd,MAAO,IAAMA,EChEA,MAAM,EAKnB,YAAYoL,GACV/L,KAAKgM,UAAY,IAAI,EACrBhM,KAAKiM,iBAAmB,GACxBjM,KAAK+L,YAAcA,EAGrB,KAAKG,EAAmBpG,EAAoBkC,GAE1C,OADAhI,KAAKgM,UAAUG,IAAID,EAAWpG,EAAUkC,GACjChI,KAGT,YAAY8F,GAEV,OADA9F,KAAKiM,iBAAiBtH,KAAKmB,GACpB9F,KAGT,OAAOkM,EAAoBpG,EAAqBkC,GAE9C,OADAhI,KAAKgM,UAAUI,OAAOF,EAAWpG,EAAUkC,GACpChI,KAGT,cAAc8F,GACZ,OAAKA,GAKL9F,KAAKiM,iBAAmB,EACtBjM,KAAKiM,kBAAoB,GACxBxL,GAAMA,IAAMqF,GAGR9F,OATLA,KAAKiM,iBAAmB,GACjBjM,MAWX,aAGE,OAFAA,KAAKqM,SACLrM,KAAKsM,gBACEtM,KAGT,KAAKkM,EAAmB1J,EAAY+J,GAClC,IAAK,IAAInM,EAAI,EAAGA,EAAIJ,KAAKiM,iBAAiB3J,OAAQlC,IAChDJ,KAAKiM,iBAAiB7L,GAAG8L,EAAW1J,GAGtC,IAAIwJ,EAAYhM,KAAKgM,UAAU/K,IAAIiL,GAC/BzF,EAAO,GAYX,GAVI8F,EAGF9F,EAAK9B,KAAKnC,EAAM+J,GACP/J,GAGTiE,EAAK9B,KAAKnC,GAGRwJ,GAAaA,EAAU1J,OAAS,EAClC,IAASlC,EAAI,EAAGA,EAAI4L,EAAU1J,OAAQlC,IACpC4L,EAAU5L,GAAGsL,GAAG5E,MAAMkF,EAAU5L,GAAG4H,SAAW,KAAQvB,QAE/CzG,KAAK+L,aACd/L,KAAK+L,YAAYG,EAAW1J,GAG9B,OAAOxC,MC7BI,UAjDf,oBAaU,KAAAwM,UAAaC,IACf,KAAOC,SAAW,KAAOA,QAAQC,KACnC,KAAOD,QAAQC,IAAIF,IAdvB,SAAShG,GACPzG,KAAK2M,IAAI3M,KAAKwM,UAAW/F,GAG3B,QAAQA,GACNzG,KAAK2M,IAAI3M,KAAK4M,cAAenG,GAG/B,SAASA,GACPzG,KAAK2M,IAAI3M,KAAK6M,eAAgBpG,GASxB,cAAcgG,GAChB,KAAOC,SAAW,KAAOA,QAAQI,KACnC,KAAOJ,QAAQI,KAAKL,GAEpBzM,KAAKwM,UAAUC,GAIX,eAAeA,GACjB,KAAOC,SAAW,KAAOA,QAAQK,MACnC,KAAOL,QAAQK,MAAMN,GAErBzM,KAAK4M,cAAcH,GAIf,IACNO,KACGvG,GAEH,IAAIgG,EAAUpF,EAAUP,MAAM9G,KAAM6G,WACpC,GAAI,GAAO8F,IACT,GAAOA,IAAIF,QACN,GAAI,GAAOQ,aAAc,CAClBD,EAAuBpL,KAAK5B,KACxC2M,CAAIF,MCTK,MAAM,UAA4B,EAc/C,YACES,EACAvM,EACAwM,EACAxL,EACAyL,GAEAlH,QACAlG,KAAKqN,WAAa,GAAQC,+BAC1BtN,KAAKkN,MAAQA,EACblN,KAAKW,KAAOA,EACZX,KAAKmN,SAAWA,EAChBnN,KAAK2B,IAAMA,EACX3B,KAAKoN,QAAUA,EAEfpN,KAAKuN,MAAQ,MACbvN,KAAKwN,SAAWJ,EAAQI,SACxBxN,KAAKmK,gBAAkBiD,EAAQjD,gBAC/BnK,KAAKyN,GAAKzN,KAAKwN,SAASE,mBAO1B,wBACE,OAAOrF,QAAQrI,KAAKkN,MAAMS,uBAO5B,eACE,OAAOtF,QAAQrI,KAAKkN,MAAMU,cAO5B,UACE,GAAI5N,KAAK6N,QAAyB,gBAAf7N,KAAKuN,MACtB,OAAO,EAGT,IAAIO,EAAM9N,KAAKkN,MAAMa,KAAK1C,WAAWrL,KAAK2B,IAAK3B,KAAKoN,SACpD,IACEpN,KAAK6N,OAAS7N,KAAKkN,MAAMc,UAAUF,EAAK9N,KAAKoN,SAC7C,MAAOpE,GAKP,OAJA,EAAKxC,MAAM,KACTxG,KAAKiO,QAAQjF,GACbhJ,KAAKkO,YAAY,aAEZ,EAOT,OAJAlO,KAAKmO,gBAEL,EAAOC,MAAM,aAAc,CAAE5D,UAAWxK,KAAKW,KAAMmN,QACnD9N,KAAKkO,YAAY,eACV,EAOT,QACE,QAAIlO,KAAK6N,SACP7N,KAAK6N,OAAOQ,SACL,GAWX,KAAK7L,GACH,MAAmB,SAAfxC,KAAKuN,QAEP,EAAK/G,MAAM,KACLxG,KAAK6N,QACP7N,KAAK6N,OAAOS,KAAK9L,MAGd,GAOX,OACqB,SAAfxC,KAAKuN,OAAoBvN,KAAK4N,gBAChC5N,KAAK6N,OAAOU,OAIR,SACFvO,KAAKkN,MAAMsB,YACbxO,KAAKkN,MAAMsB,WACTxO,KAAK6N,OACL7N,KAAKkN,MAAMa,KAAKU,QAAQzO,KAAK2B,IAAK3B,KAAKoN,UAG3CpN,KAAKkO,YAAY,QACjBlO,KAAK6N,OAAOa,YAAS9F,EAGf,QAAQmE,GACd/M,KAAK2O,KAAK,QAAS,CAAEC,KAAM,iBAAkB7B,MAAOA,IACpD/M,KAAKwN,SAAST,MAAM/M,KAAK6O,qBAAqB,CAAE9B,MAAOA,EAAMtE,cAGvD,QAAQqG,GACVA,EACF9O,KAAKkO,YAAY,SAAU,CACzBa,KAAMD,EAAWC,KACjBC,OAAQF,EAAWE,OACnBC,SAAUH,EAAWG,WAGvBjP,KAAKkO,YAAY,UAEnBlO,KAAKkP,kBACLlP,KAAK6N,YAASjF,EAGR,UAAU6D,GAChBzM,KAAK2O,KAAK,UAAWlC,GAGf,aACNzM,KAAK2O,KAAK,YAGJ,gBACN3O,KAAK6N,OAAOa,OAAS,KACnB1O,KAAKmP,UAEPnP,KAAK6N,OAAOuB,QAAWrC,IACrB/M,KAAKiO,QAAQlB,IAEf/M,KAAK6N,OAAOwB,QAAWP,IACrB9O,KAAKsP,QAAQR,IAEf9O,KAAK6N,OAAO0B,UAAa9C,IACvBzM,KAAKwP,UAAU/C,IAGbzM,KAAK4N,iBACP5N,KAAK6N,OAAO4B,WAAa,KACvBzP,KAAK0P,eAKH,kBACF1P,KAAK6N,SACP7N,KAAK6N,OAAOa,YAAS9F,EACrB5I,KAAK6N,OAAOuB,aAAUxG,EACtB5I,KAAK6N,OAAOwB,aAAUzG,EACtB5I,KAAK6N,OAAO0B,eAAY3G,EACpB5I,KAAK4N,iBACP5N,KAAK6N,OAAO4B,gBAAa7G,IAKvB,YAAY2E,EAAe5E,GACjC3I,KAAKuN,MAAQA,EACbvN,KAAKwN,SAASmC,KACZ3P,KAAK6O,qBAAqB,CACxBtB,MAAOA,EACP5E,OAAQA,KAGZ3I,KAAK2O,KAAKpB,EAAO5E,GAGnB,qBAAqB8D,GACnB,OAAO,EAAmB,CAAEmD,IAAK5P,KAAKyN,IAAMhB,ICzNjC,MAAM,EAGnB,YAAYS,GACVlN,KAAKkN,MAAQA,EAQf,YAAY2C,GACV,OAAO7P,KAAKkN,MAAM4C,YAAYD,GAWhC,iBACElP,EACAwM,EACAxL,EACAyL,GAEA,OAAO,IAAI,EAAoBpN,KAAKkN,MAAOvM,EAAMwM,EAAUxL,EAAKyL,ICrCpE,IAAI2C,EAAc,IAAI,EAA0B,CAC9ChC,KAAM,EACNJ,uBAAuB,EACvBC,cAAc,EAEdoC,cAAe,WACb,OAAO3H,QAAQ,GAAQ4H,oBAEzBH,YAAa,WACX,OAAOzH,QAAQ,GAAQ4H,oBAEzBjC,UAAW,SAAUF,GACnB,OAAO,GAAQoC,gBAAgBpC,MAI/BqC,EAAoB,CACtBpC,KAAM,EACNJ,uBAAuB,EACvBC,cAAc,EACdoC,cAAe,WACb,OAAO,IAIAI,EAAyB,EAClC,CACEpC,UAAW,SAAUF,GACnB,OAAO,GAAQuC,YAAYC,sBAAsBxC,KAGrDqC,GAESI,EAAuB,EAChC,CACEvC,UAAW,SAAUF,GACnB,OAAO,GAAQuC,YAAYG,oBAAoB1C,KAGnDqC,GAGEM,EAAmB,CACrBX,YAAa,WACX,OAAO,GAAQY,mBAwBJ,EANmB,CAChCtF,GAAI2E,EACJY,cAf0B,IAAI,EAE5B,EAAmB,GAAIP,EAAwBK,IAcjDG,YATwB,IAAI,EAE1B,EAAmB,GAAIL,EAAsBE,KCpDlC,MAAM,EAOnB,YACEI,EACArG,EACA4C,GAEApN,KAAK6Q,QAAUA,EACf7Q,KAAKwK,UAAYA,EACjBxK,KAAK8Q,aAAe1D,EAAQ0D,aAC5B9Q,KAAK+Q,aAAe3D,EAAQ2D,aAC5B/Q,KAAKgR,eAAYpI,EAanB,iBACEjI,EACAwM,EACAxL,EACAyL,GAEAA,EAAU,EAAmB,GAAIA,EAAS,CACxCjD,gBAAiBnK,KAAKgR,YAExB,IAAIC,EAAajR,KAAKwK,UAAU0G,iBAC9BvQ,EACAwM,EACAxL,EACAyL,GAGE+D,EAAgB,KAEhBhC,EAAS,WACX8B,EAAW5E,OAAO,OAAQ8C,GAC1B8B,EAAWrP,KAAK,SAAUwP,GAC1BD,EAAgB,EAAK9K,OAEnB+K,EAAYtC,IAGd,GAFAmC,EAAW5E,OAAO,SAAU+E,GAEJ,OAApBtC,EAAWC,MAAqC,OAApBD,EAAWC,KAEzC/O,KAAK6Q,QAAQQ,mBACR,IAAKvC,EAAWG,UAAYkC,EAAe,CAEhD,IAAIG,EAAW,EAAKjL,MAAQ8K,EACxBG,EAAW,EAAItR,KAAK+Q,eACtB/Q,KAAK6Q,QAAQQ,cACbrR,KAAKgR,UAAYO,KAAKC,IAAIF,EAAW,EAAGtR,KAAK8Q,iBAMnD,OADAG,EAAWrP,KAAK,OAAQuN,GACjB8B,EAWT,YAAYpB,GACV,OAAO7P,KAAK6Q,QAAQY,WAAazR,KAAKwK,UAAUsF,YAAYD,IC/FhE,MAAM6B,EAAW,CAgBfC,cAAe,SAAUC,GACvB,IACE,IAAIC,EAAc9I,KAAK+I,MAAMF,EAAapP,MACtCuP,EAAkBF,EAAYrP,KAClC,GAA+B,iBAApBuP,EACT,IACEA,EAAkBhJ,KAAK+I,MAAMD,EAAYrP,MACzC,MAAOwG,IAEX,IAAIgJ,EAA2B,CAC7BC,MAAOJ,EAAYI,MACnBC,QAASL,EAAYK,QACrB1P,KAAMuP,GAKR,OAHIF,EAAYM,UACdH,EAAYG,QAAUN,EAAYM,SAE7BH,EACP,MAAOhJ,GACP,KAAM,CAAE4F,KAAM,oBAAqB7B,MAAO/D,EAAGxG,KAAMoP,EAAapP,QAUpE4P,cAAe,SAAUH,GACvB,OAAOlJ,KAAK1B,UAAU4K,IAiBxBI,iBAAkB,SAAUT,GAC1B,IAAInF,EAAUiF,EAASC,cAAcC,GAErC,GAAsB,kCAAlBnF,EAAQwF,MAA2C,CACrD,IAAKxF,EAAQjK,KAAK8P,iBAChB,KAAM,6CAER,MAAO,CACLC,OAAQ,YACR9E,GAAIhB,EAAQjK,KAAKgQ,UACjBrI,gBAAiD,IAAhCsC,EAAQjK,KAAK8P,kBAE3B,GAAsB,iBAAlB7F,EAAQwF,MAGjB,MAAO,CACLM,OAAQvS,KAAKyS,eAAehG,EAAQjK,MACpCuK,MAAO/M,KAAK0S,cAAcjG,EAAQjK,OAGpC,KAAM,qBAcViQ,eAAgB,SAAU3D,GACxB,OAAIA,EAAWC,KAAO,IAMhBD,EAAWC,MAAQ,MAAQD,EAAWC,MAAQ,KACzC,UAEA,KAEoB,MAApBD,EAAWC,KACb,WACED,EAAWC,KAAO,KACpB,UACED,EAAWC,KAAO,KACpB,UACED,EAAWC,KAAO,KACpB,QAGA,WAaX2D,cAAe,SAAU5D,GACvB,OAAwB,MAApBA,EAAWC,MAAqC,OAApBD,EAAWC,KAClC,CACLH,KAAM,cACNpM,KAAM,CACJuM,KAAMD,EAAWC,KACjBtC,QAASqC,EAAWE,QAAUF,EAAWrC,UAItC,OAKE,QClIA,MAAM,UAAmB,EAKtC,YAAYgB,EAAYjD,GACtBtE,QACAlG,KAAKyN,GAAKA,EACVzN,KAAKwK,UAAYA,EACjBxK,KAAKmK,gBAAkBK,EAAUL,gBACjCnK,KAAKmO,gBAOP,wBACE,OAAOnO,KAAKwK,UAAUmD,wBAOxB,KAAKnL,GACH,OAAOxC,KAAKwK,UAAU8D,KAAK9L,GAU7B,WAAW7B,EAAc6B,EAAW0P,GAClC,IAAID,EAAqB,CAAEA,MAAOtR,EAAM6B,KAAMA,GAK9C,OAJI0P,IACFD,EAAMC,QAAUA,GAElB,EAAO9D,MAAM,aAAc6D,GACpBjS,KAAKsO,KAAK,EAAS8D,cAAcH,IAQ1C,OACMjS,KAAKwK,UAAUoD,eACjB5N,KAAKwK,UAAU+D,OAEfvO,KAAK2S,WAAW,cAAe,IAKnC,QACE3S,KAAKwK,UAAU6D,QAGT,gBACN,IAAIuE,EAAY,CACdnG,QAAUmF,IACR,IAAII,EACJ,IACEA,EAAc,EAASL,cAAcC,GACrC,MAAO5I,GACPhJ,KAAK2O,KAAK,QAAS,CACjBC,KAAM,oBACN7B,MAAO/D,EACPxG,KAAMoP,EAAapP,OAIvB,QAAoBoG,IAAhBoJ,EAA2B,CAG7B,OAFA,EAAO5D,MAAM,aAAc4D,GAEnBA,EAAYC,OAClB,IAAK,eACHjS,KAAK2O,KAAK,QAAS,CACjBC,KAAM,cACNpM,KAAMwP,EAAYxP,OAEpB,MACF,IAAK,cACHxC,KAAK2O,KAAK,QACV,MACF,IAAK,cACH3O,KAAK2O,KAAK,QAGd3O,KAAK2O,KAAK,UAAWqD,KAGzBa,SAAU,KACR7S,KAAK2O,KAAK,aAEZ5B,MAAQA,IACN/M,KAAK2O,KAAK,QAAS5B,IAErB+F,OAAShE,IACPI,IAEIJ,GAAcA,EAAWC,MAC3B/O,KAAK+S,iBAAiBjE,GAGxB9O,KAAKwK,UAAY,KACjBxK,KAAK2O,KAAK,YAIVO,EAAkB,KACpB,EAAwB0D,EAAW,CAACI,EAAUf,KAC5CjS,KAAKwK,UAAU6B,OAAO4F,EAAOe,MAIjC,EAAwBJ,EAAW,CAACI,EAAUf,KAC5CjS,KAAKwK,UAAU5I,KAAKqQ,EAAOe,KAIvB,iBAAiBlE,GACvB,IAAIyD,EAAS,EAASE,eAAe3D,GACjC/B,EAAQ,EAAS2F,cAAc5D,GAC/B/B,GACF/M,KAAK2O,KAAK,QAAS5B,GAEjBwF,GACFvS,KAAK2O,KAAK4D,EAAQ,CAAEA,OAAQA,EAAQxF,MAAOA,KCrIlC,MAAM,EAMnB,YACEvC,EACA1E,GAEA9F,KAAKwK,UAAYA,EACjBxK,KAAK8F,SAAWA,EAChB9F,KAAKmO,gBAGP,QACEnO,KAAKkP,kBACLlP,KAAKwK,UAAU6D,QAGT,gBACNrO,KAAKwP,UAAahP,IAGhB,IAAIoD,EAFJ5D,KAAKkP,kBAGL,IACEtL,EAAS,EAASyO,iBAAiB7R,GACnC,MAAOwI,GAGP,OAFAhJ,KAAKiT,OAAO,QAAS,CAAElG,MAAO/D,SAC9BhJ,KAAKwK,UAAU6D,QAIK,cAAlBzK,EAAO2O,OACTvS,KAAKiT,OAAO,YAAa,CACvBhC,WAAY,IAAI,EAAWrN,EAAO6J,GAAIzN,KAAKwK,WAC3CL,gBAAiBvG,EAAOuG,mBAG1BnK,KAAKiT,OAAOrP,EAAO2O,OAAQ,CAAExF,MAAOnJ,EAAOmJ,QAC3C/M,KAAKwK,UAAU6D,UAInBrO,KAAKoR,SAAYtC,IACf9O,KAAKkP,kBAEL,IAAIqD,EAAS,EAASE,eAAe3D,IAAe,UAChD/B,EAAQ,EAAS2F,cAAc5D,GACnC9O,KAAKiT,OAAOV,EAAQ,CAAExF,MAAOA,KAG/B/M,KAAKwK,UAAU5I,KAAK,UAAW5B,KAAKwP,WACpCxP,KAAKwK,UAAU5I,KAAK,SAAU5B,KAAKoR,UAG7B,kBACNpR,KAAKwK,UAAU6B,OAAO,UAAWrM,KAAKwP,WACtCxP,KAAKwK,UAAU6B,OAAO,SAAUrM,KAAKoR,UAG/B,OAAOmB,EAAgB5J,GAC7B3I,KAAK8F,SACH,EAAmB,CAAE0E,UAAWxK,KAAKwK,UAAW+H,OAAQA,GAAU5J,KC1EzD,MAAM,EAKnB,YAAY6E,EAAoBJ,GAC9BpN,KAAKwN,SAAWA,EAChBxN,KAAKoN,QAAUA,GAAW,GAG5B,KAAKrC,EAAiBjF,GAChB9F,KAAKwN,SAAS0F,WAIlBlT,KAAKwN,SAASc,KACZ,GAAQ6E,kBAAkBC,SAASpT,KAAM+K,GACzCjF,IC3BC,MAAMuN,WAAqB3P,MAChC,YAAY4P,GACVpN,MAAMoN,GAENxS,OAAOyS,eAAevT,gBAAiBgC,YAIpC,MAAMwR,WAAuB9P,MAClC,YAAY4P,GACVpN,MAAMoN,GAENxS,OAAOyS,eAAevT,gBAAiBgC,YAIN0B,MAO9B,MAAM+P,WAAgC/P,MAC3C,YAAY4P,GACVpN,MAAMoN,GAENxS,OAAOyS,eAAevT,gBAAiBgC,YAGpC,MAAM0R,WAAwBhQ,MACnC,YAAY4P,GACVpN,MAAMoN,GAENxS,OAAOyS,eAAevT,gBAAiBgC,YAGpC,MAAM2R,WAA2BjQ,MACtC,YAAY4P,GACVpN,MAAMoN,GAENxS,OAAOyS,eAAevT,gBAAiBgC,YAGpC,MAAM4R,WAA6BlQ,MACxC,YAAY4P,GACVpN,MAAMoN,GAENxS,OAAOyS,eAAevT,gBAAiBgC,YAGpC,MAAM6R,WAA4BnQ,MACvC,YAAY4P,GACVpN,MAAMoN,GAENxS,OAAOyS,eAAevT,gBAAiBgC,YAGpC,MAAM8R,WAAsBpQ,MAEjC,YAAYqQ,EAAgBT,GAC1BpN,MAAMoN,GACNtT,KAAK+T,OAASA,EAEdjT,OAAOyS,eAAevT,gBAAiBgC,YC9D3C,MAAMgS,GAAW,CACfC,QAAS,qBACTlG,KAAM,CACJmG,uBAAwB,CACtB9K,KAAM,kDAER+K,sBAAuB,CACrB/K,KAAM,gDAERgL,qBAAsB,CACpBhL,KAAM,gCAERiL,uBAAwB,CACtBjL,KAAM,uDAERkL,wBAAyB,CACvBC,QACE,iHA0BO,OAhBQ,SAAU5S,GAC/B,MACM6S,EAASR,GAASjG,KAAKpM,GAC7B,IAAK6S,EAAQ,MAAO,GAEpB,IAAI1G,EAOJ,OANI0G,EAAOD,QACTzG,EAAM0G,EAAOD,QACJC,EAAOpL,OAChB0E,EAAMkG,GAASC,QAAUO,EAAOpL,MAG7B0E,EACE,QAAgBA,EADN,ICrBJ,MAAM,WAAgB,EAQnC,YAAYnN,EAAc8T,GACxBvO,OAAM,SAAU+L,EAAOzP,GACrB,EAAO4L,MAAM,mBAAqBzN,EAAO,QAAUsR,MAGrDjS,KAAKW,KAAOA,EACZX,KAAKyU,OAASA,EACdzU,KAAK0U,YAAa,EAClB1U,KAAK2U,qBAAsB,EAC3B3U,KAAK4U,uBAAwB,EAO/B,UAAUC,EAAkB/O,GAC1B,OAAOA,EAAS,KAAM,CAAEgP,KAAM,KAIhC,QAAQ7C,EAAezP,GACrB,GAAiC,IAA7ByP,EAAMtK,QAAQ,WAChB,MAAM,IAAI,GACR,UAAYsK,EAAQ,mCAGxB,IAAKjS,KAAK0U,WAAY,CACpB,IAAIK,EAAS,GAAwB,0BACrC,EAAOjI,KACL,0EAA0EiI,GAG9E,OAAO/U,KAAKyU,OAAO9B,WAAWV,EAAOzP,EAAMxC,KAAKW,MAIlD,aACEX,KAAK0U,YAAa,EAClB1U,KAAK2U,qBAAsB,EAO7B,YAAY1C,GACV,IAAI/F,EAAY+F,EAAMA,MAClBzP,EAAOyP,EAAMzP,KACjB,GAAkB,2CAAd0J,EACFlM,KAAKgV,iCAAiC/C,QACjC,GAAkB,uCAAd/F,EACTlM,KAAKiV,6BAA6BhD,QAC7B,GAA8C,IAA1C/F,EAAUvE,QAAQ,oBAA2B,CAEtD3H,KAAK2O,KAAKzC,EAAW1J,EADI,KAK7B,iCAAiCyP,GAC/BjS,KAAK2U,qBAAsB,EAC3B3U,KAAK0U,YAAa,EACd1U,KAAK4U,sBACP5U,KAAKyU,OAAOS,YAAYlV,KAAKW,MAE7BX,KAAK2O,KAAK,gCAAiCsD,EAAMzP,MAIrD,6BAA6ByP,GACvBA,EAAMzP,KAAK2S,qBACbnV,KAAKoV,kBAAoBnD,EAAMzP,KAAK2S,oBAGtCnV,KAAK2O,KAAK,4BAA6BsD,EAAMzP,MAI/C,YACMxC,KAAK0U,aAGT1U,KAAK2U,qBAAsB,EAC3B3U,KAAK4U,uBAAwB,EAC7B5U,KAAKqV,UACHrV,KAAKyU,OAAOxD,WAAWuB,UACvB,CAACzF,EAAqBvK,KAChBuK,GACF/M,KAAK2U,qBAAsB,EAI3B,EAAO5H,MAAMA,EAAMtE,YACnBzI,KAAK2O,KACH,4BACA7N,OAAOwU,OACL,GACA,CACE1G,KAAM,YACN7B,MAAOA,EAAMN,SAEfM,aAAiB+G,GAAgB,CAAEC,OAAQhH,EAAMgH,QAAW,MAIhE/T,KAAKyU,OAAO9B,WAAW,mBAAoB,CACzCmC,KAAMtS,EAAKsS,KACXS,aAAc/S,EAAK+S,aACnBrD,QAASlS,KAAKW,UAQxB,cACEX,KAAK0U,YAAa,EAClB1U,KAAKyU,OAAO9B,WAAW,qBAAsB,CAC3CT,QAASlS,KAAKW,OAKlB,qBACEX,KAAK4U,uBAAwB,EAI/B,wBACE5U,KAAK4U,uBAAwB,GCvJlB,MAAM,WAAuB,GAM1C,UAAUC,EAAkB/O,GAC1B,OAAO9F,KAAKyU,OAAOe,OAAOC,kBACxB,CACEC,YAAa1V,KAAKW,KAClBkU,SAAUA,GAEZ/O,IClBS,MAAM,GAMnB,cACE9F,KAAK2V,QAUP,IAAIlI,GACF,OAAI3M,OAAOkB,UAAUC,eAAe1B,KAAKP,KAAK4V,QAASnI,GAC9C,CACLA,GAAIA,EACJkC,KAAM3P,KAAK4V,QAAQnI,IAGd,KAQX,KAAK3H,GACH,EAAwB9F,KAAK4V,QAAS,CAACC,EAAQpI,KAC7C3H,EAAS9F,KAAKiB,IAAIwM,MAKtB,QAAQA,GACNzN,KAAK8V,KAAOrI,EAId,eAAesI,GACb/V,KAAK4V,QAAUG,EAAiBC,SAASC,KACzCjW,KAAKkW,MAAQH,EAAiBC,SAASE,MACvClW,KAAKmW,GAAKnW,KAAKiB,IAAIjB,KAAK8V,MAI1B,UAAUM,GAKR,OAJqC,OAAjCpW,KAAKiB,IAAImV,EAAWjE,UACtBnS,KAAKkW,QAEPlW,KAAK4V,QAAQQ,EAAWjE,SAAWiE,EAAWC,UACvCrW,KAAKiB,IAAImV,EAAWjE,SAI7B,aAAaiE,GACX,IAAIP,EAAS7V,KAAKiB,IAAImV,EAAWjE,SAKjC,OAJI0D,WACK7V,KAAK4V,QAAQQ,EAAWjE,SAC/BnS,KAAKkW,SAEAL,EAIT,QACE7V,KAAK4V,QAAU,GACf5V,KAAKkW,MAAQ,EACblW,KAAK8V,KAAO,KACZ9V,KAAKmW,GAAK,M,2SCpEC,MAAM,WAAwB,GAQ3C,YAAYxV,EAAc8T,GACxBvO,MAAMvF,EAAM8T,GACZzU,KAAK4V,QAAU,IAAI,GAQrB,UAAUf,EAAkB/O,GAC1BI,MAAMmP,UAAUR,EAAU,CAAO9H,EAAOuJ,IAAa,GAAD,gCAClD,IAAKvJ,EAEH,GAA6B,OAD7BuJ,EAAWA,GACEf,aAAsB,CACjC,IAAIgB,EAAcxN,KAAK+I,MAAMwE,EAASf,cACtCvV,KAAK4V,QAAQY,QAAQD,EAAYpE,aAC5B,CAEL,SADMnS,KAAKyU,OAAOgC,KAAKC,kBACW,MAA9B1W,KAAKyU,OAAOgC,KAAKE,UAId,CACL,IAAI5B,EAAS,GAAwB,yBAOrC,OANA,EAAOhI,MACL,sCAAsC/M,KAAKW,yCACPoU,4CAGtCjP,EAAS,yBART9F,KAAK4V,QAAQY,QAAQxW,KAAKyU,OAAOgC,KAAKE,UAAUlJ,IAatD3H,EAASiH,EAAOuJ,OAQpB,YAAYrE,GACV,IAAI/F,EAAY+F,EAAMA,MACtB,GAA8C,IAA1C/F,EAAUvE,QAAQ,oBACpB3H,KAAK4W,oBAAoB3E,OACpB,CACL,IAAIzP,EAAOyP,EAAMzP,KACb+J,EAAqB,GACrB0F,EAAME,UACR5F,EAAS4F,QAAUF,EAAME,SAE3BnS,KAAK2O,KAAKzC,EAAW1J,EAAM+J,IAG/B,oBAAoB0F,GAClB,IAAI/F,EAAY+F,EAAMA,MAClBzP,EAAOyP,EAAMzP,KACjB,OAAQ0J,GACN,IAAK,yCACHlM,KAAKgV,iCAAiC/C,GACtC,MACF,IAAK,qCACHjS,KAAKiV,6BAA6BhD,GAClC,MACF,IAAK,+BACH,IAAI4E,EAAc7W,KAAK4V,QAAQkB,UAAUtU,GACzCxC,KAAK2O,KAAK,sBAAuBkI,GACjC,MACF,IAAK,iCACH,IAAIE,EAAgB/W,KAAK4V,QAAQoB,aAAaxU,GAC1CuU,GACF/W,KAAK2O,KAAK,wBAAyBoI,IAM3C,iCAAiC9E,GAC/BjS,KAAK2U,qBAAsB,EAC3B3U,KAAK0U,YAAa,EACd1U,KAAK4U,sBACP5U,KAAKyU,OAAOS,YAAYlV,KAAKW,OAE7BX,KAAK4V,QAAQqB,eAAehF,EAAMzP,MAClCxC,KAAK2O,KAAK,gCAAiC3O,KAAK4V,UAKpD,aACE5V,KAAK4V,QAAQD,QACbzP,MAAMgR,c,oBC3FK,MAAM,WAAyB,GAI5C,YAAYvW,EAAc8T,EAAgB0C,GACxCjR,MAAMvF,EAAM8T,GAJd,KAAA9S,IAAkB,KAKhB3B,KAAKmX,KAAOA,EAQd,UAAUtC,EAAkB/O,GAC1BI,MAAMmP,UACJR,EACA,CAAC9H,EAAqBuJ,KACpB,GAAIvJ,EAEF,YADAjH,EAASiH,EAAOuJ,GAGlB,IAAIc,EAAed,EAAwB,cACtCc,GASLpX,KAAK2B,IAAM,kBAAayV,UACjBd,EAAwB,cAC/BxQ,EAAS,KAAMwQ,IAVbxQ,EACE,IAAIpC,MACF,+DAA+D1D,KAAKW,MAEtE,QAWV,QAAQsR,EAAezP,GACrB,MAAM,IAAI,GACR,oEAQJ,YAAYyP,GACV,IAAI/F,EAAY+F,EAAMA,MAClBzP,EAAOyP,EAAMzP,KAE2B,IAA1C0J,EAAUvE,QAAQ,qBACe,IAAjCuE,EAAUvE,QAAQ,WAKpB3H,KAAKqX,qBAAqBnL,EAAW1J,GAHnC0D,MAAMoR,YAAYrF,GAMd,qBAAqBA,EAAezP,GAC1C,IAAKxC,KAAK2B,IAIR,YAHA,EAAOyM,MACL,gFAIJ,IAAK5L,EAAK+U,aAAe/U,EAAKgV,MAK5B,YAJA,EAAOzK,MACL,qGACEvK,GAIN,IAAIiV,EAAa,kBAAajV,EAAK+U,YACnC,GAAIE,EAAWnV,OAAStC,KAAKmX,KAAKO,UAAUC,eAI1C,YAHA,EAAO5K,MACL,oDAAoD/M,KAAKmX,KAAKO,UAAUC,wBAAwBF,EAAWnV,UAI/G,IAAIkV,EAAQ,kBAAahV,EAAKgV,OAC9B,GAAIA,EAAMlV,OAAStC,KAAKmX,KAAKO,UAAUE,YAIrC,YAHA,EAAO7K,MACL,+CAA+C/M,KAAKmX,KAAKO,UAAUE,qBAAqBJ,EAAMlV,UAKlG,IAAIuV,EAAQ7X,KAAKmX,KAAKO,UAAUI,KAAKL,EAAYD,EAAOxX,KAAK2B,KAC7D,GAAc,OAAVkW,EAuBF,OAtBA,EAAOzJ,MACL,wIAIFpO,KAAKqV,UAAUrV,KAAKyU,OAAOxD,WAAWuB,UAAW,CAACzF,EAAOuJ,KACnDvJ,EACF,EAAOA,MACL,iDAAiDuJ,4DAIrDuB,EAAQ7X,KAAKmX,KAAKO,UAAUI,KAAKL,EAAYD,EAAOxX,KAAK2B,KAC3C,OAAVkW,EAMJ7X,KAAK2O,KAAKsD,EAAOjS,KAAK+X,cAAcF,IALlC,EAAO9K,MACL,qEASR/M,KAAK2O,KAAKsD,EAAOjS,KAAK+X,cAAcF,IAKtC,cAAcA,GACZ,IAAIG,EAAM,kBAAWH,GACrB,IACE,OAAO9O,KAAK+I,MAAMkG,GAClB,SACA,OAAOA,ICpGE,MAAM,WAA0B,EAkB7C,YAAYrW,EAAayL,GACvBlH,QACAlG,KAAKuN,MAAQ,cACbvN,KAAKiR,WAAa,KAElBjR,KAAK2B,IAAMA,EACX3B,KAAKoN,QAAUA,EACfpN,KAAKwN,SAAWxN,KAAKoN,QAAQI,SAC7BxN,KAAKiY,SAAWjY,KAAKoN,QAAQrC,OAE7B/K,KAAKkY,eAAiBlY,KAAKmY,sBAC3BnY,KAAKoY,oBAAsBpY,KAAKqY,yBAC9BrY,KAAKkY,gBAEPlY,KAAKsY,mBAAqBtY,KAAKuY,wBAAwBvY,KAAKkY,gBAE5D,IAAIM,EAAU,GAAQC,aAEtBD,EAAQ5W,KAAK,SAAU,KACrB5B,KAAKwN,SAASmC,KAAK,CAAE+I,QAAS,WACX,eAAf1Y,KAAKuN,OAAyC,gBAAfvN,KAAKuN,OACtCvN,KAAK2Y,QAAQ,KAGjBH,EAAQ5W,KAAK,UAAW,KACtB5B,KAAKwN,SAASmC,KAAK,CAAE+I,QAAS,YAC1B1Y,KAAKiR,YACPjR,KAAK4Y,sBAIT5Y,KAAK6Y,iBAQP,UACM7Y,KAAKiR,YAAcjR,KAAK8Y,SAGvB9Y,KAAK+Y,SAASjJ,eAInB9P,KAAKgZ,YAAY,cACjBhZ,KAAKiZ,kBACLjZ,KAAKkZ,uBALHlZ,KAAKgZ,YAAY,WAYrB,KAAKxW,GACH,QAAIxC,KAAKiR,YACAjR,KAAKiR,WAAW3C,KAAK9L,GAahC,WAAW7B,EAAc6B,EAAW0P,GAClC,QAAIlS,KAAKiR,YACAjR,KAAKiR,WAAW0B,WAAWhS,EAAM6B,EAAM0P,GAOlD,aACElS,KAAKmZ,uBACLnZ,KAAKgZ,YAAY,gBAGnB,aACE,OAAOhZ,KAAKiY,SAGN,kBACN,IAAInS,EAAW,CAACiH,EAAOqM,KACjBrM,EACF/M,KAAK8Y,OAAS9Y,KAAK+Y,SAASM,QAAQ,EAAGvT,GAEd,UAArBsT,EAAU7G,QACZvS,KAAK2O,KAAK,QAAS,CACjBC,KAAM,iBACN7B,MAAOqM,EAAUrM,QAEnB/M,KAAKwN,SAAST,MAAM,CAAEuM,eAAgBF,EAAUrM,UAEhD/M,KAAKuZ,kBACLvZ,KAAKsY,mBAAmBc,EAAU7G,QAAQ6G,KAIhDpZ,KAAK8Y,OAAS9Y,KAAK+Y,SAASM,QAAQ,EAAGvT,GAGjC,kBACF9F,KAAK8Y,SACP9Y,KAAK8Y,OAAOU,QACZxZ,KAAK8Y,OAAS,MAIV,wBACN9Y,KAAKuZ,kBACLvZ,KAAKyZ,kBACLzZ,KAAK0Z,wBACD1Z,KAAKiR,aACUjR,KAAK2Z,oBACXtL,QAIP,iBACNrO,KAAK+Y,SAAW/Y,KAAKoN,QAAQwM,YAAY,CACvCjY,IAAK3B,KAAK2B,IACV6L,SAAUxN,KAAKwN,SACfzC,OAAQ/K,KAAKiY,WAIT,QAAQpS,GACd7F,KAAKwN,SAASmC,KAAK,CAAE4C,OAAQ,QAAS1M,MAAOA,IACzCA,EAAQ,GACV7F,KAAK2O,KAAK,gBAAiB4C,KAAKsI,MAAMhU,EAAQ,MAEhD7F,KAAK8Z,WAAa,IAAI,EAAMjU,GAAS,EAAG,KACtC7F,KAAKmZ,uBACLnZ,KAAKqZ,YAID,kBACFrZ,KAAK8Z,aACP9Z,KAAK8Z,WAAWC,gBAChB/Z,KAAK8Z,WAAa,MAId,sBACN9Z,KAAKga,iBAAmB,IAAI,EAAMha,KAAKoN,QAAQ/C,mBAAoB,KACjErK,KAAKgZ,YAAY,iBAIb,wBACFhZ,KAAKga,kBACPha,KAAKga,iBAAiBD,gBAIlB,oBACN/Z,KAAKia,oBACLja,KAAKiR,WAAW1C,OAEhBvO,KAAKka,cAAgB,IAAI,EAAMla,KAAKoN,QAAQhD,YAAa,KACvDpK,KAAKwN,SAAST,MAAM,CAAEoN,eAAgBna,KAAKoN,QAAQhD,cACnDpK,KAAK2Y,QAAQ,KAIT,qBACN3Y,KAAKia,oBAEDja,KAAKiR,aAAejR,KAAKiR,WAAWtD,0BACtC3N,KAAKka,cAAgB,IAAI,EAAMla,KAAKmK,gBAAiB,KACnDnK,KAAK4Y,uBAKH,oBACF5Y,KAAKka,eACPla,KAAKka,cAAcH,gBAIf,yBACN7B,GAEA,OAAO,EAAwC,GAAIA,EAAgB,CACjEzL,QAAUA,IAERzM,KAAKoa,qBACLpa,KAAK2O,KAAK,UAAWlC,IAEvB8B,KAAM,KACJvO,KAAK2S,WAAW,cAAe,KAEjCE,SAAU,KACR7S,KAAKoa,sBAEPrN,MAAQA,IAEN/M,KAAK2O,KAAK,QAAS5B,IAErB+F,OAAQ,KACN9S,KAAK2Z,oBACD3Z,KAAKqa,eACPra,KAAK2Y,QAAQ,QAMb,wBACNT,GAEA,OAAO,EAAuC,GAAIA,EAAgB,CAChEoC,UAAYlB,IACVpZ,KAAKmK,gBAAkBoH,KAAKhN,IAC1BvE,KAAKoN,QAAQjD,gBACbiP,EAAUjP,gBACViP,EAAUnI,WAAW9G,iBAAmBoQ,KAE1Cva,KAAK0Z,wBACL1Z,KAAKwa,cAAcpB,EAAUnI,YAC7BjR,KAAKwS,UAAYxS,KAAKiR,WAAWxD,GACjCzN,KAAKgZ,YAAY,YAAa,CAAExG,UAAWxS,KAAKwS,eAK9C,sBACN,IAAIiI,EAAoB3U,GACdlC,IACFA,EAAOmJ,OACT/M,KAAK2O,KAAK,QAAS,CAAEC,KAAM,iBAAkB7B,MAAOnJ,EAAOmJ,QAE7DjH,EAASlC,IAIb,MAAO,CACL8W,SAAUD,EAAiB,KACzBza,KAAKiY,UAAW,EAChBjY,KAAK6Y,iBACL7Y,KAAK2Y,QAAQ,KAEfgC,QAASF,EAAiB,KACxBza,KAAKkX,eAEP0D,QAASH,EAAiB,KACxBza,KAAK2Y,QAAQ,OAEfkC,MAAOJ,EAAiB,KACtBza,KAAK2Y,QAAQ,MAKX,cAAc1H,GAEpB,IAAK,IAAIgB,KADTjS,KAAKiR,WAAaA,EACAjR,KAAKoY,oBACrBpY,KAAKiR,WAAWrP,KAAKqQ,EAAOjS,KAAKoY,oBAAoBnG,IAEvDjS,KAAKoa,qBAGC,oBACN,GAAKpa,KAAKiR,WAAV,CAIA,IAAK,IAAIgB,KADTjS,KAAKia,oBACaja,KAAKoY,oBACrBpY,KAAKiR,WAAW5E,OAAO4F,EAAOjS,KAAKoY,oBAAoBnG,IAEzD,IAAIhB,EAAajR,KAAKiR,WAEtB,OADAjR,KAAKiR,WAAa,KACXA,GAGD,YAAY6J,EAAkBtY,GACpC,IAAIuY,EAAgB/a,KAAKuN,MAEzB,GADAvN,KAAKuN,MAAQuN,EACTC,IAAkBD,EAAU,CAC9B,IAAIE,EAAsBF,EACE,cAAxBE,IACFA,GAAuB,uBAAyBxY,EAAKgQ,WAEvD,EAAOpE,MACL,gBACA2M,EAAgB,OAASC,GAE3Bhb,KAAKwN,SAASmC,KAAK,CAAEpC,MAAOuN,EAAUnS,OAAQnG,IAC9CxC,KAAK2O,KAAK,eAAgB,CAAEsM,SAAUF,EAAeG,QAASJ,IAC9D9a,KAAK2O,KAAKmM,EAAUtY,IAIhB,cACN,MAAsB,eAAfxC,KAAKuN,OAAyC,cAAfvN,KAAKuN,OCtWhC,MAAM,GAGnB,cACEvN,KAAKmb,SAAW,GASlB,IAAIxa,EAAc8T,GAIhB,OAHKzU,KAAKmb,SAASxa,KACjBX,KAAKmb,SAASxa,GAwCpB,SAAuBA,EAAc8T,GACnC,GAA2C,IAAvC9T,EAAKgH,QAAQ,sBAA6B,CAC5C,GAAI8M,EAAOe,OAAO2B,KAChB,OAAO,GAAQiE,uBAAuBza,EAAM8T,EAAQA,EAAOe,OAAO2B,MAEpE,IAAIkE,EACF,0FACEtG,EAAS,GAAwB,2BACrC,MAAM,IAAI,GAA0B,GAAGsG,MAAWtG,KAC7C,GAAiC,IAA7BpU,EAAKgH,QAAQ,YACtB,OAAO,GAAQ2T,qBAAqB3a,EAAM8T,GACrC,GAAkC,IAA9B9T,EAAKgH,QAAQ,aACtB,OAAO,GAAQ4T,sBAAsB5a,EAAM8T,GACtC,GAA0B,IAAtB9T,EAAKgH,QAAQ,KACtB,MAAM,IAAI,GACR,sCAAwChH,EAAO,MAGjD,OAAO,GAAQ6a,cAAc7a,EAAM8T,GA1DX+G,CAAc7a,EAAM8T,IAErCzU,KAAKmb,SAASxa,GAOvB,MACE,OtBiEG,SAAgBmB,GACrB,IAAI2Z,EAAS,GAIb,OAHA7T,EAAY9F,GAAQ,SAAUT,GAC5Boa,EAAO9W,KAAKtD,MAEPoa,EsBtEE,CAAmBzb,KAAKmb,UAQjC,KAAKxa,GACH,OAAOX,KAAKmb,SAASxa,GAOvB,OAAOA,GACL,IAAIuR,EAAUlS,KAAKmb,SAASxa,GAE5B,cADOX,KAAKmb,SAASxa,GACduR,EAIT,aACE,EAAwBlS,KAAKmb,UAAU,SAAUjJ,GAC/CA,EAAQgF,iBClCd,IAoDe,GApDD,CACZwE,eAAc,IACL,IAAI,GAGbC,wBAAuB,CACrBha,EACAyL,IAEO,IAAI,GAAkBzL,EAAKyL,GAGpCoO,cAAa,CAAC7a,EAAc8T,IACnB,IAAI,GAAQ9T,EAAM8T,GAG3B6G,qBAAoB,CAAC3a,EAAc8T,IAC1B,IAAI,GAAe9T,EAAM8T,GAGlC8G,sBAAqB,CAAC5a,EAAc8T,IAC3B,IAAI,GAAgB9T,EAAM8T,GAGnC2G,uBAAsB,CACpBza,EACA8T,EACA0C,IAEO,IAAI,GAAiBxW,EAAM8T,EAAQ0C,GAG5CyE,qBAAoB,CAACpO,EAAoBJ,IAChC,IAAI,EAAeI,EAAUJ,GAGtCyO,gBAAe,CACbrR,EACA1E,IAEO,IAAI,EAAU0E,EAAW1E,GAGlCgW,qCAAoC,CAClCjL,EACArG,EACA4C,IAEO,IAAI,EAA+ByD,EAASrG,EAAW4C,ICxDnD,MAAM,GAInB,YAAYA,GACVpN,KAAKoN,QAAUA,GAAW,GAC1BpN,KAAK+b,UAAY/b,KAAKoN,QAAQ4O,OAASzB,IAQzC,aAAa/P,GACX,OAAO,GAAQsR,qCAAqC9b,KAAMwK,EAAW,CACnEsG,aAAc9Q,KAAKoN,QAAQ0D,aAC3BC,aAAc/Q,KAAKoN,QAAQ2D,eAQ/B,UACE,OAAO/Q,KAAK+b,UAAY,EAI1B,cACE/b,KAAK+b,WAAa,GCjCP,MAAM,GAOnB,YAAYE,EAAwB7O,GAClCpN,KAAKic,WAAaA,EAClBjc,KAAKkc,KAAO7T,QAAQ+E,EAAQ8O,MAC5Blc,KAAKmc,SAAW9T,QAAQ+E,EAAQ+O,UAChCnc,KAAKoc,QAAUhP,EAAQgP,QACvBpc,KAAKqc,aAAejP,EAAQiP,aAG9B,cACE,OAAO,EAAgBrc,KAAKic,WAAY,EAAKpT,OAAO,gBAGtD,QAAQyT,EAAqBxW,GAC3B,IAAImW,EAAajc,KAAKic,WAClBf,EAAU,EACVkB,EAAUpc,KAAKoc,QACftD,EAAS,KAETyD,EAAkB,CAACxP,EAAOqM,KACxBA,EACFtT,EAAS,KAAMsT,IAEf8B,GAAoB,EAChBlb,KAAKkc,OACPhB,GAAoBe,EAAW3Z,QAG7B4Y,EAAUe,EAAW3Z,QACnB8Z,IACFA,GAAoB,EAChBpc,KAAKqc,eACPD,EAAU7K,KAAKhN,IAAI6X,EAASpc,KAAKqc,gBAGrCvD,EAAS9Y,KAAKwc,YACZP,EAAWf,GACXoB,EACA,CAAEF,UAASD,SAAUnc,KAAKmc,UAC1BI,IAGFzW,GAAS,KAYf,OAPAgT,EAAS9Y,KAAKwc,YACZP,EAAWf,GACXoB,EACA,CAAEF,QAASA,EAASD,SAAUnc,KAAKmc,UACnCI,GAGK,CACL/C,MAAO,WACLV,EAAOU,SAETiD,iBAAkB,SAAUva,GAC1Boa,EAAcpa,EACV4W,GACFA,EAAO2D,iBAAiBva,KAMxB,YACN6W,EACAuD,EACAlP,EACAtH,GAEA,IAAIC,EAAQ,KACR+S,EAAS,KAoBb,OAlBI1L,EAAQgP,QAAU,IACpBrW,EAAQ,IAAI,EAAMqH,EAAQgP,SAAS,WACjCtD,EAAOU,QACP1T,GAAS,OAIbgT,EAASC,EAASM,QAAQiD,GAAa,SAAUvP,EAAOqM,GAClDrM,GAAShH,GAASA,EAAM2W,cAAgBtP,EAAQ+O,WAIhDpW,GACFA,EAAMgU,gBAERjU,EAASiH,EAAOqM,OAGX,CACLI,MAAO,WACDzT,GACFA,EAAMgU,gBAERjB,EAAOU,SAETiD,iBAAkB,SAAUva,GAC1B4W,EAAO2D,iBAAiBva,MCpHjB,MAAM,GAGnB,YAAY+Z,GACVjc,KAAKic,WAAaA,EAGpB,cACE,OAAO,EAAgBjc,KAAKic,WAAY,EAAKpT,OAAO,gBAGtD,QAAQyT,EAAqBxW,GAC3B,OA6BJ,SACEmW,EACAK,EACAK,GAEA,IAAIC,EAAU,EAAgBX,GAAY,SAAUlD,EAAU3Y,EAAG2H,EAAG8U,GAClE,OAAO9D,EAASM,QAAQiD,EAAaK,EAAgBvc,EAAGyc,OAE1D,MAAO,CACLrD,MAAO,WACL,EAAkBoD,EAASE,KAE7BL,iBAAkB,SAAUva,GAC1B,EAAkB0a,GAAS,SAAU9D,GACnCA,EAAO2D,iBAAiBva,QA3CrBmX,CAAQrZ,KAAKic,WAAYK,GAAa,SAAUlc,EAAGwc,GACxD,OAAO,SAAU7P,EAAOqM,GACtBwD,EAAQxc,GAAG2M,MAAQA,EACfA,EA8CZ,SAA0B6P,GACxB,O1BsLK,SAAapV,EAAcW,GAChC,IAAK,IAAI/H,EAAI,EAAGA,EAAIoH,EAAMlF,OAAQlC,IAChC,IAAK+H,EAAKX,EAAMpH,GAAIA,EAAGoH,GACrB,OAAO,EAGX,OAAO,E0B5LA,CAAgBoV,GAAS,SAAU9D,GACxC,OAAOzQ,QAAQyQ,EAAO/L,UA/CZgQ,CAAiBH,IACnB9W,GAAS,IAIb,EAAkB8W,GAAS,SAAU9D,GACnCA,EAAO2D,iBAAiBrD,EAAU5O,UAAU2C,aAE9CrH,EAAS,KAAMsT,SA2CvB,SAAS0D,GAAYhE,GACdA,EAAO/L,OAAU+L,EAAOkE,UAC3BlE,EAAOU,QACPV,EAAOkE,SAAU,GC1DN,MAAM,GAOnB,YACEjE,EACAkE,EACA7P,GAEApN,KAAK+Y,SAAWA,EAChB/Y,KAAKid,WAAaA,EAClBjd,KAAKkd,IAAM9P,EAAQ8P,KAAO,KAC1Bld,KAAKiY,SAAW7K,EAAQrC,OACxB/K,KAAKwN,SAAWJ,EAAQI,SAG1B,cACE,OAAOxN,KAAK+Y,SAASjJ,cAGvB,QAAQwM,EAAqBxW,GAC3B,IAAImS,EAAWjY,KAAKiY,SAChBtI,EAkER,SAA6BsI,GAC3B,IAAIkF,EAAU,GAAQC,kBACtB,GAAID,EACF,IACE,IAAIE,EAAkBF,EAAQG,GAAqBrF,IACnD,GAAIoF,EACF,OAAOtU,KAAK+I,MAAMuL,GAEpB,MAAOrU,GACPuU,GAAoBtF,GAGxB,OAAO,KA9EMuF,CAAoBvF,GAC3BwF,EAAiB9N,GAAQA,EAAK8N,eAAiB9N,EAAK8N,eAAiB,EAErExB,EAAa,CAACjc,KAAK+Y,UACvB,GAAIpJ,GAAQA,EAAK+N,UAAY1d,KAAKkd,KAAO,EAAK7W,MAAO,CACnD,IAAImE,EAAYxK,KAAKid,WAAWtN,EAAKnF,WACjCA,IACE,CAAC,KAAM,OAAOmT,SAAShO,EAAKnF,YAAciT,EAAiB,GAC7Dzd,KAAKwN,SAASmC,KAAK,CACjBiO,QAAQ,EACRpT,UAAWmF,EAAKnF,UAChBqT,QAASlO,EAAKkO,UAEhB5B,EAAWtX,KACT,IAAI,GAAmB,CAAC6F,GAAY,CAClC4R,QAAwB,EAAfzM,EAAKkO,QAAc,IAC5B1B,UAAU,MAIdsB,KAKN,IAAIK,EAAiB,EAAKzX,MACtByS,EAASmD,EACV8B,MACA1E,QAAQiD,GAAa,SAAS0B,EAAGjR,EAAOqM,GACnCrM,GACFwQ,GAAoBtF,GAChBgE,EAAW3Z,OAAS,GACtBwb,EAAiB,EAAKzX,MACtByS,EAASmD,EAAW8B,MAAM1E,QAAQiD,EAAa0B,IAE/ClY,EAASiH,MA8CrB,SACEkL,EACAzN,EACAqT,EACAJ,GAEA,IAAIN,EAAU,GAAQC,kBACtB,GAAID,EACF,IACEA,EAAQG,GAAqBrF,IAAa,EAA8B,CACtEyF,UAAW,EAAKrX,MAChBmE,UAAWA,EACXqT,QAASA,EACTJ,eAAgBA,IAElB,MAAOzU,KA1DHiV,CACEhG,EACAmB,EAAU5O,UAAU7J,KACpB,EAAK0F,MAAQyX,EACbL,GAEF3X,EAAS,KAAMsT,OAIrB,MAAO,CACLI,MAAO,WACLV,EAAOU,SAETiD,iBAAkB,SAAUva,GAC1Boa,EAAcpa,EACV4W,GACFA,EAAO2D,iBAAiBva,MAOlC,SAASob,GAAqBrF,GAC5B,MAAO,mBAAqBA,EAAW,MAAQ,UAuCjD,SAASsF,GAAoBtF,GAC3B,IAAIkF,EAAU,GAAQC,kBACtB,GAAID,EACF,WACSA,EAAQG,GAAqBrF,IACpC,MAAOjP,KC5IE,MAAM,GAInB,YAAY+P,GAAsBlT,MAAOqY,IACvCle,KAAK+Y,SAAWA,EAChB/Y,KAAKoN,QAAU,CAAEvH,MAAOqY,GAG1B,cACE,OAAOle,KAAK+Y,SAASjJ,cAGvB,QAAQwM,EAAqBxW,GAC3B,IACIgT,EADAC,EAAW/Y,KAAK+Y,SAEhBhT,EAAQ,IAAI,EAAM/F,KAAKoN,QAAQvH,OAAO,WACxCiT,EAASC,EAASM,QAAQiD,EAAaxW,MAGzC,MAAO,CACL0T,MAAO,WACLzT,EAAMgU,gBACFjB,GACFA,EAAOU,SAGXiD,iBAAkB,SAAUva,GAC1Boa,EAAcpa,EACV4W,GACFA,EAAO2D,iBAAiBva,MCjCnB,MAAMic,GAKnB,YACEhW,EACAiW,EACAC,GAEAre,KAAKmI,KAAOA,EACZnI,KAAKoe,WAAaA,EAClBpe,KAAKqe,YAAcA,EAGrB,cAEE,OADare,KAAKmI,OAASnI,KAAKoe,WAAape,KAAKqe,aACpCvO,cAGhB,QAAQwM,EAAqBxW,GAE3B,OADa9F,KAAKmI,OAASnI,KAAKoe,WAAape,KAAKqe,aACpChF,QAAQiD,EAAaxW,ICxBxB,MAAMwY,GAGnB,YAAYvF,GACV/Y,KAAK+Y,SAAWA,EAGlB,cACE,OAAO/Y,KAAK+Y,SAASjJ,cAGvB,QAAQwM,EAAqBxW,GAC3B,IAAIgT,EAAS9Y,KAAK+Y,SAASM,QACzBiD,GACA,SAAUvP,EAAOqM,GACXA,GACFN,EAAOU,QAET1T,EAASiH,EAAOqM,MAGpB,OAAON,GCdX,SAASyF,GAAqBxF,GAC5B,OAAO,WACL,OAAOA,EAASjJ,eAIpB,IAsIe,GAtIU,SACvB0F,EACAgJ,EACAC,GAEA,IAAIC,EAAiD,GAErD,SAASC,EACPhe,EACAiO,EACAzB,EACAC,EACAyD,GAEA,IAAIrG,EAAYiU,EACdjJ,EACA7U,EACAiO,EACAzB,EACAC,EACAyD,GAKF,OAFA6N,EAAkB/d,GAAQ6J,EAEnBA,EAGT,IA+EIoU,EA/EAC,EAA8B/d,OAAOwU,OAAO,GAAIkJ,EAAa,CAC/DvT,WAAYuK,EAAOsJ,OAAS,IAAMtJ,EAAO/L,OACzCuB,QAASwK,EAAOsJ,OAAS,IAAMtJ,EAAO9L,QACtCK,SAAUyL,EAAO7L,SAEfoV,EAA+B,EAAmB,GAAIF,EAAY,CACpE9T,QAAQ,IAENiU,EAAgCle,OAAOwU,OAAO,GAAIkJ,EAAa,CACjEvT,WAAYuK,EAAO5L,SAAW,IAAM4L,EAAO3L,SAC3CmB,QAASwK,EAAO5L,SAAW,IAAM4L,EAAO1L,UACxCC,SAAUyL,EAAOzL,WAEfkV,EAAW,CACb/C,MAAM,EACNE,QAAS,KACTC,aAAc,KAGZ6C,EAAa,IAAI,GAAiB,CACpCpO,aAAc,IACdC,aAAcyE,EAAOrL,kBAEnBgV,EAAoB,IAAI,GAAiB,CAC3CnD,MAAO,EACPlL,aAAc,IACdC,aAAcyE,EAAOrL,kBAGnBiV,EAAeT,EACjB,KACA,KACA,EACAE,EACAK,GAEEG,EAAgBV,EAClB,MACA,KACA,EACAI,EACAG,GAEEI,EAA0BX,EAC5B,gBACA,gBACA,EACAK,EACAG,GAEEI,EAAwBZ,EAC1B,cACA,cACA,EACAK,GAGEQ,EAAU,IAAI,GAAmB,CAACJ,GAAeH,GACjDQ,EAAW,IAAI,GAAmB,CAACJ,GAAgBJ,GACnDS,EAAiB,IAAI,GACvB,CAACJ,GACDL,GAEEU,EAAe,IAAI,GAAmB,CAACJ,GAAwBN,GAE/DW,EAAY,IAAI,GAClB,CACE,IAAIzB,GACFI,GAAqBmB,GACrB,IAAI,GAA0B,CAC5BA,EACA,IAAI,GAAgBC,EAAc,CAAE9Z,MAAO,QAE7C8Z,IAGJV,GAiBF,OAZEL,EADEJ,EAAYzT,OACD,IAAI,GAA0B,CACzCyU,EACA,IAAI,GAAgBI,EAAW,CAAE/Z,MAAO,QAG7B,IAAI,GAA0B,CACzC2Z,EACA,IAAI,GAAgBC,EAAU,CAAE5Z,MAAO,MACvC,IAAI,GAAgB+Z,EAAW,CAAE/Z,MAAO,QAIrC,IAAI,GACT,IAAIyY,GACF,IAAIH,GAAWI,GAAqBa,GAAeR,EAAYgB,IAEjElB,EACA,CACExB,IAAK,KACL1P,SAAUgR,EAAYhR,SACtBzC,OAAQyT,EAAYzT,UC9IX,MAAM,WAAoB,EAQvC,YAAYmC,EAAqBrE,EAAgBiF,GAC/C5H,QACAlG,KAAKkN,MAAQA,EACblN,KAAK6I,OAASA,EACd7I,KAAK8N,IAAMA,EAGb,MAAM+R,GACJ7f,KAAK8f,SAAW,EAChB9f,KAAK+f,IAAM/f,KAAKkN,MAAM8S,WAAWhgB,MAEjCA,KAAKigB,SAAW,KACdjgB,KAAKqO,SAEP,GAAQ6R,kBAAkBlgB,KAAKigB,UAE/BjgB,KAAK+f,IAAIjI,KAAK9X,KAAK6I,OAAQ7I,KAAK8N,KAAK,GAEjC9N,KAAK+f,IAAII,kBACXngB,KAAK+f,IAAII,iBAAiB,eAAgB,oBAE5CngB,KAAK+f,IAAIzR,KAAKuR,GAGhB,QACM7f,KAAKigB,WACP,GAAQG,qBAAqBpgB,KAAKigB,UAClCjgB,KAAKigB,SAAW,MAEdjgB,KAAK+f,MACP/f,KAAKkN,MAAMmT,aAAargB,KAAK+f,KAC7B/f,KAAK+f,IAAM,MAIf,QAAQhM,EAAgBvR,GACtB,OAAa,CACX,IAAI8d,EAAQtgB,KAAKugB,cAAc/d,GAC/B,IAAI8d,EAGF,MAFAtgB,KAAK2O,KAAK,QAAS,CAAEoF,OAAQA,EAAQvR,KAAM8d,IAK3CtgB,KAAKwgB,gBAAgBhe,IACvBxC,KAAK2O,KAAK,mBAIN,cAAc8R,GACpB,IAAIC,EAAaD,EAAO7Z,MAAM5G,KAAK8f,UAC/Ba,EAAoBD,EAAW/Y,QAAQ,MAE3C,OAA2B,IAAvBgZ,GACF3gB,KAAK8f,UAAYa,EAAoB,EAC9BD,EAAW9Z,MAAM,EAAG+Z,IAGpB,KAIH,gBAAgBF,GACtB,OAAOzgB,KAAK8f,WAAaW,EAAOne,QAAUme,EAAOne,OAzE3B,QCL1B,IAAKse,IAAL,SAAKA,GACH,+BACA,mBACA,uBAHF,CAAKA,QAAK,KAMK,UCGXC,GAAgB,EA0LpB,SAASC,GAAahT,GACpB,IAAIiT,GAAkC,IAAtBjT,EAAInG,QAAQ,KAAc,IAAM,IAChD,OAAOmG,EAAMiT,EAAY,OAAQ,IAAIza,KAAS,MAAQua,KAQxD,SAASG,GAAaxP,GACpB,OAAO,GAAQyP,UAAUzP,GAaZ,OAhNf,MAaE,YAAYtE,EAAoBY,GAC9B9N,KAAKkN,MAAQA,EACblN,KAAKkhB,QAAUF,GAAa,KAAQ,IAuLxC,SAAsB1e,GAGpB,IAFA,IAAIsB,EAAS,GAEJxD,EAAI,EAAGA,EAAIkC,EAAQlC,IAC1BwD,EAAOe,KAAKqc,GAAa,IAAIvY,SAAS,KAGxC,OAAO7E,EAAOgB,KAAK,IA9LyBuc,CAAa,GACvDnhB,KAAKohB,SA4JT,SAAqBtT,GACnB,IAAIuT,EAAQ,qBAAqBC,KAAKxT,GACtC,MAAO,CACLyT,KAAMF,EAAM,GACZlW,YAAakW,EAAM,IAhKHG,CAAY1T,GAC5B9N,KAAKyhB,WAAa,GAAMC,WACxB1hB,KAAK2hB,aAGP,KAAK9B,GACH,OAAO7f,KAAK4hB,QAAQ7Y,KAAK1B,UAAU,CAACwY,KAGtC,OACE7f,KAAKkN,MAAM2U,cAAc7hB,MAG3B,MAAM+O,EAAWC,GACfhP,KAAKsP,QAAQP,EAAMC,GAAQ,GAI7B,QAAQ6Q,GACN,GAAI7f,KAAKyhB,aAAe,GAAMK,KAW5B,OAAO,EAVP,IAKE,OAJA,GAAQC,oBACN,OACAjB,IA6IUhT,EA7Ic9N,KAAKohB,SA6IDF,EA7IWlhB,KAAKkhB,QA8I7CpT,EAAIyT,KAAO,IAAML,EAAU,eA7I1Bc,MAAMnC,IACD,EACP,MAAO7W,GACP,OAAO,EAyIf,IAAoB8E,EAAkBoT,EAjIpC,YACElhB,KAAKiiB,cACLjiB,KAAK2hB,aAIP,QAAQ5S,EAAMC,EAAQC,GACpBjP,KAAKiiB,cACLjiB,KAAKyhB,WAAa,GAAMS,OACpBliB,KAAKqP,SACPrP,KAAKqP,QAAQ,CACXN,KAAMA,EACNC,OAAQA,EACRC,SAAUA,IAKR,QAAQqR,GAQd,IAAIT,EAPJ,GAAqB,MAAjBS,EAAMvM,OASV,OANI/T,KAAKyhB,aAAe,GAAMK,MAC5B9hB,KAAK0P,aAII4Q,EAAM9d,KAAKoE,MAAM,EAAG,IAE7B,IAAK,IACHiZ,EAAU9W,KAAK+I,MAAMwO,EAAM9d,KAAKoE,MAAM,IAAM,MAC5C5G,KAAKmP,OAAO0Q,GACZ,MACF,IAAK,IACHA,EAAU9W,KAAK+I,MAAMwO,EAAM9d,KAAKoE,MAAM,IAAM,MAC5C,IAAK,IAAIxG,EAAI,EAAGA,EAAIyf,EAAQvd,OAAQlC,IAClCJ,KAAKmiB,QAAQtC,EAAQzf,IAEvB,MACF,IAAK,IACHyf,EAAU9W,KAAK+I,MAAMwO,EAAM9d,KAAKoE,MAAM,IAAM,QAC5C5G,KAAKmiB,QAAQtC,GACb,MACF,IAAK,IACH7f,KAAKkN,MAAMkV,YAAYpiB,MACvB,MACF,IAAK,IACH6f,EAAU9W,KAAK+I,MAAMwO,EAAM9d,KAAKoE,MAAM,IAAM,MAC5C5G,KAAKsP,QAAQuQ,EAAQ,GAAIA,EAAQ,IAAI,IAKnC,OAAOzS,GAqFjB,IAAqBU,EAAauU,EAC5BC,EArFEtiB,KAAKyhB,aAAe,GAAMC,YACxBtU,GAAWA,EAAQiV,WACrBriB,KAAKohB,SAASG,MAkFDzT,EAlFoB9N,KAAKohB,SAASG,KAkFrBc,EAlF2BjV,EAAQiV,UAmF/DC,EAAW,oCAAoChB,KAAKxT,IACxC,GAAKuU,EAAWC,EAAS,KAlFrCtiB,KAAKyhB,WAAa,GAAMK,KAEpB9hB,KAAK0O,QACP1O,KAAK0O,UAGP1O,KAAKsP,QAAQ,KAAM,uBAAuB,GAItC,QAAQ2C,GACVjS,KAAKyhB,aAAe,GAAMK,MAAQ9hB,KAAKuP,WACzCvP,KAAKuP,UAAU,CAAE/M,KAAMyP,IAInB,aACFjS,KAAKyP,YACPzP,KAAKyP,aAID,QAAQ1C,GACV/M,KAAKoP,SACPpP,KAAKoP,QAAQrC,GAIT,aACN/M,KAAKuiB,OAAS,GAAQR,oBACpB,OACAjB,GAAa9gB,KAAKkN,MAAMsV,cAAcxiB,KAAKohB,SAAUphB,KAAKkhB,WAG5DlhB,KAAKuiB,OAAO3gB,KAAK,QAAU0e,IACzBtgB,KAAKyiB,QAAQnC,KAEftgB,KAAKuiB,OAAO3gB,KAAK,WAAamS,IAC5B/T,KAAKkN,MAAMwV,WAAW1iB,KAAM+T,KAE9B/T,KAAKuiB,OAAO3gB,KAAK,kBAAmB,KAClC5B,KAAK2iB,cAGP,IACE3iB,KAAKuiB,OAAOP,QACZ,MAAOjV,GACP,EAAKvG,MAAM,KACTxG,KAAKiO,QAAQlB,GACb/M,KAAKsP,QAAQ,KAAM,6BAA6B,MAK9C,cACFtP,KAAKuiB,SACPviB,KAAKuiB,OAAOK,aACZ5iB,KAAKuiB,OAAOlU,QACZrO,KAAKuiB,OAAS,QChKL,GAfU,CACvBC,cAAe,SAAU1U,EAAKoT,GAC5B,OAAOpT,EAAIyT,KAAO,IAAML,EAAU,iBAAmBpT,EAAI3C,aAE3DiX,YAAa,SAAUvU,GACrBA,EAAO+T,QAAQ,OAEjBC,cAAe,SAAUhU,GACvBA,EAAO+T,QAAQ,OAEjBc,WAAY,SAAU7U,EAAQkG,GAC5BlG,EAAOyB,QAAQ,KAAM,2BAA6ByE,EAAS,KAAK,KCSrD,GAnBU,CACvByO,cAAe,SAAU1U,EAAkBoT,GACzC,OAAOpT,EAAIyT,KAAO,IAAML,EAAU,OAASpT,EAAI3C,aAEjDiX,YAAa,aAGbP,cAAe,SAAUhU,GACvBA,EAAO+T,QAAQ,OAEjBc,WAAY,SAAU7U,EAAQkG,GACb,MAAXA,EACFlG,EAAO8U,YAEP9U,EAAOyB,QAAQ,KAAM,2BAA6ByE,EAAS,KAAK,KCgBvD,GA7BW,CACxBiM,WAAY,SAAUnS,GACpB,IACIkS,EAAM,IADQ,GAAQ8C,aAmB1B,OAjBA9C,EAAI+C,mBAAqB/C,EAAIgD,WAAa,WACxC,OAAQhD,EAAI0B,YACV,KAAK,EACC1B,EAAIiD,cAAgBjD,EAAIiD,aAAa1gB,OAAS,GAChDuL,EAAO4U,QAAQ1C,EAAIhM,OAAQgM,EAAIiD,cAEjC,MACF,KAAK,EAECjD,EAAIiD,cAAgBjD,EAAIiD,aAAa1gB,OAAS,GAChDuL,EAAO4U,QAAQ1C,EAAIhM,OAAQgM,EAAIiD,cAEjCnV,EAAOc,KAAK,WAAYoR,EAAIhM,QAC5BlG,EAAOQ,UAIN0R,GAETM,aAAc,SAAUN,GACtBA,EAAI+C,mBAAqB,KACzB/C,EAAIvG,UC+BO,GApDO,CACpByJ,mBAAA,GACAC,WAA6B,EAC7B5V,+BCRa,WACFtN,KAENwN,SAASmC,KAFH3P,KAGJ6O,qBAAqB,CACxBrE,UAJOxK,KAISW,MAJTX,KAIsBoN,QAAQrC,OAAS,IAAM,OAJ7C/K,KAQFkN,MAAM8C,gBARJhQ,KASJkO,YAAY,eATRlO,KAWJsP,WDHPe,YEJsB,CACtB,sBAAsBvC,GACpB,OAAO9N,KAAKmjB,aAAa,GAAgBrV,IAG3C,oBAAoBA,GAClB,OAAO9N,KAAKmjB,aAAa,GAAcrV,IAGzCqV,aAAY,CAACjW,EAAoBY,IACxB,IAAI,GAAWZ,EAAOY,GAG/B,UAAUjF,EAAgBiF,GACxB,OAAO9N,KAAKojB,cAAc,GAAUva,EAAQiF,IAG9CsV,cAAa,CAAClW,EAAqBrE,EAAgBiF,IAC1C,IAAI,GAAYZ,EAAOrE,EAAQiF,IFZxC,MAAMuV,GACJA,EAAYC,SAGd,oBAIAC,kBAAiB,IACR,EACL,EAAyB,CAAEnY,GAAI,EAAWA,KAAM,SAAU9J,GACxD,OAAOA,EAAEwO,YAAY,QAK3B0T,YAAW,IACF,QAGT9S,eAAc,KACL,EAGT,oBAAoB7H,EAAgBiF,GAClC,GAAI9N,KAAK0Q,iBACP,OAAO1Q,KAAKqQ,YAAYoT,UAAU5a,EAAQiF,GAE1C,KAAM,gDAIV,YAEE,OAAO,IADW9N,KAAK6iB,cAIzB,gBAAgB/U,GAEd,OAAO,IADW9N,KAAKiQ,kBAChB,CAAgBnC,IAGzB,kBAAkBkF,KAClB,qBAAqBA,MGjDhB,IAAI,GAAU,IANd,cAAsB,EAC3B,WACE,OAAO,IC+DI,GA3DgB,SAC7BhL,EACA0b,EACAC,EACAC,EACA9d,GAEA,IAAI+d,EAAU,IAAIC,QAGlB,IAAK,IAAIC,KAFTF,EAAQle,IAAI,eAAgB,qCAELge,EAAYE,QACjCA,EAAQle,IAAIoe,EAAYJ,EAAYE,QAAQE,IAG9C,GAAmC,MAA/BJ,EAAYK,gBAAyB,CACvC,MAAMC,EAAiBN,EAAYK,kBACnC,IAAK,IAAID,KAAcE,EACrBJ,EAAQle,IAAIoe,EAAYE,EAAeF,IAI3C,IAAIG,EAAOR,EACPS,EAAU,IAAIC,QAAQT,EAAYpZ,SAAU,CAC9CsZ,UACAK,OACAG,YAAa,cACbxb,OAAQ,SAGV,OAAOyb,MAAMH,GACVI,KAAMC,IACL,IAAI,OAAEzQ,GAAWyQ,EACjB,GAAe,MAAXzQ,EAGF,OAAOyQ,EAASC,OAElB,MAAM,IAAI3Q,GACRC,EACA,iBAAiB6P,EAAgBnb,oDAAoDsL,OAGxFwQ,KAAM/hB,IACL,IAAIkiB,EACJ,IACEA,EAAa3b,KAAK+I,MAAMtP,GACxB,MAAOwG,GACP,MAAM,IAAI8K,GACR,IACA,sBAAsB8P,EAAgBnb,uEAAuEjG,KAGjHsD,EAAS,KAAM4e,KAEhBC,MAAOC,IACN9e,EAAS8e,EAAK,SC1BL,GALK,CAClBjkB,KAAM,MACNyS,SA5Ba,SAAUyR,EAAwB9Z,GAC/C,OAAO,SAAUvI,EAAWsD,GAC1B,IACIgI,EADS,QAAU/C,EAAS,IAAM,IAAM,OAEhC8Z,EAAOC,MAAQD,EAAOzX,QAAQ0X,MAAQD,EAAOzX,QAAQhE,KAC7Dsa,EAAQ,EAA6BlhB,GAGzC8hB,MAFAxW,GAAO,MAAgB4V,GAGpBa,KAAMC,IACL,GAAwB,MAApBA,EAASzQ,OACX,KAAM,YAAYyQ,EAASzQ,+BAE7B,OAAOyQ,EAASO,SAEjBR,KAAK,EAAGO,WACHA,IACFD,EAAOC,KAAOA,KAGjBH,MAAOC,IACN,EAAOxW,MAAM,yBAA0BwW,QClB/C,MACE3B,mBAAkB,GAClBC,WAAU,SACV8B,GAAK,YACLxB,GAAW,eACX9S,GAAc,gBACd0M,GAAe,UACfqG,GAAS,gBACTvT,GAAe,kBACfgQ,GAAiB,qBACjBE,GAAoB,+BACpB9S,GAA8B,oBAC9ByU,GAAmB,YACnB1R,IACE,GAkDW,IC1EV4U,GD0EU,GAhDS,CACtBhC,mBAAkB,GAClBC,WAAU,GACV8B,SACAxB,eACA9S,kBACA0M,mBACAqG,aACAvT,mBACAgQ,qBACAE,wBACA9S,kCACAyU,uBACA1R,eAEA8C,kBAAmB,GAEnB+R,eAAc,KACL,CAAEC,KAAM,KAGjBlV,gBAAe,IACNmV,UAGTvC,UAAS,IACAwC,eAGT5M,WAAU,IACD,GAGTwI,UAAUzP,GAWDD,KAAK+T,OANKC,WAAWC,QAAUD,WAAqB,UACnCE,gBAAgB,IAAIC,YAAY,IAAI,GAE1C,WAAK,IAGMlU,KCtEjC,SAAKyT,GACH,qBACA,mBACA,qBAHF,CAAKA,QAAa,KAMH,UCOA,MAAM,GAQnB,YAAYtjB,EAAauf,EAAiB9T,GACxCpN,KAAK2B,IAAMA,EACX3B,KAAKkhB,QAAUA,EACflhB,KAAK2lB,OAAS,GACd3lB,KAAKoN,QAAUA,GAAW,GAC1BpN,KAAK4lB,KAAO,EACZ5lB,KAAK6lB,SAAW,EAGlB,IAAIC,EAAO7T,GACL6T,GAAS9lB,KAAKoN,QAAQ0Y,QACxB9lB,KAAK2lB,OAAOhhB,KACV,EAAmB,GAAIsN,EAAO,CAAEyL,UAAW,EAAKrX,SAE9CrG,KAAKoN,QAAQ2Y,OAAS/lB,KAAK2lB,OAAOrjB,OAAStC,KAAKoN,QAAQ2Y,OAC1D/lB,KAAK2lB,OAAOK,SAKlB,MAAM/T,GACJjS,KAAK2M,IAAI,GAAMsZ,MAAOhU,GAGxB,KAAKA,GACHjS,KAAK2M,IAAI,GAAMuZ,KAAMjU,GAGvB,MAAMA,GACJjS,KAAK2M,IAAI,GAAMwZ,MAAOlU,GAGxB,UACE,OAA8B,IAAvBjS,KAAK2lB,OAAOrjB,OAGrB,KAAK8jB,EAAQtgB,GACX,IAAItD,EAAO,EACT,CACE0e,QAASlhB,KAAKkhB,QACdmF,OAAQrmB,KAAK4lB,KAAO,EACpBjkB,IAAK3B,KAAK2B,IACV2kB,IAAK,KACLC,QAASvmB,KAAKoN,QAAQmZ,QACtBC,QAASxmB,KAAKoN,QAAQoZ,QACtBC,SAAUzmB,KAAKoN,QAAQqZ,SACvBjZ,SAAUxN,KAAK2lB,QAEjB3lB,KAAKoN,QAAQzE,QAaf,OAVA3I,KAAK2lB,OAAS,GACdS,EAAO5jB,EAAM,CAACuK,EAAOnJ,KACdmJ,GACH/M,KAAK4lB,OAEH9f,GACFA,EAASiH,EAAOnJ,MAIb,EAGT,mBAEE,OADA5D,KAAK6lB,WACE7lB,KAAK6lB,UCvED,MAAM,GAMnB,YACEllB,EACAwM,EACA3C,EACA4C,GAEApN,KAAKW,KAAOA,EACZX,KAAKmN,SAAWA,EAChBnN,KAAKwK,UAAYA,EACjBxK,KAAKoN,QAAUA,GAAW,GAO5B,cACE,OAAOpN,KAAKwK,UAAUsF,YAAY,CAChC/E,OAAQ/K,KAAKoN,QAAQrC,SASzB,QAAQuR,EAAqBxW,GAC3B,IAAK9F,KAAK8P,cACR,OAAO4W,GAAY,IAAI,GAA8B5gB,GAChD,GAAI9F,KAAKmN,SAAWmP,EACzB,OAAOoK,GAAY,IAAI,GAAkC5gB,GAG3D,IAAIwU,GAAY,EACZ9P,EAAYxK,KAAKwK,UAAU0G,iBAC7BlR,KAAKW,KACLX,KAAKmN,SACLnN,KAAKoN,QAAQzL,IACb3B,KAAKoN,SAEHgM,EAAY,KAEZuN,EAAgB,WAClBnc,EAAU6B,OAAO,cAAesa,GAChCnc,EAAU6O,WAERlK,EAAS,WACXiK,EAAY,GAAQyC,gBAAgBrR,GAAW,SAAU5G,GACvD0W,GAAY,EACZpL,IACApJ,EAAS,KAAMlC,OAGfqK,EAAU,SAAUlB,GACtBmC,IACApJ,EAASiH,IAEPqE,EAAW,WAEb,IAAIwV,EADJ1X,IAOA0X,EAAsB,EAA8Bpc,GACpD1E,EAAS,IAAI,GAAuB8gB,KAGlC1X,EAAkB,WACpB1E,EAAU6B,OAAO,cAAesa,GAChCnc,EAAU6B,OAAO,OAAQ8C,GACzB3E,EAAU6B,OAAO,QAAS4B,GAC1BzD,EAAU6B,OAAO,SAAU+E,IAW7B,OARA5G,EAAU5I,KAAK,cAAe+kB,GAC9Bnc,EAAU5I,KAAK,OAAQuN,GACvB3E,EAAU5I,KAAK,QAASqM,GACxBzD,EAAU5I,KAAK,SAAUwP,GAGzB5G,EAAU6C,aAEH,CACLmM,MAAO,KACDc,IAGJpL,IACIkK,EACFA,EAAU/K,QAEV7D,EAAU6D,UAGdoO,iBAAmBva,IACboY,GAGAta,KAAKmN,SAAWjL,IACdkX,EACFA,EAAU/K,QAEV7D,EAAU6D,YAQtB,SAASqY,GAAY3Z,EAAcjH,GAIjC,OAHA,EAAKU,OAAM,WACTV,EAASiH,MAEJ,CACLyM,MAAO,aACPiD,iBAAkB,cCnItB,MAAQyG,WAAU,IAAK,GAEhB,ICZK2D,GDYD,GAAkB,SAC3BrR,EACA7U,EACAiO,EACAzB,EACAC,EACAyD,GAEA,IAWIrG,EAXAsc,EAAiB,GAAWlY,GAChC,IAAKkY,EACH,MAAM,IAAI,GAA4BlY,GA0BxC,QAtBI4G,EAAOuR,oBACuD,IAA9D,EAAyBvR,EAAOuR,kBAAmBpmB,IACnD6U,EAAOwR,qBACwD,IAA/D,EAAyBxR,EAAOwR,mBAAoBrmB,KAItDyM,EAAUtM,OAAOwU,OACf,CAAE2R,iBAAkBzR,EAAOyR,kBAC3B7Z,GAGF5C,EAAY,IAAI,GACd7J,EACAwM,EACA0D,EAAUA,EAAQqW,aAAaJ,GAAkBA,EACjD1Z,IAGF5C,EAAY,GAGPA,GAGL,GAAgC,CAClCsF,YAAa,WACX,OAAO,GAETuJ,QAAS,SAAUtR,EAAGjC,GACpB,IAAIqhB,EAAW,EAAK3gB,OAAM,WACxBV,EAAS,IAAI,OAEf,MAAO,CACL0T,MAAO,WACL2N,EAASpN,iBAEX0C,iBAAkB,iBC/DxB,SAAYoK,GACV,2CACA,+CAFF,CAAYA,QAAe,KC6DZ,OAtBblD,IAEA,QAA+D,IAApD,GAAQuB,iBAAiBvB,EAAYnZ,WAC9C,KAAM,IAAImZ,EAAYnZ,gDAGxB,MAAO,CACL7B,EACA7C,KAEA,MAAM4d,EAvCkB,EAC1B/a,EACAgb,KAEA,IAAID,EAAQ,aAAelb,mBAAmBG,EAAOkM,UAErD,IAAK,IAAIlT,KAAOgiB,EAAYhb,OAC1B+a,GACE,IACAlb,mBAAmB7G,GACnB,IACA6G,mBAAmBmb,EAAYhb,OAAOhH,IAG1C,GAAkC,MAA9BgiB,EAAYyD,eAAwB,CACtC,IAAIC,EAAgB1D,EAAYyD,iBAChC,IAAK,IAAIzlB,KAAO0lB,EACd3D,GACE,IACAlb,mBAAmB7G,GACnB,IACA6G,mBAAmB6e,EAAc1lB,IAIvC,OAAO+hB,GAcS4D,CAAoB3e,EAAQgb,GAE1C,GAAQuB,iBAAiBvB,EAAYnZ,WACnC,GACAkZ,EACAC,EACAkD,GAAgBU,mBAChBzhB,KCOS,OAtBb6d,IAEA,QAA+D,IAApD,GAAQuB,iBAAiBvB,EAAYnZ,WAC9C,KAAM,IAAImZ,EAAYnZ,gDAGxB,MAAO,CACL7B,EACA7C,KAEA,MAAM4d,EAzCkB,EAC1B/a,EACAgb,KAEA,IAAID,EAAQ,aAAelb,mBAAmBG,EAAOkM,UAIrD,IAAK,IAAIlT,KAFT+hB,GAAS,iBAAmBlb,mBAAmBG,EAAO+M,aAEtCiO,EAAYhb,OAC1B+a,GACE,IACAlb,mBAAmB7G,GACnB,IACA6G,mBAAmBmb,EAAYhb,OAAOhH,IAG1C,GAAkC,MAA9BgiB,EAAYyD,eAAwB,CACtC,IAAIC,EAAgB1D,EAAYyD,iBAChC,IAAK,IAAIzlB,KAAO0lB,EACd3D,GACE,IACAlb,mBAAmB7G,GACnB,IACA6G,mBAAmB6e,EAAc1lB,IAIvC,OAAO+hB,GAcS,CAAoB/a,EAAQgb,GAE1C,GAAQuB,iBAAiBvB,EAAYnZ,WACnC,GACAkZ,EACAC,EACAkD,GAAgBW,qBAChB1hB,KCgCN,SAAS2hB,GAAYC,GACnB,OAAIA,EAAK9d,SACA8d,EAAK9d,SAEV8d,EAAKlB,QACA,UAAUkB,EAAKlB,qBAEjB,EAAS5c,SAGlB,SAAS+d,GAAiBD,GACxB,OAAIA,EAAK5I,OACA4I,EAAK5I,OAMP,MAJ4B4I,EAAKlB,qBAO1C,SAASoB,GAAaF,GACpB,MAA8B,WAA1B,GAAQlE,gBAEiB,IAAlBkE,EAAKG,SASlB,SAASC,GAAqBJ,GAC5B,MAAI,gBAAiBA,EACZA,EAAKK,YAEV,iBAAkBL,IACZA,EAAKM,aAKjB,SAASC,GAAuBP,GAC9B,MAAMpd,EAAqB,OAAH,wBACnB,EAASA,oBACTod,EAAKpd,oBAEV,MACE,kBAAmBA,GACoB,MAAvCA,EAAkC,cAE3BA,EAAkC,cAGpC,GAAkBA,GA8B3B,SAAS4d,GACPR,EACAjT,GAEA,MAAMhK,EA/BR,SAA0Bid,EAAejT,GACvC,IAAIhK,EAuBJ,MAtBI,yBAA0Bid,EAC5Bjd,EAAuB,OAAH,wBACf,EAASA,sBACTid,EAAKjd,uBAGVA,EAAuB,CACrBD,UAAWkd,EAAKxd,eAAiB,EAASA,cAC1CK,SAAUmd,EAAKzd,cAAgB,EAASA,cAEtC,SAAUyd,IACR,WAAYA,EAAK5S,OAAMrK,EAAqB9B,OAAS+e,EAAK5S,KAAKnM,QAC/D,YAAa+e,EAAK5S,OACpBrK,EAAqBoZ,QAAU6D,EAAK5S,KAAK+O,UAEzC,eAAgB6D,IAClBjd,EAAqB0d,cCxIW,EACpC1T,EACAkP,EACAyE,KAEA,MAAMC,EAA2D,CAC/Dne,cAAeyZ,EAAYnZ,UAC3BP,aAAc0Z,EAAYpZ,SAC1BuK,KAAM,CACJnM,OAAQgb,EAAYhb,OACpBkb,QAASF,EAAYE,UAGzB,MAAO,CACLlb,EACA7C,KAEA,MAAMoM,EAAUuC,EAAOvC,QAAQvJ,EAAO+M,aAKpC0S,EAA2BlW,EAASmW,GACpBhT,UAAU1M,EAAOkM,SAAU/O,KDiHNwiB,CACnC7T,EACAhK,EACAid,EAAKa,cAGJ9d,EAOsB+d,CAAiBd,EAAMjT,GACpD,MACE,kBAAmBhK,GACsB,MAAzCA,EAAoC,cAE7BA,EAAoC,cAGtC,GAAkBA,GEvLZ,MAAM,WAAwB,EAG3C,YAAmBgK,GACjBvO,OAAM,SAAUgG,EAAW1J,GACzB,EAAO4L,MAAM,wCAAwClC,MAGvDlM,KAAKyU,OAASA,EACdzU,KAAKyoB,6BAGP,YAAYzW,GACVA,EAAYxP,KAAKmjB,OAAO+C,QAASC,IAC/B3oB,KAAK2O,KAAKga,EAAehoB,KAAMgoB,KAI3B,6BACN3oB,KAAKyU,OAAOxD,WAAWrP,KAAK,UAAYoQ,IAEpB,qCADFA,EAAYC,OAE1BjS,KAAKsX,YAAYtF,MCjBV,OATf,WACE,IAAI4W,EAASC,EAKb,MAAO,CAAEC,QAJO,IAAIC,QAAQ,CAACC,EAAKC,KAChCL,EAAUI,EACVH,EAASI,IAEOL,UAASC,WCKd,MAAM,WAAmB,EAStC,YAAmBpU,GACjBvO,OAAM,SAAUgG,EAAW1J,GACzB,EAAO4L,MAAM,4BAA8BlC,MAT/C,KAAAgd,kBAA4B,EAC5B,KAAAvS,UAAiB,KACjB,KAAAwS,oBAA+B,KAC/B,KAAAzS,kBAAkC,KAE1B,KAAA0S,mBAA+B,KA8D/B,KAAAC,aAA2C,CACjDzE,EACAtO,KAEA,GAAIsO,EAGF,OAFA,EAAO9X,KAAK,wBAAwB8X,QACpC5kB,KAAKspB,WAIPtpB,KAAKyU,OAAO9B,WAAW,gBAAiB,CACtCmC,KAAMwB,EAASxB,KACf6B,UAAWL,EAASK,aApEtB3W,KAAKyU,OAASA,EACdzU,KAAKyU,OAAOxD,WAAWrP,KAAK,eAAgB,EAAGqZ,WAAUC,cACtC,cAAbD,GAAwC,cAAZC,GAC9Blb,KAAKupB,UAEU,cAAbtO,GAAwC,cAAZC,IAC9Blb,KAAKspB,WACLtpB,KAAKwpB,+BAITxpB,KAAKypB,UAAY,IAAI,GAAgBhV,GAErCzU,KAAKyU,OAAOxD,WAAWrP,KAAK,UAAYqQ,IAEpB,0BADFA,EAAMA,OAEpBjS,KAAK0pB,iBAAiBzX,EAAMzP,MAG5BxC,KAAKmpB,qBACLnpB,KAAKmpB,oBAAoBxoB,OAASsR,EAAMC,SAExClS,KAAKmpB,oBAAoB7R,YAAYrF,KAKpC,SACDjS,KAAKkpB,mBAITlpB,KAAKkpB,kBAAmB,EACxBlpB,KAAKupB,WAGC,UACDvpB,KAAKkpB,mBAIVlpB,KAAKwpB,4BAEgC,cAAjCxpB,KAAKyU,OAAOxD,WAAW1D,OAK3BvN,KAAKyU,OAAOe,OAAOmU,kBACjB,CACE9U,SAAU7U,KAAKyU,OAAOxD,WAAWuB,WAEnCxS,KAAKqpB,eAsBD,iBAAiB7mB,GACvB,IACExC,KAAK2W,UAAY5N,KAAK+I,MAAMtP,EAAKmU,WACjC,MAAO3N,GAGP,OAFA,EAAO+D,MAAM,0CAA0CvK,EAAKmU,gBAC5D3W,KAAKspB,WAIP,GAAiC,iBAAtBtpB,KAAK2W,UAAUlJ,IAAyC,KAAtBzN,KAAK2W,UAAUlJ,GAK1D,OAJA,EAAOV,MACL,+CAA+C/M,KAAK2W,gBAEtD3W,KAAKspB,WAKPtpB,KAAKopB,qBACLppB,KAAK4pB,qBAGC,qBAYN5pB,KAAKmpB,oBAAsB,IAAI,GAC7B,mBAAmBnpB,KAAK2W,UAAUlJ,GAClCzN,KAAKyU,QAEPzU,KAAKmpB,oBAAoBU,YAAY,CAAC3d,EAAW1J,KAEH,IAA1C0J,EAAUvE,QAAQ,qBACe,IAAjCuE,EAAUvE,QAAQ,YAKpB3H,KAAK2O,KAAKzC,EAAW1J,KAvBG,CAAC0P,IACrBA,EAAQyC,qBAAuBzC,EAAQ0C,sBACzC1C,EAAQ4X,wBAEP5X,EAAQyC,qBACwB,cAAjC3U,KAAKyU,OAAOxD,WAAW1D,OAEvB2E,EAAQ6X,aAkBZC,CAAkBhqB,KAAKmpB,qBAGjB,WACNnpB,KAAK2W,UAAY,KACb3W,KAAKmpB,sBACPnpB,KAAKmpB,oBAAoBvG,aACzB5iB,KAAKmpB,oBAAoBjS,aACzBlX,KAAKmpB,oBAAsB,MAGzBnpB,KAAKkpB,kBAGPlpB,KAAKopB,qBAID,4BACN,IAAKppB,KAAKkpB,iBACR,OAIF,GAAIlpB,KAAK0W,oBAAuB1W,KAAK0W,kBAA0BuT,KAC7D,OAKF,MAAM,QAAEnB,EAAO,QAAEF,EAASC,OAAQ9gB,GAAM,KACvC+gB,EAAgBmB,MAAO,EACxB,MAAMC,EAAU,KACbpB,EAAgBmB,MAAO,GAE1BnB,EAAQvE,KAAK2F,GAASvF,MAAMuF,GAC5BlqB,KAAK0W,kBAAoBoS,EACzB9oB,KAAKopB,mBAAqBR,GC/J9B,MAAqB,GAYnB,eACE,GAAOuB,SAAU,EACjB,IAAK,IAAI/pB,EAAI,EAAGC,EAAI,GAAO+pB,UAAU9nB,OAAQlC,EAAIC,EAAGD,IAClD,GAAOgqB,UAAUhqB,GAAGiZ,UAMhB,2BACN,OAAO,EACL,EAAyB,CAAEjO,GAAI,GAAQ8X,WAAW9X,KAAM,SAAU9J,GAChE,OAAOA,EAAEwO,YAAY,QAgB3B,YAAYua,EAAiBjd,IAsL/B,SAAqBzL,GACnB,GAAIA,QACF,KAAM,0DAvLN2oB,CAAYD,GCnBT,SAAyBjd,GAC9B,GAAe,MAAXA,EACF,KAAM,kCAER,GAAuB,MAAnBA,EAAQoZ,QACV,KAAM,wCAEJ,iBAAkBpZ,GACpB,EAAON,KACL,iEDWFyd,CAAgBnd,GAChBpN,KAAK2B,IAAM0oB,EACXrqB,KAAKwV,OLfF,SAAmBkS,EAAejT,GACvC,IAAIe,EAAiB,CACnBrL,gBAAiBud,EAAKvd,iBAAmB,EAASA,gBAClDqc,QAASkB,EAAKlB,QACdzc,SAAU2d,EAAK3d,UAAY,EAASA,SACpCF,SAAU6d,EAAK7d,UAAY,EAASA,SACpCC,UAAW4d,EAAK5d,WAAa,EAASA,UACtCM,YAAasd,EAAKtd,aAAe,EAASA,YAC1CogB,UAAW9C,EAAK8C,WAAa,EAASxgB,WACtCK,mBAAoBqd,EAAKrd,oBAAsB,EAASA,mBACxDV,OAAQ+d,EAAK/d,QAAU,EAASA,OAChCF,OAAQie,EAAKje,QAAU,EAASA,OAChCC,QAASge,EAAKhe,SAAW,EAASA,QAElCqe,YAAaD,GAAqBJ,GAClC9d,SAAU6d,GAAYC,GACtB3c,OAAQ6c,GAAaF,GACrB5I,OAAQ6I,GAAiBD,GAEzBiC,kBAAmB1B,GAAuBP,GAC1CjS,kBAAmByS,GAAuBR,EAAMjT,IAclD,MAXI,uBAAwBiT,IAC1BlS,EAAOwR,mBAAqBU,EAAKV,oBAC/B,sBAAuBU,IACzBlS,EAAOuR,kBAAoBW,EAAKX,mBAC9B,qBAAsBW,IACxBlS,EAAOyR,iBAAmBS,EAAKT,kBAC7B,mBAAoBS,IAAMlS,EAAOiV,eAAiB/C,EAAK+C,gBACvD,SAAU/C,IACZlS,EAAO2B,KAAOuQ,EAAKvQ,MAGd3B,EKnBSkV,CAAUtd,EAASpN,MAEjCA,KAAKmb,SAAW,GAAQO,iBACxB1b,KAAK2qB,eAAiB,IAAI,EAC1B3qB,KAAK4qB,UAAY,GAAQ3J,UAAU,KAEnCjhB,KAAKwN,SAAW,IAAI,GAASxN,KAAK2B,IAAK3B,KAAK4qB,UAAW,CACrDpE,QAASxmB,KAAKwV,OAAOgR,QACrBC,SAAU,GAAOlD,oBACjB5a,OAAQ3I,KAAKwV,OAAOiV,gBAAkB,GACtC1E,MAAO,GACPD,MAAO,GAAcI,KACrBK,QAAS,EAAShd,UAEhBvJ,KAAKwV,OAAOuS,cACd/nB,KAAK6qB,eAAiB,GAAQjP,qBAAqB5b,KAAKwN,SAAU,CAChEsX,KAAM9kB,KAAKwV,OAAOgV,UAClBphB,KAAM,gBAAkB,GAAQ+J,kBAAkBxS,QAQtDX,KAAKiR,WAAa,GAAQ0K,wBAAwB3b,KAAK2B,IAAK,CAC1DiY,YALiBxM,GACV,GAAQ6V,mBAAmBjjB,KAAKwV,OAAQpI,EAAS,IAKxDI,SAAUxN,KAAKwN,SACfrD,gBAAiBnK,KAAKwV,OAAOrL,gBAC7BC,YAAapK,KAAKwV,OAAOpL,YACzBC,mBAAoBrK,KAAKwV,OAAOnL,mBAChCU,OAAQ1C,QAAQrI,KAAKwV,OAAOzK,UAG9B/K,KAAKiR,WAAWrP,KAAK,YAAa,KAChC5B,KAAK8qB,eACD9qB,KAAK6qB,gBACP7qB,KAAK6qB,eAAevc,KAAKtO,KAAKiR,WAAW8Z,gBAI7C/qB,KAAKiR,WAAWrP,KAAK,UAAYqQ,IAC/B,IACI+Y,EAAqD,IADzC/Y,EAAMA,MACGtK,QAAQ,oBACjC,GAAIsK,EAAMC,QAAS,CACjB,IAAIA,EAAUlS,KAAKkS,QAAQD,EAAMC,SAC7BA,GACFA,EAAQoF,YAAYrF,GAInB+Y,GACHhrB,KAAK2qB,eAAehc,KAAKsD,EAAMA,MAAOA,EAAMzP,QAGhDxC,KAAKiR,WAAWrP,KAAK,aAAc,KACjC5B,KAAKmb,SAASjE,eAEhBlX,KAAKiR,WAAWrP,KAAK,eAAgB,KACnC5B,KAAKmb,SAASjE,eAEhBlX,KAAKiR,WAAWrP,KAAK,QAAUgjB,IAC7B,EAAO9X,KAAK8X,KAGd,GAAOwF,UAAUzlB,KAAK3E,MACtBA,KAAKwN,SAASmC,KAAK,CAAEya,UAAW,GAAOA,UAAU9nB,SAEjDtC,KAAKyW,KAAO,IAAI,GAAWzW,MAEvB,GAAOmqB,SACTnqB,KAAKqZ,UAIT,QAAQ1Y,GACN,OAAOX,KAAKmb,SAAS8P,KAAKtqB,GAG5B,cACE,OAAOX,KAAKmb,SAAS+P,MAGvB,UAGE,GAFAlrB,KAAKiR,WAAWoI,UAEZrZ,KAAK6qB,iBACF7qB,KAAKmrB,oBAAqB,CAC7B,IAAIlT,EAAWjY,KAAKiR,WAAW8Z,aAC3BF,EAAiB7qB,KAAK6qB,eAC1B7qB,KAAKmrB,oBAAsB,IAAI,EAAc,KAAO,WAClDN,EAAevc,KAAK2J,OAM5B,aACEjY,KAAKiR,WAAWiG,aAEZlX,KAAKmrB,sBACPnrB,KAAKmrB,oBAAoBpR,gBACzB/Z,KAAKmrB,oBAAsB,MAI/B,KAAKC,EAAoBtlB,EAAoBkC,GAE3C,OADAhI,KAAK2qB,eAAe/oB,KAAKwpB,EAAYtlB,EAAUkC,GACxChI,KAGT,OAAOorB,EAAqBtlB,EAAqBkC,GAE/C,OADAhI,KAAK2qB,eAAete,OAAO+e,EAAYtlB,EAAUkC,GAC1ChI,KAGT,YAAY8F,GAEV,OADA9F,KAAK2qB,eAAed,YAAY/jB,GACzB9F,KAGT,cAAc8F,GAEZ,OADA9F,KAAK2qB,eAAere,cAAcxG,GAC3B9F,KAGT,WAAW8F,GAET,OADA9F,KAAK2qB,eAAe/H,aACb5iB,KAGT,eACE,IAAI0V,EACJ,IAAKA,KAAe1V,KAAKmb,SAASA,SAC5Bnb,KAAKmb,SAASA,SAASlZ,eAAeyT,IACxC1V,KAAK+pB,UAAUrU,GAKrB,UAAU2V,GACR,IAAInZ,EAAUlS,KAAKmb,SAAShP,IAAIkf,EAAcrrB,MAS9C,OARIkS,EAAQyC,qBAAuBzC,EAAQ0C,sBACzC1C,EAAQ4X,wBAEP5X,EAAQyC,qBACiB,cAA1B3U,KAAKiR,WAAW1D,OAEhB2E,EAAQ6X,YAEH7X,EAGT,YAAYmZ,GACV,IAAInZ,EAAUlS,KAAKmb,SAAS8P,KAAKI,GAC7BnZ,GAAWA,EAAQyC,oBACrBzC,EAAQoZ,sBAERpZ,EAAUlS,KAAKmb,SAAS/O,OAAOif,KAChBnZ,EAAQwC,YACrBxC,EAAQgD,cAKd,WAAWkW,EAAoB5oB,EAAW0P,GACxC,OAAOlS,KAAKiR,WAAW0B,WAAWyY,EAAY5oB,EAAM0P,GAGtD,eACE,OAAOlS,KAAKwV,OAAOzK,OAGrB,SACE/K,KAAKyW,KAAK8U,UAxNL,GAAAnB,UAAsB,GACtB,GAAAD,SAAmB,EACnB,GAAAld,cAAwB,EAGxB,GAAAue,QAA2B,GAC3B,GAAAC,gBAA6B,GAASA,gBACtC,GAAAC,sBAAmC,GAASA,sBAC5C,GAAAC,eAA4B,GAASA,eAVzB,oBAoOrB,GAAQ3G,MAAM", "file": "pusher.worker.min.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"Pusher\"] = factory();\n\telse\n\t\troot[\"Pusher\"] = factory();\n})(this, function() {\nreturn ", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 2);\n", "// Copyright (C) 2016 <PERSON>\n// MIT License. See LICENSE file for details.\n\n/**\n * Package base64 implements Base64 encoding and decoding.\n */\n\n// Invalid character used in decoding to indicate\n// that the character to decode is out of range of\n// alphabet and cannot be decoded.\nconst INVALID_BYTE = 256;\n\n/**\n * Implements standard Base64 encoding.\n *\n * Operates in constant time.\n */\nexport class Coder {\n    // TODO(dchest): methods to encode chunk-by-chunk.\n\n    constructor(private _padding<PERSON>haracter = \"=\") { }\n\n    encodedLength(length: number): number {\n        if (!this._padding<PERSON>haracter) {\n            return (length * 8 + 5) / 6 | 0;\n        }\n        return (length + 2) / 3 * 4 | 0;\n    }\n\n    encode(data: Uint8Array): string {\n        let out = \"\";\n\n        let i = 0;\n        for (; i < data.length - 2; i += 3) {\n            let c = (data[i] << 16) | (data[i + 1] << 8) | (data[i + 2]);\n            out += this._encodeByte((c >>> 3 * 6) & 63);\n            out += this._encodeByte((c >>> 2 * 6) & 63);\n            out += this._encodeByte((c >>> 1 * 6) & 63);\n            out += this._encodeByte((c >>> 0 * 6) & 63);\n        }\n\n        const left = data.length - i;\n        if (left > 0) {\n            let c = (data[i] << 16) | (left === 2 ? data[i + 1] << 8 : 0);\n            out += this._encodeByte((c >>> 3 * 6) & 63);\n            out += this._encodeByte((c >>> 2 * 6) & 63);\n            if (left === 2) {\n                out += this._encodeByte((c >>> 1 * 6) & 63);\n            } else {\n                out += this._paddingCharacter || \"\";\n            }\n            out += this._paddingCharacter || \"\";\n        }\n\n        return out;\n    }\n\n    maxDecodedLength(length: number): number {\n        if (!this._paddingCharacter) {\n            return (length * 6 + 7) / 8 | 0;\n        }\n        return length / 4 * 3 | 0;\n    }\n\n    decodedLength(s: string): number {\n        return this.maxDecodedLength(s.length - this._getPaddingLength(s));\n    }\n\n    decode(s: string): Uint8Array {\n        if (s.length === 0) {\n            return new Uint8Array(0);\n        }\n        const paddingLength = this._getPaddingLength(s);\n        const length = s.length - paddingLength;\n        const out = new Uint8Array(this.maxDecodedLength(length));\n        let op = 0;\n        let i = 0;\n        let haveBad = 0;\n        let v0 = 0, v1 = 0, v2 = 0, v3 = 0;\n        for (; i < length - 4; i += 4) {\n            v0 = this._decodeChar(s.charCodeAt(i + 0));\n            v1 = this._decodeChar(s.charCodeAt(i + 1));\n            v2 = this._decodeChar(s.charCodeAt(i + 2));\n            v3 = this._decodeChar(s.charCodeAt(i + 3));\n            out[op++] = (v0 << 2) | (v1 >>> 4);\n            out[op++] = (v1 << 4) | (v2 >>> 2);\n            out[op++] = (v2 << 6) | v3;\n            haveBad |= v0 & INVALID_BYTE;\n            haveBad |= v1 & INVALID_BYTE;\n            haveBad |= v2 & INVALID_BYTE;\n            haveBad |= v3 & INVALID_BYTE;\n        }\n        if (i < length - 1) {\n            v0 = this._decodeChar(s.charCodeAt(i));\n            v1 = this._decodeChar(s.charCodeAt(i + 1));\n            out[op++] = (v0 << 2) | (v1 >>> 4);\n            haveBad |= v0 & INVALID_BYTE;\n            haveBad |= v1 & INVALID_BYTE;\n        }\n        if (i < length - 2) {\n            v2 = this._decodeChar(s.charCodeAt(i + 2));\n            out[op++] = (v1 << 4) | (v2 >>> 2);\n            haveBad |= v2 & INVALID_BYTE;\n        }\n        if (i < length - 3) {\n            v3 = this._decodeChar(s.charCodeAt(i + 3));\n            out[op++] = (v2 << 6) | v3;\n            haveBad |= v3 & INVALID_BYTE;\n        }\n        if (haveBad !== 0) {\n            throw new Error(\"Base64Coder: incorrect characters for decoding\");\n        }\n        return out;\n    }\n\n    // Standard encoding have the following encoded/decoded ranges,\n    // which we need to convert between.\n    //\n    // ABCDEFGHIJKLMNOPQRSTUVWXYZ abcdefghijklmnopqrstuvwxyz 0123456789  +   /\n    // Index:   0 - 25                    26 - 51              52 - 61   62  63\n    // ASCII:  65 - 90                    97 - 122             48 - 57   43  47\n    //\n\n    // Encode 6 bits in b into a new character.\n    protected _encodeByte(b: number): string {\n        // Encoding uses constant time operations as follows:\n        //\n        // 1. Define comparison of A with B using (A - B) >>> 8:\n        //          if A > B, then result is positive integer\n        //          if A <= B, then result is 0\n        //\n        // 2. Define selection of C or 0 using bitwise AND: X & C:\n        //          if X == 0, then result is 0\n        //          if X != 0, then result is C\n        //\n        // 3. Start with the smallest comparison (b >= 0), which is always\n        //    true, so set the result to the starting ASCII value (65).\n        //\n        // 4. Continue comparing b to higher ASCII values, and selecting\n        //    zero if comparison isn't true, otherwise selecting a value\n        //    to add to result, which:\n        //\n        //          a) undoes the previous addition\n        //          b) provides new value to add\n        //\n        let result = b;\n        // b >= 0\n        result += 65;\n        // b > 25\n        result += ((25 - b) >>> 8) & ((0 - 65) - 26 + 97);\n        // b > 51\n        result += ((51 - b) >>> 8) & ((26 - 97) - 52 + 48);\n        // b > 61\n        result += ((61 - b) >>> 8) & ((52 - 48) - 62 + 43);\n        // b > 62\n        result += ((62 - b) >>> 8) & ((62 - 43) - 63 + 47);\n\n        return String.fromCharCode(result);\n    }\n\n    // Decode a character code into a byte.\n    // Must return 256 if character is out of alphabet range.\n    protected _decodeChar(c: number): number {\n        // Decoding works similar to encoding: using the same comparison\n        // function, but now it works on ranges: result is always incremented\n        // by value, but this value becomes zero if the range is not\n        // satisfied.\n        //\n        // Decoding starts with invalid value, 256, which is then\n        // subtracted when the range is satisfied. If none of the ranges\n        // apply, the function returns 256, which is then checked by\n        // the caller to throw error.\n        let result = INVALID_BYTE; // start with invalid character\n\n        // c == 43 (c > 42 and c < 44)\n        result += (((42 - c) & (c - 44)) >>> 8) & (-INVALID_BYTE + c - 43 + 62);\n        // c == 47 (c > 46 and c < 48)\n        result += (((46 - c) & (c - 48)) >>> 8) & (-INVALID_BYTE + c - 47 + 63);\n        // c > 47 and c < 58\n        result += (((47 - c) & (c - 58)) >>> 8) & (-INVALID_BYTE + c - 48 + 52);\n        // c > 64 and c < 91\n        result += (((64 - c) & (c - 91)) >>> 8) & (-INVALID_BYTE + c - 65 + 0);\n        // c > 96 and c < 123\n        result += (((96 - c) & (c - 123)) >>> 8) & (-INVALID_BYTE + c - 97 + 26);\n\n        return result;\n    }\n\n    private _getPaddingLength(s: string): number {\n        let paddingLength = 0;\n        if (this._paddingCharacter) {\n            for (let i = s.length - 1; i >= 0; i--) {\n                if (s[i] !== this._paddingCharacter) {\n                    break;\n                }\n                paddingLength++;\n            }\n            if (s.length < 4 || paddingLength > 2) {\n                throw new Error(\"Base64Coder: incorrect padding\");\n            }\n        }\n        return paddingLength;\n    }\n\n}\n\nconst stdCoder = new Coder();\n\nexport function encode(data: Uint8Array): string {\n    return stdCoder.encode(data);\n}\n\nexport function decode(s: string): Uint8Array {\n    return stdCoder.decode(s);\n}\n\n/**\n * Implements URL-safe Base64 encoding.\n * (Same as Base64, but '+' is replaced with '-', and '/' with '_').\n *\n * Operates in constant time.\n */\nexport class URLSafeCoder extends Coder {\n    // URL-safe encoding have the following encoded/decoded ranges:\n    //\n    // ABCDEFGHIJKLMNOPQRSTUVWXYZ abcdefghijklmnopqrstuvwxyz 0123456789  -   _\n    // Index:   0 - 25                    26 - 51              52 - 61   62  63\n    // ASCII:  65 - 90                    97 - 122             48 - 57   45  95\n    //\n\n    protected _encodeByte(b: number): string {\n        let result = b;\n        // b >= 0\n        result += 65;\n        // b > 25\n        result += ((25 - b) >>> 8) & ((0 - 65) - 26 + 97);\n        // b > 51\n        result += ((51 - b) >>> 8) & ((26 - 97) - 52 + 48);\n        // b > 61\n        result += ((61 - b) >>> 8) & ((52 - 48) - 62 + 45);\n        // b > 62\n        result += ((62 - b) >>> 8) & ((62 - 45) - 63 + 95);\n\n        return String.fromCharCode(result);\n    }\n\n    protected _decodeChar(c: number): number {\n        let result = INVALID_BYTE;\n\n        // c == 45 (c > 44 and c < 46)\n        result += (((44 - c) & (c - 46)) >>> 8) & (-INVALID_BYTE + c - 45 + 62);\n        // c == 95 (c > 94 and c < 96)\n        result += (((94 - c) & (c - 96)) >>> 8) & (-INVALID_BYTE + c - 95 + 63);\n        // c > 47 and c < 58\n        result += (((47 - c) & (c - 58)) >>> 8) & (-INVALID_BYTE + c - 48 + 52);\n        // c > 64 and c < 91\n        result += (((64 - c) & (c - 91)) >>> 8) & (-INVALID_BYTE + c - 65 + 0);\n        // c > 96 and c < 123\n        result += (((96 - c) & (c - 123)) >>> 8) & (-INVALID_BYTE + c - 97 + 26);\n\n        return result;\n    }\n}\n\nconst urlSafeCoder = new URLSafeCoder();\n\nexport function encodeURLSafe(data: Uint8Array): string {\n    return urlSafeCoder.encode(data);\n}\n\nexport function decodeURLSafe(s: string): Uint8Array {\n    return urlSafeCoder.decode(s);\n}\n\n\nexport const encodedLength = (length: number) =>\n    stdCoder.encodedLength(length);\n\nexport const maxDecodedLength = (length: number) =>\n    stdCoder.maxDecodedLength(length);\n\nexport const decodedLength = (s: string) =>\n    stdCoder.decodedLength(s);\n", "// Copyright (C) 2016 <PERSON>\n// MIT License. See LICENSE file for details.\n\n/**\n * Package utf8 implements UTF-8 encoding and decoding.\n */\n\nconst INVALID_UTF16 = \"utf8: invalid string\";\nconst INVALID_UTF8 = \"utf8: invalid source encoding\";\n\n/**\n * Encodes the given string into UTF-8 byte array.\n * Throws if the source string has invalid UTF-16 encoding.\n */\nexport function encode(s: string): Uint8Array {\n    // Calculate result length and allocate output array.\n    // encodedLength() also validates string and throws errors,\n    // so we don't need repeat validation here.\n    const arr = new Uint8Array(encodedLength(s));\n\n    let pos = 0;\n    for (let i = 0; i < s.length; i++) {\n        let c = s.charCodeAt(i);\n        if (c < 0x80) {\n            arr[pos++] = c;\n        } else if (c < 0x800) {\n            arr[pos++] = 0xc0 | c >> 6;\n            arr[pos++] = 0x80 | c & 0x3f;\n        } else if (c < 0xd800) {\n            arr[pos++] = 0xe0 | c >> 12;\n            arr[pos++] = 0x80 | (c >> 6) & 0x3f;\n            arr[pos++] = 0x80 | c & 0x3f;\n        } else {\n            i++; // get one more character\n            c = (c & 0x3ff) << 10;\n            c |= s.charCodeAt(i) & 0x3ff;\n            c += 0x10000;\n\n            arr[pos++] = 0xf0 | c >> 18;\n            arr[pos++] = 0x80 | (c >> 12) & 0x3f;\n            arr[pos++] = 0x80 | (c >> 6) & 0x3f;\n            arr[pos++] = 0x80 | c & 0x3f;\n        }\n    }\n    return arr;\n}\n\n/**\n * Returns the number of bytes required to encode the given string into UTF-8.\n * Throws if the source string has invalid UTF-16 encoding.\n */\nexport function encodedLength(s: string): number {\n    let result = 0;\n    for (let i = 0; i < s.length; i++) {\n        const c = s.charCodeAt(i);\n        if (c < 0x80) {\n            result += 1;\n        } else if (c < 0x800) {\n            result += 2;\n        } else if (c < 0xd800) {\n            result += 3;\n        } else if (c <= 0xdfff) {\n            if (i >= s.length - 1) {\n                throw new Error(INVALID_UTF16);\n            }\n            i++; // \"eat\" next character\n            result += 4;\n        } else {\n            throw new Error(INVALID_UTF16);\n        }\n    }\n    return result;\n}\n\n/**\n * Decodes the given byte array from UTF-8 into a string.\n * Throws if encoding is invalid.\n */\nexport function decode(arr: Uint8Array): string {\n    const chars: string[] = [];\n    for (let i = 0; i < arr.length; i++) {\n        let b = arr[i];\n\n        if (b & 0x80) {\n            let min;\n            if (b < 0xe0) {\n                // Need 1 more byte.\n                if (i >= arr.length) {\n                    throw new Error(INVALID_UTF8);\n                }\n                const n1 = arr[++i];\n                if ((n1 & 0xc0) !== 0x80) {\n                    throw new Error(INVALID_UTF8);\n                }\n                b = (b & 0x1f) << 6 | (n1 & 0x3f);\n                min = 0x80;\n            } else if (b < 0xf0) {\n                // Need 2 more bytes.\n                if (i >= arr.length - 1) {\n                    throw new Error(INVALID_UTF8);\n                }\n                const n1 = arr[++i];\n                const n2 = arr[++i];\n                if ((n1 & 0xc0) !== 0x80 || (n2 & 0xc0) !== 0x80) {\n                    throw new Error(INVALID_UTF8);\n                }\n                b = (b & 0x0f) << 12 | (n1 & 0x3f) << 6 | (n2 & 0x3f);\n                min = 0x800;\n            } else if (b < 0xf8) {\n                // Need 3 more bytes.\n                if (i >= arr.length - 2) {\n                    throw new Error(INVALID_UTF8);\n                }\n                const n1 = arr[++i];\n                const n2 = arr[++i];\n                const n3 = arr[++i];\n                if ((n1 & 0xc0) !== 0x80 || (n2 & 0xc0) !== 0x80 || (n3 & 0xc0) !== 0x80) {\n                    throw new Error(INVALID_UTF8);\n                }\n                b = (b & 0x0f) << 18 | (n1 & 0x3f) << 12 | (n2 & 0x3f) << 6 | (n3 & 0x3f);\n                min = 0x10000;\n            } else {\n                throw new Error(INVALID_UTF8);\n            }\n\n            if (b < min || (b >= 0xd800 && b <= 0xdfff)) {\n                throw new Error(INVALID_UTF8);\n            }\n\n            if (b >= 0x10000) {\n                // Surrogate pair.\n                if (b > 0x10ffff) {\n                    throw new Error(INVALID_UTF8);\n                }\n                b -= 0x10000;\n                chars.push(String.fromCharCode(0xd800 | (b >> 10)));\n                b = 0xdc00 | (b & 0x3ff);\n            }\n        }\n\n        chars.push(String.fromCharCode(b));\n    }\n    return chars.join(\"\");\n}\n", "// required so we don't have to do require('pusher').default etc.\nmodule.exports = require('./pusher').default;\n", "export default function encode(s: any): string {\n  return btoa(utob(s));\n}\n\nvar fromCharCode = String.fromCharCode;\n\nvar b64chars =\n  'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\nvar b64tab = {};\n\nfor (var i = 0, l = b64chars.length; i < l; i++) {\n  b64tab[b64chars.charAt(i)] = i;\n}\n\nvar cb_utob = function (c) {\n  var cc = c.charCodeAt(0);\n  return cc < 0x80\n    ? c\n    : cc < 0x800\n      ? fromCharCode(0xc0 | (cc >>> 6)) + fromCharCode(0x80 | (cc & 0x3f))\n      : fromCharCode(0xe0 | ((cc >>> 12) & 0x0f)) +\n        fromCharCode(0x80 | ((cc >>> 6) & 0x3f)) +\n        fromCharCode(0x80 | (cc & 0x3f));\n};\n\nvar utob = function (u) {\n  return u.replace(/[^\\x00-\\x7F]/g, cb_utob);\n};\n\nvar cb_encode = function (ccc) {\n  var padlen = [0, 2, 1][ccc.length % 3];\n  var ord =\n    (ccc.charCodeAt(0) << 16) |\n    ((ccc.length > 1 ? ccc.charCodeAt(1) : 0) << 8) |\n    (ccc.length > 2 ? ccc.charCodeAt(2) : 0);\n  var chars = [\n    b64chars.charAt(ord >>> 18),\n    b64chars.charAt((ord >>> 12) & 63),\n    padlen >= 2 ? '=' : b64chars.charAt((ord >>> 6) & 63),\n    padlen >= 1 ? '=' : b64chars.charAt(ord & 63),\n  ];\n  return chars.join('');\n};\n\nvar btoa =\n  global.btoa ||\n  function (b) {\n    return b.replace(/[\\s\\S]{1,3}/g, cb_encode);\n  };\n", "import TimedCallback from './timed_callback';\nimport { Delay, Scheduler, Canceller } from './scheduling';\n\nabstract class Timer {\n  protected clear: Canceller;\n  protected timer: number | void;\n\n  constructor(\n    set: Scheduler,\n    clear: Canceller,\n    delay: Delay,\n    callback: TimedCallback,\n  ) {\n    this.clear = clear;\n    this.timer = set(() => {\n      if (this.timer) {\n        this.timer = callback(this.timer);\n      }\n    }, delay);\n  }\n\n  /** Returns whether the timer is still running.\n   *\n   * @return {Boolean}\n   */\n  isRunning(): boolean {\n    return this.timer !== null;\n  }\n\n  /** Aborts a timer when it's running. */\n  ensureAborted() {\n    if (this.timer) {\n      this.clear(this.timer);\n      this.timer = null;\n    }\n  }\n}\n\nexport default Timer;\n", "import Timer from './abstract_timer';\nimport TimedCallback from './timed_callback';\nimport { Delay } from './scheduling';\n\n// We need to bind clear functions this way to avoid exceptions on IE8\nfunction clearTimeout(timer) {\n  global.clearTimeout(timer);\n}\nfunction clearInterval(timer) {\n  global.clearInterval(timer);\n}\n\n/** Cross-browser compatible one-off timer abstraction.\n *\n * @param {Number} delay\n * @param {Function} callback\n */\nexport class OneOffTimer extends Timer {\n  constructor(delay: Delay, callback: TimedCallback) {\n    super(setTimeout, clearTimeout, delay, function (timer) {\n      callback();\n      return null;\n    });\n  }\n}\n\n/** Cross-browser compatible periodic timer abstraction.\n *\n * @param {Number} delay\n * @param {Function} callback\n */\nexport class PeriodicTimer extends Timer {\n  constructor(delay: Delay, callback: TimedCallback) {\n    super(setInterval, clearInterval, delay, function (timer) {\n      callback();\n      return timer;\n    });\n  }\n}\n", "import * as Collections from './utils/collections';\nimport TimedCallback from './utils/timers/timed_callback';\nimport { OneOffTimer, PeriodicTimer } from './utils/timers';\n\nvar Util = {\n  now(): number {\n    if (Date.now) {\n      return Date.now();\n    } else {\n      return new Date().valueOf();\n    }\n  },\n\n  defer(callback: TimedCallback): OneOffTimer {\n    return new OneOffTimer(0, callback);\n  },\n\n  /** Builds a function that will proxy a method call to its first argument.\n   *\n   * Allows partial application of arguments, so additional arguments are\n   * prepended to the argument list.\n   *\n   * @param  {String} name method name\n   * @return {Function} proxy function\n   */\n  method(name: string, ...args: any[]): Function {\n    var boundArguments = Array.prototype.slice.call(arguments, 1);\n    return function (object) {\n      return object[name].apply(object, boundArguments.concat(arguments));\n    };\n  },\n};\n\nexport default Util;\n", "import base64encode from '../base64';\nimport Util from '../util';\n\n/** Merges multiple objects into the target argument.\n *\n * For properties that are plain Objects, performs a deep-merge. For the\n * rest it just copies the value of the property.\n *\n * To extend prototypes use it as following:\n *   Pusher.Util.extend(Target.prototype, Base.prototype)\n *\n * You can also use it to merge objects without altering them:\n *   Pusher.Util.extend({}, object1, object2)\n *\n * @param  {Object} target\n * @return {Object} the target argument\n */\nexport function extend<T>(target: any, ...sources: any[]): T {\n  for (var i = 0; i < sources.length; i++) {\n    var extensions = sources[i];\n    for (var property in extensions) {\n      if (\n        extensions[property] &&\n        extensions[property].constructor &&\n        extensions[property].constructor === Object\n      ) {\n        target[property] = extend(target[property] || {}, extensions[property]);\n      } else {\n        target[property] = extensions[property];\n      }\n    }\n  }\n  return target;\n}\n\nexport function stringify(): string {\n  var m = ['Pusher'];\n  for (var i = 0; i < arguments.length; i++) {\n    if (typeof arguments[i] === 'string') {\n      m.push(arguments[i]);\n    } else {\n      m.push(safeJSONStringify(arguments[i]));\n    }\n  }\n  return m.join(' : ');\n}\n\nexport function arrayIndexOf(array: any[], item: any): number {\n  // MSIE doesn't have array.indexOf\n  var nativeIndexOf = Array.prototype.indexOf;\n  if (array === null) {\n    return -1;\n  }\n  if (nativeIndexOf && array.indexOf === nativeIndexOf) {\n    return array.indexOf(item);\n  }\n  for (var i = 0, l = array.length; i < l; i++) {\n    if (array[i] === item) {\n      return i;\n    }\n  }\n  return -1;\n}\n\n/** Applies a function f to all properties of an object.\n *\n * Function f gets 3 arguments passed:\n * - element from the object\n * - key of the element\n * - reference to the object\n *\n * @param {Object} object\n * @param {Function} f\n */\nexport function objectApply(object: any, f: Function) {\n  for (var key in object) {\n    if (Object.prototype.hasOwnProperty.call(object, key)) {\n      f(object[key], key, object);\n    }\n  }\n}\n\n/** Return a list of objects own proerty keys\n *\n * @param {Object} object\n * @returns {Array}\n */\nexport function keys(object: any): string[] {\n  var keys = [];\n  objectApply(object, function (_, key) {\n    keys.push(key);\n  });\n  return keys;\n}\n\n/** Return a list of object's own property values\n *\n * @param {Object} object\n * @returns {Array}\n */\nexport function values(object: any): any[] {\n  var values = [];\n  objectApply(object, function (value) {\n    values.push(value);\n  });\n  return values;\n}\n\n/** Applies a function f to all elements of an array.\n *\n * Function f gets 3 arguments passed:\n * - element from the array\n * - index of the element\n * - reference to the array\n *\n * @param {Array} array\n * @param {Function} f\n */\nexport function apply(array: any[], f: Function, context?: any) {\n  for (var i = 0; i < array.length; i++) {\n    f.call(context || global, array[i], i, array);\n  }\n}\n\n/** Maps all elements of the array and returns the result.\n *\n * Function f gets 4 arguments passed:\n * - element from the array\n * - index of the element\n * - reference to the source array\n * - reference to the destination array\n *\n * @param {Array} array\n * @param {Function} f\n */\nexport function map(array: any[], f: Function): any[] {\n  var result = [];\n  for (var i = 0; i < array.length; i++) {\n    result.push(f(array[i], i, array, result));\n  }\n  return result;\n}\n\n/** Maps all elements of the object and returns the result.\n *\n * Function f gets 4 arguments passed:\n * - element from the object\n * - key of the element\n * - reference to the source object\n * - reference to the destination object\n *\n * @param {Object} object\n * @param {Function} f\n */\nexport function mapObject(object: any, f: Function): any {\n  var result = {};\n  objectApply(object, function (value, key) {\n    result[key] = f(value);\n  });\n  return result;\n}\n\n/** Filters elements of the array using a test function.\n *\n * Function test gets 4 arguments passed:\n * - element from the array\n * - index of the element\n * - reference to the source array\n * - reference to the destination array\n *\n * @param {Array} array\n * @param {Function} f\n */\nexport function filter(array: any[], test: Function): any[] {\n  test =\n    test ||\n    function (value) {\n      return !!value;\n    };\n\n  var result = [];\n  for (var i = 0; i < array.length; i++) {\n    if (test(array[i], i, array, result)) {\n      result.push(array[i]);\n    }\n  }\n  return result;\n}\n\n/** Filters properties of the object using a test function.\n *\n * Function test gets 4 arguments passed:\n * - element from the object\n * - key of the element\n * - reference to the source object\n * - reference to the destination object\n *\n * @param {Object} object\n * @param {Function} f\n */\nexport function filterObject(object: Object, test: Function) {\n  var result = {};\n  objectApply(object, function (value, key) {\n    if ((test && test(value, key, object, result)) || Boolean(value)) {\n      result[key] = value;\n    }\n  });\n  return result;\n}\n\n/** Flattens an object into a two-dimensional array.\n *\n * @param  {Object} object\n * @return {Array} resulting array of [key, value] pairs\n */\nexport function flatten(object: Object): any[] {\n  var result = [];\n  objectApply(object, function (value, key) {\n    result.push([key, value]);\n  });\n  return result;\n}\n\n/** Checks whether any element of the array passes the test.\n *\n * Function test gets 3 arguments passed:\n * - element from the array\n * - index of the element\n * - reference to the source array\n *\n * @param {Array} array\n * @param {Function} f\n */\nexport function any(array: any[], test: Function): boolean {\n  for (var i = 0; i < array.length; i++) {\n    if (test(array[i], i, array)) {\n      return true;\n    }\n  }\n  return false;\n}\n\n/** Checks whether all elements of the array pass the test.\n *\n * Function test gets 3 arguments passed:\n * - element from the array\n * - index of the element\n * - reference to the source array\n *\n * @param {Array} array\n * @param {Function} f\n */\nexport function all(array: any[], test: Function): boolean {\n  for (var i = 0; i < array.length; i++) {\n    if (!test(array[i], i, array)) {\n      return false;\n    }\n  }\n  return true;\n}\n\nexport function encodeParamsObject(data): string {\n  return mapObject(data, function (value) {\n    if (typeof value === 'object') {\n      value = safeJSONStringify(value);\n    }\n    return encodeURIComponent(base64encode(value.toString()));\n  });\n}\n\nexport function buildQueryString(data: any): string {\n  var params = filterObject(data, function (value) {\n    return value !== undefined;\n  });\n\n  var query = map(\n    flatten(encodeParamsObject(params)),\n    Util.method('join', '='),\n  ).join('&');\n\n  return query;\n}\n\n/**\n * See https://github.com/douglascrockford/JSON-js/blob/master/cycle.js\n *\n * Remove circular references from an object. Required for JSON.stringify in\n * React Native, which tends to blow up a lot.\n *\n * @param  {any} object\n * @return {any}        Decycled object\n */\nexport function decycleObject(object: any): any {\n  var objects = [],\n    paths = [];\n\n  return (function derez(value, path) {\n    var i, name, nu;\n\n    switch (typeof value) {\n      case 'object':\n        if (!value) {\n          return null;\n        }\n        for (i = 0; i < objects.length; i += 1) {\n          if (objects[i] === value) {\n            return { $ref: paths[i] };\n          }\n        }\n\n        objects.push(value);\n        paths.push(path);\n\n        if (Object.prototype.toString.apply(value) === '[object Array]') {\n          nu = [];\n          for (i = 0; i < value.length; i += 1) {\n            nu[i] = derez(value[i], path + '[' + i + ']');\n          }\n        } else {\n          nu = {};\n          for (name in value) {\n            if (Object.prototype.hasOwnProperty.call(value, name)) {\n              nu[name] = derez(\n                value[name],\n                path + '[' + JSON.stringify(name) + ']',\n              );\n            }\n          }\n        }\n        return nu;\n      case 'number':\n      case 'string':\n      case 'boolean':\n        return value;\n    }\n  })(object, '$');\n}\n\n/**\n * Provides a cross-browser and cross-platform way to safely stringify objects\n * into JSON. This is particularly necessary for ReactNative, where circular JSON\n * structures throw an exception.\n *\n * @param  {any}    source The object to stringify\n * @return {string}        The serialized output.\n */\nexport function safeJSONStringify(source: any): string {\n  try {\n    return JSON.stringify(source);\n  } catch (e) {\n    return JSON.stringify(decycleObject(source));\n  }\n}\n", "import {\n  ChannelAuthorizationOptions,\n  UserAuthenticationOptions,\n} from './auth/options';\nimport { AuthTransport } from './config';\n\nexport interface DefaultConfig {\n  VERSION: string;\n  PROTOCOL: number;\n  wsPort: number;\n  wssPort: number;\n  wsPath: string;\n  httpHost: string;\n  httpPort: number;\n  httpsPort: number;\n  httpPath: string;\n  stats_host: string;\n  authEndpoint: string;\n  authTransport: AuthTransport;\n  activityTimeout: number;\n  pongTimeout: number;\n  unavailableTimeout: number;\n  userAuthentication: UserAuthenticationOptions;\n  channelAuthorization: ChannelAuthorizationOptions;\n\n  cdn_http?: string;\n  cdn_https?: string;\n  dependency_suffix?: string;\n}\n\nvar Defaults: DefaultConfig = {\n  VERSION: VERSION,\n  PROTOCOL: 7,\n\n  wsPort: 80,\n  wssPort: 443,\n  wsPath: '',\n  // DEPRECATED: SockJS fallback parameters\n  httpHost: 'sockjs.pusher.com',\n  httpPort: 80,\n  httpsPort: 443,\n  httpPath: '/pusher',\n  // DEPRECATED: Stats\n  stats_host: 'stats.pusher.com',\n  // DEPRECATED: Other settings\n  authEndpoint: '/pusher/auth',\n  authTransport: 'ajax',\n  activityTimeout: 120000,\n  pongTimeout: 30000,\n  unavailableTimeout: 10000,\n  userAuthentication: {\n    endpoint: '/pusher/user-auth',\n    transport: 'ajax',\n  },\n  channelAuthorization: {\n    endpoint: '/pusher/auth',\n    transport: 'ajax',\n  },\n\n  // CDN configuration\n  cdn_http: CDN_HTTP,\n  cdn_https: CDN_HTTPS,\n  dependency_suffix: DEPENDENCY_SUFFIX,\n};\n\nexport default Defaults;\n", "import Defaults from '../defaults';\nimport { default as URLScheme, URLSchemeParams } from './url_scheme';\n\nfunction getGenericURL(\n  baseScheme: string,\n  params: URLSchemeParams,\n  path: string,\n): string {\n  var scheme = baseScheme + (params.useTLS ? 's' : '');\n  var host = params.useTLS ? params.hostTLS : params.hostNonTLS;\n  return scheme + '://' + host + path;\n}\n\nfunction getGenericPath(key: string, queryString?: string): string {\n  var path = '/app/' + key;\n  var query =\n    '?protocol=' +\n    Defaults.PROTOCOL +\n    '&client=js' +\n    '&version=' +\n    Defaults.VERSION +\n    (queryString ? '&' + queryString : '');\n  return path + query;\n}\n\nexport var ws: URLScheme = {\n  getInitial: function (key: string, params: URLSchemeParams): string {\n    var path = (params.httpPath || '') + getGenericPath(key, 'flash=false');\n    return getGenericURL('ws', params, path);\n  },\n};\n\nexport var http: URLScheme = {\n  getInitial: function (key: string, params: URLSchemeParams): string {\n    var path = (params.httpPath || '/pusher') + getGenericPath(key);\n    return getGenericURL('http', params, path);\n  },\n};\n\nexport var sockjs: URLScheme = {\n  getInitial: function (key: string, params: URLSchemeParams): string {\n    return getGenericURL('http', params, params.httpPath || '/pusher');\n  },\n  getPath: function (key: string, params: URLSchemeParams): string {\n    return getGenericPath(key);\n  },\n};\n", "import Callback from './callback';\nimport * as Collections from '../utils/collections';\nimport CallbackTable from './callback_table';\n\nexport default class CallbackRegistry {\n  _callbacks: CallbackTable;\n\n  constructor() {\n    this._callbacks = {};\n  }\n\n  get(name: string): Callback[] {\n    return this._callbacks[prefix(name)];\n  }\n\n  add(name: string, callback: Function, context: any) {\n    var prefixedEventName = prefix(name);\n    this._callbacks[prefixedEventName] =\n      this._callbacks[prefixedEventName] || [];\n    this._callbacks[prefixedEventName].push({\n      fn: callback,\n      context: context,\n    });\n  }\n\n  remove(name?: string, callback?: Function, context?: any) {\n    if (!name && !callback && !context) {\n      this._callbacks = {};\n      return;\n    }\n\n    var names = name ? [prefix(name)] : Collections.keys(this._callbacks);\n\n    if (callback || context) {\n      this.removeCallback(names, callback, context);\n    } else {\n      this.removeAllCallbacks(names);\n    }\n  }\n\n  private removeCallback(names: string[], callback: Function, context: any) {\n    Collections.apply(\n      names,\n      function (name) {\n        this._callbacks[name] = Collections.filter(\n          this._callbacks[name] || [],\n          function (binding) {\n            return (\n              (callback && callback !== binding.fn) ||\n              (context && context !== binding.context)\n            );\n          },\n        );\n        if (this._callbacks[name].length === 0) {\n          delete this._callbacks[name];\n        }\n      },\n      this,\n    );\n  }\n\n  private removeAllCallbacks(names: string[]) {\n    Collections.apply(\n      names,\n      function (name) {\n        delete this._callbacks[name];\n      },\n      this,\n    );\n  }\n}\n\nfunction prefix(name: string): string {\n  return '_' + name;\n}\n", "import * as Collections from '../utils/collections';\nimport Callback from './callback';\nimport Metadata from '../channels/metadata';\nimport CallbackRegistry from './callback_registry';\n\n/** Manages callback bindings and event emitting.\n *\n * @param Function failThrough called when no listeners are bound to an event\n */\nexport default class Dispatcher {\n  callbacks: CallbackRegistry;\n  global_callbacks: Function[];\n  failThrough: Function;\n\n  constructor(failThrough?: Function) {\n    this.callbacks = new CallbackRegistry();\n    this.global_callbacks = [];\n    this.failThrough = failThrough;\n  }\n\n  bind(eventName: string, callback: Function, context?: any) {\n    this.callbacks.add(eventName, callback, context);\n    return this;\n  }\n\n  bind_global(callback: Function) {\n    this.global_callbacks.push(callback);\n    return this;\n  }\n\n  unbind(eventName?: string, callback?: Function, context?: any) {\n    this.callbacks.remove(eventName, callback, context);\n    return this;\n  }\n\n  unbind_global(callback?: Function) {\n    if (!callback) {\n      this.global_callbacks = [];\n      return this;\n    }\n\n    this.global_callbacks = Collections.filter(\n      this.global_callbacks || [],\n      (c) => c !== callback,\n    );\n\n    return this;\n  }\n\n  unbind_all() {\n    this.unbind();\n    this.unbind_global();\n    return this;\n  }\n\n  emit(eventName: string, data?: any, metadata?: Metadata): Dispatcher {\n    for (var i = 0; i < this.global_callbacks.length; i++) {\n      this.global_callbacks[i](eventName, data);\n    }\n\n    var callbacks = this.callbacks.get(eventName);\n    var args = [];\n\n    if (metadata) {\n      // if there's a metadata argument, we need to call the callback with both\n      // data and metadata regardless of whether data is undefined\n      args.push(data, metadata);\n    } else if (data) {\n      // metadata is undefined, so we only need to call the callback with data\n      // if data exists\n      args.push(data);\n    }\n\n    if (callbacks && callbacks.length > 0) {\n      for (var i = 0; i < callbacks.length; i++) {\n        callbacks[i].fn.apply(callbacks[i].context || global, args);\n      }\n    } else if (this.failThrough) {\n      this.failThrough(eventName, data);\n    }\n\n    return this;\n  }\n}\n", "import { stringify } from './utils/collections';\nimport Pusher from './pusher';\n\nclass Logger {\n  debug(...args: any[]) {\n    this.log(this.globalLog, args);\n  }\n\n  warn(...args: any[]) {\n    this.log(this.globalLogWarn, args);\n  }\n\n  error(...args: any[]) {\n    this.log(this.globalLogError, args);\n  }\n\n  private globalLog = (message: string) => {\n    if (global.console && global.console.log) {\n      global.console.log(message);\n    }\n  };\n\n  private globalLogWarn(message: string) {\n    if (global.console && global.console.warn) {\n      global.console.warn(message);\n    } else {\n      this.globalLog(message);\n    }\n  }\n\n  private globalLogError(message: string) {\n    if (global.console && global.console.error) {\n      global.console.error(message);\n    } else {\n      this.globalLogWarn(message);\n    }\n  }\n\n  private log(\n    defaultLoggingFunction: (message: string) => void,\n    ...args: any[]\n  ) {\n    var message = stringify.apply(this, arguments);\n    if (Pusher.log) {\n      Pusher.log(message);\n    } else if (Pusher.logToConsole) {\n      const log = defaultLoggingFunction.bind(this);\n      log(message);\n    }\n  }\n}\n\nexport default new Logger();\n", "import Util from '../util';\nimport * as Collections from '../utils/collections';\nimport { default as EventsDispatcher } from '../events/dispatcher';\nimport Logger from '../logger';\nimport TransportHooks from './transport_hooks';\nimport Socket from '../socket';\nimport Runtime from 'runtime';\nimport Timeline from '../timeline/timeline';\nimport TransportConnectionOptions from './transport_connection_options';\n\n/** Provides universal API for transport connections.\n *\n * Transport connection is a low-level object that wraps a connection method\n * and exposes a simple evented interface for the connection state and\n * messaging. It does not implement Pusher-specific WebSocket protocol.\n *\n * Additionally, it fetches resources needed for transport to work and exposes\n * an interface for querying transport features.\n *\n * States:\n * - new - initial state after constructing the object\n * - initializing - during initialization phase, usually fetching resources\n * - intialized - ready to establish a connection\n * - connection - when connection is being established\n * - open - when connection ready to be used\n * - closed - after connection was closed be either side\n *\n * Emits:\n * - error - after the connection raised an error\n *\n * Options:\n * - useTLS - whether connection should be over TLS\n * - hostTLS - host to connect to when connection is over TLS\n * - hostNonTLS - host to connect to when connection is over TLS\n *\n * @param {String} key application key\n * @param {Object} options\n */\nexport default class TransportConnection extends EventsDispatcher {\n  hooks: TransportHooks;\n  name: string;\n  priority: number;\n  key: string;\n  options: TransportConnectionOptions;\n  state: string;\n  timeline: Timeline;\n  activityTimeout: number;\n  id: number;\n  socket: Socket;\n  beforeOpen: Function;\n  initialize: Function;\n\n  constructor(\n    hooks: TransportHooks,\n    name: string,\n    priority: number,\n    key: string,\n    options: TransportConnectionOptions,\n  ) {\n    super();\n    this.initialize = Runtime.transportConnectionInitializer;\n    this.hooks = hooks;\n    this.name = name;\n    this.priority = priority;\n    this.key = key;\n    this.options = options;\n\n    this.state = 'new';\n    this.timeline = options.timeline;\n    this.activityTimeout = options.activityTimeout;\n    this.id = this.timeline.generateUniqueID();\n  }\n\n  /** Checks whether the transport handles activity checks by itself.\n   *\n   * @return {Boolean}\n   */\n  handlesActivityChecks(): boolean {\n    return Boolean(this.hooks.handlesActivityChecks);\n  }\n\n  /** Checks whether the transport supports the ping/pong API.\n   *\n   * @return {Boolean}\n   */\n  supportsPing(): boolean {\n    return Boolean(this.hooks.supportsPing);\n  }\n\n  /** Tries to establish a connection.\n   *\n   * @returns {Boolean} false if transport is in invalid state\n   */\n  connect(): boolean {\n    if (this.socket || this.state !== 'initialized') {\n      return false;\n    }\n\n    var url = this.hooks.urls.getInitial(this.key, this.options);\n    try {\n      this.socket = this.hooks.getSocket(url, this.options);\n    } catch (e) {\n      Util.defer(() => {\n        this.onError(e);\n        this.changeState('closed');\n      });\n      return false;\n    }\n\n    this.bindListeners();\n\n    Logger.debug('Connecting', { transport: this.name, url });\n    this.changeState('connecting');\n    return true;\n  }\n\n  /** Closes the connection.\n   *\n   * @return {Boolean} true if there was a connection to close\n   */\n  close(): boolean {\n    if (this.socket) {\n      this.socket.close();\n      return true;\n    } else {\n      return false;\n    }\n  }\n\n  /** Sends data over the open connection.\n   *\n   * @param {String} data\n   * @return {Boolean} true only when in the \"open\" state\n   */\n  send(data: any): boolean {\n    if (this.state === 'open') {\n      // Workaround for MobileSafari bug (see https://gist.github.com/2052006)\n      Util.defer(() => {\n        if (this.socket) {\n          this.socket.send(data);\n        }\n      });\n      return true;\n    } else {\n      return false;\n    }\n  }\n\n  /** Sends a ping if the connection is open and transport supports it. */\n  ping() {\n    if (this.state === 'open' && this.supportsPing()) {\n      this.socket.ping();\n    }\n  }\n\n  private onOpen() {\n    if (this.hooks.beforeOpen) {\n      this.hooks.beforeOpen(\n        this.socket,\n        this.hooks.urls.getPath(this.key, this.options),\n      );\n    }\n    this.changeState('open');\n    this.socket.onopen = undefined;\n  }\n\n  private onError(error) {\n    this.emit('error', { type: 'WebSocketError', error: error });\n    this.timeline.error(this.buildTimelineMessage({ error: error.toString() }));\n  }\n\n  private onClose(closeEvent?: any) {\n    if (closeEvent) {\n      this.changeState('closed', {\n        code: closeEvent.code,\n        reason: closeEvent.reason,\n        wasClean: closeEvent.wasClean,\n      });\n    } else {\n      this.changeState('closed');\n    }\n    this.unbindListeners();\n    this.socket = undefined;\n  }\n\n  private onMessage(message) {\n    this.emit('message', message);\n  }\n\n  private onActivity() {\n    this.emit('activity');\n  }\n\n  private bindListeners() {\n    this.socket.onopen = () => {\n      this.onOpen();\n    };\n    this.socket.onerror = (error) => {\n      this.onError(error);\n    };\n    this.socket.onclose = (closeEvent) => {\n      this.onClose(closeEvent);\n    };\n    this.socket.onmessage = (message) => {\n      this.onMessage(message);\n    };\n\n    if (this.supportsPing()) {\n      this.socket.onactivity = () => {\n        this.onActivity();\n      };\n    }\n  }\n\n  private unbindListeners() {\n    if (this.socket) {\n      this.socket.onopen = undefined;\n      this.socket.onerror = undefined;\n      this.socket.onclose = undefined;\n      this.socket.onmessage = undefined;\n      if (this.supportsPing()) {\n        this.socket.onactivity = undefined;\n      }\n    }\n  }\n\n  private changeState(state: string, params?: any) {\n    this.state = state;\n    this.timeline.info(\n      this.buildTimelineMessage({\n        state: state,\n        params: params,\n      }),\n    );\n    this.emit(state, params);\n  }\n\n  buildTimelineMessage(message): any {\n    return Collections.extend({ cid: this.id }, message);\n  }\n}\n", "import Factory from '../utils/factory';\nimport TransportHooks from './transport_hooks';\nimport TransportConnection from './transport_connection';\nimport TransportConnectionOptions from './transport_connection_options';\n\n/** Provides interface for transport connection instantiation.\n *\n * Takes transport-specific hooks as the only argument, which allow checking\n * for transport support and creating its connections.\n *\n * Supported hooks: * - file - the name of the file to be fetched during initialization\n * - urls - URL scheme to be used by transport\n * - handlesActivityCheck - true when the transport handles activity checks\n * - supportsPing - true when the transport has a ping/activity API\n * - isSupported - tells whether the transport is supported in the environment\n * - getSocket - creates a WebSocket-compatible transport socket\n *\n * See transports.js for specific implementations.\n *\n * @param {Object} hooks object containing all needed transport hooks\n */\nexport default class Transport {\n  hooks: TransportHooks;\n\n  constructor(hooks: TransportHooks) {\n    this.hooks = hooks;\n  }\n\n  /** Returns whether the transport is supported in the environment.\n   *\n   * @param {Object} envronment te environment details (encryption, settings)\n   * @returns {Boolean} true when the transport is supported\n   */\n  isSupported(environment: any): boolean {\n    return this.hooks.isSupported(environment);\n  }\n\n  /** Creates a transport connection.\n   *\n   * @param {String} name\n   * @param {Number} priority\n   * @param {String} key the application key\n   * @param {Object} options\n   * @returns {TransportConnection}\n   */\n  createConnection(\n    name: string,\n    priority: number,\n    key: string,\n    options: any,\n  ): TransportConnection {\n    return new TransportConnection(this.hooks, name, priority, key, options);\n  }\n}\n", "import * as URLSchemes from 'core/transports/url_schemes';\nimport URLScheme from 'core/transports/url_scheme';\nimport Transport from 'core/transports/transport';\nimport Util from 'core/util';\nimport * as Collections from 'core/utils/collections';\nimport TransportHooks from 'core/transports/transport_hooks';\nimport TransportsTable from 'core/transports/transports_table';\nimport Runtime from 'runtime';\n\n/** WebSocket transport.\n *\n * Uses native WebSocket implementation, including MozWebSocket supported by\n * earlier Firefox versions.\n */\nvar WSTransport = new Transport(<TransportHooks>{\n  urls: URLSchemes.ws,\n  handlesActivityChecks: false,\n  supportsPing: false,\n\n  isInitialized: function () {\n    return Boolean(Runtime.getWebSocketAPI());\n  },\n  isSupported: function (): boolean {\n    return Boolean(Runtime.getWebSocketAPI());\n  },\n  getSocket: function (url) {\n    return Runtime.createWebSocket(url);\n  },\n});\n\nvar httpConfiguration = {\n  urls: URLSchemes.http,\n  handlesActivityChecks: false,\n  supportsPing: true,\n  isInitialized: function () {\n    return true;\n  },\n};\n\nexport var streamingConfiguration = Collections.extend(\n  {\n    getSocket: function (url) {\n      return Runtime.HTTPFactory.createStreamingSocket(url);\n    },\n  },\n  httpConfiguration,\n);\nexport var pollingConfiguration = Collections.extend(\n  {\n    getSocket: function (url) {\n      return Runtime.HTTPFactory.createPollingSocket(url);\n    },\n  },\n  httpConfiguration,\n);\n\nvar xhrConfiguration = {\n  isSupported: function (): boolean {\n    return Runtime.isXHRSupported();\n  },\n};\n\n/** HTTP streaming transport using CORS-enabled XMLHttpRequest. */\nvar XHRStreamingTransport = new Transport(\n  <TransportHooks>(\n    Collections.extend({}, streamingConfiguration, xhrConfiguration)\n  ),\n);\n\n/** HTTP long-polling transport using CORS-enabled XMLHttpRequest. */\nvar XHRPollingTransport = new Transport(\n  <TransportHooks>(\n    Collections.extend({}, pollingConfiguration, xhrConfiguration)\n  ),\n);\n\nvar Transports: TransportsTable = {\n  ws: WSTransport,\n  xhr_streaming: XHRStreamingTransport,\n  xhr_polling: XHRPollingTransport,\n};\n\nexport default Transports;\n", "import Util from '../util';\nimport * as Collections from '../utils/collections';\nimport TransportManager from './transport_manager';\nimport TransportConnection from './transport_connection';\nimport Transport from './transport';\nimport PingDelayOptions from './ping_delay_options';\n\n/** Creates transport connections monitored by a transport manager.\n *\n * When a transport is closed, it might mean the environment does not support\n * it. It's possible that messages get stuck in an intermediate buffer or\n * proxies terminate inactive connections. To combat these problems,\n * assistants monitor the connection lifetime, report unclean exits and\n * adjust ping timeouts to keep the connection active. The decision to disable\n * a transport is the manager's responsibility.\n *\n * @param {TransportManager} manager\n * @param {TransportConnection} transport\n * @param {Object} options\n */\nexport default class AssistantToTheTransportManager {\n  manager: TransportManager;\n  transport: Transport;\n  minPingDelay: number;\n  maxPingDelay: number;\n  pingDelay: number;\n\n  constructor(\n    manager: TransportManager,\n    transport: Transport,\n    options: PingDelayOptions,\n  ) {\n    this.manager = manager;\n    this.transport = transport;\n    this.minPingDelay = options.minPingDelay;\n    this.maxPingDelay = options.maxPingDelay;\n    this.pingDelay = undefined;\n  }\n\n  /** Creates a transport connection.\n   *\n   * This function has the same API as Transport#createConnection.\n   *\n   * @param {String} name\n   * @param {Number} priority\n   * @param {String} key the application key\n   * @param {Object} options\n   * @returns {TransportConnection}\n   */\n  createConnection(\n    name: string,\n    priority: number,\n    key: string,\n    options: Object,\n  ): TransportConnection {\n    options = Collections.extend({}, options, {\n      activityTimeout: this.pingDelay,\n    });\n    var connection = this.transport.createConnection(\n      name,\n      priority,\n      key,\n      options,\n    );\n\n    var openTimestamp = null;\n\n    var onOpen = function () {\n      connection.unbind('open', onOpen);\n      connection.bind('closed', onClosed);\n      openTimestamp = Util.now();\n    };\n    var onClosed = (closeEvent) => {\n      connection.unbind('closed', onClosed);\n\n      if (closeEvent.code === 1002 || closeEvent.code === 1003) {\n        // we don't want to use transports not obeying the protocol\n        this.manager.reportDeath();\n      } else if (!closeEvent.wasClean && openTimestamp) {\n        // report deaths only for short-living transport\n        var lifespan = Util.now() - openTimestamp;\n        if (lifespan < 2 * this.maxPingDelay) {\n          this.manager.reportDeath();\n          this.pingDelay = Math.max(lifespan / 2, this.minPingDelay);\n        }\n      }\n    };\n\n    connection.bind('open', onOpen);\n    return connection;\n  }\n\n  /** Returns whether the transport is supported in the environment.\n   *\n   * This function has the same API as Transport#isSupported. Might return false\n   * when the manager decides to kill the transport.\n   *\n   * @param {Object} environment the environment details (encryption, settings)\n   * @returns {Boolean} true when the transport is supported\n   */\n  isSupported(environment: string): boolean {\n    return this.manager.isAlive() && this.transport.isSupported(environment);\n  }\n}\n", "import Action from './action';\nimport { PusherEvent } from './message-types';\n/**\n * Provides functions for handling Pusher protocol-specific messages.\n */\n\nconst Protocol = {\n  /**\n   * Decodes a message in a Pusher format.\n   *\n   * The MessageEvent we receive from the transport should contain a pusher event\n   * (https://pusher.com/docs/pusher_protocol#events) serialized as JSO<PERSON> in the\n   * data field\n   *\n   * The pusher event may contain a data field too, and it may also be\n   * serialised as JSON\n   *\n   * Throws errors when messages are not parse'able.\n   *\n   * @param  {MessageEvent} messageEvent\n   * @return {PusherEvent}\n   */\n  decodeMessage: function (messageEvent: MessageEvent): PusherEvent {\n    try {\n      var messageData = JSON.parse(messageEvent.data);\n      var pusherEventData = messageData.data;\n      if (typeof pusherEventData === 'string') {\n        try {\n          pusherEventData = JSON.parse(messageData.data);\n        } catch (e) {}\n      }\n      var pusherEvent: PusherEvent = {\n        event: messageData.event,\n        channel: messageData.channel,\n        data: pusherEventData,\n      };\n      if (messageData.user_id) {\n        pusherEvent.user_id = messageData.user_id;\n      }\n      return pusherEvent;\n    } catch (e) {\n      throw { type: 'MessageParseError', error: e, data: messageEvent.data };\n    }\n  },\n\n  /**\n   * Encodes a message to be sent.\n   *\n   * @param  {PusherEvent} event\n   * @return {String}\n   */\n  encodeMessage: function (event: PusherEvent): string {\n    return JSON.stringify(event);\n  },\n\n  /**\n   * Processes a handshake message and returns appropriate actions.\n   *\n   * Returns an object with an 'action' and other action-specific properties.\n   *\n   * There are three outcomes when calling this function. First is a successful\n   * connection attempt, when pusher:connection_established is received, which\n   * results in a 'connected' action with an 'id' property. When passed a\n   * pusher:error event, it returns a result with action appropriate to the\n   * close code and an error. Otherwise, it raises an exception.\n   *\n   * @param {String} message\n   * @result Object\n   */\n  processHandshake: function (messageEvent: MessageEvent): Action {\n    var message = Protocol.decodeMessage(messageEvent);\n\n    if (message.event === 'pusher:connection_established') {\n      if (!message.data.activity_timeout) {\n        throw 'No activity timeout specified in handshake';\n      }\n      return {\n        action: 'connected',\n        id: message.data.socket_id,\n        activityTimeout: message.data.activity_timeout * 1000,\n      };\n    } else if (message.event === 'pusher:error') {\n      // From protocol 6 close codes are sent only once, so this only\n      // happens when connection does not support close codes\n      return {\n        action: this.getCloseAction(message.data),\n        error: this.getCloseError(message.data),\n      };\n    } else {\n      throw 'Invalid handshake';\n    }\n  },\n\n  /**\n   * Dispatches the close event and returns an appropriate action name.\n   *\n   * See:\n   * 1. https://developer.mozilla.org/en-US/docs/WebSockets/WebSockets_reference/CloseEvent\n   * 2. http://pusher.com/docs/pusher_protocol\n   *\n   * @param  {CloseEvent} closeEvent\n   * @return {String} close action name\n   */\n  getCloseAction: function (closeEvent): string {\n    if (closeEvent.code < 4000) {\n      // ignore 1000 CLOSE_NORMAL, 1001 CLOSE_GOING_AWAY,\n      //        1005 CLOSE_NO_STATUS, 1006 CLOSE_ABNORMAL\n      // ignore 1007...3999\n      // handle 1002 CLOSE_PROTOCOL_ERROR, 1003 CLOSE_UNSUPPORTED,\n      //        1004 CLOSE_TOO_LARGE\n      if (closeEvent.code >= 1002 && closeEvent.code <= 1004) {\n        return 'backoff';\n      } else {\n        return null;\n      }\n    } else if (closeEvent.code === 4000) {\n      return 'tls_only';\n    } else if (closeEvent.code < 4100) {\n      return 'refused';\n    } else if (closeEvent.code < 4200) {\n      return 'backoff';\n    } else if (closeEvent.code < 4300) {\n      return 'retry';\n    } else {\n      // unknown error\n      return 'refused';\n    }\n  },\n\n  /**\n   * Returns an error or null basing on the close event.\n   *\n   * Null is returned when connection was closed cleanly. Otherwise, an object\n   * with error details is returned.\n   *\n   * @param  {CloseEvent} closeEvent\n   * @return {Object} error object\n   */\n  getCloseError: function (closeEvent): any {\n    if (closeEvent.code !== 1000 && closeEvent.code !== 1001) {\n      return {\n        type: 'PusherError',\n        data: {\n          code: closeEvent.code,\n          message: closeEvent.reason || closeEvent.message,\n        },\n      };\n    } else {\n      return null;\n    }\n  },\n};\n\nexport default Protocol;\n", "import * as Collections from '../utils/collections';\nimport { default as EventsDispatcher } from '../events/dispatcher';\nimport Protocol from './protocol/protocol';\nimport { PusherEvent } from './protocol/message-types';\nimport Logger from '../logger';\nimport TransportConnection from '../transports/transport_connection';\nimport Socket from '../socket';\n/**\n * Provides Pusher protocol interface for transports.\n *\n * Emits following events:\n * - message - on received messages\n * - ping - on ping requests\n * - pong - on pong responses\n * - error - when the transport emits an error\n * - closed - after closing the transport\n *\n * It also emits more events when connection closes with a code.\n * See Protocol.getCloseAction to get more details.\n *\n * @param {Number} id\n * @param {AbstractTransport} transport\n */\nexport default class Connection extends EventsDispatcher implements Socket {\n  id: string;\n  transport: TransportConnection;\n  activityTimeout: number;\n\n  constructor(id: string, transport: TransportConnection) {\n    super();\n    this.id = id;\n    this.transport = transport;\n    this.activityTimeout = transport.activityTimeout;\n    this.bindListeners();\n  }\n\n  /** Returns whether used transport handles activity checks by itself\n   *\n   * @returns {Boolean} true if activity checks are handled by the transport\n   */\n  handlesActivityChecks() {\n    return this.transport.handlesActivityChecks();\n  }\n\n  /** Sends raw data.\n   *\n   * @param {String} data\n   */\n  send(data: any): boolean {\n    return this.transport.send(data);\n  }\n\n  /** Sends an event.\n   *\n   * @param {String} name\n   * @param {String} data\n   * @param {String} [channel]\n   * @returns {Boolean} whether message was sent or not\n   */\n  send_event(name: string, data: any, channel?: string): boolean {\n    var event: PusherEvent = { event: name, data: data };\n    if (channel) {\n      event.channel = channel;\n    }\n    Logger.debug('Event sent', event);\n    return this.send(Protocol.encodeMessage(event));\n  }\n\n  /** Sends a ping message to the server.\n   *\n   * Basing on the underlying transport, it might send either transport's\n   * protocol-specific ping or pusher:ping event.\n   */\n  ping() {\n    if (this.transport.supportsPing()) {\n      this.transport.ping();\n    } else {\n      this.send_event('pusher:ping', {});\n    }\n  }\n\n  /** Closes the connection. */\n  close() {\n    this.transport.close();\n  }\n\n  private bindListeners() {\n    var listeners = {\n      message: (messageEvent: MessageEvent) => {\n        var pusherEvent;\n        try {\n          pusherEvent = Protocol.decodeMessage(messageEvent);\n        } catch (e) {\n          this.emit('error', {\n            type: 'MessageParseError',\n            error: e,\n            data: messageEvent.data,\n          });\n        }\n\n        if (pusherEvent !== undefined) {\n          Logger.debug('Event recd', pusherEvent);\n\n          switch (pusherEvent.event) {\n            case 'pusher:error':\n              this.emit('error', {\n                type: 'PusherError',\n                data: pusherEvent.data,\n              });\n              break;\n            case 'pusher:ping':\n              this.emit('ping');\n              break;\n            case 'pusher:pong':\n              this.emit('pong');\n              break;\n          }\n          this.emit('message', pusherEvent);\n        }\n      },\n      activity: () => {\n        this.emit('activity');\n      },\n      error: (error) => {\n        this.emit('error', error);\n      },\n      closed: (closeEvent) => {\n        unbindListeners();\n\n        if (closeEvent && closeEvent.code) {\n          this.handleCloseEvent(closeEvent);\n        }\n\n        this.transport = null;\n        this.emit('closed');\n      },\n    };\n\n    var unbindListeners = () => {\n      Collections.objectApply(listeners, (listener, event) => {\n        this.transport.unbind(event, listener);\n      });\n    };\n\n    Collections.objectApply(listeners, (listener, event) => {\n      this.transport.bind(event, listener);\n    });\n  }\n\n  private handleCloseEvent(closeEvent: any) {\n    var action = Protocol.getCloseAction(closeEvent);\n    var error = Protocol.getCloseError(closeEvent);\n    if (error) {\n      this.emit('error', error);\n    }\n    if (action) {\n      this.emit(action, { action: action, error: error });\n    }\n  }\n}\n", "import Util from '../../util';\nimport * as Collections from '../../utils/collections';\nimport Protocol from '../protocol/protocol';\nimport Connection from '../connection';\nimport TransportConnection from '../../transports/transport_connection';\nimport HandshakePayload from './handshake_payload';\n\n/**\n * Handles Pusher protocol handshakes for transports.\n *\n * Calls back with a result object after handshake is completed. Results\n * always have two fields:\n * - action - string describing action to be taken after the handshake\n * - transport - the transport object passed to the constructor\n *\n * Different actions can set different additional properties on the result.\n * In the case of 'connected' action, there will be a 'connection' property\n * containing a Connection object for the transport. Other actions should\n * carry an 'error' property.\n *\n * @param {AbstractTransport} transport\n * @param {Function} callback\n */\nexport default class Handshake {\n  transport: TransportConnection;\n  callback: (HandshakePayload) => void;\n  onMessage: Function;\n  onClosed: Function;\n\n  constructor(\n    transport: TransportConnection,\n    callback: (HandshakePayload) => void,\n  ) {\n    this.transport = transport;\n    this.callback = callback;\n    this.bindListeners();\n  }\n\n  close() {\n    this.unbindListeners();\n    this.transport.close();\n  }\n\n  private bindListeners() {\n    this.onMessage = (m) => {\n      this.unbindListeners();\n\n      var result;\n      try {\n        result = Protocol.processHandshake(m);\n      } catch (e) {\n        this.finish('error', { error: e });\n        this.transport.close();\n        return;\n      }\n\n      if (result.action === 'connected') {\n        this.finish('connected', {\n          connection: new Connection(result.id, this.transport),\n          activityTimeout: result.activityTimeout,\n        });\n      } else {\n        this.finish(result.action, { error: result.error });\n        this.transport.close();\n      }\n    };\n\n    this.onClosed = (closeEvent) => {\n      this.unbindListeners();\n\n      var action = Protocol.getCloseAction(closeEvent) || 'backoff';\n      var error = Protocol.getCloseError(closeEvent);\n      this.finish(action, { error: error });\n    };\n\n    this.transport.bind('message', this.onMessage);\n    this.transport.bind('closed', this.onClosed);\n  }\n\n  private unbindListeners() {\n    this.transport.unbind('message', this.onMessage);\n    this.transport.unbind('closed', this.onClosed);\n  }\n\n  private finish(action: string, params: any) {\n    this.callback(\n      Collections.extend({ transport: this.transport, action: action }, params),\n    );\n  }\n}\n", "import * as Collections from '../utils/collections';\nimport Util from '../util';\nimport base64encode from '../base64';\nimport Timeline from './timeline';\nimport Runtime from 'runtime';\n\nexport interface TimelineSenderOptions {\n  host?: string;\n  port?: number;\n  path?: string;\n}\n\nexport default class TimelineSender {\n  timeline: Timeline;\n  options: TimelineSenderOptions;\n  host: string;\n\n  constructor(timeline: Timeline, options: TimelineSenderOptions) {\n    this.timeline = timeline;\n    this.options = options || {};\n  }\n\n  send(useTLS: boolean, callback?: Function) {\n    if (this.timeline.isEmpty()) {\n      return;\n    }\n\n    this.timeline.send(\n      Runtime.TimelineTransport.getAgent(this, useTLS),\n      callback,\n    );\n  }\n}\n", "/** Error classes used throughout the library. */\n// https://github.com/Microsoft/TypeScript-wiki/blob/master/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\nexport class BadEventName extends Error {\n  constructor(msg?: string) {\n    super(msg);\n\n    Object.setPrototypeOf(this, new.target.prototype);\n  }\n}\n\nexport class BadChannelName extends Error {\n  constructor(msg?: string) {\n    super(msg);\n\n    Object.setPrototypeOf(this, new.target.prototype);\n  }\n}\n\nexport class RequestTimedOut extends Error {\n  constructor(msg?: string) {\n    super(msg);\n\n    Object.setPrototypeOf(this, new.target.prototype);\n  }\n}\nexport class TransportPriorityTooLow extends Error {\n  constructor(msg?: string) {\n    super(msg);\n\n    Object.setPrototypeOf(this, new.target.prototype);\n  }\n}\nexport class TransportClosed extends Error {\n  constructor(msg?: string) {\n    super(msg);\n\n    Object.setPrototypeOf(this, new.target.prototype);\n  }\n}\nexport class UnsupportedFeature extends Error {\n  constructor(msg?: string) {\n    super(msg);\n\n    Object.setPrototypeOf(this, new.target.prototype);\n  }\n}\nexport class UnsupportedTransport extends Error {\n  constructor(msg?: string) {\n    super(msg);\n\n    Object.setPrototypeOf(this, new.target.prototype);\n  }\n}\nexport class UnsupportedStrategy extends Error {\n  constructor(msg?: string) {\n    super(msg);\n\n    Object.setPrototypeOf(this, new.target.prototype);\n  }\n}\nexport class HTTPAuthError extends Error {\n  status: number;\n  constructor(status: number, msg?: string) {\n    super(msg);\n    this.status = status;\n\n    Object.setPrototypeOf(this, new.target.prototype);\n  }\n}\n", "/**\n * A place to store help URLs for error messages etc\n */\n\nconst urlStore = {\n  baseUrl: 'https://pusher.com',\n  urls: {\n    authenticationEndpoint: {\n      path: '/docs/channels/server_api/authenticating_users',\n    },\n    authorizationEndpoint: {\n      path: '/docs/channels/server_api/authorizing-users/',\n    },\n    javascriptQuickStart: {\n      path: '/docs/javascript_quick_start',\n    },\n    triggeringClientEvents: {\n      path: '/docs/client_api_guide/client_events#trigger-events',\n    },\n    encryptedChannelSupport: {\n      fullUrl:\n        'https://github.com/pusher/pusher-js/tree/cc491015371a4bde5743d1c87a0fbac0feb53195#encrypted-channel-support',\n    },\n  },\n};\n\n/** Builds a consistent string with links to pusher documentation\n *\n * @param {string} key - relevant key in the url_store.urls object\n * @return {string} suffix string to append to log message\n */\nconst buildLogSuffix = function (key: string): string {\n  const urlPrefix = 'See:';\n  const urlObj = urlStore.urls[key];\n  if (!urlObj) return '';\n\n  let url;\n  if (urlObj.fullUrl) {\n    url = urlObj.fullUrl;\n  } else if (urlObj.path) {\n    url = urlStore.baseUrl + urlObj.path;\n  }\n\n  if (!url) return '';\n  return `${urlPrefix} ${url}`;\n};\n\nexport default { buildLogSuffix };\n", "import { default as EventsDispatcher } from '../events/dispatcher';\nimport * as Errors from '../errors';\nimport Logger from '../logger';\nimport Pusher from '../pusher';\nimport { PusherEvent } from '../connection/protocol/message-types';\nimport Metada<PERSON> from './metadata';\nimport UrlStore from '../utils/url_store';\nimport {\n  ChannelAuthorizationData,\n  ChannelAuthorizationCallback,\n} from '../auth/options';\nimport { HTTPAuthError } from '../errors';\n\n/** Provides base public channel interface with an event emitter.\n *\n * Emits:\n * - pusher:subscription_succeeded - after subscribing successfully\n * - other non-internal events\n *\n * @param {String} name\n * @param {Pusher} pusher\n */\nexport default class Channel extends EventsDispatcher {\n  name: string;\n  pusher: Pusher;\n  subscribed: boolean;\n  subscriptionPending: boolean;\n  subscriptionCancelled: boolean;\n  subscriptionCount: null;\n\n  constructor(name: string, pusher: Pusher) {\n    super(function (event, data) {\n      Logger.debug('No callbacks on ' + name + ' for ' + event);\n    });\n\n    this.name = name;\n    this.pusher = pusher;\n    this.subscribed = false;\n    this.subscriptionPending = false;\n    this.subscriptionCancelled = false;\n  }\n\n  /** Skips authorization, since public channels don't require it.\n   *\n   * @param {Function} callback\n   */\n  authorize(socketId: string, callback: ChannelAuthorizationCallback) {\n    return callback(null, { auth: '' });\n  }\n\n  /** Triggers an event */\n  trigger(event: string, data: any) {\n    if (event.indexOf('client-') !== 0) {\n      throw new Errors.BadEventName(\n        \"Event '\" + event + \"' does not start with 'client-'\",\n      );\n    }\n    if (!this.subscribed) {\n      var suffix = UrlStore.buildLogSuffix('triggeringClientEvents');\n      Logger.warn(\n        `Client event triggered before channel 'subscription_succeeded' event . ${suffix}`,\n      );\n    }\n    return this.pusher.send_event(event, data, this.name);\n  }\n\n  /** Signals disconnection to the channel. For internal use only. */\n  disconnect() {\n    this.subscribed = false;\n    this.subscriptionPending = false;\n  }\n\n  /** Handles a PusherEvent. For internal use only.\n   *\n   * @param {PusherEvent} event\n   */\n  handleEvent(event: PusherEvent) {\n    var eventName = event.event;\n    var data = event.data;\n    if (eventName === 'pusher_internal:subscription_succeeded') {\n      this.handleSubscriptionSucceededEvent(event);\n    } else if (eventName === 'pusher_internal:subscription_count') {\n      this.handleSubscriptionCountEvent(event);\n    } else if (eventName.indexOf('pusher_internal:') !== 0) {\n      var metadata: Metadata = {};\n      this.emit(eventName, data, metadata);\n    }\n  }\n\n  handleSubscriptionSucceededEvent(event: PusherEvent) {\n    this.subscriptionPending = false;\n    this.subscribed = true;\n    if (this.subscriptionCancelled) {\n      this.pusher.unsubscribe(this.name);\n    } else {\n      this.emit('pusher:subscription_succeeded', event.data);\n    }\n  }\n\n  handleSubscriptionCountEvent(event: PusherEvent) {\n    if (event.data.subscription_count) {\n      this.subscriptionCount = event.data.subscription_count;\n    }\n\n    this.emit('pusher:subscription_count', event.data);\n  }\n\n  /** Sends a subscription request. For internal use only. */\n  subscribe() {\n    if (this.subscribed) {\n      return;\n    }\n    this.subscriptionPending = true;\n    this.subscriptionCancelled = false;\n    this.authorize(\n      this.pusher.connection.socket_id,\n      (error: Error | null, data: ChannelAuthorizationData) => {\n        if (error) {\n          this.subscriptionPending = false;\n          // Why not bind to 'pusher:subscription_error' a level up, and log there?\n          // Binding to this event would cause the warning about no callbacks being\n          // bound (see constructor) to be suppressed, that's not what we want.\n          Logger.error(error.toString());\n          this.emit(\n            'pusher:subscription_error',\n            Object.assign(\n              {},\n              {\n                type: 'AuthError',\n                error: error.message,\n              },\n              error instanceof HTTPAuthError ? { status: error.status } : {},\n            ),\n          );\n        } else {\n          this.pusher.send_event('pusher:subscribe', {\n            auth: data.auth,\n            channel_data: data.channel_data,\n            channel: this.name,\n          });\n        }\n      },\n    );\n  }\n\n  /** Sends an unsubscription request. For internal use only. */\n  unsubscribe() {\n    this.subscribed = false;\n    this.pusher.send_event('pusher:unsubscribe', {\n      channel: this.name,\n    });\n  }\n\n  /** Cancels an in progress subscription. For internal use only. */\n  cancelSubscription() {\n    this.subscriptionCancelled = true;\n  }\n\n  /** Reinstates an in progress subscripiton. For internal use only. */\n  reinstateSubscription() {\n    this.subscriptionCancelled = false;\n  }\n}\n", "import Factory from '../utils/factory';\nimport Channel from './channel';\nimport { ChannelAuthorizationCallback } from '../auth/options';\n\n/** Extends public channels to provide private channel interface.\n *\n * @param {String} name\n * @param {Pusher} pusher\n */\nexport default class PrivateChannel extends Channel {\n  /** Authorizes the connection to use the channel.\n   *\n   * @param  {String} socketId\n   * @param  {Function} callback\n   */\n  authorize(socketId: string, callback: ChannelAuthorizationCallback) {\n    return this.pusher.config.channelAuthorizer(\n      {\n        channelName: this.name,\n        socketId: socketId,\n      },\n      callback,\n    );\n  }\n}\n", "import * as Collections from '../utils/collections';\n\n/** Represents a collection of members of a presence channel. */\nexport default class Members {\n  members: any;\n  count: number;\n  myID: any;\n  me: any;\n\n  constructor() {\n    this.reset();\n  }\n\n  /** Returns member's info for given id.\n   *\n   * Resulting object containts two fields - id and info.\n   *\n   * @param {Number} id\n   * @return {Object} member's info or null\n   */\n  get(id: string): any {\n    if (Object.prototype.hasOwnProperty.call(this.members, id)) {\n      return {\n        id: id,\n        info: this.members[id],\n      };\n    } else {\n      return null;\n    }\n  }\n\n  /** Calls back for each member in unspecified order.\n   *\n   * @param  {Function} callback\n   */\n  each(callback: Function) {\n    Collections.objectApply(this.members, (member, id) => {\n      callback(this.get(id));\n    });\n  }\n\n  /** Updates the id for connected member. For internal use only. */\n  setMyID(id: string) {\n    this.myID = id;\n  }\n\n  /** Handles subscription data. For internal use only. */\n  onSubscription(subscriptionData: any) {\n    this.members = subscriptionData.presence.hash;\n    this.count = subscriptionData.presence.count;\n    this.me = this.get(this.myID);\n  }\n\n  /** Adds a new member to the collection. For internal use only. */\n  addMember(memberData: any) {\n    if (this.get(memberData.user_id) === null) {\n      this.count++;\n    }\n    this.members[memberData.user_id] = memberData.user_info;\n    return this.get(memberData.user_id);\n  }\n\n  /** Adds a member from the collection. For internal use only. */\n  removeMember(memberData: any) {\n    var member = this.get(memberData.user_id);\n    if (member) {\n      delete this.members[memberData.user_id];\n      this.count--;\n    }\n    return member;\n  }\n\n  /** Resets the collection to the initial state. For internal use only. */\n  reset() {\n    this.members = {};\n    this.count = 0;\n    this.myID = null;\n    this.me = null;\n  }\n}\n", "import PrivateChannel from './private_channel';\nimport Logger from '../logger';\nimport Members from './members';\nimport Pusher from '../pusher';\nimport UrlStore from 'core/utils/url_store';\nimport { PusherEvent } from '../connection/protocol/message-types';\nimport Metadata from './metadata';\nimport { ChannelAuthorizationData } from '../auth/options';\n\nexport default class PresenceChannel extends PrivateChannel {\n  members: Members;\n\n  /** Adds presence channel functionality to private channels.\n   *\n   * @param {String} name\n   * @param {Pusher} pusher\n   */\n  constructor(name: string, pusher: Pusher) {\n    super(name, pusher);\n    this.members = new Members();\n  }\n\n  /** Authorizes the connection as a member of the channel.\n   *\n   * @param  {String} socketId\n   * @param  {Function} callback\n   */\n  authorize(socketId: string, callback: Function) {\n    super.authorize(socketId, async (error, authData) => {\n      if (!error) {\n        authData = authData as ChannelAuthorizationData;\n        if (authData.channel_data != null) {\n          var channelData = JSON.parse(authData.channel_data);\n          this.members.setMyID(channelData.user_id);\n        } else {\n          await this.pusher.user.signinDonePromise;\n          if (this.pusher.user.user_data != null) {\n            // If the user is signed in, get the id of the authenticated user\n            // and allow the presence authorization to continue.\n            this.members.setMyID(this.pusher.user.user_data.id);\n          } else {\n            let suffix = UrlStore.buildLogSuffix('authorizationEndpoint');\n            Logger.error(\n              `Invalid auth response for channel '${this.name}', ` +\n                `expected 'channel_data' field. ${suffix}, ` +\n                `or the user should be signed in.`,\n            );\n            callback('Invalid auth response');\n            return;\n          }\n        }\n      }\n      callback(error, authData);\n    });\n  }\n\n  /** Handles presence and subscription events. For internal use only.\n   *\n   * @param {PusherEvent} event\n   */\n  handleEvent(event: PusherEvent) {\n    var eventName = event.event;\n    if (eventName.indexOf('pusher_internal:') === 0) {\n      this.handleInternalEvent(event);\n    } else {\n      var data = event.data;\n      var metadata: Metadata = {};\n      if (event.user_id) {\n        metadata.user_id = event.user_id;\n      }\n      this.emit(eventName, data, metadata);\n    }\n  }\n  handleInternalEvent(event: PusherEvent) {\n    var eventName = event.event;\n    var data = event.data;\n    switch (eventName) {\n      case 'pusher_internal:subscription_succeeded':\n        this.handleSubscriptionSucceededEvent(event);\n        break;\n      case 'pusher_internal:subscription_count':\n        this.handleSubscriptionCountEvent(event);\n        break;\n      case 'pusher_internal:member_added':\n        var addedMember = this.members.addMember(data);\n        this.emit('pusher:member_added', addedMember);\n        break;\n      case 'pusher_internal:member_removed':\n        var removedMember = this.members.removeMember(data);\n        if (removedMember) {\n          this.emit('pusher:member_removed', removedMember);\n        }\n        break;\n    }\n  }\n\n  handleSubscriptionSucceededEvent(event: PusherEvent) {\n    this.subscriptionPending = false;\n    this.subscribed = true;\n    if (this.subscriptionCancelled) {\n      this.pusher.unsubscribe(this.name);\n    } else {\n      this.members.onSubscription(event.data);\n      this.emit('pusher:subscription_succeeded', this.members);\n    }\n  }\n\n  /** Resets the channel state, including members map. For internal use only. */\n  disconnect() {\n    this.members.reset();\n    super.disconnect();\n  }\n}\n", "import PrivateChannel from './private_channel';\nimport * as Errors from '../errors';\nimport Logger from '../logger';\nimport Pusher from '../pusher';\nimport { decode as encodeUTF8 } from '@stablelib/utf8';\nimport { decode as decodeBase64 } from '@stablelib/base64';\nimport Dispatcher from '../events/dispatcher';\nimport { PusherEvent } from '../connection/protocol/message-types';\nimport {\n  ChannelAuthorizationData,\n  ChannelAuthorizationCallback,\n} from '../auth/options';\nimport * as nacl from 'tweetnacl';\n\n/** Extends private channels to provide encrypted channel interface.\n *\n * @param {String} name\n * @param {Pusher} pusher\n */\nexport default class EncryptedChannel extends PrivateChannel {\n  key: Uint8Array = null;\n  nacl: nacl;\n\n  constructor(name: string, pusher: Pusher, nacl: nacl) {\n    super(name, pusher);\n    this.nacl = nacl;\n  }\n\n  /** Authorizes the connection to use the channel.\n   *\n   * @param  {String} socketId\n   * @param  {Function} callback\n   */\n  authorize(socketId: string, callback: ChannelAuthorizationCallback) {\n    super.authorize(\n      socketId,\n      (error: Error | null, authData: ChannelAuthorizationData) => {\n        if (error) {\n          callback(error, authData);\n          return;\n        }\n        let sharedSecret = authData['shared_secret'];\n        if (!sharedSecret) {\n          callback(\n            new Error(\n              `No shared_secret key in auth payload for encrypted channel: ${this.name}`,\n            ),\n            null,\n          );\n          return;\n        }\n        this.key = decodeBase64(sharedSecret);\n        delete authData['shared_secret'];\n        callback(null, authData);\n      },\n    );\n  }\n\n  trigger(event: string, data: any): boolean {\n    throw new Errors.UnsupportedFeature(\n      'Client events are not currently supported for encrypted channels',\n    );\n  }\n\n  /** Handles an event. For internal use only.\n   *\n   * @param {PusherEvent} event\n   */\n  handleEvent(event: PusherEvent) {\n    var eventName = event.event;\n    var data = event.data;\n    if (\n      eventName.indexOf('pusher_internal:') === 0 ||\n      eventName.indexOf('pusher:') === 0\n    ) {\n      super.handleEvent(event);\n      return;\n    }\n    this.handleEncryptedEvent(eventName, data);\n  }\n\n  private handleEncryptedEvent(event: string, data: any): void {\n    if (!this.key) {\n      Logger.debug(\n        'Received encrypted event before key has been retrieved from the authEndpoint',\n      );\n      return;\n    }\n    if (!data.ciphertext || !data.nonce) {\n      Logger.error(\n        'Unexpected format for encrypted event, expected object with `ciphertext` and `nonce` fields, got: ' +\n          data,\n      );\n      return;\n    }\n    let cipherText = decodeBase64(data.ciphertext);\n    if (cipherText.length < this.nacl.secretbox.overheadLength) {\n      Logger.error(\n        `Expected encrypted event ciphertext length to be ${this.nacl.secretbox.overheadLength}, got: ${cipherText.length}`,\n      );\n      return;\n    }\n    let nonce = decodeBase64(data.nonce);\n    if (nonce.length < this.nacl.secretbox.nonceLength) {\n      Logger.error(\n        `Expected encrypted event nonce length to be ${this.nacl.secretbox.nonceLength}, got: ${nonce.length}`,\n      );\n      return;\n    }\n\n    let bytes = this.nacl.secretbox.open(cipherText, nonce, this.key);\n    if (bytes === null) {\n      Logger.debug(\n        'Failed to decrypt an event, probably because it was encrypted with a different key. Fetching a new key from the authEndpoint...',\n      );\n      // Try a single time to retrieve a new auth key and decrypt the event with it\n      // If this fails, a new key will be requested when a new message is received\n      this.authorize(this.pusher.connection.socket_id, (error, authData) => {\n        if (error) {\n          Logger.error(\n            `Failed to make a request to the authEndpoint: ${authData}. Unable to fetch new key, so dropping encrypted event`,\n          );\n          return;\n        }\n        bytes = this.nacl.secretbox.open(cipherText, nonce, this.key);\n        if (bytes === null) {\n          Logger.error(\n            `Failed to decrypt event with new key. Dropping encrypted event`,\n          );\n          return;\n        }\n        this.emit(event, this.getDataToEmit(bytes));\n        return;\n      });\n      return;\n    }\n    this.emit(event, this.getDataToEmit(bytes));\n  }\n\n  // Try and parse the decrypted bytes as JSON. If we can't parse it, just\n  // return the utf-8 string\n  getDataToEmit(bytes: Uint8Array): string {\n    let raw = encodeUTF8(bytes);\n    try {\n      return JSON.parse(raw);\n    } catch {\n      return raw;\n    }\n  }\n}\n", "import { default as EventsDispatcher } from '../events/dispatcher';\nimport { OneOffTimer as Timer } from '../utils/timers';\nimport { Config } from '../config';\nimport Logger from '../logger';\nimport HandshakePayload from './handshake/handshake_payload';\nimport Connection from './connection';\nimport Strategy from '../strategies/strategy';\nimport StrategyRunner from '../strategies/strategy_runner';\nimport * as Collections from '../utils/collections';\nimport Timeline from '../timeline/timeline';\nimport ConnectionManagerOptions from './connection_manager_options';\nimport Runtime from 'runtime';\n\nimport {\n  ErrorCallbacks,\n  HandshakeCallbacks,\n  ConnectionCallbacks,\n} from './callbacks';\nimport Action from './protocol/action';\n\n/** Manages connection to Pusher.\n *\n * Uses a strategy (currently only default), timers and network availability\n * info to establish a connection and export its state. In case of failures,\n * manages reconnection attempts.\n *\n * Exports state changes as following events:\n * - \"state_change\", { previous: p, current: state }\n * - state\n *\n * States:\n * - initialized - initial state, never transitioned to\n * - connecting - connection is being established\n * - connected - connection has been fully established\n * - disconnected - on requested disconnection\n * - unavailable - after connection timeout or when there's no network\n * - failed - when the connection strategy is not supported\n *\n * Options:\n * - unavailableTimeout - time to transition to unavailable state\n * - activityTimeout - time after which ping message should be sent\n * - pongTimeout - time for Pusher to respond with pong before reconnecting\n *\n * @param {String} key application key\n * @param {Object} options\n */\nexport default class ConnectionManager extends EventsDispatcher {\n  key: string;\n  options: ConnectionManagerOptions;\n  state: string;\n  connection: Connection;\n  usingTLS: boolean;\n  timeline: Timeline;\n  socket_id: string;\n  unavailableTimer: Timer;\n  activityTimer: Timer;\n  retryTimer: Timer;\n  activityTimeout: number;\n  strategy: Strategy;\n  runner: StrategyRunner;\n  errorCallbacks: ErrorCallbacks;\n  handshakeCallbacks: HandshakeCallbacks;\n  connectionCallbacks: ConnectionCallbacks;\n\n  constructor(key: string, options: ConnectionManagerOptions) {\n    super();\n    this.state = 'initialized';\n    this.connection = null;\n\n    this.key = key;\n    this.options = options;\n    this.timeline = this.options.timeline;\n    this.usingTLS = this.options.useTLS;\n\n    this.errorCallbacks = this.buildErrorCallbacks();\n    this.connectionCallbacks = this.buildConnectionCallbacks(\n      this.errorCallbacks,\n    );\n    this.handshakeCallbacks = this.buildHandshakeCallbacks(this.errorCallbacks);\n\n    var Network = Runtime.getNetwork();\n\n    Network.bind('online', () => {\n      this.timeline.info({ netinfo: 'online' });\n      if (this.state === 'connecting' || this.state === 'unavailable') {\n        this.retryIn(0);\n      }\n    });\n    Network.bind('offline', () => {\n      this.timeline.info({ netinfo: 'offline' });\n      if (this.connection) {\n        this.sendActivityCheck();\n      }\n    });\n\n    this.updateStrategy();\n  }\n\n  /** Establishes a connection to Pusher.\n   *\n   * Does nothing when connection is already established. See top-level doc\n   * to find events emitted on connection attempts.\n   */\n  connect() {\n    if (this.connection || this.runner) {\n      return;\n    }\n    if (!this.strategy.isSupported()) {\n      this.updateState('failed');\n      return;\n    }\n    this.updateState('connecting');\n    this.startConnecting();\n    this.setUnavailableTimer();\n  }\n\n  /** Sends raw data.\n   *\n   * @param {String} data\n   */\n  send(data) {\n    if (this.connection) {\n      return this.connection.send(data);\n    } else {\n      return false;\n    }\n  }\n\n  /** Sends an event.\n   *\n   * @param {String} name\n   * @param {String} data\n   * @param {String} [channel]\n   * @returns {Boolean} whether message was sent or not\n   */\n  send_event(name: string, data: any, channel?: string) {\n    if (this.connection) {\n      return this.connection.send_event(name, data, channel);\n    } else {\n      return false;\n    }\n  }\n\n  /** Closes the connection. */\n  disconnect() {\n    this.disconnectInternally();\n    this.updateState('disconnected');\n  }\n\n  isUsingTLS() {\n    return this.usingTLS;\n  }\n\n  private startConnecting() {\n    var callback = (error, handshake) => {\n      if (error) {\n        this.runner = this.strategy.connect(0, callback);\n      } else {\n        if (handshake.action === 'error') {\n          this.emit('error', {\n            type: 'HandshakeError',\n            error: handshake.error,\n          });\n          this.timeline.error({ handshakeError: handshake.error });\n        } else {\n          this.abortConnecting(); // we don't support switching connections yet\n          this.handshakeCallbacks[handshake.action](handshake);\n        }\n      }\n    };\n    this.runner = this.strategy.connect(0, callback);\n  }\n\n  private abortConnecting() {\n    if (this.runner) {\n      this.runner.abort();\n      this.runner = null;\n    }\n  }\n\n  private disconnectInternally() {\n    this.abortConnecting();\n    this.clearRetryTimer();\n    this.clearUnavailableTimer();\n    if (this.connection) {\n      var connection = this.abandonConnection();\n      connection.close();\n    }\n  }\n\n  private updateStrategy() {\n    this.strategy = this.options.getStrategy({\n      key: this.key,\n      timeline: this.timeline,\n      useTLS: this.usingTLS,\n    });\n  }\n\n  private retryIn(delay) {\n    this.timeline.info({ action: 'retry', delay: delay });\n    if (delay > 0) {\n      this.emit('connecting_in', Math.round(delay / 1000));\n    }\n    this.retryTimer = new Timer(delay || 0, () => {\n      this.disconnectInternally();\n      this.connect();\n    });\n  }\n\n  private clearRetryTimer() {\n    if (this.retryTimer) {\n      this.retryTimer.ensureAborted();\n      this.retryTimer = null;\n    }\n  }\n\n  private setUnavailableTimer() {\n    this.unavailableTimer = new Timer(this.options.unavailableTimeout, () => {\n      this.updateState('unavailable');\n    });\n  }\n\n  private clearUnavailableTimer() {\n    if (this.unavailableTimer) {\n      this.unavailableTimer.ensureAborted();\n    }\n  }\n\n  private sendActivityCheck() {\n    this.stopActivityCheck();\n    this.connection.ping();\n    // wait for pong response\n    this.activityTimer = new Timer(this.options.pongTimeout, () => {\n      this.timeline.error({ pong_timed_out: this.options.pongTimeout });\n      this.retryIn(0);\n    });\n  }\n\n  private resetActivityCheck() {\n    this.stopActivityCheck();\n    // send ping after inactivity\n    if (this.connection && !this.connection.handlesActivityChecks()) {\n      this.activityTimer = new Timer(this.activityTimeout, () => {\n        this.sendActivityCheck();\n      });\n    }\n  }\n\n  private stopActivityCheck() {\n    if (this.activityTimer) {\n      this.activityTimer.ensureAborted();\n    }\n  }\n\n  private buildConnectionCallbacks(\n    errorCallbacks: ErrorCallbacks,\n  ): ConnectionCallbacks {\n    return Collections.extend<ConnectionCallbacks>({}, errorCallbacks, {\n      message: (message) => {\n        // includes pong messages from server\n        this.resetActivityCheck();\n        this.emit('message', message);\n      },\n      ping: () => {\n        this.send_event('pusher:pong', {});\n      },\n      activity: () => {\n        this.resetActivityCheck();\n      },\n      error: (error) => {\n        // just emit error to user - socket will already be closed by browser\n        this.emit('error', error);\n      },\n      closed: () => {\n        this.abandonConnection();\n        if (this.shouldRetry()) {\n          this.retryIn(1000);\n        }\n      },\n    });\n  }\n\n  private buildHandshakeCallbacks(\n    errorCallbacks: ErrorCallbacks,\n  ): HandshakeCallbacks {\n    return Collections.extend<HandshakeCallbacks>({}, errorCallbacks, {\n      connected: (handshake: HandshakePayload) => {\n        this.activityTimeout = Math.min(\n          this.options.activityTimeout,\n          handshake.activityTimeout,\n          handshake.connection.activityTimeout || Infinity,\n        );\n        this.clearUnavailableTimer();\n        this.setConnection(handshake.connection);\n        this.socket_id = this.connection.id;\n        this.updateState('connected', { socket_id: this.socket_id });\n      },\n    });\n  }\n\n  private buildErrorCallbacks(): ErrorCallbacks {\n    let withErrorEmitted = (callback) => {\n      return (result: Action | HandshakePayload) => {\n        if (result.error) {\n          this.emit('error', { type: 'WebSocketError', error: result.error });\n        }\n        callback(result);\n      };\n    };\n\n    return {\n      tls_only: withErrorEmitted(() => {\n        this.usingTLS = true;\n        this.updateStrategy();\n        this.retryIn(0);\n      }),\n      refused: withErrorEmitted(() => {\n        this.disconnect();\n      }),\n      backoff: withErrorEmitted(() => {\n        this.retryIn(1000);\n      }),\n      retry: withErrorEmitted(() => {\n        this.retryIn(0);\n      }),\n    };\n  }\n\n  private setConnection(connection) {\n    this.connection = connection;\n    for (var event in this.connectionCallbacks) {\n      this.connection.bind(event, this.connectionCallbacks[event]);\n    }\n    this.resetActivityCheck();\n  }\n\n  private abandonConnection() {\n    if (!this.connection) {\n      return;\n    }\n    this.stopActivityCheck();\n    for (var event in this.connectionCallbacks) {\n      this.connection.unbind(event, this.connectionCallbacks[event]);\n    }\n    var connection = this.connection;\n    this.connection = null;\n    return connection;\n  }\n\n  private updateState(newState: string, data?: any) {\n    var previousState = this.state;\n    this.state = newState;\n    if (previousState !== newState) {\n      var newStateDescription = newState;\n      if (newStateDescription === 'connected') {\n        newStateDescription += ' with new socket ID ' + data.socket_id;\n      }\n      Logger.debug(\n        'State changed',\n        previousState + ' -> ' + newStateDescription,\n      );\n      this.timeline.info({ state: newState, params: data });\n      this.emit('state_change', { previous: previousState, current: newState });\n      this.emit(newState, data);\n    }\n  }\n\n  private shouldRetry(): boolean {\n    return this.state === 'connecting' || this.state === 'connected';\n  }\n}\n", "import Channel from './channel';\nimport * as Collections from '../utils/collections';\nimport ChannelTable from './channel_table';\nimport Factory from '../utils/factory';\nimport Pusher from '../pusher';\nimport Logger from '../logger';\nimport * as Errors from '../errors';\nimport urlStore from '../utils/url_store';\n\n/** Handles a channel map. */\nexport default class Channels {\n  channels: ChannelTable;\n\n  constructor() {\n    this.channels = {};\n  }\n\n  /** Creates or retrieves an existing channel by its name.\n   *\n   * @param {String} name\n   * @param {Pusher} pusher\n   * @return {Channel}\n   */\n  add(name: string, pusher: Pusher) {\n    if (!this.channels[name]) {\n      this.channels[name] = createChannel(name, pusher);\n    }\n    return this.channels[name];\n  }\n\n  /** Returns a list of all channels\n   *\n   * @return {Array}\n   */\n  all(): Channel[] {\n    return Collections.values(this.channels);\n  }\n\n  /** Finds a channel by its name.\n   *\n   * @param {String} name\n   * @return {Channel} channel or null if it doesn't exist\n   */\n  find(name: string) {\n    return this.channels[name];\n  }\n\n  /** Removes a channel from the map.\n   *\n   * @param {String} name\n   */\n  remove(name: string) {\n    var channel = this.channels[name];\n    delete this.channels[name];\n    return channel;\n  }\n\n  /** Proxies disconnection signal to all channels. */\n  disconnect() {\n    Collections.objectApply(this.channels, function (channel) {\n      channel.disconnect();\n    });\n  }\n}\n\nfunction createChannel(name: string, pusher: Pusher): Channel {\n  if (name.indexOf('private-encrypted-') === 0) {\n    if (pusher.config.nacl) {\n      return Factory.createEncryptedChannel(name, pusher, pusher.config.nacl);\n    }\n    let errMsg =\n      'Tried to subscribe to a private-encrypted- channel but no nacl implementation available';\n    let suffix = urlStore.buildLogSuffix('encryptedChannelSupport');\n    throw new Errors.UnsupportedFeature(`${errMsg}. ${suffix}`);\n  } else if (name.indexOf('private-') === 0) {\n    return Factory.createPrivateChannel(name, pusher);\n  } else if (name.indexOf('presence-') === 0) {\n    return Factory.createPresenceChannel(name, pusher);\n  } else if (name.indexOf('#') === 0) {\n    throw new Errors.BadChannelName(\n      'Cannot create a channel with name \"' + name + '\".',\n    );\n  } else {\n    return Factory.createChannel(name, pusher);\n  }\n}\n", "import AssistantToTheTransportManager from '../transports/assistant_to_the_transport_manager';\nimport PingDelayOptions from '../transports/ping_delay_options';\nimport Transport from '../transports/transport';\nimport TransportManager from '../transports/transport_manager';\nimport Handshake from '../connection/handshake';\nimport TransportConnection from '../transports/transport_connection';\nimport SocketHooks from '../http/socket_hooks';\nimport HTTPSocket from '../http/http_socket';\n\nimport Timeline from '../timeline/timeline';\nimport {\n  default as TimelineSender,\n  TimelineSenderOptions,\n} from '../timeline/timeline_sender';\nimport PresenceChannel from '../channels/presence_channel';\nimport PrivateChannel from '../channels/private_channel';\nimport EncryptedChannel from '../channels/encrypted_channel';\nimport Channel from '../channels/channel';\nimport ConnectionManager from '../connection/connection_manager';\nimport ConnectionManagerOptions from '../connection/connection_manager_options';\nimport Ajax from '../http/ajax';\nimport Channels from '../channels/channels';\nimport Pusher from '../pusher';\nimport { Config } from '../config';\nimport * as nacl from 'tweetnacl';\n\nvar Factory = {\n  createChannels(): Channels {\n    return new Channels();\n  },\n\n  createConnectionManager(\n    key: string,\n    options: ConnectionManagerOptions,\n  ): ConnectionManager {\n    return new ConnectionManager(key, options);\n  },\n\n  createChannel(name: string, pusher: Pusher): Channel {\n    return new Channel(name, pusher);\n  },\n\n  createPrivateChannel(name: string, pusher: Pusher): PrivateChannel {\n    return new PrivateChannel(name, pusher);\n  },\n\n  createPresenceChannel(name: string, pusher: Pusher): PresenceChannel {\n    return new PresenceChannel(name, pusher);\n  },\n\n  createEncryptedChannel(\n    name: string,\n    pusher: Pusher,\n    nacl: nacl,\n  ): EncryptedChannel {\n    return new EncryptedChannel(name, pusher, nacl);\n  },\n\n  createTimelineSender(timeline: Timeline, options: TimelineSenderOptions) {\n    return new TimelineSender(timeline, options);\n  },\n\n  createHandshake(\n    transport: TransportConnection,\n    callback: (HandshakePayload) => void,\n  ): Handshake {\n    return new Handshake(transport, callback);\n  },\n\n  createAssistantToTheTransportManager(\n    manager: TransportManager,\n    transport: Transport,\n    options: PingDelayOptions,\n  ): AssistantToTheTransportManager {\n    return new AssistantToTheTransportManager(manager, transport, options);\n  },\n};\n\nexport default Factory;\n", "import AssistantToTheTransportManager from './assistant_to_the_transport_manager';\nimport Transport from './transport';\nimport PingDelayOptions from './ping_delay_options';\nimport Factory from '../utils/factory';\n\nexport interface TransportManagerOptions extends PingDelayOptions {\n  lives?: number;\n}\n\n/** Keeps track of the number of lives left for a transport.\n *\n * In the beginning of a session, transports may be assigned a number of\n * lives. When an AssistantToTheTransportManager instance reports a transport\n * connection closed uncleanly, the transport loses a life. When the number\n * of lives drops to zero, the transport gets disabled by its manager.\n *\n * @param {Object} options\n */\nexport default class TransportManager {\n  options: TransportManagerOptions;\n  livesLeft: number;\n\n  constructor(options: TransportManagerOptions) {\n    this.options = options || {};\n    this.livesLeft = this.options.lives || Infinity;\n  }\n\n  /** Creates a assistant for the transport.\n   *\n   * @param {Transport} transport\n   * @returns {AssistantToTheTransportManager}\n   */\n  getAssistant(transport: Transport): AssistantToTheTransportManager {\n    return Factory.createAssistantToTheTransportManager(this, transport, {\n      minPingDelay: this.options.minPingDelay,\n      maxPingDelay: this.options.maxPingDelay,\n    });\n  }\n\n  /** Returns whether the transport has any lives left.\n   *\n   * @returns {Boolean}\n   */\n  isAlive(): boolean {\n    return this.livesLeft > 0;\n  }\n\n  /** Takes one life from the transport. */\n  reportDeath() {\n    this.livesLeft -= 1;\n  }\n}\n", "import * as Collections from '../utils/collections';\nimport Util from '../util';\nimport { OneOffTimer as Timer } from '../utils/timers';\nimport Strategy from './strategy';\nimport StrategyOptions from './strategy_options';\n\n/** Loops through strategies with optional timeouts.\n *\n * Options:\n * - loop - whether it should loop through the substrategy list\n * - timeout - initial timeout for a single substrategy\n * - timeoutLimit - maximum timeout\n *\n * @param {Strategy[]} strategies\n * @param {Object} options\n */\nexport default class SequentialStrategy implements Strategy {\n  strategies: Strategy[];\n  loop: boolean;\n  failFast: boolean;\n  timeout: number;\n  timeoutLimit: number;\n\n  constructor(strategies: Strategy[], options: StrategyOptions) {\n    this.strategies = strategies;\n    this.loop = Boolean(options.loop);\n    this.failFast = Boolean(options.failFast);\n    this.timeout = options.timeout;\n    this.timeoutLimit = options.timeoutLimit;\n  }\n\n  isSupported(): boolean {\n    return Collections.any(this.strategies, Util.method('isSupported'));\n  }\n\n  connect(minPriority: number, callback: Function) {\n    var strategies = this.strategies;\n    var current = 0;\n    var timeout = this.timeout;\n    var runner = null;\n\n    var tryNextStrategy = (error, handshake) => {\n      if (handshake) {\n        callback(null, handshake);\n      } else {\n        current = current + 1;\n        if (this.loop) {\n          current = current % strategies.length;\n        }\n\n        if (current < strategies.length) {\n          if (timeout) {\n            timeout = timeout * 2;\n            if (this.timeoutLimit) {\n              timeout = Math.min(timeout, this.timeoutLimit);\n            }\n          }\n          runner = this.tryStrategy(\n            strategies[current],\n            minPriority,\n            { timeout, failFast: this.failFast },\n            tryNextStrategy,\n          );\n        } else {\n          callback(true);\n        }\n      }\n    };\n\n    runner = this.tryStrategy(\n      strategies[current],\n      minPriority,\n      { timeout: timeout, failFast: this.failFast },\n      tryNextStrategy,\n    );\n\n    return {\n      abort: function () {\n        runner.abort();\n      },\n      forceMinPriority: function (p) {\n        minPriority = p;\n        if (runner) {\n          runner.forceMinPriority(p);\n        }\n      },\n    };\n  }\n\n  private tryStrategy(\n    strategy: Strategy,\n    minPriority: number,\n    options: StrategyOptions,\n    callback: Function,\n  ) {\n    var timer = null;\n    var runner = null;\n\n    if (options.timeout > 0) {\n      timer = new Timer(options.timeout, function () {\n        runner.abort();\n        callback(true);\n      });\n    }\n\n    runner = strategy.connect(minPriority, function (error, handshake) {\n      if (error && timer && timer.isRunning() && !options.failFast) {\n        // advance to the next strategy after the timeout\n        return;\n      }\n      if (timer) {\n        timer.ensureAborted();\n      }\n      callback(error, handshake);\n    });\n\n    return {\n      abort: function () {\n        if (timer) {\n          timer.ensureAborted();\n        }\n        runner.abort();\n      },\n      forceMinPriority: function (p) {\n        runner.forceMinPriority(p);\n      },\n    };\n  }\n}\n", "import * as Collections from '../utils/collections';\nimport Util from '../util';\nimport Strategy from './strategy';\n\n/** Launches all substrategies and emits prioritized connected transports.\n *\n * @param {Array} strategies\n */\nexport default class BestConnectedEverStrategy implements Strategy {\n  strategies: Strategy[];\n\n  constructor(strategies: Strategy[]) {\n    this.strategies = strategies;\n  }\n\n  isSupported(): boolean {\n    return Collections.any(this.strategies, Util.method('isSupported'));\n  }\n\n  connect(minPriority: number, callback: Function) {\n    return connect(this.strategies, minPriority, function (i, runners) {\n      return function (error, handshake) {\n        runners[i].error = error;\n        if (error) {\n          if (allRunnersFailed(runners)) {\n            callback(true);\n          }\n          return;\n        }\n        Collections.apply(runners, function (runner) {\n          runner.forceMinPriority(handshake.transport.priority);\n        });\n        callback(null, handshake);\n      };\n    });\n  }\n}\n\n/** Connects to all strategies in parallel.\n *\n * Callback builder should be a function that takes two arguments: index\n * and a list of runners. It should return another function that will be\n * passed to the substrategy with given index. Runners can be aborted using\n * abortRunner(s) functions from this class.\n *\n * @param  {Array} strategies\n * @param  {Function} callbackBuilder\n * @return {Object} strategy runner\n */\nfunction connect(\n  strategies: Strategy[],\n  minPriority: number,\n  callbackBuilder: Function,\n) {\n  var runners = Collections.map(strategies, function (strategy, i, _, rs) {\n    return strategy.connect(minPriority, callbackBuilder(i, rs));\n  });\n  return {\n    abort: function () {\n      Collections.apply(runners, abortRunner);\n    },\n    forceMinPriority: function (p) {\n      Collections.apply(runners, function (runner) {\n        runner.forceMinPriority(p);\n      });\n    },\n  };\n}\n\nfunction allRunnersFailed(runners): boolean {\n  return Collections.all(runners, function (runner) {\n    return Boolean(runner.error);\n  });\n}\n\nfunction abortRunner(runner) {\n  if (!runner.error && !runner.aborted) {\n    runner.abort();\n    runner.aborted = true;\n  }\n}\n", "import Util from '../util';\nimport Runtime from 'runtime';\nimport Strategy from './strategy';\nimport SequentialStrategy from './sequential_strategy';\nimport StrategyOptions from './strategy_options';\nimport TransportStrategy from './transport_strategy';\nimport Timeline from '../timeline/timeline';\nimport * as Collections from '../utils/collections';\n\nexport interface TransportStrategyDictionary {\n  [key: string]: TransportStrategy;\n}\n\n/** Caches the last successful transport and, after the first few attempts,\n *  uses the cached transport for subsequent attempts.\n *\n * @param {Strategy} strategy\n * @param {Object} transports\n * @param {Object} options\n */\nexport default class WebSocketPrioritizedCachedStrategy implements Strategy {\n  strategy: Strategy;\n  transports: TransportStrategyDictionary;\n  ttl: number;\n  usingTLS: boolean;\n  timeline: Timeline;\n\n  constructor(\n    strategy: Strategy,\n    transports: TransportStrategyDictionary,\n    options: StrategyOptions,\n  ) {\n    this.strategy = strategy;\n    this.transports = transports;\n    this.ttl = options.ttl || 1800 * 1000;\n    this.usingTLS = options.useTLS;\n    this.timeline = options.timeline;\n  }\n\n  isSupported(): boolean {\n    return this.strategy.isSupported();\n  }\n\n  connect(minPriority: number, callback: Function) {\n    var usingTLS = this.usingTLS;\n    var info = fetchTransportCache(usingTLS);\n    var cacheSkipCount = info && info.cacheSkipCount ? info.cacheSkipCount : 0;\n\n    var strategies = [this.strategy];\n    if (info && info.timestamp + this.ttl >= Util.now()) {\n      var transport = this.transports[info.transport];\n      if (transport) {\n        if (['ws', 'wss'].includes(info.transport) || cacheSkipCount > 3) {\n          this.timeline.info({\n            cached: true,\n            transport: info.transport,\n            latency: info.latency,\n          });\n          strategies.push(\n            new SequentialStrategy([transport], {\n              timeout: info.latency * 2 + 1000,\n              failFast: true,\n            }),\n          );\n        } else {\n          cacheSkipCount++;\n        }\n      }\n    }\n\n    var startTimestamp = Util.now();\n    var runner = strategies\n      .pop()\n      .connect(minPriority, function cb(error, handshake) {\n        if (error) {\n          flushTransportCache(usingTLS);\n          if (strategies.length > 0) {\n            startTimestamp = Util.now();\n            runner = strategies.pop().connect(minPriority, cb);\n          } else {\n            callback(error);\n          }\n        } else {\n          storeTransportCache(\n            usingTLS,\n            handshake.transport.name,\n            Util.now() - startTimestamp,\n            cacheSkipCount,\n          );\n          callback(null, handshake);\n        }\n      });\n\n    return {\n      abort: function () {\n        runner.abort();\n      },\n      forceMinPriority: function (p) {\n        minPriority = p;\n        if (runner) {\n          runner.forceMinPriority(p);\n        }\n      },\n    };\n  }\n}\n\nfunction getTransportCacheKey(usingTLS: boolean): string {\n  return 'pusherTransport' + (usingTLS ? 'TLS' : 'NonTLS');\n}\n\nfunction fetchTransportCache(usingTLS: boolean): any {\n  var storage = Runtime.getLocalStorage();\n  if (storage) {\n    try {\n      var serializedCache = storage[getTransportCacheKey(usingTLS)];\n      if (serializedCache) {\n        return JSON.parse(serializedCache);\n      }\n    } catch (e) {\n      flushTransportCache(usingTLS);\n    }\n  }\n  return null;\n}\n\nfunction storeTransportCache(\n  usingTLS: boolean,\n  transport: TransportStrategy,\n  latency: number,\n  cacheSkipCount: number,\n) {\n  var storage = Runtime.getLocalStorage();\n  if (storage) {\n    try {\n      storage[getTransportCacheKey(usingTLS)] = Collections.safeJSONStringify({\n        timestamp: Util.now(),\n        transport: transport,\n        latency: latency,\n        cacheSkipCount: cacheSkipCount,\n      });\n    } catch (e) {\n      // catch over quota exceptions raised by localStorage\n    }\n  }\n}\n\nfunction flushTransportCache(usingTLS: boolean) {\n  var storage = Runtime.getLocalStorage();\n  if (storage) {\n    try {\n      delete storage[getTransportCacheKey(usingTLS)];\n    } catch (e) {\n      // catch exceptions raised by localStorage\n    }\n  }\n}\n", "import { OneOffTimer as Timer } from '../utils/timers';\nimport Strategy from './strategy';\nimport StrategyOptions from './strategy_options';\n\n/** Runs substrategy after specified delay.\n *\n * Options:\n * - delay - time in miliseconds to delay the substrategy attempt\n *\n * @param {Strategy} strategy\n * @param {Object} options\n */\nexport default class DelayedStrategy implements Strategy {\n  strategy: Strategy;\n  options: { delay: number };\n\n  constructor(strategy: Strategy, { delay: number }) {\n    this.strategy = strategy;\n    this.options = { delay: number };\n  }\n\n  isSupported(): boolean {\n    return this.strategy.isSupported();\n  }\n\n  connect(minPriority: number, callback: Function) {\n    var strategy = this.strategy;\n    var runner;\n    var timer = new Timer(this.options.delay, function () {\n      runner = strategy.connect(minPriority, callback);\n    });\n\n    return {\n      abort: function () {\n        timer.ensureAborted();\n        if (runner) {\n          runner.abort();\n        }\n      },\n      forceMinPriority: function (p) {\n        minPriority = p;\n        if (runner) {\n          runner.forceMinPriority(p);\n        }\n      },\n    };\n  }\n}\n", "import Strategy from './strategy';\nimport StrategyRunner from './strategy_runner';\n\n/** Proxies method calls to one of substrategies basing on the test function.\n *\n * @param {Function} test\n * @param {Strategy} trueBranch strategy used when test returns true\n * @param {Strategy} falseBranch strategy used when test returns false\n */\nexport default class IfStrategy implements Strategy {\n  test: () => boolean;\n  trueBranch: Strategy;\n  falseBranch: Strategy;\n\n  constructor(\n    test: () => boolean,\n    trueBranch: Strategy,\n    falseBranch: Strategy,\n  ) {\n    this.test = test;\n    this.trueBranch = trueBranch;\n    this.falseBranch = falseBranch;\n  }\n\n  isSupported(): boolean {\n    var branch = this.test() ? this.trueBranch : this.falseBranch;\n    return branch.isSupported();\n  }\n\n  connect(minPriority: number, callback: Function): StrategyRunner {\n    var branch = this.test() ? this.trueBranch : this.falseBranch;\n    return branch.connect(minPriority, callback);\n  }\n}\n", "import Strategy from './strategy';\nimport StrategyRunner from './strategy_runner';\n\n/** Launches the substrategy and terminates on the first open connection.\n *\n * @param {Strategy} strategy\n */\nexport default class FirstConnectedStrategy implements Strategy {\n  strategy: Strategy;\n\n  constructor(strategy: Strategy) {\n    this.strategy = strategy;\n  }\n\n  isSupported(): boolean {\n    return this.strategy.isSupported();\n  }\n\n  connect(minPriority: number, callback: Function): StrategyRunner {\n    var runner = this.strategy.connect(\n      minPriority,\n      function (error, handshake) {\n        if (handshake) {\n          runner.abort();\n        }\n        callback(error, handshake);\n      },\n    );\n    return runner;\n  }\n}\n", "import * as Collections from 'core/utils/collections';\nimport TransportManager from 'core/transports/transport_manager';\nimport Strategy from 'core/strategies/strategy';\nimport SequentialStrategy from 'core/strategies/sequential_strategy';\nimport BestConnectedEverStrategy from 'core/strategies/best_connected_ever_strategy';\nimport WebSocketPrioritizedCachedStrategy, {\n  TransportStrategyDictionary,\n} from 'core/strategies/websocket_prioritized_cached_strategy';\nimport DelayedStrategy from 'core/strategies/delayed_strategy';\nimport IfStrategy from 'core/strategies/if_strategy';\nimport FirstConnectedStrategy from 'core/strategies/first_connected_strategy';\nimport { Config } from 'core/config';\nimport StrategyOptions from 'core/strategies/strategy_options';\n\nfunction testSupportsStrategy(strategy: Strategy) {\n  return function () {\n    return strategy.isSupported();\n  };\n}\n\nvar getDefaultStrategy = function (\n  config: Config,\n  baseOptions: StrategyOptions,\n  defineTransport: Function,\n): Strategy {\n  var definedTransports = <TransportStrategyDictionary>{};\n\n  function defineTransportStrategy(\n    name: string,\n    type: string,\n    priority: number,\n    options: StrategyOptions,\n    manager?: TransportManager,\n  ) {\n    var transport = defineTransport(\n      config,\n      name,\n      type,\n      priority,\n      options,\n      manager,\n    );\n\n    definedTransports[name] = transport;\n\n    return transport;\n  }\n\n  var ws_options: StrategyOptions = Object.assign({}, baseOptions, {\n    hostNonTLS: config.wsHost + ':' + config.wsPort,\n    hostTLS: config.wsHost + ':' + config.wssPort,\n    httpPath: config.wsPath,\n  });\n  var wss_options: StrategyOptions = Collections.extend({}, ws_options, {\n    useTLS: true,\n  });\n  var http_options: StrategyOptions = Object.assign({}, baseOptions, {\n    hostNonTLS: config.httpHost + ':' + config.httpPort,\n    hostTLS: config.httpHost + ':' + config.httpsPort,\n    httpPath: config.httpPath,\n  });\n  var timeouts = {\n    loop: true,\n    timeout: 15000,\n    timeoutLimit: 60000,\n  };\n\n  var ws_manager = new TransportManager({\n    minPingDelay: 10000,\n    maxPingDelay: config.activityTimeout,\n  });\n  var streaming_manager = new TransportManager({\n    lives: 2,\n    minPingDelay: 10000,\n    maxPingDelay: config.activityTimeout,\n  });\n\n  var ws_transport = defineTransportStrategy(\n    'ws',\n    'ws',\n    3,\n    ws_options,\n    ws_manager,\n  );\n  var wss_transport = defineTransportStrategy(\n    'wss',\n    'ws',\n    3,\n    wss_options,\n    ws_manager,\n  );\n  var xhr_streaming_transport = defineTransportStrategy(\n    'xhr_streaming',\n    'xhr_streaming',\n    1,\n    http_options,\n    streaming_manager,\n  );\n  var xhr_polling_transport = defineTransportStrategy(\n    'xhr_polling',\n    'xhr_polling',\n    1,\n    http_options,\n  );\n\n  var ws_loop = new SequentialStrategy([ws_transport], timeouts);\n  var wss_loop = new SequentialStrategy([wss_transport], timeouts);\n  var streaming_loop = new SequentialStrategy(\n    [xhr_streaming_transport],\n    timeouts,\n  );\n  var polling_loop = new SequentialStrategy([xhr_polling_transport], timeouts);\n\n  var http_loop = new SequentialStrategy(\n    [\n      new IfStrategy(\n        testSupportsStrategy(streaming_loop),\n        new BestConnectedEverStrategy([\n          streaming_loop,\n          new DelayedStrategy(polling_loop, { delay: 4000 }),\n        ]),\n        polling_loop,\n      ),\n    ],\n    timeouts,\n  );\n\n  var wsStrategy;\n  if (baseOptions.useTLS) {\n    wsStrategy = new BestConnectedEverStrategy([\n      ws_loop,\n      new DelayedStrategy(http_loop, { delay: 2000 }),\n    ]);\n  } else {\n    wsStrategy = new BestConnectedEverStrategy([\n      ws_loop,\n      new DelayedStrategy(wss_loop, { delay: 2000 }),\n      new DelayedStrategy(http_loop, { delay: 5000 }),\n    ]);\n  }\n\n  return new WebSocketPrioritizedCachedStrategy(\n    new FirstConnectedStrategy(\n      new IfStrategy(testSupportsStrategy(ws_transport), wsStrategy, http_loop),\n    ),\n    definedTransports,\n    {\n      ttl: 1800000,\n      timeline: baseOptions.timeline,\n      useTLS: baseOptions.useTLS,\n    },\n  );\n};\n\nexport default getDefaultStrategy;\n", "import Runtime from 'runtime';\nimport RequestHooks from './request_hooks';\nimport <PERSON> from './ajax';\nimport { default as EventsDispatcher } from '../events/dispatcher';\n\nconst MAX_BUFFER_LENGTH = 256 * 1024;\n\nexport default class HTTPRequest extends EventsDispatcher {\n  hooks: RequestHooks;\n  method: string;\n  url: string;\n  position: number;\n  xhr: Ajax;\n  unloader: Function;\n\n  constructor(hooks: RequestHooks, method: string, url: string) {\n    super();\n    this.hooks = hooks;\n    this.method = method;\n    this.url = url;\n  }\n\n  start(payload?: any) {\n    this.position = 0;\n    this.xhr = this.hooks.getRequest(this);\n\n    this.unloader = () => {\n      this.close();\n    };\n    Runtime.addUnloadListener(this.unloader);\n\n    this.xhr.open(this.method, this.url, true);\n\n    if (this.xhr.setRequestHeader) {\n      this.xhr.setRequestHeader('Content-Type', 'application/json'); // ReactNative doesn't set this header by default.\n    }\n    this.xhr.send(payload);\n  }\n\n  close() {\n    if (this.unloader) {\n      Runtime.removeUnloadListener(this.unloader);\n      this.unloader = null;\n    }\n    if (this.xhr) {\n      this.hooks.abortRequest(this.xhr);\n      this.xhr = null;\n    }\n  }\n\n  onChunk(status: number, data: any) {\n    while (true) {\n      var chunk = this.advanceBuffer(data);\n      if (chunk) {\n        this.emit('chunk', { status: status, data: chunk });\n      } else {\n        break;\n      }\n    }\n    if (this.isBufferTooLong(data)) {\n      this.emit('buffer_too_long');\n    }\n  }\n\n  private advanceBuffer(buffer: any[]): any {\n    var unreadData = buffer.slice(this.position);\n    var endOfLinePosition = unreadData.indexOf('\\n');\n\n    if (endOfLinePosition !== -1) {\n      this.position += endOfLinePosition + 1;\n      return unreadData.slice(0, endOfLinePosition);\n    } else {\n      // chunk is not finished yet, don't move the buffer pointer\n      return null;\n    }\n  }\n\n  private isBufferTooLong(buffer: any): boolean {\n    return this.position === buffer.length && buffer.length > MAX_BUFFER_LENGTH;\n  }\n}\n", "enum State {\n  CONNECTING = 0,\n  OPEN = 1,\n  CLOSED = 3,\n}\n\nexport default State;\n", "import URLLocation from './url_location';\nimport State from './state';\nimport Socket from '../socket';\nimport SocketHooks from './socket_hooks';\nimport Util from '../util';\nimport Ajax from './ajax';\nimport HTTPRequest from './http_request';\nimport Runtime from 'runtime';\n\nvar autoIncrement = 1;\n\nclass HTTPSocket implements Socket {\n  hooks: SocketHooks;\n  session: string;\n  location: URLLocation;\n  readyState: State;\n  stream: HTTPRequest;\n\n  onopen: () => void;\n  onerror: (error: any) => void;\n  onclose: (closeEvent: any) => void;\n  onmessage: (message: any) => void;\n  onactivity: () => void;\n\n  constructor(hooks: SocketHooks, url: string) {\n    this.hooks = hooks;\n    this.session = randomNumber(1000) + '/' + randomString(8);\n    this.location = getLocation(url);\n    this.readyState = State.CONNECTING;\n    this.openStream();\n  }\n\n  send(payload: any) {\n    return this.sendRaw(JSON.stringify([payload]));\n  }\n\n  ping() {\n    this.hooks.sendHeartbeat(this);\n  }\n\n  close(code: any, reason: any) {\n    this.onClose(code, reason, true);\n  }\n\n  /** For internal use only */\n  sendRaw(payload: any): boolean {\n    if (this.readyState === State.OPEN) {\n      try {\n        Runtime.createSocketRequest(\n          'POST',\n          getUniqueURL(getSendURL(this.location, this.session)),\n        ).start(payload);\n        return true;\n      } catch (e) {\n        return false;\n      }\n    } else {\n      return false;\n    }\n  }\n\n  /** For internal use only */\n  reconnect() {\n    this.closeStream();\n    this.openStream();\n  }\n\n  /** For internal use only */\n  onClose(code, reason, wasClean) {\n    this.closeStream();\n    this.readyState = State.CLOSED;\n    if (this.onclose) {\n      this.onclose({\n        code: code,\n        reason: reason,\n        wasClean: wasClean,\n      });\n    }\n  }\n\n  private onChunk(chunk) {\n    if (chunk.status !== 200) {\n      return;\n    }\n    if (this.readyState === State.OPEN) {\n      this.onActivity();\n    }\n\n    var payload;\n    var type = chunk.data.slice(0, 1);\n    switch (type) {\n      case 'o':\n        payload = JSON.parse(chunk.data.slice(1) || '{}');\n        this.onOpen(payload);\n        break;\n      case 'a':\n        payload = JSON.parse(chunk.data.slice(1) || '[]');\n        for (var i = 0; i < payload.length; i++) {\n          this.onEvent(payload[i]);\n        }\n        break;\n      case 'm':\n        payload = JSON.parse(chunk.data.slice(1) || 'null');\n        this.onEvent(payload);\n        break;\n      case 'h':\n        this.hooks.onHeartbeat(this);\n        break;\n      case 'c':\n        payload = JSON.parse(chunk.data.slice(1) || '[]');\n        this.onClose(payload[0], payload[1], true);\n        break;\n    }\n  }\n\n  private onOpen(options) {\n    if (this.readyState === State.CONNECTING) {\n      if (options && options.hostname) {\n        this.location.base = replaceHost(this.location.base, options.hostname);\n      }\n      this.readyState = State.OPEN;\n\n      if (this.onopen) {\n        this.onopen();\n      }\n    } else {\n      this.onClose(1006, 'Server lost session', true);\n    }\n  }\n\n  private onEvent(event) {\n    if (this.readyState === State.OPEN && this.onmessage) {\n      this.onmessage({ data: event });\n    }\n  }\n\n  private onActivity() {\n    if (this.onactivity) {\n      this.onactivity();\n    }\n  }\n\n  private onError(error) {\n    if (this.onerror) {\n      this.onerror(error);\n    }\n  }\n\n  private openStream() {\n    this.stream = Runtime.createSocketRequest(\n      'POST',\n      getUniqueURL(this.hooks.getReceiveURL(this.location, this.session)),\n    );\n\n    this.stream.bind('chunk', (chunk) => {\n      this.onChunk(chunk);\n    });\n    this.stream.bind('finished', (status) => {\n      this.hooks.onFinished(this, status);\n    });\n    this.stream.bind('buffer_too_long', () => {\n      this.reconnect();\n    });\n\n    try {\n      this.stream.start();\n    } catch (error) {\n      Util.defer(() => {\n        this.onError(error);\n        this.onClose(1006, 'Could not start streaming', false);\n      });\n    }\n  }\n\n  private closeStream() {\n    if (this.stream) {\n      this.stream.unbind_all();\n      this.stream.close();\n      this.stream = null;\n    }\n  }\n}\n\nfunction getLocation(url): URLLocation {\n  var parts = /([^\\?]*)\\/*(\\??.*)/.exec(url);\n  return {\n    base: parts[1],\n    queryString: parts[2],\n  };\n}\n\nfunction getSendURL(url: URLLocation, session: string): string {\n  return url.base + '/' + session + '/xhr_send';\n}\n\nfunction getUniqueURL(url: string): string {\n  var separator = url.indexOf('?') === -1 ? '?' : '&';\n  return url + separator + 't=' + +new Date() + '&n=' + autoIncrement++;\n}\n\nfunction replaceHost(url: string, hostname: string): string {\n  var urlParts = /(https?:\\/\\/)([^\\/:]+)((\\/|:)?.*)/.exec(url);\n  return urlParts[1] + hostname + urlParts[3];\n}\n\nfunction randomNumber(max: number): number {\n  return Runtime.randomInt(max);\n}\n\nfunction randomString(length: number): string {\n  var result = [];\n\n  for (var i = 0; i < length; i++) {\n    result.push(randomNumber(32).toString(32));\n  }\n\n  return result.join('');\n}\n\nexport default HTTPSocket;\n", "import SocketHooks from './socket_hooks';\nimport HTTPSocket from './http_socket';\n\nvar hooks: SocketHooks = {\n  getReceiveURL: function (url, session) {\n    return url.base + '/' + session + '/xhr_streaming' + url.queryString;\n  },\n  onHeartbeat: function (socket) {\n    socket.sendRaw('[]');\n  },\n  sendHeartbeat: function (socket) {\n    socket.sendRaw('[]');\n  },\n  onFinished: function (socket, status) {\n    socket.onClose(1006, 'Connection interrupted (' + status + ')', false);\n  },\n};\n\nexport default hooks;\n", "import SocketHooks from './socket_hooks';\nimport URLLocation from './url_location';\nimport HTTPSocket from './http_socket';\n\nvar hooks: SocketHooks = {\n  getReceiveURL: function (url: URLLocation, session: string): string {\n    return url.base + '/' + session + '/xhr' + url.queryString;\n  },\n  onHeartbeat: function () {\n    // next HTTP request will reset server's activity timer\n  },\n  sendHeartbeat: function (socket) {\n    socket.sendRaw('[]');\n  },\n  onFinished: function (socket, status) {\n    if (status === 200) {\n      socket.reconnect();\n    } else {\n      socket.onClose(1006, 'Connection interrupted (' + status + ')', false);\n    }\n  },\n};\n\nexport default hooks;\n", "import HTTPRequest from 'core/http/http_request';\nimport <PERSON>questHooks from 'core/http/request_hooks';\nimport Ajax from 'core/http/ajax';\nimport Runtime from 'runtime';\n\nvar hooks: RequestHooks = {\n  getRequest: function (socket: HTTPRequest): Ajax {\n    var Constructor = Runtime.getXHRAPI();\n    var xhr = new Constructor();\n    xhr.onreadystatechange = xhr.onprogress = function () {\n      switch (xhr.readyState) {\n        case 3:\n          if (xhr.responseText && xhr.responseText.length > 0) {\n            socket.onChunk(xhr.status, xhr.responseText);\n          }\n          break;\n        case 4:\n          // this happens only on errors, never after calling close\n          if (xhr.responseText && xhr.responseText.length > 0) {\n            socket.onChunk(xhr.status, xhr.responseText);\n          }\n          socket.emit('finished', xhr.status);\n          socket.close();\n          break;\n      }\n    };\n    return xhr;\n  },\n  abortRequest: function (xhr: Ajax) {\n    xhr.onreadystatechange = null;\n    xhr.abort();\n  },\n};\n\nexport default hooks;\n", "import * as Collections from 'core/utils/collections';\nimport Transports from 'isomorphic/transports/transports';\nimport TimelineSender from 'core/timeline/timeline_sender';\nimport Ajax from 'core/http/ajax';\nimport getDefaultStrategy from './default_strategy';\nimport TransportsTable from 'core/transports/transports_table';\nimport transportConnectionInitializer from './transports/transport_connection_initializer';\nimport HTTPFactory from './http/http';\n\nvar Isomorphic: any = {\n  getDefaultStrategy,\n  Transports: <TransportsTable>Transports,\n  transportConnectionInitializer,\n  HTTPFactory,\n\n  setup(PusherClass): void {\n    PusherClass.ready();\n  },\n\n  getLocalStorage(): any {\n    return undefined;\n  },\n\n  getClientFeatures(): any[] {\n    return Collections.keys(\n      Collections.filterObject({ ws: Transports.ws }, function (t) {\n        return t.isSupported({});\n      }),\n    );\n  },\n\n  getProtocol(): string {\n    return 'http:';\n  },\n\n  isXHRSupported(): boolean {\n    return true;\n  },\n\n  createSocketRequest(method: string, url: string) {\n    if (this.isXHRSupported()) {\n      return this.HTTPFactory.createXHR(method, url);\n    } else {\n      throw 'Cross-origin HTTP requests are not supported';\n    }\n  },\n\n  createXHR(): Ajax {\n    var Constructor = this.getXHRAPI();\n    return new Constructor();\n  },\n\n  createWebSocket(url: string): any {\n    var Constructor = this.getWebSocketAPI();\n    return new Constructor(url);\n  },\n\n  addUnloadListener(listener: any) {},\n  removeUnloadListener(listener: any) {},\n};\n\nexport default Isomorphic;\n", "/** Initializes the transport.\n *\n * Fetches resources if needed and then transitions to initialized.\n */\nexport default function () {\n  var self = this;\n\n  self.timeline.info(\n    self.buildTimelineMessage({\n      transport: self.name + (self.options.useTLS ? 's' : ''),\n    }),\n  );\n\n  if (self.hooks.isInitialized()) {\n    self.changeState('initialized');\n  } else {\n    self.onClose();\n  }\n}\n", "import HTTPRequest from 'core/http/http_request';\nimport HTTPSocket from 'core/http/http_socket';\nimport SocketHooks from 'core/http/socket_hooks';\nimport RequestHooks from 'core/http/request_hooks';\nimport streamingHooks from 'core/http/http_streaming_socket';\nimport pollingHooks from 'core/http/http_polling_socket';\nimport xhrHooks from './http_xhr_request';\nimport HTTPFactory from 'core/http/http_factory';\n\nvar HTTP: HTTPFactory = {\n  createStreamingSocket(url: string): HTTPSocket {\n    return this.createSocket(streamingHooks, url);\n  },\n\n  createPollingSocket(url: string): HTTPSocket {\n    return this.createSocket(pollingHooks, url);\n  },\n\n  createSocket(hooks: SocketHooks, url: string): HTTPSocket {\n    return new HTTPSocket(hooks, url);\n  },\n\n  createXHR(method: string, url: string): HTTPRequest {\n    return this.createRequest(xhrHooks, method, url);\n  },\n\n  createRequest(hooks: RequestHooks, method: string, url: string): HTTPRequest {\n    return new HTTPRequest(hooks, method, url);\n  },\n};\n\nexport default HTTP;\n", "import { default as EventsDispatcher } from 'core/events/dispatcher';\nimport Reachability from 'core/reachability';\n\nexport class NetInfo extends EventsDispatcher implements Reachability {\n  isOnline(): boolean {\n    return true;\n  }\n}\n\nexport var Network = new NetInfo();\n", "import AbstractRuntime from 'runtimes/interface';\nimport { AuthTransport } from 'core/auth/auth_transports';\nimport {\n  AuthRequestType,\n  AuthTransportCallback,\n  InternalAuthOptions,\n} from 'core/auth/options';\nimport { HTTPAuthError } from 'core/errors';\n\nvar fetchAuth: AuthTransport = function (\n  context: AbstractRuntime,\n  query: string,\n  authOptions: InternalAuthOptions,\n  authRequestType: AuthRequestType,\n  callback: AuthTransportCallback,\n) {\n  var headers = new Headers();\n  headers.set('Content-Type', 'application/x-www-form-urlencoded');\n\n  for (var headerName in authOptions.headers) {\n    headers.set(headerName, authOptions.headers[headerName]);\n  }\n\n  if (authOptions.headersProvider != null) {\n    const dynamicHeaders = authOptions.headersProvider();\n    for (var headerName in dynamicHeaders) {\n      headers.set(headerName, dynamicHeaders[headerName]);\n    }\n  }\n\n  var body = query;\n  var request = new Request(authOptions.endpoint, {\n    headers,\n    body,\n    credentials: 'same-origin',\n    method: 'POST',\n  });\n\n  return fetch(request)\n    .then((response) => {\n      let { status } = response;\n      if (status === 200) {\n        // manually parse the json so we can provide a more helpful error in\n        // failure case\n        return response.text();\n      }\n      throw new HTTPAuthError(\n        status,\n        `Could not get ${authRequestType.toString()} info from your auth endpoint, status: ${status}`,\n      );\n    })\n    .then((data) => {\n      let parsedData;\n      try {\n        parsedData = JSON.parse(data);\n      } catch (e) {\n        throw new HTTPAuthError(\n          200,\n          `JSON returned from ${authRequestType.toString()} endpoint was invalid, yet status code was 200. Data was: ${data}`,\n        );\n      }\n      callback(null, parsedData);\n    })\n    .catch((err) => {\n      callback(err, null);\n    });\n};\n\nexport default fetchAuth;\n", "import Logger from 'core/logger';\nimport TimelineSender from 'core/timeline/timeline_sender';\nimport * as Collections from 'core/utils/collections';\nimport Util from 'core/util';\nimport Runtime from 'runtime';\nimport TimelineTransport from 'core/timeline/timeline_transport';\n\nvar getAgent = function (sender: TimelineSender, useTLS: boolean) {\n  return function (data: any, callback: Function) {\n    var scheme = 'http' + (useTLS ? 's' : '') + '://';\n    var url =\n      scheme + (sender.host || sender.options.host) + sender.options.path;\n    var query = Collections.buildQueryString(data);\n    url += '/' + 2 + '?' + query;\n\n    fetch(url)\n      .then((response) => {\n        if (response.status !== 200) {\n          throw `received ${response.status} from stats.pusher.com`;\n        }\n        return response.json();\n      })\n      .then(({ host }) => {\n        if (host) {\n          sender.host = host;\n        }\n      })\n      .catch((err) => {\n        Logger.debug('TimelineSender Error: ', err);\n      });\n  };\n};\n\nvar fetchTimeline = {\n  name: 'xhr',\n  getAgent,\n};\n\nexport default fetchTimeline;\n", "import Isomorphic from 'isomorphic/runtime';\nimport Runtime from '../interface';\nimport { Network } from './net_info';\nimport fetchAuth from './auth/fetch_auth';\nimport { AuthTransports } from 'core/auth/auth_transports';\nimport fetchTimeline from './timeline/fetch_timeline';\n\n// Very verbose but until unavoidable until\n// TypeScript 2.1, when spread attributes will be\n// supported\nconst {\n  getDefaultStrategy,\n  Transports,\n  setup,\n  getProtocol,\n  isXHRSupported,\n  getLocalStorage,\n  createXHR,\n  createWebSocket,\n  addUnloadListener,\n  removeUnloadListener,\n  transportConnectionInitializer,\n  createSocketRequest,\n  HTTPFactory,\n} = Isomorphic;\n\nconst Worker: Runtime = {\n  getDefaultStrategy,\n  Transports,\n  setup,\n  getProtocol,\n  isXHRSupported,\n  getLocalStorage,\n  createXHR,\n  createWebSocket,\n  addUnloadListener,\n  removeUnloadListener,\n  transportConnectionInitializer,\n  createSocketRequest,\n  HTTPFactory,\n\n  TimelineTransport: fetchTimeline,\n\n  getAuthorizers(): AuthTransports {\n    return { ajax: fetchAuth };\n  },\n\n  getWebSocketAPI() {\n    return WebSocket;\n  },\n\n  getXHRAPI() {\n    return XMLHttpRequest;\n  },\n\n  getNetwork() {\n    return Network;\n  },\n\n  randomInt(max: number): number {\n    /**\n     * Return values in the range of [0, 1[\n     */\n    const random = function () {\n      const crypto = globalThis.crypto || globalThis['msCrypto'];\n      const random = crypto.getRandomValues(new Uint32Array(1))[0];\n\n      return random / 2 ** 32;\n    };\n\n    return Math.floor(random() * max);\n  },\n};\n\nexport default Worker;\n", "enum TimelineLevel {\n  ERROR = 3,\n  INFO = 6,\n  DEBUG = 7,\n}\n\nexport default TimelineLevel;\n", "import * as Collections from '../utils/collections';\nimport Util from '../util';\nimport { default as Level } from './level';\n\nexport interface TimelineOptions {\n  level?: Level;\n  limit?: number;\n  version?: string;\n  cluster?: string;\n  features?: string[];\n  params?: any;\n}\n\nexport default class Timeline {\n  key: string;\n  session: number;\n  events: any[];\n  options: TimelineOptions;\n  sent: number;\n  uniqueID: number;\n\n  constructor(key: string, session: number, options: TimelineOptions) {\n    this.key = key;\n    this.session = session;\n    this.events = [];\n    this.options = options || {};\n    this.sent = 0;\n    this.uniqueID = 0;\n  }\n\n  log(level, event) {\n    if (level <= this.options.level) {\n      this.events.push(\n        Collections.extend({}, event, { timestamp: Util.now() }),\n      );\n      if (this.options.limit && this.events.length > this.options.limit) {\n        this.events.shift();\n      }\n    }\n  }\n\n  error(event) {\n    this.log(Level.ERROR, event);\n  }\n\n  info(event) {\n    this.log(Level.INFO, event);\n  }\n\n  debug(event) {\n    this.log(Level.DEBUG, event);\n  }\n\n  isEmpty() {\n    return this.events.length === 0;\n  }\n\n  send(sendfn, callback) {\n    var data = Collections.extend(\n      {\n        session: this.session,\n        bundle: this.sent + 1,\n        key: this.key,\n        lib: 'js',\n        version: this.options.version,\n        cluster: this.options.cluster,\n        features: this.options.features,\n        timeline: this.events,\n      },\n      this.options.params,\n    );\n\n    this.events = [];\n    sendfn(data, (error, result) => {\n      if (!error) {\n        this.sent++;\n      }\n      if (callback) {\n        callback(error, result);\n      }\n    });\n\n    return true;\n  }\n\n  generateUniqueID(): number {\n    this.uniqueID++;\n    return this.uniqueID;\n  }\n}\n", "import Factory from '../utils/factory';\nimport Util from '../util';\nimport * as Errors from '../errors';\nimport * as Collections from '../utils/collections';\nimport Strategy from './strategy';\nimport Transport from '../transports/transport';\nimport StrategyOptions from './strategy_options';\nimport Handshake from '../connection/handshake';\n\n/** Provides a strategy interface for transports.\n *\n * @param {String} name\n * @param {Number} priority\n * @param {Class} transport\n * @param {Object} options\n */\nexport default class TransportStrategy implements Strategy {\n  name: string;\n  priority: number;\n  transport: Transport;\n  options: StrategyOptions;\n\n  constructor(\n    name: string,\n    priority: number,\n    transport: Transport,\n    options: StrategyOptions,\n  ) {\n    this.name = name;\n    this.priority = priority;\n    this.transport = transport;\n    this.options = options || {};\n  }\n\n  /** Returns whether the transport is supported in the browser.\n   *\n   * @returns {Boolean}\n   */\n  isSupported(): boolean {\n    return this.transport.isSupported({\n      useTLS: this.options.useTLS,\n    });\n  }\n\n  /** Launches a connection attempt and returns a strategy runner.\n   *\n   * @param  {Function} callback\n   * @return {Object} strategy runner\n   */\n  connect(minPriority: number, callback: Function) {\n    if (!this.isSupported()) {\n      return failAttempt(new Errors.UnsupportedStrategy(), callback);\n    } else if (this.priority < minPriority) {\n      return failAttempt(new Errors.TransportPriorityTooLow(), callback);\n    }\n\n    var connected = false;\n    var transport = this.transport.createConnection(\n      this.name,\n      this.priority,\n      this.options.key,\n      this.options,\n    );\n    var handshake = null;\n\n    var onInitialized = function () {\n      transport.unbind('initialized', onInitialized);\n      transport.connect();\n    };\n    var onOpen = function () {\n      handshake = Factory.createHandshake(transport, function (result) {\n        connected = true;\n        unbindListeners();\n        callback(null, result);\n      });\n    };\n    var onError = function (error) {\n      unbindListeners();\n      callback(error);\n    };\n    var onClosed = function () {\n      unbindListeners();\n      var serializedTransport;\n\n      // The reason for this try/catch block is that on React Native\n      // the WebSocket object is circular. Therefore transport.socket will\n      // throw errors upon stringification. Collections.safeJSONStringify\n      // discards circular references when serializing.\n      serializedTransport = Collections.safeJSONStringify(transport);\n      callback(new Errors.TransportClosed(serializedTransport));\n    };\n\n    var unbindListeners = function () {\n      transport.unbind('initialized', onInitialized);\n      transport.unbind('open', onOpen);\n      transport.unbind('error', onError);\n      transport.unbind('closed', onClosed);\n    };\n\n    transport.bind('initialized', onInitialized);\n    transport.bind('open', onOpen);\n    transport.bind('error', onError);\n    transport.bind('closed', onClosed);\n\n    // connect will be called automatically after initialization\n    transport.initialize();\n\n    return {\n      abort: () => {\n        if (connected) {\n          return;\n        }\n        unbindListeners();\n        if (handshake) {\n          handshake.close();\n        } else {\n          transport.close();\n        }\n      },\n      forceMinPriority: (p) => {\n        if (connected) {\n          return;\n        }\n        if (this.priority < p) {\n          if (handshake) {\n            handshake.close();\n          } else {\n            transport.close();\n          }\n        }\n      },\n    };\n  }\n}\n\nfunction failAttempt(error: Error, callback: Function) {\n  Util.defer(function () {\n    callback(error);\n  });\n  return {\n    abort: function () {},\n    forceMinPriority: function () {},\n  };\n}\n", "import * as Collections from '../utils/collections';\nimport Util from '../util';\nimport TransportManager from '../transports/transport_manager';\nimport * as Errors from '../errors';\nimport Strategy from './strategy';\nimport TransportStrategy from './transport_strategy';\nimport StrategyOptions from '../strategies/strategy_options';\nimport { Config } from '../config';\nimport Runtime from 'runtime';\n\nconst { Transports } = Runtime;\n\nexport var defineTransport = function (\n  config: Config,\n  name: string,\n  type: string,\n  priority: number,\n  options: StrategyOptions,\n  manager?: TransportManager,\n): Strategy {\n  var transportClass = Transports[type];\n  if (!transportClass) {\n    throw new Errors.UnsupportedTransport(type);\n  }\n\n  var enabled =\n    (!config.enabledTransports ||\n      Collections.arrayIndexOf(config.enabledTransports, name) !== -1) &&\n    (!config.disabledTransports ||\n      Collections.arrayIndexOf(config.disabledTransports, name) === -1);\n\n  var transport;\n  if (enabled) {\n    options = Object.assign(\n      { ignoreNullOrigin: config.ignoreNullOrigin },\n      options,\n    );\n\n    transport = new TransportStrategy(\n      name,\n      priority,\n      manager ? manager.getAssistant(transportClass) : transportClass,\n      options,\n    );\n  } else {\n    transport = UnsupportedStrategy;\n  }\n\n  return transport;\n};\n\nvar UnsupportedStrategy: Strategy = {\n  isSupported: function () {\n    return false;\n  },\n  connect: function (_, callback) {\n    var deferred = Util.defer(function () {\n      callback(new Errors.UnsupportedStrategy());\n    });\n    return {\n      abort: function () {\n        deferred.ensureAborted();\n      },\n      forceMinPriority: function () {},\n    };\n  },\n};\n", "export enum AuthRequestType {\n  UserAuthentication = 'user-authentication',\n  ChannelAuthorization = 'channel-authorization',\n}\n\nexport interface ChannelAuthorizationData {\n  auth: string;\n  channel_data?: string;\n  shared_secret?: string;\n}\n\nexport type ChannelAuthorizationCallback = (\n  error: Error | null,\n  authData: ChannelAuthorizationData | null,\n) => void;\n\nexport interface ChannelAuthorizationRequestParams {\n  socketId: string;\n  channelName: string;\n}\n\nexport interface ChannelAuthorizationHandler {\n  (\n    params: ChannelAuthorizationRequestParams,\n    callback: ChannelAuthorizationCallback,\n  ): void;\n}\n\nexport interface UserAuthenticationData {\n  auth: string;\n  user_data: string;\n}\n\nexport type UserAuthenticationCallback = (\n  error: Error | null,\n  authData: UserAuthenticationData | null,\n) => void;\n\nexport interface UserAuthenticationRequestParams {\n  socketId: string;\n}\n\nexport interface UserAuthenticationHandler {\n  (\n    params: UserAuthenticationRequestParams,\n    callback: UserAuthenticationCallback,\n  ): void;\n}\n\nexport type AuthTransportCallback =\n  | ChannelAuthorizationCallback\n  | UserAuthenticationCallback;\n\nexport interface AuthOptionsT<AuthHandler> {\n  transport: 'ajax' | 'jsonp';\n  endpoint: string;\n  params?: any;\n  headers?: any;\n  paramsProvider?: () => any;\n  headersProvider?: () => any;\n  customHandler?: AuthHandler;\n}\n\nexport declare type UserAuthenticationOptions =\n  AuthOptionsT<UserAuthenticationHandler>;\nexport declare type ChannelAuthorizationOptions =\n  AuthOptionsT<ChannelAuthorizationHandler>;\n\nexport interface InternalAuthOptions {\n  transport: 'ajax' | 'jsonp';\n  endpoint: string;\n  params?: any;\n  headers?: any;\n  paramsProvider?: () => any;\n  headersProvider?: () => any;\n}\n", "import {\n  UserAuthenticationCallback,\n  InternalAuthOptions,\n  UserAuthenticationHandler,\n  UserAuthenticationRequestParams,\n  AuthRequestType,\n} from './options';\n\nimport Runtime from 'runtime';\n\nconst composeChannelQuery = (\n  params: UserAuthenticationRequestParams,\n  authOptions: InternalAuthOptions,\n) => {\n  var query = 'socket_id=' + encodeURIComponent(params.socketId);\n\n  for (var key in authOptions.params) {\n    query +=\n      '&' +\n      encodeURIComponent(key) +\n      '=' +\n      encodeURIComponent(authOptions.params[key]);\n  }\n\n  if (authOptions.paramsProvider != null) {\n    let dynamicParams = authOptions.paramsProvider();\n    for (var key in dynamicParams) {\n      query +=\n        '&' +\n        encodeURIComponent(key) +\n        '=' +\n        encodeURIComponent(dynamicParams[key]);\n    }\n  }\n\n  return query;\n};\n\nconst UserAuthenticator = (\n  authOptions: InternalAuthOptions,\n): UserAuthenticationHandler => {\n  if (typeof Runtime.getAuthorizers()[authOptions.transport] === 'undefined') {\n    throw `'${authOptions.transport}' is not a recognized auth transport`;\n  }\n\n  return (\n    params: UserAuthenticationRequestParams,\n    callback: UserAuthenticationCallback,\n  ) => {\n    const query = composeChannelQuery(params, authOptions);\n\n    Runtime.getAuthorizers()[authOptions.transport](\n      Runtime,\n      query,\n      authOptions,\n      AuthRequestType.UserAuthentication,\n      callback,\n    );\n  };\n};\n\nexport default UserAuthenticator;\n", "import {\n  AuthRequestType,\n  InternalAuthOptions,\n  ChannelAuthorizationHandler,\n  ChannelAuthorizationRequestParams,\n  ChannelAuthorizationCallback,\n} from './options';\n\nimport Runtime from 'runtime';\n\nconst composeChannelQuery = (\n  params: ChannelAuthorizationRequestParams,\n  authOptions: InternalAuthOptions,\n) => {\n  var query = 'socket_id=' + encodeURIComponent(params.socketId);\n\n  query += '&channel_name=' + encodeURIComponent(params.channelName);\n\n  for (var key in authOptions.params) {\n    query +=\n      '&' +\n      encodeURIComponent(key) +\n      '=' +\n      encodeURIComponent(authOptions.params[key]);\n  }\n\n  if (authOptions.paramsProvider != null) {\n    let dynamicParams = authOptions.paramsProvider();\n    for (var key in dynamicParams) {\n      query +=\n        '&' +\n        encodeURIComponent(key) +\n        '=' +\n        encodeURIComponent(dynamicParams[key]);\n    }\n  }\n\n  return query;\n};\n\nconst ChannelAuthorizer = (\n  authOptions: InternalAuthOptions,\n): ChannelAuthorizationHandler => {\n  if (typeof Runtime.getAuthorizers()[authOptions.transport] === 'undefined') {\n    throw `'${authOptions.transport}' is not a recognized auth transport`;\n  }\n\n  return (\n    params: ChannelAuthorizationRequestParams,\n    callback: ChannelAuthorizationCallback,\n  ) => {\n    const query = composeChannelQuery(params, authOptions);\n\n    Runtime.getAuthorizers()[authOptions.transport](\n      Runtime,\n      query,\n      authOptions,\n      AuthRequestType.ChannelAuthorization,\n      callback,\n    );\n  };\n};\n\nexport default ChannelAuthorizer;\n", "import { Options } from './options';\nimport Defaults from './defaults';\nimport {\n  Channel<PERSON>uth<PERSON>zation<PERSON><PERSON><PERSON>,\n  UserAuthenticationHandler,\n  ChannelAuthorizationOptions,\n} from './auth/options';\nimport UserAuthenticator from './auth/user_authenticator';\nimport ChannelAuthorizer from './auth/channel_authorizer';\nimport { ChannelAuthorizerProxy } from './auth/deprecated_channel_authorizer';\nimport Runtime from 'runtime';\nimport * as nacl from 'tweetnacl';\n\nexport type AuthTransport = 'ajax' | 'jsonp';\nexport type Transport =\n  | 'ws'\n  | 'wss'\n  | 'xhr_streaming'\n  | 'xhr_polling'\n  | 'sockjs';\n\nexport interface Config {\n  // these are all 'required' config parameters, it's not necessary for the user\n  // to set them, but they have configured defaults.\n  activityTimeout: number;\n  enableStats: boolean;\n  httpHost: string;\n  httpPath: string;\n  httpPort: number;\n  httpsPort: number;\n  pongTimeout: number;\n  statsHost: string;\n  unavailableTimeout: number;\n  useTLS: boolean;\n  wsHost: string;\n  wsPath: string;\n  wsPort: number;\n  wssPort: number;\n  userAuthenticator: UserAuthenticationHandler;\n  channelAuthorizer: ChannelAuthorizationHandler;\n\n  // these are all optional parameters or overrrides. The customer can set these\n  // but it's not strictly necessary\n  forceTLS?: boolean;\n  cluster?: string;\n  disabledTransports?: Transport[];\n  enabledTransports?: Transport[];\n  ignoreNullOrigin?: boolean;\n  nacl?: nacl;\n  timelineParams?: any;\n}\n\n// getConfig mainly sets the defaults for the options that are not provided\nexport function getConfig(opts: Options, pusher): Config {\n  let config: Config = {\n    activityTimeout: opts.activityTimeout || Defaults.activityTimeout,\n    cluster: opts.cluster,\n    httpPath: opts.httpPath || Defaults.httpPath,\n    httpPort: opts.httpPort || Defaults.httpPort,\n    httpsPort: opts.httpsPort || Defaults.httpsPort,\n    pongTimeout: opts.pongTimeout || Defaults.pongTimeout,\n    statsHost: opts.statsHost || Defaults.stats_host,\n    unavailableTimeout: opts.unavailableTimeout || Defaults.unavailableTimeout,\n    wsPath: opts.wsPath || Defaults.wsPath,\n    wsPort: opts.wsPort || Defaults.wsPort,\n    wssPort: opts.wssPort || Defaults.wssPort,\n\n    enableStats: getEnableStatsConfig(opts),\n    httpHost: getHttpHost(opts),\n    useTLS: shouldUseTLS(opts),\n    wsHost: getWebsocketHost(opts),\n\n    userAuthenticator: buildUserAuthenticator(opts),\n    channelAuthorizer: buildChannelAuthorizer(opts, pusher),\n  };\n\n  if ('disabledTransports' in opts)\n    config.disabledTransports = opts.disabledTransports;\n  if ('enabledTransports' in opts)\n    config.enabledTransports = opts.enabledTransports;\n  if ('ignoreNullOrigin' in opts)\n    config.ignoreNullOrigin = opts.ignoreNullOrigin;\n  if ('timelineParams' in opts) config.timelineParams = opts.timelineParams;\n  if ('nacl' in opts) {\n    config.nacl = opts.nacl;\n  }\n\n  return config;\n}\n\nfunction getHttpHost(opts: Options): string {\n  if (opts.httpHost) {\n    return opts.httpHost;\n  }\n  if (opts.cluster) {\n    return `sockjs-${opts.cluster}.pusher.com`;\n  }\n  return Defaults.httpHost;\n}\n\nfunction getWebsocketHost(opts: Options): string {\n  if (opts.wsHost) {\n    return opts.wsHost;\n  }\n  return getWebsocketHostFromCluster(opts.cluster);\n}\n\nfunction getWebsocketHostFromCluster(cluster: string): string {\n  return `ws-${cluster}.pusher.com`;\n}\n\nfunction shouldUseTLS(opts: Options): boolean {\n  if (Runtime.getProtocol() === 'https:') {\n    return true;\n  } else if (opts.forceTLS === false) {\n    return false;\n  }\n  return true;\n}\n\n// if enableStats is set take the value\n// if disableStats is set take the inverse\n// otherwise default to false\nfunction getEnableStatsConfig(opts: Options): boolean {\n  if ('enableStats' in opts) {\n    return opts.enableStats;\n  }\n  if ('disableStats' in opts) {\n    return !opts.disableStats;\n  }\n  return false;\n}\n\nfunction buildUserAuthenticator(opts: Options): UserAuthenticationHandler {\n  const userAuthentication = {\n    ...Defaults.userAuthentication,\n    ...opts.userAuthentication,\n  };\n  if (\n    'customHandler' in userAuthentication &&\n    userAuthentication['customHandler'] != null\n  ) {\n    return userAuthentication['customHandler'];\n  }\n\n  return UserAuthenticator(userAuthentication);\n}\n\nfunction buildChannelAuth(opts: Options, pusher): ChannelAuthorizationOptions {\n  let channelAuthorization: ChannelAuthorizationOptions;\n  if ('channelAuthorization' in opts) {\n    channelAuthorization = {\n      ...Defaults.channelAuthorization,\n      ...opts.channelAuthorization,\n    };\n  } else {\n    channelAuthorization = {\n      transport: opts.authTransport || Defaults.authTransport,\n      endpoint: opts.authEndpoint || Defaults.authEndpoint,\n    };\n    if ('auth' in opts) {\n      if ('params' in opts.auth) channelAuthorization.params = opts.auth.params;\n      if ('headers' in opts.auth)\n        channelAuthorization.headers = opts.auth.headers;\n    }\n    if ('authorizer' in opts)\n      channelAuthorization.customHandler = ChannelAuthorizerProxy(\n        pusher,\n        channelAuthorization,\n        opts.authorizer,\n      );\n  }\n  return channelAuthorization;\n}\n\nfunction buildChannelAuthorizer(\n  opts: Options,\n  pusher,\n): ChannelAuthorizationHandler {\n  const channelAuthorization = buildChannelAuth(opts, pusher);\n  if (\n    'customHandler' in channelAuthorization &&\n    channelAuthorization['customHandler'] != null\n  ) {\n    return channelAuthorization['customHandler'];\n  }\n\n  return ChannelAuthorizer(channelAuthorization);\n}\n", "import Channel from '../channels/channel';\nimport {\n  ChannelAuthorizationCallback,\n  ChannelAuthorizationHandler,\n  ChannelAuthorizationRequestParams,\n  InternalAuthOptions,\n} from './options';\n\nexport interface DeprecatedChannelAuthorizer {\n  authorize(socketId: string, callback: ChannelAuthorizationCallback): void;\n}\n\nexport interface ChannelAuthorizerGenerator {\n  (\n    channel: Channel,\n    options: DeprecatedAuthorizerOptions,\n  ): DeprecatedChannelAuthorizer;\n}\n\nexport interface DeprecatedAuthOptions {\n  params?: any;\n  headers?: any;\n}\n\nexport interface DeprecatedAuthorizerOptions {\n  authTransport: 'ajax' | 'jsonp';\n  authEndpoint: string;\n  auth?: DeprecatedAuthOptions;\n}\n\nexport const ChannelAuthorizerProxy = (\n  pusher,\n  authOptions: InternalAuthOptions,\n  channelAuthorizerGenerator: ChannelAuthorizerGenerator,\n): ChannelAuthorizationHandler => {\n  const deprecatedAuthorizerOptions: DeprecatedAuthorizerOptions = {\n    authTransport: authOptions.transport,\n    authEndpoint: authOptions.endpoint,\n    auth: {\n      params: authOptions.params,\n      headers: authOptions.headers,\n    },\n  };\n  return (\n    params: ChannelAuthorizationRequestParams,\n    callback: ChannelAuthorizationCallback,\n  ) => {\n    const channel = pusher.channel(params.channelName);\n    // This line creates a new channel authorizer every time.\n    // In the past, this was only done once per channel and reused.\n    // We can do that again if we want to keep this behavior intact.\n    const channelAuthorizer: DeprecatedChannelAuthorizer =\n      channelAuthorizerGenerator(channel, deprecatedAuthorizerOptions);\n    channelAuthorizer.authorize(params.socketId, callback);\n  };\n};\n", "import Logger from './logger';\nimport Pusher from './pusher';\nimport EventsDispatcher from './events/dispatcher';\n\nexport default class WatchlistFacade extends EventsDispatcher {\n  private pusher: Pusher;\n\n  public constructor(pusher: Pusher) {\n    super(function (eventName, data) {\n      Logger.debug(`No callbacks on watchlist events for ${eventName}`);\n    });\n\n    this.pusher = pusher;\n    this.bindWatchlistInternalEvent();\n  }\n\n  handleEvent(pusherEvent) {\n    pusherEvent.data.events.forEach((watchlistEvent) => {\n      this.emit(watchlistEvent.name, watchlistEvent);\n    });\n  }\n\n  private bindWatchlistInternalEvent() {\n    this.pusher.connection.bind('message', (pusherEvent) => {\n      var eventName = pusherEvent.event;\n      if (eventName === 'pusher_internal:watchlist_events') {\n        this.handleEvent(pusherEvent);\n      }\n    });\n  }\n}\n", "function flatPromise() {\n  let resolve, reject;\n  const promise = new Promise((res, rej) => {\n    resolve = res;\n    reject = rej;\n  });\n  return { promise, resolve, reject };\n}\n\nexport default flatPromise;\n", "import Pusher from './pusher';\nimport Logger from './logger';\nimport {\n  UserAuthenticationData,\n  UserAuthenticationCallback,\n} from './auth/options';\nimport Channel from './channels/channel';\nimport WatchlistFacade from './watchlist';\nimport EventsDispatcher from './events/dispatcher';\nimport flatPromise from './utils/flat_promise';\n\nexport default class UserFacade extends EventsDispatcher {\n  pusher: Pusher;\n  signin_requested: boolean = false;\n  user_data: any = null;\n  serverToUserChannel: Channel = null;\n  signinDonePromise: Promise<any> = null;\n  watchlist: WatchlistFacade;\n  private _signinDoneResolve: Function = null;\n\n  public constructor(pusher: Pusher) {\n    super(function (eventName, data) {\n      Logger.debug('No callbacks on user for ' + eventName);\n    });\n    this.pusher = pusher;\n    this.pusher.connection.bind('state_change', ({ previous, current }) => {\n      if (previous !== 'connected' && current === 'connected') {\n        this._signin();\n      }\n      if (previous === 'connected' && current !== 'connected') {\n        this._cleanup();\n        this._newSigninPromiseIfNeeded();\n      }\n    });\n\n    this.watchlist = new WatchlistFacade(pusher);\n\n    this.pusher.connection.bind('message', (event) => {\n      var eventName = event.event;\n      if (eventName === 'pusher:signin_success') {\n        this._onSigninSuccess(event.data);\n      }\n      if (\n        this.serverToUserChannel &&\n        this.serverToUserChannel.name === event.channel\n      ) {\n        this.serverToUserChannel.handleEvent(event);\n      }\n    });\n  }\n\n  public signin() {\n    if (this.signin_requested) {\n      return;\n    }\n\n    this.signin_requested = true;\n    this._signin();\n  }\n\n  private _signin() {\n    if (!this.signin_requested) {\n      return;\n    }\n\n    this._newSigninPromiseIfNeeded();\n\n    if (this.pusher.connection.state !== 'connected') {\n      // Signin will be attempted when the connection is connected\n      return;\n    }\n\n    this.pusher.config.userAuthenticator(\n      {\n        socketId: this.pusher.connection.socket_id,\n      },\n      this._onAuthorize,\n    );\n  }\n\n  private _onAuthorize: UserAuthenticationCallback = (\n    err,\n    authData: UserAuthenticationData,\n  ) => {\n    if (err) {\n      Logger.warn(`Error during signin: ${err}`);\n      this._cleanup();\n      return;\n    }\n\n    this.pusher.send_event('pusher:signin', {\n      auth: authData.auth,\n      user_data: authData.user_data,\n    });\n\n    // Later when we get pusher:singin_success event, the user will be marked as signed in\n  };\n\n  private _onSigninSuccess(data: any) {\n    try {\n      this.user_data = JSON.parse(data.user_data);\n    } catch (e) {\n      Logger.error(`Failed parsing user data after signin: ${data.user_data}`);\n      this._cleanup();\n      return;\n    }\n\n    if (typeof this.user_data.id !== 'string' || this.user_data.id === '') {\n      Logger.error(\n        `user_data doesn't contain an id. user_data: ${this.user_data}`,\n      );\n      this._cleanup();\n      return;\n    }\n\n    // Signin succeeded\n    this._signinDoneResolve();\n    this._subscribeChannels();\n  }\n\n  private _subscribeChannels() {\n    const ensure_subscribed = (channel) => {\n      if (channel.subscriptionPending && channel.subscriptionCancelled) {\n        channel.reinstateSubscription();\n      } else if (\n        !channel.subscriptionPending &&\n        this.pusher.connection.state === 'connected'\n      ) {\n        channel.subscribe();\n      }\n    };\n\n    this.serverToUserChannel = new Channel(\n      `#server-to-user-${this.user_data.id}`,\n      this.pusher,\n    );\n    this.serverToUserChannel.bind_global((eventName, data) => {\n      if (\n        eventName.indexOf('pusher_internal:') === 0 ||\n        eventName.indexOf('pusher:') === 0\n      ) {\n        // ignore internal events\n        return;\n      }\n      this.emit(eventName, data);\n    });\n    ensure_subscribed(this.serverToUserChannel);\n  }\n\n  private _cleanup() {\n    this.user_data = null;\n    if (this.serverToUserChannel) {\n      this.serverToUserChannel.unbind_all();\n      this.serverToUserChannel.disconnect();\n      this.serverToUserChannel = null;\n    }\n\n    if (this.signin_requested) {\n      // If signin is in progress and cleanup is called,\n      // Mark the current signin process as done.\n      this._signinDoneResolve();\n    }\n  }\n\n  private _newSigninPromiseIfNeeded() {\n    if (!this.signin_requested) {\n      return;\n    }\n\n    // If there is a promise and it is not resolved, return without creating a new one.\n    if (this.signinDonePromise && !(this.signinDonePromise as any).done) {\n      return;\n    }\n\n    // This promise is never rejected.\n    // It gets resolved when the signin process is done whether it failed or succeeded\n    const { promise, resolve, reject: _ } = flatPromise();\n    (promise as any).done = false;\n    const setDone = () => {\n      (promise as any).done = true;\n    };\n    promise.then(setDone).catch(setDone);\n    this.signinDonePromise = promise;\n    this._signinDoneResolve = resolve;\n  }\n}\n", "import AbstractRuntime from '../runtimes/interface';\nimport Runtime from 'runtime';\nimport Util from './util';\nimport * as Collections from './utils/collections';\nimport Channels from './channels/channels';\nimport Channel from './channels/channel';\nimport { default as EventsDispatcher } from './events/dispatcher';\nimport Timeline from './timeline/timeline';\nimport TimelineSender from './timeline/timeline_sender';\nimport TimelineLevel from './timeline/level';\nimport { defineTransport } from './strategies/strategy_builder';\nimport ConnectionManager from './connection/connection_manager';\nimport ConnectionManagerOptions from './connection/connection_manager_options';\nimport { PeriodicTimer } from './utils/timers';\nimport Defaults from './defaults';\nimport * as DefaultConfig from './config';\nimport Logger from './logger';\nimport Factory from './utils/factory';\nimport UrlStore from 'core/utils/url_store';\nimport { Options, validateOptions } from './options';\nimport { Config, getConfig } from './config';\nimport StrategyOptions from './strategies/strategy_options';\nimport UserFacade from './user';\n\nexport default class Pusher {\n  /*  STATIC PROPERTIES */\n  static instances: Pusher[] = [];\n  static isReady: boolean = false;\n  static logToConsole: boolean = false;\n\n  // for jsonp\n  static Runtime: AbstractRuntime = Runtime;\n  static ScriptReceivers: any = (<any>Runtime).ScriptReceivers;\n  static DependenciesReceivers: any = (<any>Runtime).DependenciesReceivers;\n  static auth_callbacks: any = (<any>Runtime).auth_callbacks;\n\n  static ready() {\n    Pusher.isReady = true;\n    for (var i = 0, l = Pusher.instances.length; i < l; i++) {\n      Pusher.instances[i].connect();\n    }\n  }\n\n  static log: (message: any) => void;\n\n  private static getClientFeatures(): string[] {\n    return Collections.keys(\n      Collections.filterObject({ ws: Runtime.Transports.ws }, function (t) {\n        return t.isSupported({});\n      }),\n    );\n  }\n\n  /* INSTANCE PROPERTIES */\n  key: string;\n  config: Config;\n  channels: Channels;\n  global_emitter: EventsDispatcher;\n  sessionID: number;\n  timeline: Timeline;\n  timelineSender: TimelineSender;\n  connection: ConnectionManager;\n  timelineSenderTimer: PeriodicTimer;\n  user: UserFacade;\n  constructor(app_key: string, options: Options) {\n    checkAppKey(app_key);\n    validateOptions(options);\n    this.key = app_key;\n    this.config = getConfig(options, this);\n\n    this.channels = Factory.createChannels();\n    this.global_emitter = new EventsDispatcher();\n    this.sessionID = Runtime.randomInt(1000000000);\n\n    this.timeline = new Timeline(this.key, this.sessionID, {\n      cluster: this.config.cluster,\n      features: Pusher.getClientFeatures(),\n      params: this.config.timelineParams || {},\n      limit: 50,\n      level: TimelineLevel.INFO,\n      version: Defaults.VERSION,\n    });\n    if (this.config.enableStats) {\n      this.timelineSender = Factory.createTimelineSender(this.timeline, {\n        host: this.config.statsHost,\n        path: '/timeline/v2/' + Runtime.TimelineTransport.name,\n      });\n    }\n\n    var getStrategy = (options: StrategyOptions) => {\n      return Runtime.getDefaultStrategy(this.config, options, defineTransport);\n    };\n\n    this.connection = Factory.createConnectionManager(this.key, {\n      getStrategy: getStrategy,\n      timeline: this.timeline,\n      activityTimeout: this.config.activityTimeout,\n      pongTimeout: this.config.pongTimeout,\n      unavailableTimeout: this.config.unavailableTimeout,\n      useTLS: Boolean(this.config.useTLS),\n    });\n\n    this.connection.bind('connected', () => {\n      this.subscribeAll();\n      if (this.timelineSender) {\n        this.timelineSender.send(this.connection.isUsingTLS());\n      }\n    });\n\n    this.connection.bind('message', (event) => {\n      var eventName = event.event;\n      var internal = eventName.indexOf('pusher_internal:') === 0;\n      if (event.channel) {\n        var channel = this.channel(event.channel);\n        if (channel) {\n          channel.handleEvent(event);\n        }\n      }\n      // Emit globally [deprecated]\n      if (!internal) {\n        this.global_emitter.emit(event.event, event.data);\n      }\n    });\n    this.connection.bind('connecting', () => {\n      this.channels.disconnect();\n    });\n    this.connection.bind('disconnected', () => {\n      this.channels.disconnect();\n    });\n    this.connection.bind('error', (err) => {\n      Logger.warn(err);\n    });\n\n    Pusher.instances.push(this);\n    this.timeline.info({ instances: Pusher.instances.length });\n\n    this.user = new UserFacade(this);\n\n    if (Pusher.isReady) {\n      this.connect();\n    }\n  }\n\n  channel(name: string): Channel {\n    return this.channels.find(name);\n  }\n\n  allChannels(): Channel[] {\n    return this.channels.all();\n  }\n\n  connect() {\n    this.connection.connect();\n\n    if (this.timelineSender) {\n      if (!this.timelineSenderTimer) {\n        var usingTLS = this.connection.isUsingTLS();\n        var timelineSender = this.timelineSender;\n        this.timelineSenderTimer = new PeriodicTimer(60000, function () {\n          timelineSender.send(usingTLS);\n        });\n      }\n    }\n  }\n\n  disconnect() {\n    this.connection.disconnect();\n\n    if (this.timelineSenderTimer) {\n      this.timelineSenderTimer.ensureAborted();\n      this.timelineSenderTimer = null;\n    }\n  }\n\n  bind(event_name: string, callback: Function, context?: any): Pusher {\n    this.global_emitter.bind(event_name, callback, context);\n    return this;\n  }\n\n  unbind(event_name?: string, callback?: Function, context?: any): Pusher {\n    this.global_emitter.unbind(event_name, callback, context);\n    return this;\n  }\n\n  bind_global(callback: Function): Pusher {\n    this.global_emitter.bind_global(callback);\n    return this;\n  }\n\n  unbind_global(callback?: Function): Pusher {\n    this.global_emitter.unbind_global(callback);\n    return this;\n  }\n\n  unbind_all(callback?: Function): Pusher {\n    this.global_emitter.unbind_all();\n    return this;\n  }\n\n  subscribeAll() {\n    var channelName;\n    for (channelName in this.channels.channels) {\n      if (this.channels.channels.hasOwnProperty(channelName)) {\n        this.subscribe(channelName);\n      }\n    }\n  }\n\n  subscribe(channel_name: string) {\n    var channel = this.channels.add(channel_name, this);\n    if (channel.subscriptionPending && channel.subscriptionCancelled) {\n      channel.reinstateSubscription();\n    } else if (\n      !channel.subscriptionPending &&\n      this.connection.state === 'connected'\n    ) {\n      channel.subscribe();\n    }\n    return channel;\n  }\n\n  unsubscribe(channel_name: string) {\n    var channel = this.channels.find(channel_name);\n    if (channel && channel.subscriptionPending) {\n      channel.cancelSubscription();\n    } else {\n      channel = this.channels.remove(channel_name);\n      if (channel && channel.subscribed) {\n        channel.unsubscribe();\n      }\n    }\n  }\n\n  send_event(event_name: string, data: any, channel?: string) {\n    return this.connection.send_event(event_name, data, channel);\n  }\n\n  shouldUseTLS(): boolean {\n    return this.config.useTLS;\n  }\n\n  signin() {\n    this.user.signin();\n  }\n}\n\nfunction checkAppKey(key) {\n  if (key === null || key === undefined) {\n    throw 'You must pass your app key when you instantiate Pusher.';\n  }\n}\n\nRuntime.setup(Pusher);\n", "import ConnectionManager from './connection/connection_manager';\nimport {\n  ChannelAuthorizationOptions,\n  UserAuthenticationOptions,\n} from './auth/options';\nimport {\n  ChannelAuthorizerGenerator,\n  DeprecatedAuthOptions,\n} from './auth/deprecated_channel_authorizer';\nimport { AuthTransport, Transport } from './config';\nimport * as nacl from 'tweetnacl';\nimport Logger from './logger';\n\nexport interface Options {\n  activityTimeout?: number;\n\n  auth?: DeprecatedAuthOptions; // DEPRECATED use channelAuthorization instead\n  authEndpoint?: string; // DEPRECATED use channelAuthorization instead\n  authTransport?: AuthTransport; // DEPRECATED use channelAuthorization instead\n  authorizer?: ChannelAuthorizerGenerator; // DEPRECATED use channelAuthorization instead\n\n  channelAuthorization?: ChannelAuthorizationOptions;\n  userAuthentication?: UserAuthenticationOptions;\n\n  cluster: string;\n  enableStats?: boolean;\n  disableStats?: boolean;\n  disabledTransports?: Transport[];\n  enabledTransports?: Transport[];\n  forceTLS?: boolean;\n  httpHost?: string;\n  httpPath?: string;\n  httpPort?: number;\n  httpsPort?: number;\n  ignoreNullOrigin?: boolean;\n  nacl?: nacl;\n  pongTimeout?: number;\n  statsHost?: string;\n  timelineParams?: any;\n  unavailableTimeout?: number;\n  wsHost?: string;\n  wsPath?: string;\n  wsPort?: number;\n  wssPort?: number;\n}\n\nexport function validateOptions(options) {\n  if (options == null) {\n    throw 'You must pass an options object';\n  }\n  if (options.cluster == null) {\n    throw 'Options object must provide a cluster';\n  }\n  if ('disableStats' in options) {\n    Logger.warn(\n      'The disableStats option is deprecated in favor of enableStats',\n    );\n  }\n}\n"], "sourceRoot": ""}