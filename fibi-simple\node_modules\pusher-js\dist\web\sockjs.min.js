/* SockJS client, http://sockjs.org, MIT License
 *
 * Copyright (c) 2011-2012 VMware, Inc.
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 */

var JSON;JSON||(JSON={});
(function(){function p(d,n){var s,x,t,u,A=m,k,l=n[d];l&&"object"==typeof l&&"function"==typeof l.toJSON&&(l=l.toJSON(d));"function"==typeof w&&(l=w.call(n,d,l));switch(typeof l){case "string":return h(l);case "number":return isFinite(l)?String(l):"null";case "boolean":case "null":return String(l);case "object":if(!l)return"null";m+=z;k=[];if("[object Array]"===Object.prototype.toString.apply(l)){u=l.length;for(s=0;s<u;s+=1)k[s]=p(s,l)||"null";t=0===k.length?"[]":m?"[\n"+m+k.join(",\n"+m)+"\n"+A+"]":
"["+k.join(",")+"]";m=A;return t}if(w&&"object"==typeof w){u=w.length;for(s=0;s<u;s+=1)"string"==typeof w[s]&&(x=w[s],t=p(x,l),t&&k.push(h(x)+(m?": ":":")+t))}else for(x in l)Object.prototype.hasOwnProperty.call(l,x)&&(t=p(x,l),t&&k.push(h(x)+(m?": ":":")+t));t=0===k.length?"{}":m?"{\n"+m+k.join(",\n"+m)+"\n"+A+"}":"{"+k.join(",")+"}";m=A;return t}}function h(d){n.lastIndex=0;return n.test(d)?'"'+d.replace(n,function(d){var h=G[d];return"string"==typeof h?h:"\\u"+("0000"+d.charCodeAt(0).toString(16)).slice(-4)})+
'"':'"'+d+'"'}function d(d){return 10>d?"0"+d:d}"use strict";"function"!=typeof Date.prototype.toJSON&&(Date.prototype.toJSON=function(h){return isFinite(this.valueOf())?this.getUTCFullYear()+"-"+d(this.getUTCMonth()+1)+"-"+d(this.getUTCDate())+"T"+d(this.getUTCHours())+":"+d(this.getUTCMinutes())+":"+d(this.getUTCSeconds())+"Z":null},String.prototype.toJSON=Number.prototype.toJSON=Boolean.prototype.toJSON=function(d){return this.valueOf()});var u=/[\u0000\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g,
n=/[\\\"\x00-\x1f\x7f-\x9f\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g,m,z,G={"\b":"\\b","\t":"\\t","\n":"\\n","\f":"\\f","\r":"\\r",'"':'\\"',"\\":"\\\\"},w;"function"!=typeof JSON.stringify&&(JSON.stringify=function(d,h,s){var n;z=m="";if("number"==typeof s)for(n=0;n<s;n+=1)z+=" ";else"string"==typeof s&&(z=s);w=h;if(!h||"function"==typeof h||"object"==typeof h&&"number"==typeof h.length)return p("",{"":d});throw Error("JSON.stringify");});"function"!=
typeof JSON.parse&&(JSON.parse=function(d,h){function p(d,m){var n,k,l=d[m];if(l&&"object"==typeof l)for(n in l)Object.prototype.hasOwnProperty.call(l,n)&&(k=p(l,n),void 0!==k?l[n]=k:delete l[n]);return h.call(d,m,l)}var m;d=String(d);u.lastIndex=0;u.test(d)&&(d=d.replace(u,function(d){return"\\u"+("0000"+d.charCodeAt(0).toString(16)).slice(-4)}));if(/^[\],:{}\s]*$/.test(d.replace(/\\(?:["\\\/bfnrt]|u[0-9a-fA-F]{4})/g,"@").replace(/"[^"\\\n\r]*"|true|false|null|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?/g,
"]").replace(/(?:^|:|,)(?:\s*\[)+/g,"")))return m=eval("("+d+")"),"function"==typeof h?p({"":m},""):m;throw new SyntaxError("JSON.parse");})})();
var SockJS=function(){var p=document,h=window,d={},u=function(){};u.prototype.addEventListener=function(a,b){this._listeners||(this._listeners={});a in this._listeners||(this._listeners[a]=[]);var c=this._listeners[a];-1===d.arrIndexOf(c,b)&&c.push(b)};u.prototype.removeEventListener=function(a,b){if(this._listeners&&a in this._listeners){var c=this._listeners[a],e=d.arrIndexOf(c,b);-1!==e&&(1<c.length?this._listeners[a]=c.slice(0,e).concat(c.slice(e+1)):delete this._listeners[a])}};u.prototype.dispatchEvent=
function(a){var b=a.type,c=Array.prototype.slice.call(arguments,0);this["on"+b]&&this["on"+b].apply(this,c);if(this._listeners&&b in this._listeners)for(var e=0;e<this._listeners[b].length;e++)this._listeners[b][e].apply(this,c)};var n=function(a,b){this.type=a;if("undefined"!==typeof b)for(var c in b)b.hasOwnProperty(c)&&(this[c]=b[c])};n.prototype.toString=function(){var a=[],b;for(b in this)if(this.hasOwnProperty(b)){var c=this[b];"function"===typeof c&&(c="[function]");a.push(b+"="+c)}return"SimpleEvent("+
a.join(", ")+")"};var m=function(a){this._events=a||[];this._listeners={}};m.prototype.emit=function(a){this._verifyType(a);if(!this._nuked){var b=Array.prototype.slice.call(arguments,1);this["on"+a]&&this["on"+a].apply(this,b);if(a in this._listeners)for(var c=0;c<this._listeners[a].length;c++)this._listeners[a][c].apply(this,b)}};m.prototype.on=function(a,b){this._verifyType(a);this._nuked||(a in this._listeners||(this._listeners[a]=[]),this._listeners[a].push(b))};m.prototype._verifyType=function(a){-1===
d.arrIndexOf(this._events,a)&&d.log("Event "+JSON.stringify(a)+" not listed "+JSON.stringify(this._events)+" in "+this)};m.prototype.nuke=function(){this._nuked=!0;for(var a=0;a<this._events.length;a++)delete this[this._events[a]];this._listeners={}};d.random_string=function(a,b){b=b||37;var c,e=[];for(c=0;c<a;c++)e.push("abcdefghijklmnopqrstuvwxyz0123456789_".substr(Math.floor(Math.random()*b),1));return e.join("")};d.random_number=function(a){return Math.floor(Math.random()*a)};d.random_number_string=
function(a){var b=(""+(a-1)).length;return(Array(b+1).join("0")+d.random_number(a)).slice(-b)};d.getOrigin=function(a){if(a.match(/^file:\/\//))return null;var b=a.match(/^(https?):\/\/([^\/:]+)(:([0-9]+))?/);a=b[1];var c=b[2];(b=b[4])||(b="https"===a?443:80);return a+"://"+c+":"+b};d.isSameOriginUrl=function(a,b){b||(b=h.location.href);return d.getOrigin(a)===d.getOrigin(b)};d.objectExtend=function(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c]);return a};d.polluteGlobalNamespace=function(){"_jp"in
h||(h._jp={})};d.closeFrame=function(a,b){return"c"+JSON.stringify([a,b])};d.userSetCode=function(a){return 1E3===a||3E3<=a&&4999>=a};d.countRTO=function(a){return 100<a?3*a:a+200};d.log=function(){h.console&&(console.log&&console.log.apply)&&console.log.apply(console,arguments)};d.bind=function(a,b){return a.bind?a.bind(b):function(){return a.apply(b,arguments)}};d.flatUrl=function(a){return-1===a.indexOf("?")&&-1===a.indexOf("#")};d.amendUrl=function(a){var b=p.location;if(!a)throw Error("Wrong url for SockJS");
if(!d.flatUrl(a))throw Error("Only basic urls are supported in SockJS");0===a.indexOf("//")&&(a=b.protocol+a);0===a.indexOf("/")&&(a=b.protocol+"//"+b.host+a);return a=a.replace(/[/]+$/,"")};d.arrIndexOf=function(a,b){for(var c=0;c<a.length;c++)if(a[c]===b)return c;return-1};d.arrSkip=function(a,b){var c=d.arrIndexOf(a,b);return-1===c?a.slice():a.slice(0,c).concat(a.slice(c+1))};d.isArray=Array.isArray||function(a){return 0<={}.toString.call(a).indexOf("Array")};d.delay=function(a,b){"function"===
typeof a&&(b=a,a=0);return setTimeout(b,a)};var z=/[\\\"\x00-\x1f\x7f-\x9f\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g,G={"\x00":"\\u0000","\u0001":"\\u0001","\u0002":"\\u0002","\u0003":"\\u0003","\u0004":"\\u0004","\u0005":"\\u0005","\u0006":"\\u0006","\u0007":"\\u0007","\b":"\\b","\t":"\\t","\n":"\\n","\x0B":"\\u000b","\f":"\\f","\r":"\\r","\u000e":"\\u000e","\u000f":"\\u000f","\u0010":"\\u0010","\u0011":"\\u0011","\u0012":"\\u0012","\u0013":"\\u0013",
"\u0014":"\\u0014","\u0015":"\\u0015","\u0016":"\\u0016","\u0017":"\\u0017","\u0018":"\\u0018","\u0019":"\\u0019","\u001a":"\\u001a","\u001b":"\\u001b","\u001c":"\\u001c","\u001d":"\\u001d","\u001e":"\\u001e","\u001f":"\\u001f",'"':'\\"',"\\":"\\\\","\u007f":"\\u007f","\u0080":"\\u0080","\u0081":"\\u0081","\u0082":"\\u0082","\u0083":"\\u0083","\u0084":"\\u0084","\u0085":"\\u0085","\u0086":"\\u0086","\u0087":"\\u0087","\u0088":"\\u0088","\u0089":"\\u0089","\u008a":"\\u008a","\u008b":"\\u008b","\u008c":"\\u008c",
"\u008d":"\\u008d","\u008e":"\\u008e","\u008f":"\\u008f","\u0090":"\\u0090","\u0091":"\\u0091","\u0092":"\\u0092","\u0093":"\\u0093","\u0094":"\\u0094","\u0095":"\\u0095","\u0096":"\\u0096","\u0097":"\\u0097","\u0098":"\\u0098","\u0099":"\\u0099","\u009a":"\\u009a","\u009b":"\\u009b","\u009c":"\\u009c","\u009d":"\\u009d","\u009e":"\\u009e","\u009f":"\\u009f","\u00ad":"\\u00ad","\u0600":"\\u0600","\u0601":"\\u0601","\u0602":"\\u0602","\u0603":"\\u0603","\u0604":"\\u0604","\u070f":"\\u070f","\u17b4":"\\u17b4",
"\u17b5":"\\u17b5","\u200c":"\\u200c","\u200d":"\\u200d","\u200e":"\\u200e","\u200f":"\\u200f","\u2028":"\\u2028","\u2029":"\\u2029","\u202a":"\\u202a","\u202b":"\\u202b","\u202c":"\\u202c","\u202d":"\\u202d","\u202e":"\\u202e","\u202f":"\\u202f","\u2060":"\\u2060","\u2061":"\\u2061","\u2062":"\\u2062","\u2063":"\\u2063","\u2064":"\\u2064","\u2065":"\\u2065","\u2066":"\\u2066","\u2067":"\\u2067","\u2068":"\\u2068","\u2069":"\\u2069","\u206a":"\\u206a","\u206b":"\\u206b","\u206c":"\\u206c","\u206d":"\\u206d",
"\u206e":"\\u206e","\u206f":"\\u206f","\ufeff":"\\ufeff","\ufff0":"\\ufff0","\ufff1":"\\ufff1","\ufff2":"\\ufff2","\ufff3":"\\ufff3","\ufff4":"\\ufff4","\ufff5":"\\ufff5","\ufff6":"\\ufff6","\ufff7":"\\ufff7","\ufff8":"\\ufff8","\ufff9":"\\ufff9","\ufffa":"\\ufffa","\ufffb":"\\ufffb","\ufffc":"\\ufffc","\ufffd":"\\ufffd","\ufffe":"\\ufffe","\uffff":"\\uffff"},w=/[\x00-\x1f\ud800-\udfff\ufffe\uffff\u0300-\u0333\u033d-\u0346\u034a-\u034c\u0350-\u0352\u0357-\u0358\u035c-\u0362\u0374\u037e\u0387\u0591-\u05af\u05c4\u0610-\u0617\u0653-\u0654\u0657-\u065b\u065d-\u065e\u06df-\u06e2\u06eb-\u06ec\u0730\u0732-\u0733\u0735-\u0736\u073a\u073d\u073f-\u0741\u0743\u0745\u0747\u07eb-\u07f1\u0951\u0958-\u095f\u09dc-\u09dd\u09df\u0a33\u0a36\u0a59-\u0a5b\u0a5e\u0b5c-\u0b5d\u0e38-\u0e39\u0f43\u0f4d\u0f52\u0f57\u0f5c\u0f69\u0f72-\u0f76\u0f78\u0f80-\u0f83\u0f93\u0f9d\u0fa2\u0fa7\u0fac\u0fb9\u1939-\u193a\u1a17\u1b6b\u1cda-\u1cdb\u1dc0-\u1dcf\u1dfc\u1dfe\u1f71\u1f73\u1f75\u1f77\u1f79\u1f7b\u1f7d\u1fbb\u1fbe\u1fc9\u1fcb\u1fd3\u1fdb\u1fe3\u1feb\u1fee-\u1fef\u1ff9\u1ffb\u1ffd\u2000-\u2001\u20d0-\u20d1\u20d4-\u20d7\u20e7-\u20e9\u2126\u212a-\u212b\u2329-\u232a\u2adc\u302b-\u302c\uaab2-\uaab3\uf900-\ufa0d\ufa10\ufa12\ufa15-\ufa1e\ufa20\ufa22\ufa25-\ufa26\ufa2a-\ufa2d\ufa30-\ufa6d\ufa70-\ufad9\ufb1d\ufb1f\ufb2a-\ufb36\ufb38-\ufb3c\ufb3e\ufb40-\ufb41\ufb43-\ufb44\ufb46-\ufb4e\ufff0-\uffff]/g,
E,P=JSON&&JSON.stringify||function(a){z.lastIndex=0;z.test(a)&&(a=a.replace(z,function(a){return G[a]}));return'"'+a+'"'},s=function(a){var b,c={},e=[];for(b=0;65536>b;b++)e.push(String.fromCharCode(b));a.lastIndex=0;e.join("").replace(a,function(a){c[a]="\\u"+("0000"+a.charCodeAt(0).toString(16)).slice(-4);return""});a.lastIndex=0;return c};d.quote=function(a){a=P(a);w.lastIndex=0;if(!w.test(a))return a;E||(E=s(w));return a.replace(w,function(a){return E[a]})};var x=["iframe-eventsource","iframe-htmlfile",
"iframe-xhr-polling","jsonp-polling"];d.probeProtocols=function(a){for(var b={},c=0;c<x.length;c++){var e=x[c];b[e]=g[e]&&g[e].enabled(a)}return b};d.detectProtocols=function(a,b,c){b=b||x;c=[];for(var e=0;e<b.length;e++){var d=b[e];a[d]&&c.push(d)}return c};d.defaultOptions=function(){return{devel:!1,debug:!1,protocols_whitelist:[],info:void 0,rtt:void 0,max_window_time:4E3,init_window_size:4096}};d.attachMessage=function(a){d.attachEvent("message",a)};d.attachEvent=function(a,b){"undefined"!==typeof h.addEventListener?
h.addEventListener(a,b,!1):(p.attachEvent("on"+a,b),h.attachEvent("on"+a,b))};d.detachMessage=function(a){d.detachEvent("message",a)};d.detachEvent=function(a,b){"undefined"!==typeof h.addEventListener?h.removeEventListener(a,b,!1):(p.detachEvent("on"+a,b),h.detachEvent("on"+a,b))};var t={},F=!1,A=function(){for(var a in t)t[a](),delete t[a]};d.attachEvent("unload",function(){F||(F=!0,A())});d.unload_add=function(a){var b=d.random_string(8);t[b]=a;F&&d.delay(A);return b};d.unload_del=function(a){a in
t&&delete t[a]};d.createIframe=function(a,b){var c=p.createElement("iframe"),e,f,q=function(){clearTimeout(e);try{c.onload=null}catch(a){}c.onerror=null},g=function(){c&&(q(),setTimeout(function(){c&&c.parentNode.removeChild(c);c=null},0),d.unload_del(f))},h=function(a){c&&(g(),b(a))};c.src=a;c.style.display="none";c.style.position="absolute";c.onerror=function(){h("onerror")};c.onload=function(){clearTimeout(e);e=setTimeout(function(){h("onload timeout")},2E3)};p.body.appendChild(c);e=setTimeout(function(){h("timeout")},
15E3);f=d.unload_add(g);return{post:function(a,b){try{c&&c.contentWindow&&c.contentWindow.postMessage(a,b)}catch(d){}},cleanup:g,loaded:q}};d.createHtmlfile=function(a,b){var c=new ActiveXObject("htmlfile"),e,f,q,g=function(){clearTimeout(e)},k=function(){c&&(g(),d.unload_del(f),q.parentNode.removeChild(q),q=c=null,CollectGarbage())};c.open();c.write('<html><script>document.domain="'+document.domain+'";\x3c/script></html>');c.close();c.parentWindow._jp=h._jp;var l=c.createElement("div");c.body.appendChild(l);
q=c.createElement("iframe");l.appendChild(q);q.src=a;e=setTimeout(function(){c&&(k(),b("timeout"))},15E3);f=d.unload_add(k);return{post:function(a,b){try{q&&q.contentWindow&&q.contentWindow.postMessage(a,b)}catch(c){}},cleanup:k,loaded:g}};var k=function(){};k.prototype=new m(["chunk","finish"]);k.prototype._start=function(a,b,c,e){var f=this;try{f.xhr=new XMLHttpRequest}catch(q){}if(!f.xhr)try{f.xhr=new h.ActiveXObject("Microsoft.XMLHTTP")}catch(g){}if(h.ActiveXObject||h.XDomainRequest)b+=(-1===
b.indexOf("?")?"?":"&")+"t="+ +new Date;f.unload_ref=d.unload_add(function(){f._cleanup(!0)});try{f.xhr.open(a,b,!0)}catch(k){f.emit("finish",0,"");f._cleanup();return}if(!e||!e.no_credentials)f.xhr.withCredentials="true";if(e&&e.headers)for(var l in e.headers)f.xhr.setRequestHeader(l,e.headers[l]);f.xhr.onreadystatechange=function(){if(f.xhr){var a=f.xhr;switch(a.readyState){case 3:try{var b=a.status,c=a.responseText}catch(d){}1223===b&&(b=204);c&&0<c.length&&f.emit("chunk",b,c);break;case 4:b=a.status,
1223===b&&(b=204),f.emit("finish",b,a.responseText),f._cleanup(!1)}}};f.xhr.send(c)};k.prototype._cleanup=function(a){if(this.xhr){d.unload_del(this.unload_ref);this.xhr.onreadystatechange=function(){};if(a)try{this.xhr.abort()}catch(b){}this.unload_ref=this.xhr=null}};k.prototype.close=function(){this.nuke();this._cleanup(!0)};(d.XHRCorsObject=function(){var a=this,b=arguments;d.delay(function(){a._start.apply(a,b)})}).prototype=new k;(d.XHRLocalObject=function(a,b,c){var e=this;d.delay(function(){e._start(a,
b,c,{no_credentials:!0})})}).prototype=new k;k=d.XDRObject=function(a,b,c){var e=this;d.delay(function(){e._start(a,b,c)})};k.prototype=new m(["chunk","finish"]);k.prototype._start=function(a,b,c){var e=this,f=new XDomainRequest;b+=(-1===b.indexOf("?")?"?":"&")+"t="+ +new Date;var q=f.ontimeout=f.onerror=function(){e.emit("finish",0,"");e._cleanup(!1)};f.onprogress=function(){e.emit("chunk",200,f.responseText)};f.onload=function(){e.emit("finish",200,f.responseText);e._cleanup(!1)};e.xdr=f;e.unload_ref=
d.unload_add(function(){e._cleanup(!0)});try{e.xdr.open(a,b),e.xdr.send(c)}catch(g){q()}};k.prototype._cleanup=function(a){if(this.xdr){d.unload_del(this.unload_ref);this.xdr.ontimeout=this.xdr.onerror=this.xdr.onprogress=this.xdr.onload=null;if(a)try{this.xdr.abort()}catch(b){}this.unload_ref=this.xdr=null}};k.prototype.close=function(){this.nuke();this._cleanup(!0)};d.isXHRCorsCapable=function(a){a=a.match(/^(https?:)/)[0];return h.XMLHttpRequest&&"withCredentials"in new XMLHttpRequest?1:h.XDomainRequest&&
p.domain&&p.location.protocol==a?2:v.enabled()?3:4};var l=function(a,b,c){var e=this;d.delay(function(){e._start(a,b,c)})};l.prototype=new m(["finish"]);l.prototype._start=function(a,b,c){var e=this;if(!("_send_form"in c)){var f=c._send_form=p.createElement("form"),q=c._send_area=p.createElement("textarea");q.name="d";f.style.display="none";f.style.position="absolute";f.method="POST";f.enctype="application/x-www-form-urlencoded";f.acceptCharset="UTF-8";f.appendChild(q);p.body.appendChild(f)}f=c._send_form;
q=c._send_area;c="a"+d.random_string(8);e.area=q;f.target=c;f.action=a+"/jsonp_send?i="+c;var g;try{g=p.createElement('<iframe name="'+c+'">')}catch(h){g=p.createElement("iframe"),g.name=c}e.iframe=g;g.id=c;f.appendChild(g);g.style.display="none";try{q.value=b}catch(k){d.log("Your browser is seriously broken. Go home! "+k.message)}f.submit();var l=function(a){e.iframe&&(e._cleanup(),e.emit("finish"))};g.onerror=g.onload=l;g.onreadystatechange=function(a){"complete"==g.readyState&&l()}};l.prototype._cleanup=
function(){if(this.iframe){var a=this.iframe;this.area.value="";this.iframe.onreadystatechange=this.iframe.onerror=this.iframe.onload=this.area=null;this.iframe="";d.delay(500,function(){a.parentNode.removeChild(a);a=null})}};l.prototype.close=function(){this.nuke();this._cleanup()};var g=function(a,b,c){if(this===h)return new g(a,b,c);var e=this,f;e._options=d.defaultOptions();c&&d.objectExtend(e._options,c);e._base_url=d.amendUrl(a);e._server=e._options.server||d.random_number_string(1E3);e._bus=
new m(["close","heartbeat","message"]);e._options.protocols_whitelist&&e._options.protocols_whitelist.length?f=e._options.protocols_whitelist:(f="string"===typeof b&&0<b.length?[b]:d.isArray(b)?b:null)&&e._debug('Deprecated API: Use "protocols_whitelist" option instead of supplying protocol list as a second parameter to SockJS constructor.');e._protocols=[];e.protocol=null;e.readyState=g.CONNECTING;e._ir=Q(e,e._base_url);e._ir.onfinish=function(a,b){e._ir=null;a?(e._options.info&&(a=d.objectExtend(a,
e._options.info)),e._options.rtt&&(b=e._options.rtt),e._applyInfo(a,b,f),e._didClose(void 0,void 0,void 0,"info-onfinish")):e._didClose(1002,"Can't connect to server",!0,"info-failed")}};g.prototype=new u;g.version="0.3.4.72.g5b91";g.CONNECTING=0;g.OPEN=1;g.CLOSING=2;g.CLOSED=3;g.prototype._debug=function(){this._options.debug&&d.log.apply(d,arguments)};g.prototype._dispatchOpen=function(){if(this.readyState===g.CONNECTING){this._transport_tref&&(clearTimeout(this._transport_tref),this._transport_tref=
null);this.readyState=g.OPEN;var a=this._options.info.server_heartbeat_interval;a&&this._startHeartbeats(a);this.dispatchEvent(new n("open"))}else this._didClose(1006,"Server lost session",void 0,"_dispatchOpen")};g.prototype._dispatchMessage=function(a){this.readyState===g.OPEN&&(this._bus.emit("message",a),this.dispatchEvent(new n("message",{data:a})))};g.prototype._dispatchHeartbeat=function(a){this.readyState===g.OPEN&&(this._bus.emit("heartbeat",a),this.dispatchEvent(new n("heartbeat",{flavour:a})))};
g.prototype._didClose=function(a,b,c,e){var f=this;if(!(f.readyState!==g.CONNECTING&&f.readyState!==g.OPEN&&f.readyState!==g.CLOSING)){f._ir&&(f._ir.nuke(),f._ir=null);f._transport&&(f._transport.doCleanup(),f._transport=null);var q=new n("close",{code:a,reason:b,wasClean:d.userSetCode(a)});if(!d.userSetCode(a)&&f.readyState===g.CONNECTING&&!c){if(f._try_next_protocol(q))return;q=new n("close",{code:2E3,reason:"All transports failed",wasClean:!1,last_event:q})}f.readyState=g.CLOSED;f._bus.emit("close");
d.delay(function(){f.dispatchEvent(q)})}};g.prototype._didMessage=function(a){switch(a.slice(0,1)){case "o":this._dispatchOpen();break;case "a":a=JSON.parse(a.slice(1)||"[]");for(var b=0;b<a.length;b++)this._dispatchMessage(a[b]);0===a.length&&this._dispatchHeartbeat("recv");break;case "m":a=JSON.parse(a.slice(1)||"null");this._dispatchMessage(a);break;case "c":a=JSON.parse(a.slice(1)||"[]");this._didClose(a[0],a[1],void 0,"_didMessage");break;case "h":this._dispatchHeartbeat("send")}};g.prototype._try_next_protocol=
function(a){var b=this;b.protocol&&(b._debug("Closed transport:",b.protocol,""+a),b.protocol=null);b._transport_tref&&(clearTimeout(b._transport_tref),b._transport_tref=null);for(;;){a=b.protocol=b._protocols.shift();if(!a)return!1;if(g[a]&&!0===g[a].need_body&&(!p.body||"undefined"!==typeof p.readyState&&"complete"!==p.readyState))return b._protocols.unshift(a),b.protocol="waiting-for-load",d.attachEvent("load",function(){b._try_next_protocol()}),!0;if(!g[a]||!g[a].enabled(b._base_url))b._debug("Skipping transport:",
a);else{b._transport_tref=d.delay((b._options.rto||0)*(g[a].roundTrips||1)||5E3,function(){b.readyState===g.CONNECTING&&b._didClose(2007,"Transport timeouted")});var c=d.random_string(8),c=b._base_url+"/"+b._server+"/"+c;b._debug("Opening transport:",a," url:"+c," RTO:"+b._options.rto);b._transport=new g[a](b,c,b._base_url);return!0}}};g.prototype.close=function(a,b){if(a&&!d.userSetCode(a))throw Error("INVALID_ACCESS_ERR");if(this.readyState!==g.CONNECTING&&this.readyState!==g.OPEN)return!1;this.readyState=
g.CLOSING;this._didClose(a||1E3,b||"Normal closure",void 0,"close");return!0};g.prototype.send=function(a){if(this.readyState===g.CONNECTING)throw Error("INVALID_STATE_ERR");this.readyState===g.OPEN&&this._transport.doSend(d.quote(""+a));return!0};g.prototype._sendEmpty=function(){this.readyState===g.OPEN&&this._transport.doSend("");return!0};g.prototype._applyInfo=function(a,b,c){this._options.info=a;this._options.rtt=b;this._options.rto=d.countRTO(b);this._options.info.null_origin=Boolean(this._options.ignore_null_origin||
!p.domain);b=d.probeProtocols(this._base_url);this._protocols=d.detectProtocols(b,c,a);a.hostname&&(c=/(https?:\/\/)([^\/:]+)((\/|:)?.*)/.exec(this._base_url),this._base_url=c[1]+a.hostname+c[3])};var r=function(){};r.prototype.send_constructor=function(a){this.send_buffer=[];this.sender=a;this.window_size=this.init_window_size=this.ri._options.init_window_size;this.max_window_time=this.ri._options.max_window_time};r.prototype.doSend=function(a){0<a.length&&this.send_buffer.push(a);this.send_stop&&
0==a.length&&this.ri._debug("Attempting to send a heartbeat, but sending previous messages is still in progress.");this.send_stop||this.send_schedule()};r.prototype.adjustWindow=function(a,b){return a>this.max_window_time?(this.window_size=Math.max(this.window_size/2,this.init_window_size),!0):b?(this.window_size=2*a<this.max_window_time?2*this.window_size:1.1*this.window_size,!0):!1};r.prototype.send_schedule_wait=function(){var a=this,b;a.send_stop=function(){a.send_stop=null;clearTimeout(b)};b=
d.delay(25,function(){a.send_stop=null;0<a.send_buffer.length&&a.send_schedule()})};r.prototype.send_schedule=function(){var a=this,b=+new Date,c,d=2,f=!1;for(c=0;c<a.send_buffer.length;c++)if(d+=a.send_buffer[c].length+1,d>a.window_size){f=!0;break}c="["+a.send_buffer.splice(0,c).join(",")+"]";a.send_stop=a.sender(a.trans_url,c,function(c,d){a.send_stop=null;!1===c?a.ri._didClose(1006,"Sending error "+d):(a.adjustWindow(+new Date-b,f)&&a.ri._debug("window_size adjusted: "+a.window_size),a.send_schedule_wait())})};
r.prototype.send_destructor=function(){this.send_stop&&this.send_stop();this.send_stop=null};var R=function(a,b,c){var d=new l(a,b,this);d.onfinish=function(){c(!0)};return function(){d.close()}},S=function(a){return function(b,c,d){var f=new a("POST",b+"/xhr_send",c);f.onfinish=function(a,b){d(200===a||204===a,"http status "+a)};return function(a){f.close()}}},T=function(a,b){var c,e=p.createElement("script"),f,g=function(a){f&&(f.parentNode.removeChild(f),f=null);e&&(clearTimeout(c),e.parentNode.removeChild(e),
e=e.onreadystatechange=e.onerror=e.onload=e.onclick=null,b(a),b=null)},h=!1,k=null;e.id="a"+d.random_string(8);e.src=a;e.type="text/javascript";e.charset="UTF-8";e.onerror=function(a){k||(k=setTimeout(function(){h||g(d.closeFrame(1006,"JSONP script loaded abnormally (onerror)"))},1E3))};e.onload=function(a){g(d.closeFrame(1006,"JSONP script loaded abnormally (onload)"))};e.onreadystatechange=function(a){if(/loaded|closed/.test(e.readyState)){if(e&&e.htmlFor&&e.onclick){h=!0;try{e.onclick()}catch(b){}}e&&
g(d.closeFrame(1006,"JSONP script loaded abnormally (onreadystatechange)"))}};if("undefined"===typeof e.async&&p.attachEvent)if(/opera/i.test(navigator.userAgent))f=p.createElement("script"),f.text="try{var a = document.getElementById('"+e.id+"'); if(a)a.onerror();}catch(x){};",e.async=f.async=!1;else{try{e.htmlFor=e.id,e.event="onclick"}catch(l){}e.async=!0}"undefined"!==typeof e.async&&(e.async=!0);c=setTimeout(function(){g(d.closeFrame(1006,"JSONP script loaded abnormally (timeout)"))},35E3);var m=
p.getElementsByTagName("head")[0];m.insertBefore(e,m.firstChild);f&&m.insertBefore(f,m.firstChild);return g},k=g["jsonp-polling"]=function(a,b){d.polluteGlobalNamespace();this.ri=a;this.trans_url=b;this.send_constructor(R);this._schedule_recv()};k.prototype=new r;k.prototype._schedule_recv=function(){var a=this;a._recv_stop=U(a.trans_url+"/jsonp",T,function(b){a._recv_stop=null;b&&(a._is_closing||a.ri._didMessage(b));a._is_closing||a._schedule_recv()})};k.enabled=function(){return!0};k.need_body=
!0;k.polling=!0;k.prototype.doCleanup=function(){this._is_closing=!0;this._recv_stop&&this._recv_stop();this.ri=this._recv_stop=null;this.send_destructor()};var U=function(a,b,c){var e="a"+d.random_string(6);a=a+"?c="+escape("_jp."+e);var f=0;b=b(a,function(a){switch(f){case 0:delete h._jp[e];c(a);break;case 1:c(a);f=2;break;case 2:delete h._jp[e]}});h._jp[e]=b;return function(){h._jp[e]&&(f=1,h._jp[e](d.closeFrame(1E3,"JSONP user aborted read")))}},k=function(){};k.prototype=new r;k.prototype.run=
function(a,b,c,d,f){this.ri=a;this.trans_url=b;this.send_constructor(S(f));this.poll=new H(a,d,b+c,f)};k.prototype.doCleanup=function(){this.poll&&(this.poll.abort(),this.poll=null);this.send_destructor()};var v=function(){};v.prototype.i_constructor=function(a,b,c){var e=this;e.ri=a;e.origin=d.getOrigin(c);e.base_url=c;e.trans_url=b;a=c+"/iframe.html";b=[];e.ri._options.devel&&b.push("t="+ +new Date);e.ri._options.js_path&&b.push("js_path="+encodeURIComponent(e.ri._options.js_path));0<b.length&&
(a+="?"+b.join("&"));e.window_id=d.random_string(8);a+="#"+e.window_id;e.iframeObj=d.createIframe(a,function(a){e.ri._didClose(1006,"Unable to load an iframe ("+a+")")});e.onmessage_cb=d.bind(e.onmessage,e);d.attachMessage(e.onmessage_cb)};v.prototype.doCleanup=function(){if(this.iframeObj){d.detachMessage(this.onmessage_cb);try{this.iframeObj.iframe.contentWindow&&this.postMessage("c")}catch(a){}this.iframeObj.cleanup();this.onmessage_cb=this.iframeObj=this.iframeObj=null}};v.prototype.onmessage=
function(a){if(d.getOrigin(a.origin)===d.getOrigin(this.origin)){var b=a.data.slice(0,8),c=a.data.slice(8,9);a=a.data.slice(9);if(b===this.window_id)switch(c){case "s":this.iframeObj.loaded();this.postMessage("s",JSON.stringify([g.version,this.protocol,this.trans_url,this.base_url,this.ri._options]));break;case "t":this.ri._didMessage(a)}}};v.prototype.postMessage=function(a,b){this.iframeObj.post(this.window_id+a+(b||""),this.origin)};v.prototype.doSend=function(a){this.postMessage("m",a)};v.enabled=
function(){var a=navigator&&navigator.userAgent&&-1!==navigator.userAgent.indexOf("Konqueror");return("function"===typeof h.postMessage||"object"===typeof h.postMessage)&&!a};var I,J=function(a,b){parent!==h?parent.postMessage(I+a+(b||""),"*"):d.log("Can't postMessage, no parent window.",a,b)},y=function(a){this._options=a||d.defaultOptions()};y.prototype._didClose=function(a,b){J("t",d.closeFrame(a,b))};y.prototype._didMessage=function(a){J("t",a)};y.prototype._doSend=function(a){this._transport.doSend(a)};
y.prototype._doCleanup=function(){this._transport.doCleanup()};y.prototype._debug=function(){d.log.apply(d,arguments)};d.parent_origin=void 0;g.bootstrap_iframe=function(){var a;I=p.location.hash.slice(1);d.attachMessage(function(b){if(b.source===parent&&("undefined"===typeof d.parent_origin&&(d.parent_origin=b.origin),b.origin===d.parent_origin)){var c=b.data.slice(0,8),e=b.data.slice(8,9);b=b.data.slice(9);if(c===I)switch(e){case "s":var f=JSON.parse(b),c=f[0],e=f[1];b=f[2];var k=f[3],f=f[4];c!==
g.version&&d.log("Incompatibile SockJS! Main site uses: "+c+", the iframe: "+g.version+". Double check SockJS urls and flush browser cache.");if(!d.flatUrl(b)||!d.flatUrl(k)){d.log("Only basic urls are supported in SockJS");break}if(!d.isSameOriginUrl(b)||!d.isSameOriginUrl(k)){d.log("Can't connect to different domain from within an iframe. ("+JSON.stringify([h.location.href,b,k])+")");break}a=new y(f);a._transport=new y[e](a,b,k);break;case "m":a._doSend(b);break;case "c":a&&a._doCleanup(),a=null}}});
J("s")};var B=function(a,b){var c=this;d.delay(function(){c.doXhr(a,b)})};B.prototype=new m(["finish"]);B.prototype.doXhr=function(a,b){var c=this,e=(new Date).getTime(),f=new b("GET",a+"/info"),g=d.delay(8E3,function(){f.ontimeout()});f.onfinish=function(a,b){clearTimeout(g);g=null;if(200===a){var d=(new Date).getTime()-e,f=JSON.parse(b);"object"!==typeof f&&(f={});c.emit("finish",f,d)}else c.emit("finish")};f.ontimeout=function(){f.close();c.emit("finish")}};var N=function(a,b){var c=this,e=function(){var d=
new v;d.protocol="w-iframe-info-receiver";var e=function(a){"string"===typeof a&&"m"===a.substr(0,1)?(a=JSON.parse(a.substr(1)),c.emit("finish",a[0],a[1])):c.emit("finish");d.doCleanup();d=null};d.i_constructor({_options:{js_path:a._options.js_path},_didClose:e,_didMessage:e},b,b)};p.body?e():d.attachEvent("load",e)};N.prototype=new m(["finish"]);var O=function(){var a=this;d.delay(function(){a.emit("finish",{},2E3)})};O.prototype=new m(["finish"]);var Q=function(a,b){if(d.isSameOriginUrl(b))return new B(b,
d.XHRLocalObject);switch(d.isXHRCorsCapable(b)){case 1:return new B(b,d.XHRLocalObject);case 2:return new B(b,d.XDRObject);case 3:return new N(a,b);default:return new O}};(y["w-iframe-info-receiver"]=function(a,b,c){(new B(c,d.XHRLocalObject)).onfinish=function(b,c){a._didMessage("m"+JSON.stringify([b,c]));a._didClose(void 0,void 0,void 0,"w-iframe-info-receiver")}}).prototype.doCleanup=function(){};r=g["iframe-eventsource"]=function(){this.protocol="w-iframe-eventsource";this.i_constructor.apply(this,
arguments)};r.prototype=new v;r.enabled=function(){return"EventSource"in h&&v.enabled()};r.need_body=!0;r.roundTrips=3;(y["w-iframe-eventsource"]=function(a,b){this.run(a,b,"/eventsource",K,d.XHRLocalObject)}).prototype=new k;r=g["iframe-xhr-polling"]=function(){this.protocol="w-iframe-xhr-polling";this.i_constructor.apply(this,arguments)};r.prototype=new v;r.enabled=function(){return h.XMLHttpRequest&&v.enabled()};r.need_body=!0;r.roundTrips=3;(y["w-iframe-xhr-polling"]=function(a,b){this.run(a,
b,"/xhr",L,d.XHRLocalObject)}).prototype=new k;r=g["iframe-htmlfile"]=function(){this.protocol="w-iframe-htmlfile";this.i_constructor.apply(this,arguments)};r.prototype=new v;r.enabled=function(){return v.enabled()};r.need_body=!0;r.roundTrips=3;(y["w-iframe-htmlfile"]=function(a,b){this.run(a,b,"/htmlfile",M,d.XHRLocalObject)}).prototype=new k;var H=function(a,b,c,d){this.ri=a;this.Receiver=b;this.recv_url=c;this.AjaxObject=d;this._scheduleRecv()};H.prototype._scheduleRecv=function(){var a=this,
b=a.poll=new a.Receiver(a.recv_url,a.AjaxObject);b.onmessage=function(b){a.ri._didMessage(b.data)};b.onclose=function(c){a.poll=b=b.onmessage=b.onclose=null;a.poll_is_closing||("permanent"===c.reason?a.ri._didClose(1006,"Polling error ("+c.reason+")"):a._scheduleRecv())}};H.prototype.abort=function(){this.poll_is_closing=!0;this.poll&&this.poll.abort()};var K=function(a){var b=this,c=new EventSource(a);c.onmessage=function(a){b.dispatchEvent(new n("message",{data:unescape(a.data)}))};b.es_close=c.onerror=
function(a,f){var g=f?"user":2!==c.readyState?"network":"permanent";b.es_close=c.onmessage=c.onerror=null;c.close();c=null;d.delay(200,function(){b.dispatchEvent(new n("close",{reason:g}))})}};K.prototype=new u;K.prototype.abort=function(){this.es_close&&this.es_close({},!0)};var D,M=function(a){var b=this;d.polluteGlobalNamespace();b.id="a"+d.random_string(6,26);a+=(-1===a.indexOf("?")?"?":"&")+"c="+escape("_jp."+b.id);if(void 0===D)if("ActiveXObject"in h)try{D=!!new ActiveXObject("htmlfile")}catch(c){}else D=
!1;var e=D?d.createHtmlfile:d.createIframe,f;h._jp[b.id]={start:function(){f.loaded()},message:function(a){b.dispatchEvent(new n("message",{data:a}))},stop:function(){b.iframe_close({},"network")}};b.iframe_close=function(a,c){f.cleanup();b.iframe_close=f=null;delete h._jp[b.id];b.dispatchEvent(new n("close",{reason:c}))};f=e(a,function(a){b.iframe_close({},"permanent")})};M.prototype=new u;M.prototype.abort=function(){this.iframe_close&&this.iframe_close({},"user")};var L=function(a,b){var c=this,
d=0;c.xo=new b("POST",a,null);c.xo.onchunk=function(a,b){if(200===a)for(;;){var g=b.slice(d),h=g.indexOf("\n");if(-1===h)break;d+=h+1;g=g.slice(0,h);c.dispatchEvent(new n("message",{data:g}))}};c.xo.onfinish=function(a,b){c.xo.onchunk(a,b);c.xo=null;c.dispatchEvent(new n("close",{reason:200===a?"network":"permanent"}))}};L.prototype=new u;L.prototype.abort=function(){this.xo&&(this.xo.close(),this.dispatchEvent(new n("close",{reason:"user"})),this.xo=null)};g.getUtils=function(){return d};g.getIframeTransport=
function(){return v};var C=function(a,b){var c=this;c.timeout=a;c.not_poked=0;c._onTimeout=function(){c.not_poked+=1;c.not_poked===b&&c.emit("timeout");c._restartTimeout()};c._restartTimeout()};C.prototype=new m(["first","second","timeout"]);C.prototype.poke=function(){this.not_poked=0;this._restartTimeout()};C.prototype.close=function(){clearTimeout(this.tref)};C.prototype._restartTimeout=function(){this.tref&&clearTimeout(this.tref);this.tref=setTimeout(this._onTimeout,this.timeout)};g.prototype._startHeartbeats=
function(a){var b=this;b._heartbeater=new C(a,2);b._bus.on("heartbeat",function(){g[b.protocol].polling||b._sendEmpty()});b._heartbeater.ontimeout=function(){b._didClose(1007,"Server heartbeat missed")};a=function(){b._heartbeater.poke()};b._bus.on("heartbeat",a);b._bus.on("message",a);b._bus.on("close",function(){b._heartbeater.nuke();b._heartbeater.close()})};return g}();"_sockjs_onload"in window&&setTimeout(_sockjs_onload,1);"function"===typeof define&&define.amd&&define("sockjs",[],function(){return SockJS});
"object"===typeof module&&(module&&module.exports)&&(module.exports=SockJS);