import AssistantToTheTransportManager from '../transports/assistant_to_the_transport_manager';
import PingDelayOptions from '../transports/ping_delay_options';
import Transport from '../transports/transport';
import TransportManager from '../transports/transport_manager';
import Handshake from '../connection/handshake';
import TransportConnection from '../transports/transport_connection';
import Timeline from '../timeline/timeline';
import { default as TimelineSender, TimelineSenderOptions } from '../timeline/timeline_sender';
import PresenceChannel from '../channels/presence_channel';
import PrivateChannel from '../channels/private_channel';
import EncryptedChannel from '../channels/encrypted_channel';
import Channel from '../channels/channel';
import ConnectionManager from '../connection/connection_manager';
import ConnectionManagerOptions from '../connection/connection_manager_options';
import Channels from '../channels/channels';
import Pusher from '../pusher';
import * as nacl from 'tweetnacl';
declare var Factory: {
    createChannels(): Channels;
    createConnectionManager(key: string, options: ConnectionManagerOptions): ConnectionManager;
    createChannel(name: string, pusher: Pusher): Channel;
    createPrivateChannel(name: string, pusher: Pusher): PrivateChannel;
    createPresenceChannel(name: string, pusher: Pusher): PresenceChannel;
    createEncryptedChannel(name: string, pusher: Pusher, nacl: nacl): EncryptedChannel;
    createTimelineSender(timeline: Timeline, options: TimelineSenderOptions): TimelineSender;
    createHandshake(transport: TransportConnection, callback: (HandshakePayload: any) => void): Handshake;
    createAssistantToTheTransportManager(manager: TransportManager, transport: Transport, options: PingDelayOptions): AssistantToTheTransportManager;
};
export default Factory;
