(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[105],{381:(e,s,a)=>{"use strict";a.d(s,{A:()=>l});let l=(0,a(9946).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},1007:(e,s,a)=>{"use strict";a.d(s,{A:()=>l});let l=(0,a(9946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},2099:(e,s,a)=>{"use strict";a.d(s,{N:()=>l});let l=(0,a(851).UU)("your_supabase_url_here","your_supabase_anon_key_here")},2614:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>N});var l=a(5155),t=a(2115),r=a(5695),i=a(6681),c=a(9420),d=a(4835),n=a(6517),x=a(9946);let m=(0,x.A)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]]),h=(0,x.A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]);var o=a(1007),u=a(2713),j=a(381);function N(){let{user:e,profile:s,loading:a,signOut:x}=(0,i.A)(),N=(0,r.useRouter)();if((0,t.useEffect)(()=>{a||e||N.push("/auth/login")},[e,a,N]),a)return(0,l.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"}),(0,l.jsx)("p",{className:"mt-4 text-gray-600",children:"Загрузка..."})]})});if(!e)return null;let g=async()=>{await x(),N.push("/")};return(0,l.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,l.jsx)("header",{className:"bg-white shadow-sm",children:(0,l.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,l.jsxs)("div",{className:"flex justify-between items-center py-6",children:[(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)(c.A,{className:"h-8 w-8 text-blue-600 mr-2"}),(0,l.jsx)("span",{className:"text-2xl font-bold text-gray-900",children:"Fibi Telecom"})]}),(0,l.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,l.jsxs)("span",{className:"text-gray-700",children:["Добро пожаловать, ",(null==s?void 0:s.full_name)||e.email]}),(0,l.jsxs)("button",{onClick:g,className:"flex items-center text-gray-700 hover:text-red-600",children:[(0,l.jsx)(d.A,{className:"h-5 w-5 mr-1"}),"Выйти"]})]})]})})}),(0,l.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,l.jsxs)("div",{className:"mb-8",children:[(0,l.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Личный кабинет"}),(0,l.jsx)("p",{className:"text-gray-600",children:"Управляйте вашими услугами и настройками"})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[(0,l.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)(n.A,{className:"h-8 w-8 text-blue-600"}),(0,l.jsxs)("div",{className:"ml-4",children:[(0,l.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Интернет"}),(0,l.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:"100 Мбит/с"})]})]})}),(0,l.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)(c.A,{className:"h-8 w-8 text-green-600"}),(0,l.jsxs)("div",{className:"ml-4",children:[(0,l.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Телефония"}),(0,l.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:"Активна"})]})]})}),(0,l.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)(m,{className:"h-8 w-8 text-purple-600"}),(0,l.jsxs)("div",{className:"ml-4",children:[(0,l.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Баланс"}),(0,l.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:"1,250 ₽"})]})]})})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,l.jsx)("div",{className:"lg:col-span-2",children:(0,l.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,l.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,l.jsx)("h2",{className:"text-lg font-medium text-gray-900",children:"Мои услуги"})}),(0,l.jsxs)("div",{className:"p-6",children:[(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between p-4 border border-gray-200 rounded-lg",children:[(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)(n.A,{className:"h-6 w-6 text-blue-600 mr-3"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"font-medium",children:'Интернет "Стандарт"'}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"100 Мбит/с • Безлимит"})]})]}),(0,l.jsxs)("div",{className:"text-right",children:[(0,l.jsx)("p",{className:"font-medium",children:"990 ₽/мес"}),(0,l.jsx)("p",{className:"text-sm text-green-600",children:"Активна"})]})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between p-4 border border-gray-200 rounded-lg",children:[(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)(c.A,{className:"h-6 w-6 text-green-600 mr-3"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"font-medium",children:"IP-телефония"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"+7 (495) 123-45-67"})]})]}),(0,l.jsxs)("div",{className:"text-right",children:[(0,l.jsx)("p",{className:"font-medium",children:"290 ₽/мес"}),(0,l.jsx)("p",{className:"text-sm text-green-600",children:"Активна"})]})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between p-4 border border-gray-200 rounded-lg opacity-50",children:[(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)(h,{className:"h-6 w-6 text-gray-400 mr-3"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"font-medium",children:"Антивирус"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"Защита устройств"})]})]}),(0,l.jsxs)("div",{className:"text-right",children:[(0,l.jsx)("p",{className:"font-medium",children:"190 ₽/мес"}),(0,l.jsx)("p",{className:"text-sm text-gray-500",children:"Не подключена"})]})]})]}),(0,l.jsx)("button",{className:"mt-6 w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors",children:"Подключить новую услугу"})]})]})}),(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,l.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,l.jsx)("h2",{className:"text-lg font-medium text-gray-900",children:"Информация об аккаунте"})}),(0,l.jsxs)("div",{className:"p-6",children:[(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)(o.A,{className:"h-5 w-5 text-gray-400 mr-3"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"Имя"}),(0,l.jsx)("p",{className:"font-medium",children:(null==s?void 0:s.full_name)||"Не указано"})]})]}),(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)(c.A,{className:"h-5 w-5 text-gray-400 mr-3"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"Email"}),(0,l.jsx)("p",{className:"font-medium",children:e.email})]})]}),(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)(m,{className:"h-5 w-5 text-gray-400 mr-3"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"ID клиента"}),(0,l.jsxs)("p",{className:"font-medium",children:["#",e.id.slice(0,8)]})]})]})]}),(0,l.jsx)("button",{className:"mt-4 w-full border border-gray-300 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-50 transition-colors",children:"Редактировать профиль"})]})]}),(0,l.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[(0,l.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,l.jsx)("h2",{className:"text-lg font-medium text-gray-900",children:"Быстрые действия"})}),(0,l.jsx)("div",{className:"p-6",children:(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsxs)("button",{className:"w-full flex items-center text-left p-3 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,l.jsx)(m,{className:"h-5 w-5 text-blue-600 mr-3"}),"Пополнить баланс"]}),(0,l.jsxs)("button",{className:"w-full flex items-center text-left p-3 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,l.jsx)(u.A,{className:"h-5 w-5 text-green-600 mr-3"}),"Статистика использования"]}),(0,l.jsxs)("button",{className:"w-full flex items-center text-left p-3 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,l.jsx)(j.A,{className:"h-5 w-5 text-gray-600 mr-3"}),"Настройки"]})]})})]})]})]})]})]})}},2713:(e,s,a)=>{"use strict";a.d(s,{A:()=>l});let l=(0,a(9946).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},4835:(e,s,a)=>{"use strict";a.d(s,{A:()=>l});let l=(0,a(9946).A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},6517:(e,s,a)=>{"use strict";a.d(s,{A:()=>l});let l=(0,a(9946).A)("wifi",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["path",{d:"M5 12.859a10 10 0 0 1 14 0",key:"1x1e6c"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}]])},6681:(e,s,a)=>{"use strict";a.d(s,{A:()=>r});var l=a(2115),t=a(2099);function r(){let[e,s]=(0,l.useState)(null),[a,r]=(0,l.useState)(null),[i,c]=(0,l.useState)(!0);(0,l.useEffect)(()=>{(async()=>{var e;let{data:{session:a}}=await t.N.auth.getSession();s(null!=(e=null==a?void 0:a.user)?e:null),(null==a?void 0:a.user)&&await d(a.user.id),c(!1)})();let{data:{subscription:e}}=t.N.auth.onAuthStateChange(async(e,a)=>{var l;s(null!=(l=null==a?void 0:a.user)?l:null),(null==a?void 0:a.user)?await d(a.user.id):r(null),c(!1)});return()=>e.unsubscribe()},[]);let d=async e=>{try{let{data:s,error:a}=await t.N.from("profiles").select("*").eq("id",e).single();if(a)return void console.error("Error fetching profile:",a);r(s)}catch(e){console.error("Error fetching profile:",e)}},n=async()=>{await t.N.auth.signOut()},x=(null==a?void 0:a.role)==="admin";return{user:e,profile:a,loading:i,signOut:n,isAdmin:x}}},9145:(e,s,a)=>{Promise.resolve().then(a.bind(a,2614))}},e=>{var s=s=>e(e.s=s);e.O(0,[664,441,684,358],()=>s(9145)),_N_E=e.O()}]);