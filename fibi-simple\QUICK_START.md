# 🚀 Быстрый запуск Fibi Telecom

## 📋 Что уже готово

✅ **Полнофункциональный сайт** с React компонентами  
✅ **Система авторизации** с Supabase  
✅ **Формы обратной связи** с валидацией и API  
✅ **Админ-панель** для управления  
✅ **База данных** с готовыми таблицами  

## 🎯 Демонстрация

**Текущие доступные страницы:**
- 🌐 **Главная страница**: http://localhost:8000/fibi-simple/index.html
- 📋 **Демонстрация функций**: http://localhost:8000/fibi-simple/demo.html

## ⚡ Запуск Next.js версии

### 1. Настройка Supabase
```bash
# 1. Создайте проект на https://supabase.com
# 2. Скопируйте URL и Anon Key
# 3. Обновите .env.local:
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

### 2. Настройка базы данных
```sql
-- Выполните в Supabase SQL Editor:
-- Содержимое файла supabase-setup.sql
```

### 3. Запуск проекта
```bash
# Установка зависимостей (уже выполнено)
npm install

# Запуск сервера разработки
npm run dev
# или
npx next dev
```

### 4. Доступ к приложению
- 🏠 Главная: http://localhost:3000
- 🔐 Вход: http://localhost:3000/auth/login
- 📝 Регистрация: http://localhost:3000/auth/signup
- 👤 Кабинет: http://localhost:3000/dashboard
- ⚙️ Админ: http://localhost:3000/admin

## 🔧 Создание администратора

1. Зарегистрируйтесь через интерфейс
2. В Supabase Dashboard выполните:
```sql
UPDATE profiles SET role = 'admin' 
WHERE email = '<EMAIL>';
```

## 📁 Файлы проекта

- `components/` - React компоненты
- `src/app/` - Next.js страницы
- `hooks/useAuth.ts` - Хук авторизации
- `lib/supabase.ts` - Конфигурация Supabase
- `supabase-setup.sql` - SQL для настройки БД
- `demo.html` - Демонстрация функций

## 🎯 Готовые функции

### Авторизация
- ✅ Регистрация с email подтверждением
- ✅ Вход в систему
- ✅ Роли пользователей (user/admin)
- ✅ Защищенные маршруты

### Формы
- ✅ Валидация в реальном времени
- ✅ API endpoint `/api/contact`
- ✅ Сохранение в базу данных
- ✅ Уведомления об успехе/ошибке

### Интерфейс
- ✅ Адаптивный дизайн
- ✅ Современные компоненты
- ✅ Иконки Lucide React
- ✅ Tailwind CSS стили

## 🚀 Развертывание

### Vercel (рекомендуется)
1. Подключите GitHub репозиторий
2. Добавьте переменные окружения
3. Деплой произойдет автоматически

### Другие платформы
- Netlify
- Railway  
- Heroku
- DigitalOcean

## 📞 Поддержка

Если возникли вопросы:
1. Проверьте README.md
2. Посмотрите demo.html
3. Проверьте консоль браузера на ошибки

---

**🎉 Проект готов к использованию!**
