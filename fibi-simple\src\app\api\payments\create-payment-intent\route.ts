import { NextRequest, NextResponse } from 'next/server'
import { stripe } from '@/lib/stripe'
import { createClient } from '@/lib/supabase-server'
import { z } from 'zod'

const paymentSchema = z.object({
  amount: z.number().min(100), // Минимум 1 рубль (100 копеек)
  currency: z.string().default('rub'),
  serviceId: z.string().uuid(),
  description: z.string().optional()
})

export async function POST(request: NextRequest) {
  try {
    const supabase = createClient()
    
    // Проверяем авторизацию
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Необходима авторизация' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { amount, currency, serviceId, description } = paymentSchema.parse(body)

    // Получаем информацию об услуге
    const { data: service, error: serviceError } = await supabase
      .from('services')
      .select('*')
      .eq('id', serviceId)
      .single()

    if (serviceError || !service) {
      return NextResponse.json(
        { error: 'Услуга не найдена' },
        { status: 404 }
      )
    }

    // Создаем PaymentIntent в Stripe
    const paymentIntent = await stripe.paymentIntents.create({
      amount,
      currency,
      metadata: {
        userId: user.id,
        serviceId,
        serviceName: service.name
      },
      description: description || `Оплата услуги: ${service.name}`
    })

    // Сохраняем информацию о платеже в базу данных
    const { error: insertError } = await supabase
      .from('payments')
      .insert({
        id: paymentIntent.id,
        user_id: user.id,
        service_id: serviceId,
        amount,
        currency,
        status: 'pending',
        stripe_payment_intent_id: paymentIntent.id
      })

    if (insertError) {
      console.error('Error saving payment:', insertError)
      // Не возвращаем ошибку, так как PaymentIntent уже создан
    }

    return NextResponse.json({
      clientSecret: paymentIntent.client_secret,
      paymentIntentId: paymentIntent.id
    })

  } catch (error) {
    console.error('Payment creation error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Неверные данные', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Ошибка создания платежа' },
      { status: 500 }
    )
  }
}
