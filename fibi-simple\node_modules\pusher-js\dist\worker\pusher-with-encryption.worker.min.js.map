{"version": 3, "sources": ["webpack://Pusher/webpack/universalModuleDefinition", "webpack://Pusher/webpack/bootstrap", "webpack://Pusher/./node_modules/@stablelib/base64/base64.ts", "webpack://Pusher/./node_modules/@stablelib/utf8/utf8.ts", "webpack://Pusher/./node_modules/tweetnacl/nacl-fast.js", "webpack://Pusher/./src/core/pusher-with-encryption.js", "webpack://Pusher/./src/core/base64.ts", "webpack://Pusher/./src/core/utils/timers/abstract_timer.ts", "webpack://Pusher/./src/core/utils/timers/index.ts", "webpack://Pusher/./src/core/util.ts", "webpack://Pusher/./src/core/utils/collections.ts", "webpack://Pusher/./src/core/defaults.ts", "webpack://Pusher/./src/core/transports/url_schemes.ts", "webpack://Pusher/./src/core/events/callback_registry.ts", "webpack://Pusher/./src/core/events/dispatcher.ts", "webpack://Pusher/./src/core/logger.ts", "webpack://Pusher/./src/core/transports/transport_connection.ts", "webpack://Pusher/./src/core/transports/transport.ts", "webpack://Pusher/./src/runtimes/isomorphic/transports/transports.ts", "webpack://Pusher/./src/core/transports/assistant_to_the_transport_manager.ts", "webpack://Pusher/./src/core/connection/protocol/protocol.ts", "webpack://Pusher/./src/core/connection/connection.ts", "webpack://Pusher/./src/core/connection/handshake/index.ts", "webpack://Pusher/./src/core/timeline/timeline_sender.ts", "webpack://Pusher/./src/core/errors.ts", "webpack://Pusher/./src/core/utils/url_store.ts", "webpack://Pusher/./src/core/channels/channel.ts", "webpack://Pusher/./src/core/channels/private_channel.ts", "webpack://Pusher/./src/core/channels/members.ts", "webpack://Pusher/./src/core/channels/presence_channel.ts", "webpack://Pusher/./src/core/channels/encrypted_channel.ts", "webpack://Pusher/./src/core/connection/connection_manager.ts", "webpack://Pusher/./src/core/channels/channels.ts", "webpack://Pusher/./src/core/utils/factory.ts", "webpack://Pusher/./src/core/transports/transport_manager.ts", "webpack://Pusher/./src/core/strategies/sequential_strategy.ts", "webpack://Pusher/./src/core/strategies/best_connected_ever_strategy.ts", "webpack://Pusher/./src/core/strategies/websocket_prioritized_cached_strategy.ts", "webpack://Pusher/./src/core/strategies/delayed_strategy.ts", "webpack://Pusher/./src/core/strategies/if_strategy.ts", "webpack://Pusher/./src/core/strategies/first_connected_strategy.ts", "webpack://Pusher/./src/runtimes/isomorphic/default_strategy.ts", "webpack://Pusher/./src/core/http/http_request.ts", "webpack://Pusher/./src/core/http/state.ts", "webpack://Pusher/./src/core/http/http_socket.ts", "webpack://Pusher/./src/core/http/http_streaming_socket.ts", "webpack://Pusher/./src/core/http/http_polling_socket.ts", "webpack://Pusher/./src/runtimes/isomorphic/http/http_xhr_request.ts", "webpack://Pusher/./src/runtimes/isomorphic/runtime.ts", "webpack://Pusher/./src/runtimes/isomorphic/transports/transport_connection_initializer.ts", "webpack://Pusher/./src/runtimes/isomorphic/http/http.ts", "webpack://Pusher/./src/runtimes/worker/net_info.ts", "webpack://Pusher/./src/runtimes/worker/auth/fetch_auth.ts", "webpack://Pusher/./src/runtimes/worker/timeline/fetch_timeline.ts", "webpack://Pusher/./src/runtimes/worker/runtime.ts", "webpack://Pusher/./src/core/timeline/level.ts", "webpack://Pusher/./src/core/timeline/timeline.ts", "webpack://Pusher/./src/core/strategies/transport_strategy.ts", "webpack://Pusher/./src/core/strategies/strategy_builder.ts", "webpack://Pusher/./src/core/auth/options.ts", "webpack://Pusher/./src/core/options.ts", "webpack://Pusher/./src/core/auth/user_authenticator.ts", "webpack://Pusher/./src/core/auth/channel_authorizer.ts", "webpack://Pusher/./src/core/config.ts", "webpack://Pusher/./src/core/auth/deprecated_channel_authorizer.ts", "webpack://Pusher/./src/core/watchlist.ts", "webpack://Pusher/./src/core/utils/flat_promise.ts", "webpack://Pusher/./src/core/user.ts", "webpack://Pusher/./src/core/pusher.ts", "webpack://Pusher/./src/core/pusher-with-encryption.ts"], "names": ["root", "factory", "exports", "module", "define", "amd", "this", "installedModules", "__webpack_require__", "moduleId", "i", "l", "modules", "call", "m", "c", "d", "name", "getter", "o", "Object", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "object", "property", "prototype", "hasOwnProperty", "p", "s", "_padding<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "length", "encode", "data", "out", "_encodeByte", "left", "maxDecoded<PERSON><PERSON>th", "decodedLength", "_getPaddingLength", "decode", "Uint8Array", "paddingLength", "op", "haveBad", "v0", "v1", "v2", "v3", "_decodeChar", "charCodeAt", "Error", "b", "result", "String", "fromCharCode", "Coder", "stdCoder", "URLSafeCoder", "urlSafeCoder", "INVALID_UTF8", "arr", "pos", "chars", "min", "n1", "n2", "n3", "push", "join", "nacl", "gf", "init", "Float64Array", "randombytes", "_0", "_9", "gf0", "gf1", "_121665", "D", "D2", "X", "Y", "I", "ts64", "x", "h", "vn", "xi", "y", "yi", "crypto_verify_16", "crypto_verify_32", "crypto_core_salsa20", "inp", "k", "u", "j0", "j1", "j2", "j3", "j4", "j5", "j6", "j7", "j8", "j9", "j10", "j11", "j12", "j13", "j14", "j15", "x0", "x1", "x2", "x3", "x4", "x5", "x6", "x7", "x8", "x9", "x10", "x11", "x12", "x13", "x14", "x15", "core_salsa20", "crypto_core_hsalsa20", "core_hsalsa20", "sigma", "crypto_stream_salsa20_xor", "cpos", "mpos", "z", "crypto_stream_salsa20", "crypto_stream", "sn", "crypto_stream_xor", "poly1305", "t0", "t1", "t2", "t3", "t4", "t5", "t6", "t7", "buffer", "Uint16Array", "pad", "leftover", "fin", "crypto_onetimeauth", "outpos", "update", "finish", "crypto_onetimeauth_verify", "hpos", "crypto_secretbox", "crypto_secretbox_open", "set25519", "a", "car25519", "v", "Math", "floor", "sel25519", "q", "pack25519", "j", "neq25519", "par25519", "unpack25519", "A", "Z", "M", "t8", "t9", "t10", "t11", "t12", "t13", "t14", "t15", "t16", "t17", "t18", "t19", "t20", "t21", "t22", "t23", "t24", "t25", "t26", "t27", "t28", "t29", "t30", "b0", "b1", "b2", "b3", "b4", "b5", "b6", "b7", "b8", "b9", "b10", "b11", "b12", "b13", "b14", "b15", "S", "inv25519", "pow2523", "crypto_scalarmult", "e", "f", "x32", "subarray", "x16", "crypto_scalarmult_base", "crypto_box_keypair", "crypto_box_beforenm", "blocks", "bytes", "d0", "d1", "d2", "d3", "d4", "d5", "d6", "d7", "d8", "d9", "hibit", "h0", "h1", "h2", "h3", "h4", "h5", "h6", "h7", "h8", "h9", "r0", "r1", "r2", "r3", "r4", "r5", "r6", "r7", "r8", "r9", "mac", "macpos", "mask", "g", "want", "crypto_box_afternm", "crypto_box_open_afternm", "K", "crypto_hashblocks_hl", "hh", "hl", "bh0", "bh1", "bh2", "bh3", "bh4", "bh5", "bh6", "bh7", "bl0", "bl1", "bl2", "bl3", "bl4", "bl5", "bl6", "bl7", "th", "tl", "wh", "Int32Array", "wl", "ah0", "ah1", "ah2", "ah3", "ah4", "ah5", "ah6", "ah7", "al0", "al1", "al2", "al3", "al4", "al5", "al6", "al7", "crypto_hash", "add", "cswap", "pack", "tx", "ty", "zi", "scalarmult", "scalarbase", "crypto_sign_keypair", "pk", "sk", "seeded", "L", "modL", "carry", "reduce", "crypto_sign", "sm", "smlen", "crypto_sign_open", "chk", "num", "den", "den2", "den4", "den6", "unpackneg", "checkLengths", "checkArrayTypes", "arguments", "TypeError", "cleanup", "lowlevel", "crypto_box", "crypto_box_open", "crypto_secretbox_KEYBYTES", "crypto_secretbox_NONCEBYTES", "crypto_secretbox_ZEROBYTES", "crypto_secretbox_BOXZEROBYTES", "crypto_scalarmult_BYTES", "crypto_scalarmult_SCALARBYTES", "crypto_box_PUBLICKEYBYTES", "crypto_box_SECRETKEYBYTES", "crypto_box_BEFORENMBYTES", "crypto_box_NONCEBYTES", "crypto_box_ZEROBYTES", "crypto_box_BOXZEROBYTES", "crypto_sign_BYTES", "crypto_sign_PUBLICKEYBYTES", "crypto_sign_SECRETKEYBYTES", "crypto_sign_SEEDBYTES", "crypto_hash_BYTES", "randomBytes", "secretbox", "msg", "nonce", "open", "box", "<PERSON><PERSON><PERSON><PERSON>", "non<PERSON><PERSON><PERSON><PERSON>", "overheadLength", "scalarMult", "base", "scalar<PERSON>ength", "groupElementLength", "public<PERSON>ey", "secret<PERSON>ey", "before", "checkBoxLengths", "after", "keyPair", "fromSecretKey", "publicKeyLength", "secretKeyLength", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sign", "signed<PERSON>g", "tmp", "mlen", "detached", "sig", "verify", "fromSeed", "seed", "seedLength", "<PERSON><PERSON><PERSON><PERSON>", "hash", "hash<PERSON><PERSON><PERSON>", "setPRNG", "fn", "crypto", "self", "msCrypto", "getRandomValues", "default", "b64chars", "b64tab", "char<PERSON>t", "cb_utob", "cc", "utob", "replace", "cb_encode", "ccc", "padlen", "ord", "btoa", "set", "clear", "delay", "callback", "timer", "clearTimeout", "clearInterval", "super", "setTimeout", "setInterval", "now", "Date", "valueOf", "defer", "args", "boundArguments", "Array", "slice", "apply", "concat", "extend", "target", "sources", "extensions", "constructor", "stringify", "safeJSONStringify", "arrayIndexOf", "array", "item", "nativeIndexOf", "indexOf", "objectApply", "keys", "_", "context", "map", "filter", "test", "filterObject", "Boolean", "any", "encodeParamsObject", "encodeURIComponent", "toString", "buildQueryString", "params", "undefined", "method", "source", "JSON", "objects", "paths", "derez", "path", "nu", "$ref", "VERSION", "PROTOCOL", "wsPort", "wssPort", "wsPath", "httpHost", "httpPort", "httpsPort", "httpPath", "stats_host", "authEndpoint", "authTransport", "activityTimeout", "pongTimeout", "unavailableTimeout", "userAuthentication", "endpoint", "transport", "channelAuthorization", "cdn_http", "cdn_https", "dependency_suffix", "getGenericURL", "baseScheme", "useTLS", "hostTLS", "hostNonTLS", "getGenericPath", "queryString", "ws", "getInitial", "http", "_callbacks", "prefix", "prefixedEventName", "names", "removeCallback", "removeAllCallbacks", "binding", "failThrough", "callbacks", "global_callbacks", "eventName", "remove", "unbind", "unbind_global", "metadata", "globalLog", "message", "console", "log", "globalLogWarn", "globalLogError", "warn", "error", "defaultLoggingFunction", "logToConsole", "hooks", "priority", "options", "initialize", "transportConnectionInitializer", "state", "timeline", "id", "generateUniqueID", "handlesActivityChecks", "supportsPing", "socket", "url", "urls", "getSocket", "onError", "changeState", "bindListeners", "debug", "close", "send", "ping", "beforeOpen", "<PERSON><PERSON><PERSON>", "onopen", "emit", "type", "buildTimelineMessage", "closeEvent", "code", "reason", "<PERSON><PERSON><PERSON>", "unbindListeners", "onOpen", "onerror", "onclose", "onClose", "onmessage", "onMessage", "onactivity", "onActivity", "info", "cid", "environment", "isSupported", "WSTransport", "isInitialized", "getWebSocketAPI", "createWebSocket", "httpConfiguration", "streamingConfiguration", "HTTPFactory", "createStreamingSocket", "pollingConfiguration", "createPollingSocket", "xhrConfiguration", "isXHRSupported", "xhr_streaming", "xhr_polling", "manager", "min<PERSON>ing<PERSON>elay", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "ping<PERSON><PERSON><PERSON>", "connection", "createConnection", "openTimestamp", "onClosed", "reportDeath", "lifespan", "max", "isAlive", "Protocol", "decodeMessage", "messageEvent", "messageData", "parse", "pusherEventData", "pusherEvent", "event", "channel", "user_id", "encodeMessage", "processHandshake", "activity_timeout", "action", "socket_id", "getCloseAction", "getCloseError", "send_event", "listeners", "activity", "closed", "handleCloseEvent", "listener", "isEmpty", "TimelineTransport", "getAgent", "BadEventName", "setPrototypeOf", "BadChannelName", "TransportPriorityTooLow", "TransportClosed", "UnsupportedFeature", "UnsupportedTransport", "UnsupportedStrategy", "HTTPAuthError", "status", "urlStore", "baseUrl", "authenticationEndpoint", "authorizationEndpoint", "javascriptQuickStart", "triggeringClientEvents", "encryptedChannelSupport", "fullUrl", "url<PERSON>bj", "pusher", "subscribed", "subscriptionPending", "subscriptionCancelled", "socketId", "auth", "suffix", "handleSubscriptionSucceededEvent", "handleSubscriptionCountEvent", "unsubscribe", "subscription_count", "subscriptionCount", "authorize", "assign", "channel_data", "config", "channelAuthorizer", "channelName", "reset", "members", "member", "myID", "subscriptionData", "presence", "count", "me", "memberData", "user_info", "authData", "channelData", "setMyID", "user", "signinDonePromise", "user_data", "handleInternalEvent", "addedMember", "addMember", "removedMember", "removeMember", "onSubscription", "disconnect", "sharedSecret", "handleEncryptedEvent", "handleEvent", "ciphertext", "cipherText", "getDataToEmit", "raw", "usingTLS", "errorCallbacks", "buildErrorCallbacks", "connectionCallbacks", "buildConnectionCallbacks", "handshakeCallbacks", "buildHandshakeCallbacks", "Network", "getNetwork", "netinfo", "retryIn", "sendActivityCheck", "updateStrategy", "runner", "strategy", "updateState", "startConnecting", "setUnavailableTimer", "disconnectInternally", "handshake", "connect", "handshake<PERSON><PERSON><PERSON>", "abortConnecting", "abort", "clearRetryTimer", "clearUnavailableTimer", "abandonConnection", "getStrategy", "round", "retryTimer", "ensureAborted", "unavailableTimer", "stopActivityCheck", "activityTimer", "pong_timed_out", "resetActivity<PERSON>heck", "shouldRetry", "connected", "Infinity", "setConnection", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tls_only", "refused", "backoff", "retry", "newState", "previousState", "newStateDescription", "previous", "current", "channels", "createEncryptedChannel", "errMsg", "createPrivateChannel", "createPresenceChannel", "createChannel", "values", "createChannels", "createConnectionManager", "createTimelineSender", "createHandshake", "createAssistantToTheTransportManager", "livesLeft", "lives", "strategies", "loop", "failFast", "timeout", "timeoutLimit", "minPriority", "tryNextStrategy", "tryStrategy", "forceMinPriority", "isRunning", "callbackBuilder", "runners", "rs", "abort<PERSON><PERSON><PERSON>", "allRunnersFailed", "aborted", "transports", "ttl", "storage", "getLocalStorage", "serializedCache", "getTransportCacheKey", "flushTransportCache", "fetchTransportCache", "cacheSkipCount", "timestamp", "includes", "cached", "latency", "startTimestamp", "pop", "cb", "storeTransportCache", "number", "IfStrategy", "trueBranch", "falseBranch", "FirstConnectedStrategy", "testSupportsStrategy", "baseOptions", "defineTransport", "definedTransports", "defineTransportStrategy", "wsStrategy", "ws_options", "wsHost", "wss_options", "http_options", "timeouts", "ws_manager", "streaming_manager", "ws_transport", "wss_transport", "xhr_streaming_transport", "xhr_polling_transport", "ws_loop", "wss_loop", "streaming_loop", "polling_loop", "http_loop", "payload", "position", "xhr", "getRequest", "unloader", "addUnloadListener", "setRequestHeader", "removeUnloadListener", "abortRequest", "chunk", "advanceBuffer", "isBufferTooLong", "unreadData", "endOfLinePosition", "State", "autoIncrement", "getUniqueURL", "separator", "randomNumber", "randomInt", "session", "randomString", "location", "parts", "exec", "getLocation", "readyState", "CONNECTING", "openStream", "sendRaw", "sendHeartbeat", "OPEN", "createSocketRequest", "start", "closeStream", "CLOSED", "onEvent", "onHeartbeat", "hostname", "urlParts", "stream", "getReceiveURL", "onChunk", "onFinished", "reconnect", "unbind_all", "getXHRAPI", "onreadystatechange", "onprogress", "responseText", "getDefaultStrategy", "Transports", "createSocket", "createRequest", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ready", "getClientFeatures", "getProtocol", "createXHR", "query", "authOptions", "authRequestType", "headers", "Headers", "headerName", "headers<PERSON>rovider", "dynamicHeaders", "body", "request", "Request", "credentials", "fetch", "then", "response", "text", "parsedData", "catch", "err", "sender", "host", "json", "setup", "TimelineLevel", "getAuthorizers", "ajax", "WebSocket", "XMLHttpRequest", "globalThis", "Uint32Array", "events", "sent", "uniqueID", "level", "limit", "shift", "ERROR", "INFO", "DEBUG", "sendfn", "bundle", "lib", "version", "cluster", "features", "failAttempt", "onInitialized", "serializedTransport", "AuthRequestType", "transportClass", "enabledTransports", "disabledTransports", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getAssistant", "deferred", "validateOptions", "params<PERSON>rov<PERSON>", "dynamicParams", "composeChannel<PERSON><PERSON>y", "UserAuthentication", "ChannelAuthorization", "getHttpHost", "opts", "getWebsocketHost", "shouldUseTLS", "forceTLS", "getEnableStatsConfig", "enableStats", "disableStats", "buildUserAuthenticator", "buildChannelAuthorizer", "customHandler", "channelAuthorizerGenerator", "deprecatedAuthorizerOptions", "ChannelAuthorizerProxy", "authorizer", "buildChannelAuth", "bindWatchlistInternalEvent", "for<PERSON>ach", "watchlistEvent", "resolve", "reject", "promise", "Promise", "res", "rej", "signin_requested", "serverToUserChannel", "_signinDoneResolve", "_onAuthorize", "_cleanup", "_signin", "_newSigninPromiseIfNeeded", "watchlist", "_onSigninSuccess", "userAuthenticator", "_subscribeChannels", "bind_global", "reinstateSubscription", "subscribe", "ensure_subscribed", "done", "setDone", "isReady", "instances", "app_key", "check<PERSON><PERSON><PERSON><PERSON>", "statsHost", "timelineParams", "getConfig", "global_emitter", "sessionID", "timelineSender", "subscribeAll", "isUsingTLS", "internal", "find", "all", "timelineSenderTimer", "event_name", "channel_name", "cancelSubscription", "signin", "Runtime", "ScriptReceivers", "DependenciesReceivers", "auth_callbacks"], "mappings": ";;;;;;;CAAA,SAA2CA,EAAMC,GAC1B,iBAAZC,SAA0C,iBAAXC,OACxCA,OAAOD,QAAUD,IACQ,mBAAXG,QAAyBA,OAAOC,IAC9CD,OAAO,GAAIH,GACe,iBAAZC,QACdA,QAAgB,OAAID,IAEpBD,EAAa,OAAIC,IARnB,CASGK,MAAM,WACT,O,YCTE,IAAIC,EAAmB,GAGvB,SAASC,EAAoBC,GAG5B,GAAGF,EAAiBE,GACnB,OAAOF,EAAiBE,GAAUP,QAGnC,IAAIC,EAASI,EAAiBE,GAAY,CACzCC,EAAGD,EACHE,GAAG,EACHT,QAAS,IAUV,OANAU,EAAQH,GAAUI,KAAKV,EAAOD,QAASC,EAAQA,EAAOD,QAASM,GAG/DL,EAAOQ,GAAI,EAGJR,EAAOD,QA0Df,OArDAM,EAAoBM,EAAIF,EAGxBJ,EAAoBO,EAAIR,EAGxBC,EAAoBQ,EAAI,SAASd,EAASe,EAAMC,GAC3CV,EAAoBW,EAAEjB,EAASe,IAClCG,OAAOC,eAAenB,EAASe,EAAM,CAAEK,YAAY,EAAMC,IAAKL,KAKhEV,EAAoBgB,EAAI,SAAStB,GACX,oBAAXuB,QAA0BA,OAAOC,aAC1CN,OAAOC,eAAenB,EAASuB,OAAOC,YAAa,CAAEC,MAAO,WAE7DP,OAAOC,eAAenB,EAAS,aAAc,CAAEyB,OAAO,KAQvDnB,EAAoBoB,EAAI,SAASD,EAAOE,GAEvC,GADU,EAAPA,IAAUF,EAAQnB,EAAoBmB,IAC/B,EAAPE,EAAU,OAAOF,EACpB,GAAW,EAAPE,GAA8B,iBAAVF,GAAsBA,GAASA,EAAMG,WAAY,OAAOH,EAChF,IAAII,EAAKX,OAAOY,OAAO,MAGvB,GAFAxB,EAAoBgB,EAAEO,GACtBX,OAAOC,eAAeU,EAAI,UAAW,CAAET,YAAY,EAAMK,MAAOA,IACtD,EAAPE,GAA4B,iBAATF,EAAmB,IAAI,IAAIM,KAAON,EAAOnB,EAAoBQ,EAAEe,EAAIE,EAAK,SAASA,GAAO,OAAON,EAAMM,IAAQC,KAAK,KAAMD,IAC9I,OAAOF,GAIRvB,EAAoB2B,EAAI,SAAShC,GAChC,IAAIe,EAASf,GAAUA,EAAO2B,WAC7B,WAAwB,OAAO3B,EAAgB,SAC/C,WAA8B,OAAOA,GAEtC,OADAK,EAAoBQ,EAAEE,EAAQ,IAAKA,GAC5BA,GAIRV,EAAoBW,EAAI,SAASiB,EAAQC,GAAY,OAAOjB,OAAOkB,UAAUC,eAAe1B,KAAKuB,EAAQC,IAGzG7B,EAAoBgC,EAAI,GAIjBhC,EAAoBA,EAAoBiC,EAAI,G,gaCxErD,IAOA,aAGI,WAAoBC,QAAA,IAAAA,MAAA,UAAAA,oBAwLxB,OAtLI,YAAAC,cAAA,SAAcC,GACV,OAAKtC,KAAKoC,mBAGFE,EAAS,GAAK,EAAI,EAAI,GAFT,EAATA,EAAa,GAAK,EAAI,GAKtC,YAAAC,OAAA,SAAOC,GAIH,IAHA,IAAIC,EAAM,GAENrC,EAAI,EACDA,EAAIoC,EAAKF,OAAS,EAAGlC,GAAK,EAAG,CAChC,IAAIK,EAAK+B,EAAKpC,IAAM,GAAOoC,EAAKpC,EAAI,IAAM,EAAMoC,EAAKpC,EAAI,GACzDqC,GAAOzC,KAAK0C,YAAajC,IAAM,GAAS,IACxCgC,GAAOzC,KAAK0C,YAAajC,IAAM,GAAS,IACxCgC,GAAOzC,KAAK0C,YAAajC,IAAM,EAAS,IACxCgC,GAAOzC,KAAK0C,YAAajC,IAAM,EAAS,IAG5C,IAAMkC,EAAOH,EAAKF,OAASlC,EAC3B,GAAIuC,EAAO,EAAG,CACNlC,EAAK+B,EAAKpC,IAAM,IAAgB,IAATuC,EAAaH,EAAKpC,EAAI,IAAM,EAAI,GAC3DqC,GAAOzC,KAAK0C,YAAajC,IAAM,GAAS,IACxCgC,GAAOzC,KAAK0C,YAAajC,IAAM,GAAS,IAEpCgC,GADS,IAATE,EACO3C,KAAK0C,YAAajC,IAAM,EAAS,IAEjCT,KAAKoC,mBAAqB,GAErCK,GAAOzC,KAAKoC,mBAAqB,GAGrC,OAAOK,GAGX,YAAAG,iBAAA,SAAiBN,GACb,OAAKtC,KAAKoC,kBAGHE,EAAS,EAAI,EAAI,GAFH,EAATA,EAAa,GAAK,EAAI,GAKtC,YAAAO,cAAA,SAAcV,GACV,OAAOnC,KAAK4C,iBAAiBT,EAAEG,OAAStC,KAAK8C,kBAAkBX,KAGnE,YAAAY,OAAA,SAAOZ,GACH,GAAiB,IAAbA,EAAEG,OACF,OAAO,IAAIU,WAAW,GAS1B,IAPA,IAAMC,EAAgBjD,KAAK8C,kBAAkBX,GACvCG,EAASH,EAAEG,OAASW,EACpBR,EAAM,IAAIO,WAAWhD,KAAK4C,iBAAiBN,IAC7CY,EAAK,EACL9C,EAAI,EACJ+C,EAAU,EACVC,EAAK,EAAGC,EAAK,EAAGC,EAAK,EAAGC,EAAK,EAC1BnD,EAAIkC,EAAS,EAAGlC,GAAK,EACxBgD,EAAKpD,KAAKwD,YAAYrB,EAAEsB,WAAWrD,EAAI,IACvCiD,EAAKrD,KAAKwD,YAAYrB,EAAEsB,WAAWrD,EAAI,IACvCkD,EAAKtD,KAAKwD,YAAYrB,EAAEsB,WAAWrD,EAAI,IACvCmD,EAAKvD,KAAKwD,YAAYrB,EAAEsB,WAAWrD,EAAI,IACvCqC,EAAIS,KAASE,GAAM,EAAMC,IAAO,EAChCZ,EAAIS,KAASG,GAAM,EAAMC,IAAO,EAChCb,EAAIS,KAASI,GAAM,EAAKC,EACxBJ,GA7ES,IA6EEC,EACXD,GA9ES,IA8EEE,EACXF,GA/ES,IA+EEG,EACXH,GAhFS,IAgFEI,EAmBf,GAjBInD,EAAIkC,EAAS,IACbc,EAAKpD,KAAKwD,YAAYrB,EAAEsB,WAAWrD,IACnCiD,EAAKrD,KAAKwD,YAAYrB,EAAEsB,WAAWrD,EAAI,IACvCqC,EAAIS,KAASE,GAAM,EAAMC,IAAO,EAChCF,GAtFS,IAsFEC,EACXD,GAvFS,IAuFEE,GAEXjD,EAAIkC,EAAS,IACbgB,EAAKtD,KAAKwD,YAAYrB,EAAEsB,WAAWrD,EAAI,IACvCqC,EAAIS,KAASG,GAAM,EAAMC,IAAO,EAChCH,GA5FS,IA4FEG,GAEXlD,EAAIkC,EAAS,IACbiB,EAAKvD,KAAKwD,YAAYrB,EAAEsB,WAAWrD,EAAI,IACvCqC,EAAIS,KAASI,GAAM,EAAKC,EACxBJ,GAjGS,IAiGEI,GAEC,IAAZJ,EACA,MAAM,IAAIO,MAAM,kDAEpB,OAAOjB,GAYD,YAAAC,YAAV,SAAsBiB,GAqBlB,IAAIC,EAASD,EAYb,OAVAC,GAAU,GAEVA,GAAY,GAAKD,IAAO,EAAK,EAE7BC,GAAY,GAAKD,IAAO,GAAK,GAE7BC,GAAY,GAAKD,IAAO,GAAK,GAE7BC,GAAY,GAAKD,IAAO,EAAK,EAEtBE,OAAOC,aAAaF,IAKrB,YAAAJ,YAAV,SAAsB/C,GAUlB,IAAImD,EAlKS,IA+Kb,OAVAA,IAAa,GAAKnD,EAAMA,EAAI,MAAS,GArKxB,IAqK8CA,EAAI,GAAK,GAEpEmD,IAAa,GAAKnD,EAAMA,EAAI,MAAS,GAvKxB,IAuK8CA,EAAI,GAAK,GAEpEmD,IAAa,GAAKnD,EAAMA,EAAI,MAAS,GAzKxB,IAyK8CA,EAAI,GAAK,GAEpEmD,IAAa,GAAKnD,EAAMA,EAAI,MAAS,GA3KxB,IA2K8CA,EAAI,GAAK,EAEpEmD,IAAa,GAAKnD,EAAMA,EAAI,OAAU,GA7KzB,IA6K+CA,EAAI,GAAK,IAKjE,YAAAqC,kBAAR,SAA0BX,GACtB,IAAIc,EAAgB,EACpB,GAAIjD,KAAKoC,kBAAmB,CACxB,IAAK,IAAIhC,EAAI+B,EAAEG,OAAS,EAAGlC,GAAK,GACxB+B,EAAE/B,KAAOJ,KAAKoC,kBADahC,IAI/B6C,IAEJ,GAAId,EAAEG,OAAS,GAAKW,EAAgB,EAChC,MAAM,IAAIS,MAAM,kCAGxB,OAAOT,GAGf,EA3LA,GAAa,EAAAc,QA6Lb,IAAMC,EAAW,IAAID,EAErB,kBAAuBvB,GACnB,OAAOwB,EAASzB,OAAOC,IAG3B,kBAAuBL,GACnB,OAAO6B,EAASjB,OAAOZ,IAS3B,+B,+CAwCA,OAxCkC,OAQpB,YAAAO,YAAV,SAAsBiB,GAClB,IAAIC,EAASD,EAYb,OAVAC,GAAU,GAEVA,GAAY,GAAKD,IAAO,EAAK,EAE7BC,GAAY,GAAKD,IAAO,GAAK,GAE7BC,GAAY,GAAKD,IAAO,GAAK,GAE7BC,GAAY,GAAKD,IAAO,EAAK,GAEtBE,OAAOC,aAAaF,IAGrB,YAAAJ,YAAV,SAAsB/C,GAClB,IAAImD,EA7OS,IA0Pb,OAVAA,IAAa,GAAKnD,EAAMA,EAAI,MAAS,GAhPxB,IAgP8CA,EAAI,GAAK,GAEpEmD,IAAa,GAAKnD,EAAMA,EAAI,MAAS,GAlPxB,IAkP8CA,EAAI,GAAK,GAEpEmD,IAAa,GAAKnD,EAAMA,EAAI,MAAS,GApPxB,IAoP8CA,EAAI,GAAK,GAEpEmD,IAAa,GAAKnD,EAAMA,EAAI,MAAS,GAtPxB,IAsP8CA,EAAI,GAAK,EAEpEmD,IAAa,GAAKnD,EAAMA,EAAI,OAAU,GAxPzB,IAwP+CA,EAAI,GAAK,IAI7E,EAxCA,CAAkCsD,GAArB,EAAAE,eA0Cb,IAAMC,EAAe,IAAID,EAEzB,yBAA8BzB,GAC1B,OAAO0B,EAAa3B,OAAOC,IAG/B,yBAA8BL,GAC1B,OAAO+B,EAAanB,OAAOZ,IAIlB,EAAAE,cAAgB,SAACC,GAC1B,OAAA0B,EAAS3B,cAAcC,IAEd,EAAAM,iBAAmB,SAACN,GAC7B,OAAA0B,EAASpB,iBAAiBN,IAEjB,EAAAO,cAAgB,SAACV,GAC1B,OAAA6B,EAASnB,cAAcV,K,8ECnR3B,IACMgC,EAAe,gCA2CrB,SAAgB9B,EAAcF,GAE1B,IADA,IAAIyB,EAAS,EACJxD,EAAI,EAAGA,EAAI+B,EAAEG,OAAQlC,IAAK,CAC/B,IAAMK,EAAI0B,EAAEsB,WAAWrD,GACvB,GAAIK,EAAI,IACJmD,GAAU,OACP,GAAInD,EAAI,KACXmD,GAAU,OACP,GAAInD,EAAI,MACXmD,GAAU,MACP,MAAInD,GAAK,OAOZ,MAAM,IAAIiD,MA7DA,wBAuDV,GAAItD,GAAK+B,EAAEG,OAAS,EAChB,MAAM,IAAIoB,MAxDJ,wBA0DVtD,IACAwD,GAAU,GAKlB,OAAOA,EAzDX,kBAAuBzB,GAOnB,IAHA,IAAMiC,EAAM,IAAIpB,WAAWX,EAAcF,IAErCkC,EAAM,EACDjE,EAAI,EAAGA,EAAI+B,EAAEG,OAAQlC,IAAK,CAC/B,IAAIK,EAAI0B,EAAEsB,WAAWrD,GACjBK,EAAI,IACJ2D,EAAIC,KAAS5D,EACNA,EAAI,MACX2D,EAAIC,KAAS,IAAO5D,GAAK,EACzB2D,EAAIC,KAAS,IAAW,GAAJ5D,GACbA,EAAI,OACX2D,EAAIC,KAAS,IAAO5D,GAAK,GACzB2D,EAAIC,KAAS,IAAQ5D,GAAK,EAAK,GAC/B2D,EAAIC,KAAS,IAAW,GAAJ5D,IAEpBL,IACAK,GAAS,KAAJA,IAAc,GACnBA,GAAuB,KAAlB0B,EAAEsB,WAAWrD,GAClBK,GAAK,MAEL2D,EAAIC,KAAS,IAAO5D,GAAK,GACzB2D,EAAIC,KAAS,IAAQ5D,GAAK,GAAM,GAChC2D,EAAIC,KAAS,IAAQ5D,GAAK,EAAK,GAC/B2D,EAAIC,KAAS,IAAW,GAAJ5D,GAG5B,OAAO2D,GAOX,kBA2BA,kBAAuBA,GAEnB,IADA,IAAME,EAAkB,GACflE,EAAI,EAAGA,EAAIgE,EAAI9B,OAAQlC,IAAK,CACjC,IAAIuD,EAAIS,EAAIhE,GAEZ,GAAQ,IAAJuD,EAAU,CACV,IAAIY,OAAG,EACP,GAAIZ,EAAI,IAAM,CAEV,GAAIvD,GAAKgE,EAAI9B,OACT,MAAM,IAAIoB,MAAMS,GAGpB,GAAoB,MAAV,KADJK,EAAKJ,IAAMhE,KAEb,MAAM,IAAIsD,MAAMS,GAEpBR,GAAS,GAAJA,IAAa,EAAU,GAALa,EACvBD,EAAM,SACH,GAAIZ,EAAI,IAAM,CAEjB,GAAIvD,GAAKgE,EAAI9B,OAAS,EAClB,MAAM,IAAIoB,MAAMS,GAEpB,IAAMK,EAAKJ,IAAMhE,GACXqE,EAAKL,IAAMhE,GACjB,GAAoB,MAAV,IAALoE,IAAuC,MAAV,IAALC,GACzB,MAAM,IAAIf,MAAMS,GAEpBR,GAAS,GAAJA,IAAa,IAAW,GAALa,IAAc,EAAU,GAALC,EAC3CF,EAAM,SACH,MAAIZ,EAAI,KAcX,MAAM,IAAID,MAAMS,GAZhB,GAAI/D,GAAKgE,EAAI9B,OAAS,EAClB,MAAM,IAAIoB,MAAMS,GAEdK,EAAKJ,IAAMhE,GACXqE,EAAKL,IAAMhE,GADjB,IAEMsE,EAAKN,IAAMhE,GACjB,GAAoB,MAAV,IAALoE,IAAuC,MAAV,IAALC,IAAuC,MAAV,IAALC,GACjD,MAAM,IAAIhB,MAAMS,GAEpBR,GAAS,GAAJA,IAAa,IAAW,GAALa,IAAc,IAAW,GAALC,IAAc,EAAU,GAALC,EAC/DH,EAAM,MAKV,GAAIZ,EAAIY,GAAQZ,GAAK,OAAUA,GAAK,MAChC,MAAM,IAAID,MAAMS,GAGpB,GAAIR,GAAK,MAAS,CAEd,GAAIA,EAAI,QACJ,MAAM,IAAID,MAAMS,GAEpBR,GAAK,MACLW,EAAMK,KAAKd,OAAOC,aAAa,MAAUH,GAAK,KAC9CA,EAAI,MAAc,KAAJA,GAItBW,EAAMK,KAAKd,OAAOC,aAAaH,IAEnC,OAAOW,EAAMM,KAAK,M,iBC9ItB,SAAUC,GACV,aAQA,IAAIC,EAAK,SAASC,GAChB,IAAI3E,EAAGc,EAAI,IAAI8D,aAAa,IAC5B,GAAID,EAAM,IAAK3E,EAAI,EAAGA,EAAI2E,EAAKzC,OAAQlC,IAAKc,EAAEd,GAAK2E,EAAK3E,GACxD,OAAOc,GAIL+D,EAAc,WAAuB,MAAM,IAAIvB,MAAM,YAErDwB,EAAK,IAAIlC,WAAW,IACpBmC,EAAK,IAAInC,WAAW,IAAKmC,EAAG,GAAK,EAErC,IAAIC,EAAMN,IACNO,EAAMP,EAAG,CAAC,IACVQ,EAAUR,EAAG,CAAC,MAAQ,IACtBS,EAAIT,EAAG,CAAC,MAAQ,KAAQ,MAAQ,MAAQ,MAAQ,MAAQ,KAAQ,IAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,QAChIU,EAAKV,EAAG,CAAC,MAAQ,KAAQ,MAAQ,MAAQ,MAAQ,MAAQ,KAAQ,IAAQ,MAAQ,MAAQ,MAAQ,KAAQ,MAAQ,MAAQ,MAAQ,OACjIW,EAAIX,EAAG,CAAC,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,OAChIY,EAAIZ,EAAG,CAAC,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,MAAQ,QAChIa,EAAIb,EAAG,CAAC,MAAQ,MAAQ,KAAQ,MAAQ,MAAQ,MAAQ,KAAQ,MAAQ,MAAQ,MAAQ,IAAQ,MAAQ,MAAQ,MAAQ,KAAQ,QAEpI,SAASc,EAAKC,EAAGzF,EAAG0F,EAAGzF,GACrBwF,EAAEzF,GAAQ0F,GAAK,GAAM,IACrBD,EAAEzF,EAAE,GAAM0F,GAAK,GAAM,IACrBD,EAAEzF,EAAE,GAAM0F,GAAM,EAAK,IACrBD,EAAEzF,EAAE,GAAS,IAAJ0F,EACTD,EAAEzF,EAAE,GAAMC,GAAK,GAAO,IACtBwF,EAAEzF,EAAE,GAAMC,GAAK,GAAO,IACtBwF,EAAEzF,EAAE,GAAMC,GAAM,EAAM,IACtBwF,EAAEzF,EAAE,GAAS,IAAJC,EAGX,SAAS0F,EAAGF,EAAGG,EAAIC,EAAGC,EAAIrE,GACxB,IAAIzB,EAAEM,EAAI,EACV,IAAKN,EAAI,EAAGA,EAAIyB,EAAGzB,IAAKM,GAAKmF,EAAEG,EAAG5F,GAAG6F,EAAEC,EAAG9F,GAC1C,OAAQ,EAAMM,EAAI,IAAO,GAAM,EAGjC,SAASyF,EAAiBN,EAAGG,EAAIC,EAAGC,GAClC,OAAOH,EAAGF,EAAEG,EAAGC,EAAEC,EAAG,IAGtB,SAASE,EAAiBP,EAAGG,EAAIC,EAAGC,GAClC,OAAOH,EAAGF,EAAEG,EAAGC,EAAEC,EAAG,IA6UtB,SAASG,EAAoB5D,EAAI6D,EAAIC,EAAE9F,IA1UvC,SAAsBI,EAAGqB,EAAGqE,EAAG9F,GAsB7B,IArBA,IAmBe+F,EAnBXC,EAAc,IAARhG,EAAG,IAAqB,IAARA,EAAG,KAAY,GAAa,IAARA,EAAG,KAAY,IAAc,IAARA,EAAG,KAAY,GAC9EiG,EAAc,IAARH,EAAG,IAAqB,IAARA,EAAG,KAAY,GAAa,IAARA,EAAG,KAAY,IAAc,IAARA,EAAG,KAAY,GAC9EI,EAAc,IAARJ,EAAG,IAAqB,IAARA,EAAG,KAAY,GAAa,IAARA,EAAG,KAAY,IAAc,IAARA,EAAG,KAAY,GAC9EK,EAAc,IAARL,EAAG,IAAqB,IAARA,EAAG,KAAY,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAC9EM,EAAc,IAARN,EAAE,KAAsB,IAARA,EAAE,MAAa,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAC9EO,EAAc,IAARrG,EAAG,IAAqB,IAARA,EAAG,KAAY,GAAa,IAARA,EAAG,KAAY,IAAc,IAARA,EAAG,KAAY,GAC9EsG,EAAc,IAAR7E,EAAG,IAAqB,IAARA,EAAG,KAAY,GAAa,IAARA,EAAG,KAAY,IAAc,IAARA,EAAG,KAAY,GAC9E8E,EAAc,IAAR9E,EAAG,IAAqB,IAARA,EAAG,KAAY,GAAa,IAARA,EAAG,KAAY,IAAc,IAARA,EAAG,KAAY,GAC9E+E,EAAc,IAAR/E,EAAG,IAAqB,IAARA,EAAG,KAAY,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAC9EgF,EAAc,IAARhF,EAAE,KAAsB,IAARA,EAAE,MAAa,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAC9EiF,EAAc,IAAR1G,EAAG,IAAqB,IAARA,EAAG,KAAY,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAC9E2G,EAAc,IAARb,EAAE,KAAsB,IAARA,EAAE,MAAa,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAC9Ec,EAAc,IAARd,EAAE,KAAsB,IAARA,EAAE,MAAa,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAC9Ee,EAAc,IAARf,EAAE,KAAsB,IAARA,EAAE,MAAa,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAC9EgB,EAAc,IAARhB,EAAE,KAAsB,IAARA,EAAE,MAAa,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAC9EiB,EAAc,IAAR/G,EAAE,KAAsB,IAARA,EAAE,MAAa,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAE9EgH,EAAKhB,EAAIiB,EAAKhB,EAAIiB,EAAKhB,EAAIiB,EAAKhB,EAAIiB,EAAKhB,EAAIiB,EAAKhB,EAAIiB,EAAKhB,EAAIiB,EAAKhB,EACpEiB,EAAKhB,EAAIiB,EAAKhB,EAAIiB,EAAMhB,EAAKiB,EAAMhB,EAAKiB,EAAMhB,EAAKiB,EAAMhB,EAAKiB,EAAMhB,EACpEiB,EAAMhB,EAEDpH,EAAI,EAAGA,EAAI,GAAIA,GAAK,EAQ3BqH,IADAjB,GADA6B,IADA7B,GADAyB,IADAzB,GADAqB,IADArB,EAAIiB,EAAKY,EAAM,IACN,EAAI7B,IAAI,IACRiB,EAAK,IACL,EAAIjB,IAAI,IACRqB,EAAK,IACJ,GAAKrB,IAAI,IACTyB,EAAK,IACN,GAAKzB,IAAI,GASlBsB,IADAtB,GADAkB,IADAlB,GADA8B,IADA9B,GADA0B,IADA1B,EAAIsB,EAAKJ,EAAK,IACL,EAAIlB,IAAI,IACRsB,EAAK,IACJ,EAAItB,IAAI,IACR0B,EAAK,IACN,GAAK1B,IAAI,IACT8B,EAAM,IACN,GAAK9B,IAAI,GASlB2B,IADA3B,GADAuB,IADAvB,GADAmB,IADAnB,GADA+B,IADA/B,EAAI2B,EAAMJ,EAAK,IACL,EAAIvB,IAAI,IACR2B,EAAM,IACP,EAAI3B,IAAI,IACR+B,EAAM,IACN,GAAK/B,IAAI,IACTmB,EAAK,IACJ,GAAKnB,IAAI,GASnBgC,IADAhC,GADA4B,IADA5B,GADAwB,IADAxB,GADAoB,IADApB,EAAIgC,EAAMJ,EAAM,IACP,EAAI5B,IAAI,IACRgC,EAAM,IACN,EAAIhC,IAAI,IACRoB,EAAK,IACJ,GAAKpB,IAAI,IACTwB,EAAK,IACL,GAAKxB,IAAI,GASnBiB,IADAjB,GADAoB,IADApB,GADAmB,IADAnB,GADAkB,IADAlB,EAAIiB,EAAKG,EAAK,IACL,EAAIpB,IAAI,IACRiB,EAAK,IACL,EAAIjB,IAAI,IACRkB,EAAK,IACL,GAAKlB,IAAI,IACTmB,EAAK,IACL,GAAKnB,IAAI,GASlBsB,IADAtB,GADAqB,IADArB,GADAwB,IADAxB,GADAuB,IADAvB,EAAIsB,EAAKD,EAAK,IACL,EAAIrB,IAAI,IACRsB,EAAK,IACL,EAAItB,IAAI,IACRuB,EAAK,IACL,GAAKvB,IAAI,IACTwB,EAAK,IACL,GAAKxB,IAAI,GASlB2B,IADA3B,GADA0B,IADA1B,GADAyB,IADAzB,GADA4B,IADA5B,EAAI2B,EAAMD,EAAK,IACL,EAAI1B,IAAI,IACR2B,EAAM,IACP,EAAI3B,IAAI,IACR4B,EAAM,IACN,GAAK5B,IAAI,IACTyB,EAAK,IACJ,GAAKzB,IAAI,GASnBgC,IADAhC,GADA+B,IADA/B,GADA8B,IADA9B,GADA6B,IADA7B,EAAIgC,EAAMD,EAAM,IACN,EAAI/B,IAAI,IACRgC,EAAM,IACN,EAAIhC,IAAI,IACR6B,EAAM,IACN,GAAK7B,IAAI,IACT8B,EAAM,IACN,GAAK9B,IAAI,GAEpBiB,EAAMA,EAAMhB,EAAK,EACjBiB,EAAMA,EAAMhB,EAAK,EACjBiB,EAAMA,EAAMhB,EAAK,EACjBiB,EAAMA,EAAMhB,EAAK,EACjBiB,EAAMA,EAAMhB,EAAK,EACjBiB,EAAMA,EAAMhB,EAAK,EACjBiB,EAAMA,EAAMhB,EAAK,EACjBiB,EAAMA,EAAMhB,EAAK,EACjBiB,EAAMA,EAAMhB,EAAK,EACjBiB,EAAMA,EAAMhB,EAAK,EAClBiB,EAAMA,EAAMhB,EAAM,EAClBiB,EAAMA,EAAMhB,EAAM,EAClBiB,EAAMA,EAAMhB,EAAM,EAClBiB,EAAMA,EAAMhB,EAAM,EAClBiB,EAAMA,EAAMhB,EAAM,EAClBiB,EAAMA,EAAMhB,EAAM,EAElB3G,EAAG,GAAK4G,IAAQ,EAAI,IACpB5G,EAAG,GAAK4G,IAAQ,EAAI,IACpB5G,EAAG,GAAK4G,IAAO,GAAK,IACpB5G,EAAG,GAAK4G,IAAO,GAAK,IAEpB5G,EAAG,GAAK6G,IAAQ,EAAI,IACpB7G,EAAG,GAAK6G,IAAQ,EAAI,IACpB7G,EAAG,GAAK6G,IAAO,GAAK,IACpB7G,EAAG,GAAK6G,IAAO,GAAK,IAEpB7G,EAAG,GAAK8G,IAAQ,EAAI,IACpB9G,EAAG,GAAK8G,IAAQ,EAAI,IACpB9G,EAAE,IAAM8G,IAAO,GAAK,IACpB9G,EAAE,IAAM8G,IAAO,GAAK,IAEpB9G,EAAE,IAAM+G,IAAQ,EAAI,IACpB/G,EAAE,IAAM+G,IAAQ,EAAI,IACpB/G,EAAE,IAAM+G,IAAO,GAAK,IACpB/G,EAAE,IAAM+G,IAAO,GAAK,IAEpB/G,EAAE,IAAMgH,IAAQ,EAAI,IACpBhH,EAAE,IAAMgH,IAAQ,EAAI,IACpBhH,EAAE,IAAMgH,IAAO,GAAK,IACpBhH,EAAE,IAAMgH,IAAO,GAAK,IAEpBhH,EAAE,IAAMiH,IAAQ,EAAI,IACpBjH,EAAE,IAAMiH,IAAQ,EAAI,IACpBjH,EAAE,IAAMiH,IAAO,GAAK,IACpBjH,EAAE,IAAMiH,IAAO,GAAK,IAEpBjH,EAAE,IAAMkH,IAAQ,EAAI,IACpBlH,EAAE,IAAMkH,IAAQ,EAAI,IACpBlH,EAAE,IAAMkH,IAAO,GAAK,IACpBlH,EAAE,IAAMkH,IAAO,GAAK,IAEpBlH,EAAE,IAAMmH,IAAQ,EAAI,IACpBnH,EAAE,IAAMmH,IAAQ,EAAI,IACpBnH,EAAE,IAAMmH,IAAO,GAAK,IACpBnH,EAAE,IAAMmH,IAAO,GAAK,IAEpBnH,EAAE,IAAMoH,IAAQ,EAAI,IACpBpH,EAAE,IAAMoH,IAAQ,EAAI,IACpBpH,EAAE,IAAMoH,IAAO,GAAK,IACpBpH,EAAE,IAAMoH,IAAO,GAAK,IAEpBpH,EAAE,IAAMqH,IAAQ,EAAI,IACpBrH,EAAE,IAAMqH,IAAQ,EAAI,IACpBrH,EAAE,IAAMqH,IAAO,GAAK,IACpBrH,EAAE,IAAMqH,IAAO,GAAK,IAEpBrH,EAAE,IAAMsH,IAAS,EAAI,IACrBtH,EAAE,IAAMsH,IAAS,EAAI,IACrBtH,EAAE,IAAMsH,IAAQ,GAAK,IACrBtH,EAAE,IAAMsH,IAAQ,GAAK,IAErBtH,EAAE,IAAMuH,IAAS,EAAI,IACrBvH,EAAE,IAAMuH,IAAS,EAAI,IACrBvH,EAAE,IAAMuH,IAAQ,GAAK,IACrBvH,EAAE,IAAMuH,IAAQ,GAAK,IAErBvH,EAAE,IAAMwH,IAAS,EAAI,IACrBxH,EAAE,IAAMwH,IAAS,EAAI,IACrBxH,EAAE,IAAMwH,IAAQ,GAAK,IACrBxH,EAAE,IAAMwH,IAAQ,GAAK,IAErBxH,EAAE,IAAMyH,IAAS,EAAI,IACrBzH,EAAE,IAAMyH,IAAS,EAAI,IACrBzH,EAAE,IAAMyH,IAAQ,GAAK,IACrBzH,EAAE,IAAMyH,IAAQ,GAAK,IAErBzH,EAAE,IAAM0H,IAAS,EAAI,IACrB1H,EAAE,IAAM0H,IAAS,EAAI,IACrB1H,EAAE,IAAM0H,IAAQ,GAAK,IACrB1H,EAAE,IAAM0H,IAAQ,GAAK,IAErB1H,EAAE,IAAM2H,IAAS,EAAI,IACrB3H,EAAE,IAAM2H,IAAS,EAAI,IACrB3H,EAAE,IAAM2H,IAAQ,GAAK,IACrB3H,EAAE,IAAM2H,IAAQ,GAAK,IA6IrBC,CAAahG,EAAI6D,EAAIC,EAAE9F,GAGzB,SAASiI,EAAqBjG,EAAI6D,EAAIC,EAAE9F,IA7IxC,SAAuBI,EAAEqB,EAAEqE,EAAE9F,GAsB3B,IArBA,IAmBe+F,EAFXiB,EAjBc,IAARhH,EAAG,IAAqB,IAARA,EAAG,KAAY,GAAa,IAARA,EAAG,KAAY,IAAc,IAARA,EAAG,KAAY,GAiBrEiH,EAhBK,IAARnB,EAAG,IAAqB,IAARA,EAAG,KAAY,GAAa,IAARA,EAAG,KAAY,IAAc,IAARA,EAAG,KAAY,GAgB5DoB,EAfJ,IAARpB,EAAG,IAAqB,IAARA,EAAG,KAAY,GAAa,IAARA,EAAG,KAAY,IAAc,IAARA,EAAG,KAAY,GAenDqB,EAdb,IAARrB,EAAG,IAAqB,IAARA,EAAG,KAAY,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAc1CsB,EAbtB,IAARtB,EAAE,KAAsB,IAARA,EAAE,MAAa,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAajCuB,EAZ/B,IAARrH,EAAG,IAAqB,IAARA,EAAG,KAAY,GAAa,IAARA,EAAG,KAAY,IAAc,IAARA,EAAG,KAAY,GAYxBsH,EAXxC,IAAR7F,EAAG,IAAqB,IAARA,EAAG,KAAY,GAAa,IAARA,EAAG,KAAY,IAAc,IAARA,EAAG,KAAY,GAWf8F,EAVjD,IAAR9F,EAAG,IAAqB,IAARA,EAAG,KAAY,GAAa,IAARA,EAAG,KAAY,IAAc,IAARA,EAAG,KAAY,GAW9E+F,EAVc,IAAR/F,EAAG,IAAqB,IAARA,EAAG,KAAY,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAUrEgG,EATK,IAARhG,EAAE,KAAsB,IAARA,EAAE,MAAa,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAS5DiG,EARJ,IAAR1H,EAAG,IAAqB,IAARA,EAAG,KAAY,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAQjD2H,EAPf,IAAR7B,EAAE,KAAsB,IAARA,EAAE,MAAa,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAOtC8B,EAN1B,IAAR9B,EAAE,KAAsB,IAARA,EAAE,MAAa,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAM3B+B,EALrC,IAAR/B,EAAE,KAAsB,IAARA,EAAE,MAAa,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAKhBgC,EAJhD,IAARhC,EAAE,KAAsB,IAARA,EAAE,MAAa,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAK9EiC,EAJc,IAAR/H,EAAE,KAAsB,IAARA,EAAE,MAAa,GAAa,IAARA,EAAE,MAAa,IAAc,IAARA,EAAE,MAAa,GAMzEL,EAAI,EAAGA,EAAI,GAAIA,GAAK,EAQ3BqH,IADAjB,GADA6B,IADA7B,GADAyB,IADAzB,GADAqB,IADArB,EAAIiB,EAAKY,EAAM,IACN,EAAI7B,IAAI,IACRiB,EAAK,IACL,EAAIjB,IAAI,IACRqB,EAAK,IACJ,GAAKrB,IAAI,IACTyB,EAAK,IACN,GAAKzB,IAAI,GASlBsB,IADAtB,GADAkB,IADAlB,GADA8B,IADA9B,GADA0B,IADA1B,EAAIsB,EAAKJ,EAAK,IACL,EAAIlB,IAAI,IACRsB,EAAK,IACJ,EAAItB,IAAI,IACR0B,EAAK,IACN,GAAK1B,IAAI,IACT8B,EAAM,IACN,GAAK9B,IAAI,GASlB2B,IADA3B,GADAuB,IADAvB,GADAmB,IADAnB,GADA+B,IADA/B,EAAI2B,EAAMJ,EAAK,IACL,EAAIvB,IAAI,IACR2B,EAAM,IACP,EAAI3B,IAAI,IACR+B,EAAM,IACN,GAAK/B,IAAI,IACTmB,EAAK,IACJ,GAAKnB,IAAI,GASnBgC,IADAhC,GADA4B,IADA5B,GADAwB,IADAxB,GADAoB,IADApB,EAAIgC,EAAMJ,EAAM,IACP,EAAI5B,IAAI,IACRgC,EAAM,IACN,EAAIhC,IAAI,IACRoB,EAAK,IACJ,GAAKpB,IAAI,IACTwB,EAAK,IACL,GAAKxB,IAAI,GASnBiB,IADAjB,GADAoB,IADApB,GADAmB,IADAnB,GADAkB,IADAlB,EAAIiB,EAAKG,EAAK,IACL,EAAIpB,IAAI,IACRiB,EAAK,IACL,EAAIjB,IAAI,IACRkB,EAAK,IACL,GAAKlB,IAAI,IACTmB,EAAK,IACL,GAAKnB,IAAI,GASlBsB,IADAtB,GADAqB,IADArB,GADAwB,IADAxB,GADAuB,IADAvB,EAAIsB,EAAKD,EAAK,IACL,EAAIrB,IAAI,IACRsB,EAAK,IACL,EAAItB,IAAI,IACRuB,EAAK,IACL,GAAKvB,IAAI,IACTwB,EAAK,IACL,GAAKxB,IAAI,GASlB2B,IADA3B,GADA0B,IADA1B,GADAyB,IADAzB,GADA4B,IADA5B,EAAI2B,EAAMD,EAAK,IACL,EAAI1B,IAAI,IACR2B,EAAM,IACP,EAAI3B,IAAI,IACR4B,EAAM,IACN,GAAK5B,IAAI,IACTyB,EAAK,IACJ,GAAKzB,IAAI,GASnBgC,IADAhC,GADA+B,IADA/B,GADA8B,IADA9B,GADA6B,IADA7B,EAAIgC,EAAMD,EAAM,IACN,EAAI/B,IAAI,IACRgC,EAAM,IACN,EAAIhC,IAAI,IACR6B,EAAM,IACN,GAAK7B,IAAI,IACT8B,EAAM,IACN,GAAK9B,IAAI,GAGrB3F,EAAG,GAAK4G,IAAQ,EAAI,IACpB5G,EAAG,GAAK4G,IAAQ,EAAI,IACpB5G,EAAG,GAAK4G,IAAO,GAAK,IACpB5G,EAAG,GAAK4G,IAAO,GAAK,IAEpB5G,EAAG,GAAKiH,IAAQ,EAAI,IACpBjH,EAAG,GAAKiH,IAAQ,EAAI,IACpBjH,EAAG,GAAKiH,IAAO,GAAK,IACpBjH,EAAG,GAAKiH,IAAO,GAAK,IAEpBjH,EAAG,GAAKsH,IAAS,EAAI,IACrBtH,EAAG,GAAKsH,IAAS,EAAI,IACrBtH,EAAE,IAAMsH,IAAQ,GAAK,IACrBtH,EAAE,IAAMsH,IAAQ,GAAK,IAErBtH,EAAE,IAAM2H,IAAS,EAAI,IACrB3H,EAAE,IAAM2H,IAAS,EAAI,IACrB3H,EAAE,IAAM2H,IAAQ,GAAK,IACrB3H,EAAE,IAAM2H,IAAQ,GAAK,IAErB3H,EAAE,IAAMkH,IAAQ,EAAI,IACpBlH,EAAE,IAAMkH,IAAQ,EAAI,IACpBlH,EAAE,IAAMkH,IAAO,GAAK,IACpBlH,EAAE,IAAMkH,IAAO,GAAK,IAEpBlH,EAAE,IAAMmH,IAAQ,EAAI,IACpBnH,EAAE,IAAMmH,IAAQ,EAAI,IACpBnH,EAAE,IAAMmH,IAAO,GAAK,IACpBnH,EAAE,IAAMmH,IAAO,GAAK,IAEpBnH,EAAE,IAAMoH,IAAQ,EAAI,IACpBpH,EAAE,IAAMoH,IAAQ,EAAI,IACpBpH,EAAE,IAAMoH,IAAO,GAAK,IACpBpH,EAAE,IAAMoH,IAAO,GAAK,IAEpBpH,EAAE,IAAMqH,IAAQ,EAAI,IACpBrH,EAAE,IAAMqH,IAAQ,EAAI,IACpBrH,EAAE,IAAMqH,IAAO,GAAK,IACpBrH,EAAE,IAAMqH,IAAO,GAAK,IAQpBS,CAAclG,EAAI6D,EAAIC,EAAE9F,GAG1B,IAAImI,EAAQ,IAAI5F,WAAW,CAAC,IAAK,IAAK,IAAK,GAAI,IAAK,IAAK,GAAI,GAAI,GAAI,GAAI,GAAI,IAAK,IAAK,IAAK,GAAI,MAGhG,SAAS6F,EAA0BpI,EAAEqI,EAAKtI,EAAEuI,EAAKpF,EAAE9B,EAAE0E,GACnD,IACIC,EAAGpG,EADH4I,EAAI,IAAIhG,WAAW,IAAK6C,EAAI,IAAI7C,WAAW,IAE/C,IAAK5C,EAAI,EAAGA,EAAI,GAAIA,IAAK4I,EAAE5I,GAAK,EAChC,IAAKA,EAAI,EAAGA,EAAI,EAAGA,IAAK4I,EAAE5I,GAAKyB,EAAEzB,GACjC,KAAOuD,GAAK,IAAI,CAEd,IADA0C,EAAoBR,EAAEmD,EAAEzC,EAAEqC,GACrBxI,EAAI,EAAGA,EAAI,GAAIA,IAAKK,EAAEqI,EAAK1I,GAAKI,EAAEuI,EAAK3I,GAAKyF,EAAEzF,GAEnD,IADAoG,EAAI,EACCpG,EAAI,EAAGA,EAAI,GAAIA,IAClBoG,EAAIA,GAAY,IAAPwC,EAAE5I,IAAa,EACxB4I,EAAE5I,GAAS,IAAJoG,EACPA,KAAO,EAET7C,GAAK,GACLmF,GAAQ,GACRC,GAAQ,GAEV,GAAIpF,EAAI,EAEN,IADA0C,EAAoBR,EAAEmD,EAAEzC,EAAEqC,GACrBxI,EAAI,EAAGA,EAAIuD,EAAGvD,IAAKK,EAAEqI,EAAK1I,GAAKI,EAAEuI,EAAK3I,GAAKyF,EAAEzF,GAEpD,OAAO,EAGT,SAAS6I,EAAsBxI,EAAEqI,EAAKnF,EAAE9B,EAAE0E,GACxC,IACIC,EAAGpG,EADH4I,EAAI,IAAIhG,WAAW,IAAK6C,EAAI,IAAI7C,WAAW,IAE/C,IAAK5C,EAAI,EAAGA,EAAI,GAAIA,IAAK4I,EAAE5I,GAAK,EAChC,IAAKA,EAAI,EAAGA,EAAI,EAAGA,IAAK4I,EAAE5I,GAAKyB,EAAEzB,GACjC,KAAOuD,GAAK,IAAI,CAEd,IADA0C,EAAoBR,EAAEmD,EAAEzC,EAAEqC,GACrBxI,EAAI,EAAGA,EAAI,GAAIA,IAAKK,EAAEqI,EAAK1I,GAAKyF,EAAEzF,GAEvC,IADAoG,EAAI,EACCpG,EAAI,EAAGA,EAAI,GAAIA,IAClBoG,EAAIA,GAAY,IAAPwC,EAAE5I,IAAa,EACxB4I,EAAE5I,GAAS,IAAJoG,EACPA,KAAO,EAET7C,GAAK,GACLmF,GAAQ,GAEV,GAAInF,EAAI,EAEN,IADA0C,EAAoBR,EAAEmD,EAAEzC,EAAEqC,GACrBxI,EAAI,EAAGA,EAAIuD,EAAGvD,IAAKK,EAAEqI,EAAK1I,GAAKyF,EAAEzF,GAExC,OAAO,EAGT,SAAS8I,EAAczI,EAAEqI,EAAKpI,EAAEmB,EAAE0E,GAChC,IAAIpE,EAAI,IAAIa,WAAW,IACvB0F,EAAqBvG,EAAEN,EAAE0E,EAAEqC,GAE3B,IADA,IAAIO,EAAK,IAAInG,WAAW,GACf5C,EAAI,EAAGA,EAAI,EAAGA,IAAK+I,EAAG/I,GAAKyB,EAAEzB,EAAE,IACxC,OAAO6I,EAAsBxI,EAAEqI,EAAKpI,EAAEyI,EAAGhH,GAG3C,SAASiH,EAAkB3I,EAAEqI,EAAKtI,EAAEuI,EAAKrI,EAAEmB,EAAE0E,GAC3C,IAAIpE,EAAI,IAAIa,WAAW,IACvB0F,EAAqBvG,EAAEN,EAAE0E,EAAEqC,GAE3B,IADA,IAAIO,EAAK,IAAInG,WAAW,GACf5C,EAAI,EAAGA,EAAI,EAAGA,IAAK+I,EAAG/I,GAAKyB,EAAEzB,EAAE,IACxC,OAAOyI,EAA0BpI,EAAEqI,EAAKtI,EAAEuI,EAAKrI,EAAEyI,EAAGhH,GAQtD,IAAIkH,EAAW,SAAS1H,GAQtB,IAAI2H,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAPhC7J,KAAK8J,OAAS,IAAI9G,WAAW,IAC7BhD,KAAKkB,EAAI,IAAI6I,YAAY,IACzB/J,KAAK8F,EAAI,IAAIiE,YAAY,IACzB/J,KAAKgK,IAAM,IAAID,YAAY,GAC3B/J,KAAKiK,SAAW,EAChBjK,KAAKkK,IAAM,EAIXZ,EAAe,IAAV3H,EAAK,IAAuB,IAAVA,EAAK,KAAc,EAAG3B,KAAKkB,EAAE,GAAkC,KAA7B,EACzDqI,EAAe,IAAV5H,EAAK,IAAuB,IAAVA,EAAK,KAAc,EAAG3B,KAAKkB,EAAE,GAAkC,MAA3BoI,IAAO,GAAOC,GAAO,GAChFC,EAAe,IAAV7H,EAAK,IAAuB,IAAVA,EAAK,KAAc,EAAG3B,KAAKkB,EAAE,GAAkC,MAA3BqI,IAAO,GAAOC,GAAO,GAChFC,EAAe,IAAV9H,EAAK,IAAuB,IAAVA,EAAK,KAAc,EAAG3B,KAAKkB,EAAE,GAAkC,MAA3BsI,IAAQ,EAAMC,GAAO,GAChFC,EAAe,IAAV/H,EAAK,IAAuB,IAAVA,EAAK,KAAc,EAAG3B,KAAKkB,EAAE,GAAkC,KAA3BuI,IAAQ,EAAMC,GAAM,IAC/E1J,KAAKkB,EAAE,GAAOwI,IAAQ,EAAM,KAC5BC,EAAe,IAAVhI,EAAI,KAAwB,IAAVA,EAAI,MAAe,EAAG3B,KAAKkB,EAAE,GAAkC,MAA3BwI,IAAO,GAAOC,GAAO,GAChFC,EAAe,IAAVjI,EAAI,KAAwB,IAAVA,EAAI,MAAe,EAAG3B,KAAKkB,EAAE,GAAkC,MAA3ByI,IAAO,GAAOC,GAAO,GAChFC,EAAe,IAAVlI,EAAI,KAAwB,IAAVA,EAAI,MAAe,EAAG3B,KAAKkB,EAAE,GAAkC,MAA3B0I,IAAQ,EAAMC,GAAO,GAChF7J,KAAKkB,EAAE,GAAO2I,IAAQ,EAAM,IAE5B7J,KAAKgK,IAAI,GAAe,IAAVrI,EAAI,KAAwB,IAAVA,EAAI,MAAe,EACnD3B,KAAKgK,IAAI,GAAe,IAAVrI,EAAI,KAAwB,IAAVA,EAAI,MAAe,EACnD3B,KAAKgK,IAAI,GAAe,IAAVrI,EAAI,KAAwB,IAAVA,EAAI,MAAe,EACnD3B,KAAKgK,IAAI,GAAe,IAAVrI,EAAI,KAAwB,IAAVA,EAAI,MAAe,EACnD3B,KAAKgK,IAAI,GAAe,IAAVrI,EAAI,KAAwB,IAAVA,EAAI,MAAe,EACnD3B,KAAKgK,IAAI,GAAe,IAAVrI,EAAI,KAAwB,IAAVA,EAAI,MAAe,EACnD3B,KAAKgK,IAAI,GAAe,IAAVrI,EAAI,KAAwB,IAAVA,EAAI,MAAe,EACnD3B,KAAKgK,IAAI,GAAe,IAAVrI,EAAI,KAAwB,IAAVA,EAAI,MAAe,GAoUrD,SAASwI,EAAmB1H,EAAK2H,EAAQ5J,EAAGuI,EAAMlH,EAAG0E,GACnD,IAAIpE,EAAI,IAAIkH,EAAS9C,GAGrB,OAFApE,EAAEkI,OAAO7J,EAAGuI,EAAMlH,GAClBM,EAAEmI,OAAO7H,EAAK2H,GACP,EAGT,SAASG,EAA0BzE,EAAG0E,EAAMhK,EAAGuI,EAAMlH,EAAG0E,GACtD,IAAIV,EAAI,IAAI7C,WAAW,IAEvB,OADAmH,EAAmBtE,EAAE,EAAErF,EAAEuI,EAAKlH,EAAE0E,GACzBJ,EAAiBL,EAAE0E,EAAK3E,EAAE,GAGnC,SAAS4E,EAAiBhK,EAAED,EAAEE,EAAEmB,EAAE0E,GAChC,IAAInG,EACJ,GAAIM,EAAI,GAAI,OAAQ,EAGpB,IAFA0I,EAAkB3I,EAAE,EAAED,EAAE,EAAEE,EAAEmB,EAAE0E,GAC9B4D,EAAmB1J,EAAG,GAAIA,EAAG,GAAIC,EAAI,GAAID,GACpCL,EAAI,EAAGA,EAAI,GAAIA,IAAKK,EAAEL,GAAK,EAChC,OAAO,EAGT,SAASsK,EAAsBlK,EAAEC,EAAEC,EAAEmB,EAAE0E,GACrC,IAAInG,EACAyF,EAAI,IAAI7C,WAAW,IACvB,GAAItC,EAAI,GAAI,OAAQ,EAEpB,GADAwI,EAAcrD,EAAE,EAAE,GAAGhE,EAAE0E,GACiC,IAApDgE,EAA0B9J,EAAG,GAAGA,EAAG,GAAGC,EAAI,GAAGmF,GAAU,OAAQ,EAEnE,IADAuD,EAAkB5I,EAAE,EAAEC,EAAE,EAAEC,EAAEmB,EAAE0E,GACzBnG,EAAI,EAAGA,EAAI,GAAIA,IAAKI,EAAEJ,GAAK,EAChC,OAAO,EAGT,SAASuK,EAASzJ,EAAG0J,GACnB,IAAIxK,EACJ,IAAKA,EAAI,EAAGA,EAAI,GAAIA,IAAKc,EAAEd,GAAU,EAALwK,EAAExK,GAGpC,SAASyK,EAAShK,GAChB,IAAIT,EAAG0K,EAAGrK,EAAI,EACd,IAAKL,EAAI,EAAGA,EAAI,GAAIA,IAClB0K,EAAIjK,EAAET,GAAKK,EAAI,MACfA,EAAIsK,KAAKC,MAAMF,EAAI,OACnBjK,EAAET,GAAK0K,EAAQ,MAAJrK,EAEbI,EAAE,IAAMJ,EAAE,EAAI,IAAMA,EAAE,GAGxB,SAASwK,EAAS/I,EAAGgJ,EAAGvH,GAEtB,IADA,IAAIrC,EAAGb,IAAMkD,EAAE,GACNvD,EAAI,EAAGA,EAAI,GAAIA,IACtBkB,EAAIb,GAAKyB,EAAE9B,GAAK8K,EAAE9K,IAClB8B,EAAE9B,IAAMkB,EACR4J,EAAE9K,IAAMkB,EAIZ,SAAS6J,EAAUtK,EAAGgB,GACpB,IAAIzB,EAAGgL,EAAGzH,EACNnD,EAAIsE,IAAMxD,EAAIwD,IAClB,IAAK1E,EAAI,EAAGA,EAAI,GAAIA,IAAKkB,EAAElB,GAAKyB,EAAEzB,GAIlC,IAHAyK,EAASvJ,GACTuJ,EAASvJ,GACTuJ,EAASvJ,GACJ8J,EAAI,EAAGA,EAAI,EAAGA,IAAK,CAEtB,IADA5K,EAAE,GAAKc,EAAE,GAAK,MACTlB,EAAI,EAAGA,EAAI,GAAIA,IAClBI,EAAEJ,GAAKkB,EAAElB,GAAK,OAAWI,EAAEJ,EAAE,IAAI,GAAM,GACvCI,EAAEJ,EAAE,IAAM,MAEZI,EAAE,IAAMc,EAAE,IAAM,OAAWd,EAAE,KAAK,GAAM,GACxCmD,EAAKnD,EAAE,KAAK,GAAM,EAClBA,EAAE,KAAO,MACTyK,EAAS3J,EAAGd,EAAG,EAAEmD,GAEnB,IAAKvD,EAAI,EAAGA,EAAI,GAAIA,IAClBS,EAAE,EAAET,GAAY,IAAPkB,EAAElB,GACXS,EAAE,EAAET,EAAE,GAAKkB,EAAElB,IAAI,EAIrB,SAASiL,EAAST,EAAGjH,GACnB,IAAIlD,EAAI,IAAIuC,WAAW,IAAKtC,EAAI,IAAIsC,WAAW,IAG/C,OAFAmI,EAAU1K,EAAGmK,GACbO,EAAUzK,EAAGiD,GACNyC,EAAiB3F,EAAG,EAAGC,EAAG,GAGnC,SAAS4K,EAASV,GAChB,IAAIlK,EAAI,IAAIsC,WAAW,IAEvB,OADAmI,EAAUzK,EAAGkK,GACC,EAAPlK,EAAE,GAGX,SAAS6K,EAAY1K,EAAGgB,GACtB,IAAIzB,EACJ,IAAKA,EAAI,EAAGA,EAAI,GAAIA,IAAKS,EAAET,GAAKyB,EAAE,EAAEzB,IAAMyB,EAAE,EAAEzB,EAAE,IAAM,GACtDS,EAAE,KAAO,MAGX,SAAS2K,EAAE3K,EAAG+J,EAAGjH,GACf,IAAK,IAAIvD,EAAI,EAAGA,EAAI,GAAIA,IAAKS,EAAET,GAAKwK,EAAExK,GAAKuD,EAAEvD,GAG/C,SAASqL,EAAE5K,EAAG+J,EAAGjH,GACf,IAAK,IAAIvD,EAAI,EAAGA,EAAI,GAAIA,IAAKS,EAAET,GAAKwK,EAAExK,GAAKuD,EAAEvD,GAG/C,SAASsL,EAAE7K,EAAG+J,EAAGjH,GACf,IAAImH,EAAGrK,EACJ6I,EAAK,EAAIC,EAAK,EAAIC,EAAK,EAAIC,EAAK,EAAIC,EAAK,EAAIC,EAAK,EAAIC,EAAK,EAAIC,EAAK,EACpE8B,EAAK,EAAIC,EAAK,EAAGC,EAAM,EAAGC,EAAM,EAAGC,EAAM,EAAGC,EAAM,EAAGC,EAAM,EAAGC,EAAM,EACrEC,EAAM,EAAGC,EAAM,EAAGC,EAAM,EAAGC,EAAM,EAAGC,EAAM,EAAGC,EAAM,EAAGC,EAAM,EAAGC,EAAM,EACrEC,EAAM,EAAGC,EAAM,EAAGC,EAAM,EAAGC,EAAM,EAAGC,EAAM,EAAGC,EAAM,EAAGC,EAAM,EAC5DC,EAAKvJ,EAAE,GACPwJ,EAAKxJ,EAAE,GACPyJ,EAAKzJ,EAAE,GACP0J,EAAK1J,EAAE,GACP2J,EAAK3J,EAAE,GACP4J,EAAK5J,EAAE,GACP6J,EAAK7J,EAAE,GACP8J,EAAK9J,EAAE,GACP+J,EAAK/J,EAAE,GACPgK,EAAKhK,EAAE,GACPiK,EAAMjK,EAAE,IACRkK,EAAMlK,EAAE,IACRmK,EAAMnK,EAAE,IACRoK,EAAMpK,EAAE,IACRqK,EAAMrK,EAAE,IACRsK,EAAMtK,EAAE,IAGV2F,IADAwB,EAAIF,EAAE,IACIsC,EACV3D,GAAMuB,EAAIqC,EACV3D,GAAMsB,EAAIsC,EACV3D,GAAMqB,EAAIuC,EACV3D,GAAMoB,EAAIwC,EACV3D,GAAMmB,EAAIyC,EACV3D,GAAMkB,EAAI0C,EACV3D,GAAMiB,EAAI2C,EACV9B,GAAMb,EAAI4C,EACV9B,GAAMd,EAAI6C,EACV9B,GAAOf,EAAI8C,EACX9B,GAAOhB,EAAI+C,EACX9B,GAAOjB,EAAIgD,EACX9B,GAAOlB,EAAIiD,EACX9B,GAAOnB,EAAIkD,EACX9B,GAAOpB,EAAImD,EAEX1E,IADAuB,EAAIF,EAAE,IACIsC,EACV1D,GAAMsB,EAAIqC,EACV1D,GAAMqB,EAAIsC,EACV1D,GAAMoB,EAAIuC,EACV1D,GAAMmB,EAAIwC,EACV1D,GAAMkB,EAAIyC,EACV1D,GAAMiB,EAAI0C,EACV7B,GAAMb,EAAI2C,EACV7B,GAAMd,EAAI4C,EACV7B,GAAOf,EAAI6C,EACX7B,GAAOhB,EAAI8C,EACX7B,GAAOjB,EAAI+C,EACX7B,GAAOlB,EAAIgD,EACX7B,GAAOnB,EAAIiD,EACX7B,GAAOpB,EAAIkD,EACX7B,GAAOrB,EAAImD,EAEXzE,IADAsB,EAAIF,EAAE,IACIsC,EACVzD,GAAMqB,EAAIqC,EACVzD,GAAMoB,EAAIsC,EACVzD,GAAMmB,EAAIuC,EACVzD,GAAMkB,EAAIwC,EACVzD,GAAMiB,EAAIyC,EACV5B,GAAMb,EAAI0C,EACV5B,GAAMd,EAAI2C,EACV5B,GAAOf,EAAI4C,EACX5B,GAAOhB,EAAI6C,EACX5B,GAAOjB,EAAI8C,EACX5B,GAAOlB,EAAI+C,EACX5B,GAAOnB,EAAIgD,EACX5B,GAAOpB,EAAIiD,EACX5B,GAAOrB,EAAIkD,EACX5B,GAAOtB,EAAImD,EAEXxE,IADAqB,EAAIF,EAAE,IACIsC,EACVxD,GAAMoB,EAAIqC,EACVxD,GAAMmB,EAAIsC,EACVxD,GAAMkB,EAAIuC,EACVxD,GAAMiB,EAAIwC,EACV3B,GAAMb,EAAIyC,EACV3B,GAAMd,EAAI0C,EACV3B,GAAOf,EAAI2C,EACX3B,GAAOhB,EAAI4C,EACX3B,GAAOjB,EAAI6C,EACX3B,GAAOlB,EAAI8C,EACX3B,GAAOnB,EAAI+C,EACX3B,GAAOpB,EAAIgD,EACX3B,GAAOrB,EAAIiD,EACX3B,GAAOtB,EAAIkD,EACX3B,GAAOvB,EAAImD,EAEXvE,IADAoB,EAAIF,EAAE,IACIsC,EACVvD,GAAMmB,EAAIqC,EACVvD,GAAMkB,EAAIsC,EACVvD,GAAMiB,EAAIuC,EACV1B,GAAMb,EAAIwC,EACV1B,GAAMd,EAAIyC,EACV1B,GAAOf,EAAI0C,EACX1B,GAAOhB,EAAI2C,EACX1B,GAAOjB,EAAI4C,EACX1B,GAAOlB,EAAI6C,EACX1B,GAAOnB,EAAI8C,EACX1B,GAAOpB,EAAI+C,EACX1B,GAAOrB,EAAIgD,EACX1B,GAAOtB,EAAIiD,EACX1B,GAAOvB,EAAIkD,EACX1B,GAAOxB,EAAImD,EAEXtE,IADAmB,EAAIF,EAAE,IACIsC,EACVtD,GAAMkB,EAAIqC,EACVtD,GAAMiB,EAAIsC,EACVzB,GAAMb,EAAIuC,EACVzB,GAAMd,EAAIwC,EACVzB,GAAOf,EAAIyC,EACXzB,GAAOhB,EAAI0C,EACXzB,GAAOjB,EAAI2C,EACXzB,GAAOlB,EAAI4C,EACXzB,GAAOnB,EAAI6C,EACXzB,GAAOpB,EAAI8C,EACXzB,GAAOrB,EAAI+C,EACXzB,GAAOtB,EAAIgD,EACXzB,GAAOvB,EAAIiD,EACXzB,GAAOxB,EAAIkD,EACXzB,GAAOzB,EAAImD,EAEXrE,IADAkB,EAAIF,EAAE,IACIsC,EACVrD,GAAMiB,EAAIqC,EACVxB,GAAMb,EAAIsC,EACVxB,GAAMd,EAAIuC,EACVxB,GAAOf,EAAIwC,EACXxB,GAAOhB,EAAIyC,EACXxB,GAAOjB,EAAI0C,EACXxB,GAAOlB,EAAI2C,EACXxB,GAAOnB,EAAI4C,EACXxB,GAAOpB,EAAI6C,EACXxB,GAAOrB,EAAI8C,EACXxB,GAAOtB,EAAI+C,EACXxB,GAAOvB,EAAIgD,EACXxB,GAAOxB,EAAIiD,EACXxB,GAAOzB,EAAIkD,EACXxB,GAAO1B,EAAImD,EAEXpE,IADAiB,EAAIF,EAAE,IACIsC,EACVvB,GAAMb,EAAIqC,EACVvB,GAAMd,EAAIsC,EACVvB,GAAOf,EAAIuC,EACXvB,GAAOhB,EAAIwC,EACXvB,GAAOjB,EAAIyC,EACXvB,GAAOlB,EAAI0C,EACXvB,GAAOnB,EAAI2C,EACXvB,GAAOpB,EAAI4C,EACXvB,GAAOrB,EAAI6C,EACXvB,GAAOtB,EAAI8C,EACXvB,GAAOvB,EAAI+C,EACXvB,GAAOxB,EAAIgD,EACXvB,GAAOzB,EAAIiD,EACXvB,GAAO1B,EAAIkD,EACXvB,GAAO3B,EAAImD,EAEXtC,IADAb,EAAIF,EAAE,IACIsC,EACVtB,GAAMd,EAAIqC,EACVtB,GAAOf,EAAIsC,EACXtB,GAAOhB,EAAIuC,EACXtB,GAAOjB,EAAIwC,EACXtB,GAAOlB,EAAIyC,EACXtB,GAAOnB,EAAI0C,EACXtB,GAAOpB,EAAI2C,EACXtB,GAAOrB,EAAI4C,EACXtB,GAAOtB,EAAI6C,EACXtB,GAAOvB,EAAI8C,EACXtB,GAAOxB,EAAI+C,EACXtB,GAAOzB,EAAIgD,EACXtB,GAAO1B,EAAIiD,EACXtB,GAAO3B,EAAIkD,EACXtB,GAAO5B,EAAImD,EAEXrC,IADAd,EAAIF,EAAE,IACIsC,EACVrB,GAAOf,EAAIqC,EACXrB,GAAOhB,EAAIsC,EACXrB,GAAOjB,EAAIuC,EACXrB,GAAOlB,EAAIwC,EACXrB,GAAOnB,EAAIyC,EACXrB,GAAOpB,EAAI0C,EACXrB,GAAOrB,EAAI2C,EACXrB,GAAOtB,EAAI4C,EACXrB,GAAOvB,EAAI6C,EACXrB,GAAOxB,EAAI8C,EACXrB,GAAOzB,EAAI+C,EACXrB,GAAO1B,EAAIgD,EACXrB,GAAO3B,EAAIiD,EACXrB,GAAO5B,EAAIkD,EACXrB,GAAO7B,EAAImD,EAEXpC,IADAf,EAAIF,EAAE,KACKsC,EACXpB,GAAOhB,EAAIqC,EACXpB,GAAOjB,EAAIsC,EACXpB,GAAOlB,EAAIuC,EACXpB,GAAOnB,EAAIwC,EACXpB,GAAOpB,EAAIyC,EACXpB,GAAOrB,EAAI0C,EACXpB,GAAOtB,EAAI2C,EACXpB,GAAOvB,EAAI4C,EACXpB,GAAOxB,EAAI6C,EACXpB,GAAOzB,EAAI8C,EACXpB,GAAO1B,EAAI+C,EACXpB,GAAO3B,EAAIgD,EACXpB,GAAO5B,EAAIiD,EACXpB,GAAO7B,EAAIkD,EACXpB,GAAO9B,EAAImD,EAEXnC,IADAhB,EAAIF,EAAE,KACKsC,EACXnB,GAAOjB,EAAIqC,EACXnB,GAAOlB,EAAIsC,EACXnB,GAAOnB,EAAIuC,EACXnB,GAAOpB,EAAIwC,EACXnB,GAAOrB,EAAIyC,EACXnB,GAAOtB,EAAI0C,EACXnB,GAAOvB,EAAI2C,EACXnB,GAAOxB,EAAI4C,EACXnB,GAAOzB,EAAI6C,EACXnB,GAAO1B,EAAI8C,EACXnB,GAAO3B,EAAI+C,EACXnB,GAAO5B,EAAIgD,EACXnB,GAAO7B,EAAIiD,EACXnB,GAAO9B,EAAIkD,EACXnB,GAAO/B,EAAImD,EAEXlC,IADAjB,EAAIF,EAAE,KACKsC,EACXlB,GAAOlB,EAAIqC,EACXlB,GAAOnB,EAAIsC,EACXlB,GAAOpB,EAAIuC,EACXlB,GAAOrB,EAAIwC,EACXlB,GAAOtB,EAAIyC,EACXlB,GAAOvB,EAAI0C,EACXlB,GAAOxB,EAAI2C,EACXlB,GAAOzB,EAAI4C,EACXlB,GAAO1B,EAAI6C,EACXlB,GAAO3B,EAAI8C,EACXlB,GAAO5B,EAAI+C,EACXlB,GAAO7B,EAAIgD,EACXlB,GAAO9B,EAAIiD,EACXlB,GAAO/B,EAAIkD,EACXlB,GAAOhC,EAAImD,EAEXjC,IADAlB,EAAIF,EAAE,KACKsC,EACXjB,GAAOnB,EAAIqC,EACXjB,GAAOpB,EAAIsC,EACXjB,GAAOrB,EAAIuC,EACXjB,GAAOtB,EAAIwC,EACXjB,GAAOvB,EAAIyC,EACXjB,GAAOxB,EAAI0C,EACXjB,GAAOzB,EAAI2C,EACXjB,GAAO1B,EAAI4C,EACXjB,GAAO3B,EAAI6C,EACXjB,GAAO5B,EAAI8C,EACXjB,GAAO7B,EAAI+C,EACXjB,GAAO9B,EAAIgD,EACXjB,GAAO/B,EAAIiD,EACXjB,GAAOhC,EAAIkD,EACXjB,GAAOjC,EAAImD,EAEXhC,IADAnB,EAAIF,EAAE,KACKsC,EACXhB,GAAOpB,EAAIqC,EACXhB,GAAOrB,EAAIsC,EACXhB,GAAOtB,EAAIuC,EACXhB,GAAOvB,EAAIwC,EACXhB,GAAOxB,EAAIyC,EACXhB,GAAOzB,EAAI0C,EACXhB,GAAO1B,EAAI2C,EACXhB,GAAO3B,EAAI4C,EACXhB,GAAO5B,EAAI6C,EACXhB,GAAO7B,EAAI8C,EACXhB,GAAO9B,EAAI+C,EACXhB,GAAO/B,EAAIgD,EACXhB,GAAOhC,EAAIiD,EACXhB,GAAOjC,EAAIkD,EACXhB,GAAOlC,EAAImD,EAEX/B,IADApB,EAAIF,EAAE,KACKsC,EAkBX3D,GAAO,IAhBP6C,GAAOtB,EAAIsC,GAiBX5D,GAAO,IAhBP6C,GAAOvB,EAAIuC,GAiBX5D,GAAO,IAhBP6C,GAAOxB,EAAIwC,GAiBX5D,GAAO,IAhBP6C,GAAOzB,EAAIyC,GAiBX5D,GAAO,IAhBP6C,GAAO1B,EAAI0C,GAiBX5D,GAAO,IAhBP6C,GAAO3B,EAAI2C,GAiBX5D,GAAO,IAhBP6C,GAAO5B,EAAI4C,GAiBX/B,GAAO,IAhBPgB,GAAO7B,EAAI6C,GAiBX/B,GAAO,IAhBPgB,GAAO9B,EAAI8C,GAiBX/B,GAAO,IAhBPgB,GAAO/B,EAAI+C,GAiBX/B,GAAO,IAhBPgB,GAAOhC,EAAIgD,GAiBX/B,GAAO,IAhBPgB,GAAOjC,EAAIiD,GAiBX/B,GAAO,IAhBPgB,GAAOlC,EAAIkD,GAiBX/B,GAAO,IAhBPgB,GAAOnC,EAAImD,GAqBsC3E,GAAjDwB,GAnBAxB,GAAO,IAhBP6C,GAAOrB,EAAIqC,KAkCX1M,EAAI,GACU,OAAgD,OAAzCA,EAAIsK,KAAKC,MAAMF,EAAI,QACSvB,GAAjDuB,EAAKvB,EAAK9I,EAAI,OAAgD,OAAzCA,EAAIsK,KAAKC,MAAMF,EAAI,QACStB,GAAjDsB,EAAKtB,EAAK/I,EAAI,OAAgD,OAAzCA,EAAIsK,KAAKC,MAAMF,EAAI,QACSrB,GAAjDqB,EAAKrB,EAAKhJ,EAAI,OAAgD,OAAzCA,EAAIsK,KAAKC,MAAMF,EAAI,QACSpB,GAAjDoB,EAAKpB,EAAKjJ,EAAI,OAAgD,OAAzCA,EAAIsK,KAAKC,MAAMF,EAAI,QACSnB,GAAjDmB,EAAKnB,EAAKlJ,EAAI,OAAgD,OAAzCA,EAAIsK,KAAKC,MAAMF,EAAI,QACSlB,GAAjDkB,EAAKlB,EAAKnJ,EAAI,OAAgD,OAAzCA,EAAIsK,KAAKC,MAAMF,EAAI,QACSjB,GAAjDiB,EAAKjB,EAAKpJ,EAAI,OAAgD,OAAzCA,EAAIsK,KAAKC,MAAMF,EAAI,QACSa,GAAjDb,EAAKa,EAAKlL,EAAI,OAAgD,OAAzCA,EAAIsK,KAAKC,MAAMF,EAAI,QACSc,GAAjDd,EAAKc,EAAKnL,EAAI,OAAgD,OAAzCA,EAAIsK,KAAKC,MAAMF,EAAI,QACQe,GAAhDf,EAAIe,EAAMpL,EAAI,OAAgD,OAAzCA,EAAIsK,KAAKC,MAAMF,EAAI,QACQgB,GAAhDhB,EAAIgB,EAAMrL,EAAI,OAAgD,OAAzCA,EAAIsK,KAAKC,MAAMF,EAAI,QACQiB,GAAhDjB,EAAIiB,EAAMtL,EAAI,OAAgD,OAAzCA,EAAIsK,KAAKC,MAAMF,EAAI,QACQkB,GAAhDlB,EAAIkB,EAAMvL,EAAI,OAAgD,OAAzCA,EAAIsK,KAAKC,MAAMF,EAAI,QACQmB,GAAhDnB,EAAImB,EAAMxL,EAAI,OAAgD,OAAzCA,EAAIsK,KAAKC,MAAMF,EAAI,QACQoB,GAAhDpB,EAAIoB,EAAMzL,EAAI,OAAgD,OAAzCA,EAAIsK,KAAKC,MAAMF,EAAI,QAKSxB,GAAjDwB,GAJAxB,GAAM7I,EAAE,EAAI,IAAMA,EAAE,KAGpBA,EAAI,GACU,OAAgD,OAAzCA,EAAIsK,KAAKC,MAAMF,EAAI,QACSvB,GAAjDuB,EAAKvB,EAAK9I,EAAI,OAAgD,OAAzCA,EAAIsK,KAAKC,MAAMF,EAAI,QACStB,GAAjDsB,EAAKtB,EAAK/I,EAAI,OAAgD,OAAzCA,EAAIsK,KAAKC,MAAMF,EAAI,QACSrB,GAAjDqB,EAAKrB,EAAKhJ,EAAI,OAAgD,OAAzCA,EAAIsK,KAAKC,MAAMF,EAAI,QACSpB,GAAjDoB,EAAKpB,EAAKjJ,EAAI,OAAgD,OAAzCA,EAAIsK,KAAKC,MAAMF,EAAI,QACSnB,GAAjDmB,EAAKnB,EAAKlJ,EAAI,OAAgD,OAAzCA,EAAIsK,KAAKC,MAAMF,EAAI,QACSlB,GAAjDkB,EAAKlB,EAAKnJ,EAAI,OAAgD,OAAzCA,EAAIsK,KAAKC,MAAMF,EAAI,QACSjB,GAAjDiB,EAAKjB,EAAKpJ,EAAI,OAAgD,OAAzCA,EAAIsK,KAAKC,MAAMF,EAAI,QACSa,GAAjDb,EAAKa,EAAKlL,EAAI,OAAgD,OAAzCA,EAAIsK,KAAKC,MAAMF,EAAI,QACSc,GAAjDd,EAAKc,EAAKnL,EAAI,OAAgD,OAAzCA,EAAIsK,KAAKC,MAAMF,EAAI,QACQe,GAAhDf,EAAIe,EAAMpL,EAAI,OAAgD,OAAzCA,EAAIsK,KAAKC,MAAMF,EAAI,QACQgB,GAAhDhB,EAAIgB,EAAMrL,EAAI,OAAgD,OAAzCA,EAAIsK,KAAKC,MAAMF,EAAI,QACQiB,GAAhDjB,EAAIiB,EAAMtL,EAAI,OAAgD,OAAzCA,EAAIsK,KAAKC,MAAMF,EAAI,QACQkB,GAAhDlB,EAAIkB,EAAMvL,EAAI,OAAgD,OAAzCA,EAAIsK,KAAKC,MAAMF,EAAI,QACQmB,GAAhDnB,EAAImB,EAAMxL,EAAI,OAAgD,OAAzCA,EAAIsK,KAAKC,MAAMF,EAAI,QACQoB,GAAhDpB,EAAIoB,EAAMzL,EAAI,OAAgD,OAAzCA,EAAIsK,KAAKC,MAAMF,EAAI,QACxCxB,GAAM7I,EAAE,EAAI,IAAMA,EAAE,GAEpBI,EAAG,GAAKyI,EACRzI,EAAG,GAAK0I,EACR1I,EAAG,GAAK2I,EACR3I,EAAG,GAAK4I,EACR5I,EAAG,GAAK6I,EACR7I,EAAG,GAAK8I,EACR9I,EAAG,GAAK+I,EACR/I,EAAG,GAAKgJ,EACRhJ,EAAG,GAAK8K,EACR9K,EAAG,GAAK+K,EACR/K,EAAE,IAAMgL,EACRhL,EAAE,IAAMiL,EACRjL,EAAE,IAAMkL,EACRlL,EAAE,IAAMmL,EACRnL,EAAE,IAAMoL,EACRpL,EAAE,IAAMqL,EAGV,SAASgC,EAAErN,EAAG+J,GACZc,EAAE7K,EAAG+J,EAAGA,GAGV,SAASuD,EAAStN,EAAGT,GACnB,IACIwK,EADAnK,EAAIqE,IAER,IAAK8F,EAAI,EAAGA,EAAI,GAAIA,IAAKnK,EAAEmK,GAAKxK,EAAEwK,GAClC,IAAKA,EAAI,IAAKA,GAAK,EAAGA,IACpBsD,EAAEzN,EAAGA,GACI,IAANmK,GAAiB,IAANA,GAASc,EAAEjL,EAAGA,EAAGL,GAEjC,IAAKwK,EAAI,EAAGA,EAAI,GAAIA,IAAK/J,EAAE+J,GAAKnK,EAAEmK,GAGpC,SAASwD,EAAQvN,EAAGT,GAClB,IACIwK,EADAnK,EAAIqE,IAER,IAAK8F,EAAI,EAAGA,EAAI,GAAIA,IAAKnK,EAAEmK,GAAKxK,EAAEwK,GAClC,IAAKA,EAAI,IAAKA,GAAK,EAAGA,IAClBsD,EAAEzN,EAAGA,GACI,IAANmK,GAASc,EAAEjL,EAAGA,EAAGL,GAExB,IAAKwK,EAAI,EAAGA,EAAI,GAAIA,IAAK/J,EAAE+J,GAAKnK,EAAEmK,GAGpC,SAASyD,EAAkBnD,EAAGrJ,EAAGK,GAC/B,IAC8BhB,EAAGd,EAD7B4I,EAAI,IAAIhG,WAAW,IACnB6C,EAAI,IAAIb,aAAa,IACrB4F,EAAI9F,IAAMnB,EAAImB,IAAMrE,EAAIqE,IACxBpE,EAAIoE,IAAMwJ,EAAIxJ,IAAMyJ,EAAIzJ,IAC5B,IAAK1E,EAAI,EAAGA,EAAI,GAAIA,IAAK4I,EAAE5I,GAAKyB,EAAEzB,GAIlC,IAHA4I,EAAE,IAAW,IAANnH,EAAE,IAAS,GAClBmH,EAAE,IAAI,IACNuC,EAAY1F,EAAE3D,GACT9B,EAAI,EAAGA,EAAI,GAAIA,IAClBuD,EAAEvD,GAAGyF,EAAEzF,GACPM,EAAEN,GAAGwK,EAAExK,GAAGK,EAAEL,GAAG,EAGjB,IADAwK,EAAE,GAAGlK,EAAE,GAAG,EACLN,EAAE,IAAKA,GAAG,IAAKA,EAElB6K,EAASL,EAAEjH,EADXzC,EAAG8H,EAAE5I,IAAI,MAAQ,EAAFA,GAAM,GAErB6K,EAASxK,EAAEC,EAAEQ,GACbsK,EAAE8C,EAAE1D,EAAEnK,GACNgL,EAAEb,EAAEA,EAAEnK,GACN+K,EAAE/K,EAAEkD,EAAEjD,GACN+K,EAAE9H,EAAEA,EAAEjD,GACNwN,EAAExN,EAAE4N,GACJJ,EAAEK,EAAE3D,GACJc,EAAEd,EAAEnK,EAAEmK,GACNc,EAAEjL,EAAEkD,EAAE2K,GACN9C,EAAE8C,EAAE1D,EAAEnK,GACNgL,EAAEb,EAAEA,EAAEnK,GACNyN,EAAEvK,EAAEiH,GACJa,EAAEhL,EAAEC,EAAE6N,GACN7C,EAAEd,EAAEnK,EAAE6E,GACNkG,EAAEZ,EAAEA,EAAElK,GACNgL,EAAEjL,EAAEA,EAAEmK,GACNc,EAAEd,EAAElK,EAAE6N,GACN7C,EAAEhL,EAAEiD,EAAEkC,GACNqI,EAAEvK,EAAE2K,GACJrD,EAASL,EAAEjH,EAAEzC,GACb+J,EAASxK,EAAEC,EAAEQ,GAEf,IAAKd,EAAI,EAAGA,EAAI,GAAIA,IAClByF,EAAEzF,EAAE,IAAIwK,EAAExK,GACVyF,EAAEzF,EAAE,IAAIK,EAAEL,GACVyF,EAAEzF,EAAE,IAAIuD,EAAEvD,GACVyF,EAAEzF,EAAE,IAAIM,EAAEN,GAEZ,IAAIoO,EAAM3I,EAAE4I,SAAS,IACjBC,EAAM7I,EAAE4I,SAAS,IAIrB,OAHAN,EAASK,EAAIA,GACb9C,EAAEgD,EAAIA,EAAIF,GACVrD,EAAUD,EAAEwD,GACL,EAGT,SAASC,EAAuBzD,EAAGrJ,GACjC,OAAOwM,EAAkBnD,EAAGrJ,EAAGsD,GAGjC,SAASyJ,EAAmB3I,EAAGJ,GAE7B,OADAZ,EAAYY,EAAG,IACR8I,EAAuB1I,EAAGJ,GAGnC,SAASgJ,EAAoBtI,EAAGN,EAAGJ,GACjC,IAAI1D,EAAI,IAAIa,WAAW,IAEvB,OADAqL,EAAkBlM,EAAG0D,EAAGI,GACjByC,EAAqBnC,EAAGrB,EAAI/C,EAAGyG,GA33BxCS,EAASrH,UAAU8M,OAAS,SAAStO,EAAGuI,EAAMgG,GA2B5C,IA1BA,IACIzF,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIpJ,EAChCuO,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAAIC,EAFpCC,EAAQ1P,KAAKkK,IAAM,EAAI,KAIvByF,EAAK3P,KAAK8F,EAAE,GACZ8J,EAAK5P,KAAK8F,EAAE,GACZ+J,EAAK7P,KAAK8F,EAAE,GACZgK,EAAK9P,KAAK8F,EAAE,GACZiK,EAAK/P,KAAK8F,EAAE,GACZkK,EAAKhQ,KAAK8F,EAAE,GACZmK,EAAKjQ,KAAK8F,EAAE,GACZoK,EAAKlQ,KAAK8F,EAAE,GACZqK,EAAKnQ,KAAK8F,EAAE,GACZsK,EAAKpQ,KAAK8F,EAAE,GAEZuK,EAAKrQ,KAAKkB,EAAE,GACZoP,EAAKtQ,KAAKkB,EAAE,GACZqP,EAAKvQ,KAAKkB,EAAE,GACZsP,EAAKxQ,KAAKkB,EAAE,GACZuP,EAAKzQ,KAAKkB,EAAE,GACZwP,EAAK1Q,KAAKkB,EAAE,GACZyP,EAAK3Q,KAAKkB,EAAE,GACZ0P,EAAK5Q,KAAKkB,EAAE,GACZ2P,EAAK7Q,KAAKkB,EAAE,GACZ4P,EAAK9Q,KAAKkB,EAAE,GAET6N,GAAS,IAcdC,EAFAvO,EAAI,EAGJuO,IAdmDW,GAAmC,MAAtFrG,EAAkB,IAAb9I,EAAEuI,EAAM,IAA0B,IAAbvI,EAAEuI,EAAM,KAAc,IAcrCsH,EACXrB,IAdmDY,GAAmC,MAA3BtG,IAAO,IAAlEC,EAAkB,IAAb/I,EAAEuI,EAAM,IAA0B,IAAbvI,EAAEuI,EAAM,KAAc,IAAgC,KAcpE,EAAI+H,GAChB9B,IAdmDa,GAAmC,MAA3BtG,IAAO,IAAlEC,EAAkB,IAAbhJ,EAAEuI,EAAM,IAA0B,IAAbvI,EAAEuI,EAAM,KAAc,IAAgC,KAcpE,EAAI8H,GAChB7B,IAdmDc,GAAmC,MAA3BtG,IAAQ,GAAnEC,EAAkB,IAAbjJ,EAAEuI,EAAM,IAA0B,IAAbvI,EAAEuI,EAAM,KAAc,IAAgC,KAcpE,EAAI6H,GAEhBnQ,GADAuO,IAdmDe,GAAmC,MAA3BtG,IAAQ,GAAnEC,EAAkB,IAAblJ,EAAEuI,EAAM,IAA0B,IAAbvI,EAAEuI,EAAM,KAAc,IAA+B,MAcnE,EAAI4H,MACJ,GAAK3B,GAAM,KACvBA,IAfAgB,GAAQtG,IAAQ,EAAM,OAeV,EAAIgH,GAChB1B,IAfmDiB,GAAmC,MAA3BvG,IAAO,IAAlEC,EAAkB,IAAbnJ,EAAEuI,EAAK,KAA2B,IAAbvI,EAAEuI,EAAK,MAAe,IAAgC,KAepE,EAAI0H,GAChBzB,IAfmDkB,GAAmC,MAA3BvG,IAAO,IAAlEC,EAAkB,IAAbpJ,EAAEuI,EAAK,KAA2B,IAAbvI,EAAEuI,EAAK,MAAe,IAAgC,KAepE,EAAIyH,GAChBxB,IAfmDmB,GAAmC,MAA3BvG,IAAQ,GAAnEC,EAAkB,IAAbrJ,EAAEuI,EAAK,KAA2B,IAAbvI,EAAEuI,EAAK,MAAe,IAAgC,KAepE,EAAIwH,GAIhBtB,EAFAxO,IADAuO,IAfAoB,GAAQvG,IAAO,EAAM6F,IAeT,EAAIY,MACH,GAGbrB,GAAMU,EAAKW,EACXrB,GAAMW,EAAKS,EACXpB,GAAMY,GAAM,EAAIiB,GAChB7B,GAAMa,GAAM,EAAIe,GAEhBpQ,GADAwO,GAAMc,GAAM,EAAIa,MACJ,GAAK3B,GAAM,KACvBA,GAAMe,GAAM,EAAIW,GAChB1B,GAAMgB,GAAM,EAAIS,GAChBzB,GAAMiB,GAAM,EAAIO,GAChBxB,GAAMkB,GAAM,EAAIK,GAEhB/P,IADAwO,GAAMmB,GAAM,EAAIG,MACH,GAAKtB,GAAM,KAExBC,EAAKzO,EACLyO,GAAMS,EAAKY,EACXrB,GAAMU,EAAKU,EACXpB,GAAMW,EAAKQ,EACXnB,GAAMY,GAAM,EAAIgB,GAEhBrQ,GADAyO,GAAMa,GAAM,EAAIc,MACJ,GAAK3B,GAAM,KACvBA,GAAMc,GAAM,EAAIY,GAChB1B,GAAMe,GAAM,EAAIU,GAChBzB,GAAMgB,GAAM,EAAIQ,GAChBxB,GAAMiB,GAAM,EAAIM,GAIhBtB,EAFA1O,IADAyO,GAAMkB,GAAM,EAAII,MACH,GAGbrB,GAAMQ,EAAKa,EACXrB,GAAMS,EAAKW,EACXpB,GAAMU,EAAKS,EACXnB,GAAMW,EAAKO,EAEX5P,GADA0O,GAAMY,GAAM,EAAIe,MACJ,GAAK3B,GAAM,KACvBA,GAAMa,GAAM,EAAIa,GAChB1B,GAAMc,GAAM,EAAIW,GAChBzB,GAAMe,GAAM,EAAIS,GAChBxB,GAAMgB,GAAM,EAAIO,GAIhBtB,EAFA3O,IADA0O,GAAMiB,GAAM,EAAIK,MACH,GAGbrB,GAAMO,EAAKc,EACXrB,GAAMQ,EAAKY,EACXpB,GAAMS,EAAKU,EACXnB,GAAMU,EAAKQ,EAEX7P,GADA2O,GAAMW,EAAKM,KACC,GAAKjB,GAAM,KACvBA,GAAMY,GAAM,EAAIc,GAChB1B,GAAMa,GAAM,EAAIY,GAChBzB,GAAMc,GAAM,EAAIU,GAChBxB,GAAMe,GAAM,EAAIQ,GAIhBtB,EAFA5O,IADA2O,GAAMgB,GAAM,EAAIM,MACH,GAGbrB,GAAMM,EAAKe,EACXrB,GAAMO,EAAKa,EACXpB,GAAMQ,EAAKW,EACXnB,GAAMS,EAAKS,EAEX9P,GADA4O,GAAMU,EAAKO,KACC,GAAKjB,GAAM,KACvBA,GAAMW,EAAKK,EACXhB,GAAMY,GAAM,EAAIa,GAChBzB,GAAMa,GAAM,EAAIW,GAChBxB,GAAMc,GAAM,EAAIS,GAIhBtB,EAFA7O,IADA4O,GAAMe,GAAM,EAAIO,MACH,GAGbrB,GAAMK,EAAKgB,EACXrB,GAAMM,EAAKc,EACXpB,GAAMO,EAAKY,EACXnB,GAAMQ,EAAKU,EAEX/P,GADA6O,GAAMS,EAAKQ,KACC,GAAKjB,GAAM,KACvBA,GAAMU,EAAKM,EACXhB,GAAMW,EAAKI,EACXf,GAAMY,GAAM,EAAIY,GAChBxB,GAAMa,GAAM,EAAIU,GAIhBtB,EAFA9O,IADA6O,GAAMc,GAAM,EAAIQ,MACH,GAGbrB,GAAMI,EAAKiB,EACXrB,GAAMK,EAAKe,EACXpB,GAAMM,EAAKa,EACXnB,GAAMO,EAAKW,EAEXhQ,GADA8O,GAAMQ,EAAKS,KACC,GAAKjB,GAAM,KACvBA,GAAMS,EAAKO,EACXhB,GAAMU,EAAKK,EACXf,GAAMW,EAAKG,EACXd,GAAMY,GAAM,EAAIW,GAIhBtB,EAFA/O,IADA8O,GAAMa,GAAM,EAAIS,MACH,GAGbrB,GAAMG,EAAKkB,EACXrB,GAAMI,EAAKgB,EACXpB,GAAMK,EAAKc,EACXnB,GAAMM,EAAKY,EAEXjQ,GADA+O,GAAMO,EAAKU,KACC,GAAKjB,GAAM,KACvBA,GAAMQ,EAAKQ,EACXhB,GAAMS,EAAKM,EACXf,GAAMU,EAAKI,EACXd,GAAMW,EAAKE,EAIXZ,EAFAhP,IADA+O,GAAMY,GAAM,EAAIU,MACH,GAGbrB,GAAME,EAAKmB,EACXrB,GAAMG,EAAKiB,EACXpB,GAAMI,EAAKe,EACXnB,GAAMK,EAAKa,EAEXlQ,GADAgP,GAAMM,EAAKW,KACC,GAAKjB,GAAM,KACvBA,GAAMO,EAAKS,EACXhB,GAAMQ,EAAKO,EACXf,GAAMS,EAAKK,EACXd,GAAMU,EAAKG,EAUXX,EAJAX,EAAS,MADTvO,GADAA,IAFAA,IADAgP,GAAMW,EAAKC,KACE,KAED,GAAK5P,EAAM,IAhILuO,GAAM,MAiIT,GAMfY,EAHAX,GADAxO,KAAW,GAKXoP,EA5GkBX,GAAM,KA6GxBY,EA/FkBX,GAAM,KAgGxBY,EAlFkBX,GAAM,KAmFxBY,EArEkBX,GAAM,KAsExBY,EAxDkBX,GAAM,KAyDxBY,EA3CkBX,GAAM,KA4CxBY,EA9BkBX,GAAM,KA+BxBY,EAjBkBX,GAAM,KAmBxB1G,GAAQ,GACRgG,GAAS,GAEX/O,KAAK8F,EAAE,GAAK6J,EACZ3P,KAAK8F,EAAE,GAAK8J,EACZ5P,KAAK8F,EAAE,GAAK+J,EACZ7P,KAAK8F,EAAE,GAAKgK,EACZ9P,KAAK8F,EAAE,GAAKiK,EACZ/P,KAAK8F,EAAE,GAAKkK,EACZhQ,KAAK8F,EAAE,GAAKmK,EACZjQ,KAAK8F,EAAE,GAAKoK,EACZlQ,KAAK8F,EAAE,GAAKqK,EACZnQ,KAAK8F,EAAE,GAAKsK,GAGd/G,EAASrH,UAAUsI,OAAS,SAASyG,EAAKC,GACxC,IACIvQ,EAAGwQ,EAAM1C,EAAGnO,EADZ8Q,EAAI,IAAInH,YAAY,IAGxB,GAAI/J,KAAKiK,SAAU,CAGjB,IAFA7J,EAAIJ,KAAKiK,SACTjK,KAAK8J,OAAO1J,KAAO,EACZA,EAAI,GAAIA,IAAKJ,KAAK8J,OAAO1J,GAAK,EACrCJ,KAAKkK,IAAM,EACXlK,KAAK8O,OAAO9O,KAAK8J,OAAQ,EAAG,IAK9B,IAFArJ,EAAIT,KAAK8F,EAAE,KAAO,GAClB9F,KAAK8F,EAAE,IAAM,KACR1F,EAAI,EAAGA,EAAI,GAAIA,IAClBJ,KAAK8F,EAAE1F,IAAMK,EACbA,EAAIT,KAAK8F,EAAE1F,KAAO,GAClBJ,KAAK8F,EAAE1F,IAAM,KAaf,IAXAJ,KAAK8F,EAAE,IAAW,EAAJrF,EACdA,EAAIT,KAAK8F,EAAE,KAAO,GAClB9F,KAAK8F,EAAE,IAAM,KACb9F,KAAK8F,EAAE,IAAMrF,EACbA,EAAIT,KAAK8F,EAAE,KAAO,GAClB9F,KAAK8F,EAAE,IAAM,KACb9F,KAAK8F,EAAE,IAAMrF,EAEbyQ,EAAE,GAAKlR,KAAK8F,EAAE,GAAK,EACnBrF,EAAIyQ,EAAE,KAAO,GACbA,EAAE,IAAM,KACH9Q,EAAI,EAAGA,EAAI,GAAIA,IAClB8Q,EAAE9Q,GAAKJ,KAAK8F,EAAE1F,GAAKK,EACnBA,EAAIyQ,EAAE9Q,KAAO,GACb8Q,EAAE9Q,IAAM,KAKV,IAHA8Q,EAAE,IAAM,KAERD,GAAY,EAAJxQ,GAAS,EACZL,EAAI,EAAGA,EAAI,GAAIA,IAAK8Q,EAAE9Q,IAAM6Q,EAEjC,IADAA,GAAQA,EACH7Q,EAAI,EAAGA,EAAI,GAAIA,IAAKJ,KAAK8F,EAAE1F,GAAMJ,KAAK8F,EAAE1F,GAAK6Q,EAAQC,EAAE9Q,GAa5D,IAXAJ,KAAK8F,EAAE,GAAoE,OAA7D9F,KAAK8F,EAAE,GAAc9F,KAAK8F,EAAE,IAAM,IAChD9F,KAAK8F,EAAE,GAAoE,OAA7D9F,KAAK8F,EAAE,KAAQ,EAAM9F,KAAK8F,EAAE,IAAM,IAChD9F,KAAK8F,EAAE,GAAoE,OAA7D9F,KAAK8F,EAAE,KAAQ,EAAM9F,KAAK8F,EAAE,IAAO,GACjD9F,KAAK8F,EAAE,GAAoE,OAA7D9F,KAAK8F,EAAE,KAAQ,EAAM9F,KAAK8F,EAAE,IAAO,GACjD9F,KAAK8F,EAAE,GAAoE,OAA7D9F,KAAK8F,EAAE,KAAO,GAAO9F,KAAK8F,EAAE,IAAO,EAAM9F,KAAK8F,EAAE,IAAM,IACpE9F,KAAK8F,EAAE,GAAoE,OAA7D9F,KAAK8F,EAAE,KAAQ,EAAM9F,KAAK8F,EAAE,IAAM,IAChD9F,KAAK8F,EAAE,GAAoE,OAA7D9F,KAAK8F,EAAE,KAAQ,EAAM9F,KAAK8F,EAAE,IAAO,GACjD9F,KAAK8F,EAAE,GAAoE,OAA7D9F,KAAK8F,EAAE,KAAQ,EAAM9F,KAAK8F,EAAE,IAAO,GAEjDyI,EAAIvO,KAAK8F,EAAE,GAAK9F,KAAKgK,IAAI,GACzBhK,KAAK8F,EAAE,GAAS,MAAJyI,EACPnO,EAAI,EAAGA,EAAI,EAAGA,IACjBmO,GAAOvO,KAAK8F,EAAE1F,GAAKJ,KAAKgK,IAAI5J,GAAM,IAAMmO,IAAM,IAAO,EACrDvO,KAAK8F,EAAE1F,GAAS,MAAJmO,EAGdwC,EAAIC,EAAQ,GAAMhR,KAAK8F,EAAE,KAAO,EAAK,IACrCiL,EAAIC,EAAQ,GAAMhR,KAAK8F,EAAE,KAAO,EAAK,IACrCiL,EAAIC,EAAQ,GAAMhR,KAAK8F,EAAE,KAAO,EAAK,IACrCiL,EAAIC,EAAQ,GAAMhR,KAAK8F,EAAE,KAAO,EAAK,IACrCiL,EAAIC,EAAQ,GAAMhR,KAAK8F,EAAE,KAAO,EAAK,IACrCiL,EAAIC,EAAQ,GAAMhR,KAAK8F,EAAE,KAAO,EAAK,IACrCiL,EAAIC,EAAQ,GAAMhR,KAAK8F,EAAE,KAAO,EAAK,IACrCiL,EAAIC,EAAQ,GAAMhR,KAAK8F,EAAE,KAAO,EAAK,IACrCiL,EAAIC,EAAQ,GAAMhR,KAAK8F,EAAE,KAAO,EAAK,IACrCiL,EAAIC,EAAQ,GAAMhR,KAAK8F,EAAE,KAAO,EAAK,IACrCiL,EAAIC,EAAO,IAAOhR,KAAK8F,EAAE,KAAO,EAAK,IACrCiL,EAAIC,EAAO,IAAOhR,KAAK8F,EAAE,KAAO,EAAK,IACrCiL,EAAIC,EAAO,IAAOhR,KAAK8F,EAAE,KAAO,EAAK,IACrCiL,EAAIC,EAAO,IAAOhR,KAAK8F,EAAE,KAAO,EAAK,IACrCiL,EAAIC,EAAO,IAAOhR,KAAK8F,EAAE,KAAO,EAAK,IACrCiL,EAAIC,EAAO,IAAOhR,KAAK8F,EAAE,KAAO,EAAK,KAGvCuD,EAASrH,UAAUqI,OAAS,SAAS7J,EAAGuI,EAAMgG,GAC5C,IAAI3O,EAAG+Q,EAEP,GAAInR,KAAKiK,SAAU,CAIjB,KAHAkH,EAAQ,GAAKnR,KAAKiK,UACP8E,IACToC,EAAOpC,GACJ3O,EAAI,EAAGA,EAAI+Q,EAAM/Q,IACpBJ,KAAK8J,OAAO9J,KAAKiK,SAAW7J,GAAKI,EAAEuI,EAAK3I,GAI1C,GAHA2O,GAASoC,EACTpI,GAAQoI,EACRnR,KAAKiK,UAAYkH,EACbnR,KAAKiK,SAAW,GAClB,OACFjK,KAAK8O,OAAO9O,KAAK8J,OAAQ,EAAG,IAC5B9J,KAAKiK,SAAW,EAUlB,GAPI8E,GAAS,KACXoC,EAAOpC,EAASA,EAAQ,GACxB/O,KAAK8O,OAAOtO,EAAGuI,EAAMoI,GACrBpI,GAAQoI,EACRpC,GAASoC,GAGPpC,EAAO,CACT,IAAK3O,EAAI,EAAGA,EAAI2O,EAAO3O,IACrBJ,KAAK8J,OAAO9J,KAAKiK,SAAW7J,GAAKI,EAAEuI,EAAK3I,GAC1CJ,KAAKiK,UAAY8E,IAikBrB,IAAIqC,EAAqB3G,EACrB4G,EAA0B3G,EAc9B,IAAI4G,EAAI,CACN,WAAY,WAAY,WAAY,UACpC,WAAY,WAAY,WAAY,WACpC,UAAY,WAAY,WAAY,WACpC,WAAY,WAAY,WAAY,WACpC,WAAY,WAAY,UAAY,WACpC,UAAY,WAAY,WAAY,WACpC,WAAY,WAAY,WAAY,UACpC,WAAY,UAAY,WAAY,WACpC,WAAY,WAAY,WAAY,UACpC,UAAY,WAAY,UAAY,WACpC,UAAY,WAAY,WAAY,WACpC,WAAY,WAAY,WAAY,WACpC,WAAY,WAAY,WAAY,UACpC,WAAY,WAAY,WAAY,WACpC,WAAY,WAAY,WAAY,WACpC,UAAY,WAAY,UAAY,UACpC,UAAY,WAAY,UAAY,WACpC,WAAY,WAAY,WAAY,WACpC,WAAY,WAAY,WAAY,WACpC,WAAY,WAAY,WAAY,UACpC,WAAY,WAAY,WAAY,WACpC,WAAY,WAAY,WAAY,UACpC,WAAY,WAAY,WAAY,WACpC,WAAY,WAAY,UAAY,UACpC,UAAY,WAAY,UAAY,WACpC,UAAY,WAAY,UAAY,WACpC,UAAY,WAAY,WAAY,WACpC,WAAY,WAAY,WAAY,WACpC,WAAY,WAAY,WAAY,WACpC,WAAY,WAAY,WAAY,UACpC,WAAY,UAAY,WAAY,WACpC,WAAY,WAAY,WAAY,WACpC,WAAY,WAAY,WAAY,UACpC,WAAY,WAAY,WAAY,WACpC,UAAY,WAAY,UAAY,WACpC,UAAY,WAAY,UAAY,UACpC,UAAY,UAAY,UAAY,WACpC,WAAY,UAAY,WAAY,WACpC,WAAY,WAAY,WAAY,WACpC,WAAY,UAAY,WAAY,YAGtC,SAASC,EAAqBC,EAAIC,EAAIjR,EAAGqB,GAyBvC,IAxBA,IACI6P,EAAKC,EAAKC,EAAKC,EAAKC,EAAKC,EAAKC,EAAKC,EACnCC,EAAKC,EAAKC,EAAKC,EAAKC,EAAKC,EAAKC,EAAKC,EACnCC,EAAIC,EAAIvS,EAAGgL,EAAGtF,EAAGzF,EAAGuK,EAAGjH,EAAGlD,EAAGC,EAH7BkS,EAAK,IAAIC,WAAW,IAAKC,EAAK,IAAID,WAAW,IAK7CE,EAAMvB,EAAG,GACTwB,EAAMxB,EAAG,GACTyB,EAAMzB,EAAG,GACT0B,EAAM1B,EAAG,GACT2B,EAAM3B,EAAG,GACT4B,EAAM5B,EAAG,GACT6B,EAAM7B,EAAG,GACT8B,EAAM9B,EAAG,GAET+B,EAAM9B,EAAG,GACT+B,EAAM/B,EAAG,GACTgC,EAAMhC,EAAG,GACTiC,EAAMjC,EAAG,GACTkC,EAAMlC,EAAG,GACTmC,EAAMnC,EAAG,GACToC,EAAMpC,EAAG,GACTqC,EAAMrC,EAAG,GAETpN,EAAM,EACHxC,GAAK,KAAK,CACf,IAAKzB,EAAI,EAAGA,EAAI,GAAIA,IAClBgL,EAAI,EAAIhL,EAAIiE,EACZuO,EAAGxS,GAAMI,EAAE4K,EAAE,IAAM,GAAO5K,EAAE4K,EAAE,IAAM,GAAO5K,EAAE4K,EAAE,IAAM,EAAK5K,EAAE4K,EAAE,GAC9D0H,EAAG1S,GAAMI,EAAE4K,EAAE,IAAM,GAAO5K,EAAE4K,EAAE,IAAM,GAAO5K,EAAE4K,EAAE,IAAM,EAAK5K,EAAE4K,EAAE,GAEhE,IAAKhL,EAAI,EAAGA,EAAI,GAAIA,IA+HlB,GA9HAsR,EAAMqB,EACNpB,EAAMqB,EACNpB,EAAMqB,EACNpB,EAAMqB,EACNpB,EAAMqB,EACNpB,EAAMqB,EACNpB,EAAMqB,EACAC,EAENpB,EAAMqB,EACNpB,EAAMqB,EACNpB,EAAMqB,EACNpB,EAAMqB,EACNpB,EAAMqB,EACNpB,EAAMqB,EACNpB,EAAMqB,EACAC,EAMNlJ,EAAQ,OAFRvK,EAAIyT,GAEYnQ,EAAItD,IAAM,GAC1BI,EAAQ,OAJRqF,EAAIwN,GAIY5S,EAAIoF,IAAM,GAM1B8E,GAAS,OAFTvK,GAAMsT,IAAQ,GAAOR,GAAO,KAAcQ,IAAQ,GAAOR,GAAO,KAAcA,IAAQ,EAAYQ,GAAO,KAExFhQ,GAAKtD,IAAM,GAC5BI,GAAS,OAJTqF,GAAMqN,IAAQ,GAAOQ,GAAO,KAAcR,IAAQ,GAAOQ,GAAO,KAAcA,IAAQ,EAAYR,GAAO,KAIxFzS,GAAKoF,IAAM,GAM5B8E,GAAS,OAFTvK,EAAKsT,EAAMC,GAASD,EAAME,GAETlQ,GAAKtD,IAAM,GAC5BI,GAAS,OAJTqF,EAAKqN,EAAMC,GAASD,EAAME,GAIT3S,GAAKoF,IAAM,GAM5B8E,GAAS,OAFTvK,EAAIiR,EAAI,EAAFlR,EAAI,IAEOuD,GAAKtD,IAAM,GAC5BI,GAAS,OAJTqF,EAAIwL,EAAI,EAAFlR,IAIWM,GAAKoF,IAAM,GAG5BA,EAAI8M,EAAGxS,EAAE,IAGQuD,IAFjBtD,EAAIyS,EAAG1S,EAAE,OAEmB,GAC5BK,GAAS,MAAJqF,EAAYpF,GAAKoF,IAAM,GAG5BrF,IADAkD,IAHAiH,GAAS,MAAJvK,KAGM,MACA,GAUXuK,EAAQ,OAFRvK,EAJAsS,EAAS,MAAJ/H,EAAajH,GAAK,IAMPA,EAAItD,IAAM,GAC1BI,EAAQ,OAJRqF,EAJA4M,EAAS,MAAJjS,GAFLC,GAAKD,IAAM,KAEY,IAQPC,EAAIoF,IAAM,GAM1B8E,GAAS,OAFTvK,GAAMkT,IAAQ,GAAOR,GAAO,IAAcA,IAAQ,EAAYQ,GAAO,KAAmBR,IAAQ,EAAYQ,GAAO,KAElG5P,GAAKtD,IAAM,GAC5BI,GAAS,OAJTqF,GAAMiN,IAAQ,GAAOQ,GAAO,IAAcA,IAAQ,EAAYR,GAAO,KAAmBQ,IAAQ,EAAYR,GAAO,KAIlGrS,GAAKoF,IAAM,GAMXnC,IAFjBtD,EAAKkT,EAAMC,EAAQD,EAAME,EAAQD,EAAMC,KAEX,GAC5BhT,GAAS,OAJTqF,EAAKiN,EAAMC,EAAQD,EAAME,EAAQD,EAAMC,GAItBvS,GAAKoF,IAAM,GAM5BmM,EAAW,OAHXxR,IADAkD,IAHAiH,GAAS,MAAJvK,KAGM,MACA,KACXK,GAAKD,IAAM,KAEgB,GAC3BgS,EAAW,MAAJ7H,EAAejH,GAAK,GAM3BiH,EAAQ,OAFRvK,EAAIgS,GAEY1O,EAAItD,IAAM,GAC1BI,EAAQ,OAJRqF,EAAI+L,GAIYnR,EAAIoF,IAAM,GAKTnC,IAFjBtD,EAAIsS,KAEwB,GAC5BlS,GAAS,OAJTqF,EAAI4M,GAIahS,GAAKoF,IAAM,GAS5BkN,EAAMtB,EACNuB,EAAMtB,EACNuB,EAAMtB,EACNuB,EANAtB,EAAW,OAHXpR,IADAkD,IAHAiH,GAAS,MAAJvK,KAGM,MACA,KACXK,GAAKD,IAAM,KAEgB,GAO3B2S,EAAMtB,EACNuB,EAAMtB,EACNuB,EAAMtB,EACNe,EAAMd,EAENuB,EAAMtB,EACNuB,EAAMtB,EACNuB,EAAMtB,EACNuB,EAdAtB,EAAW,MAAJzH,EAAejH,GAAK,GAe3BiQ,EAAMtB,EACNuB,EAAMtB,EACNuB,EAAMtB,EACNe,EAAMd,EAEFrS,EAAE,IAAO,GACX,IAAKgL,EAAI,EAAGA,EAAI,GAAIA,IAElBtF,EAAI8M,EAAGxH,GAGPR,EAAQ,OAFRvK,EAAIyS,EAAG1H,IAESzH,EAAItD,IAAM,GAC1BI,EAAQ,MAAJqF,EAAYpF,EAAIoF,IAAM,GAE1BA,EAAI8M,GAAIxH,EAAE,GAAG,IAGbR,GAAS,OAFTvK,EAAIyS,GAAI1H,EAAE,GAAG,KAEIzH,GAAKtD,IAAM,GAC5BI,GAAS,MAAJqF,EAAYpF,GAAKoF,IAAM,GAG5B4M,EAAKE,GAAIxH,EAAE,GAAG,IAKdR,GAAS,OAFTvK,IAFAsS,EAAKG,GAAI1H,EAAE,GAAG,OAED,EAAMsH,GAAM,KAAaC,IAAO,EAAMD,GAAM,KAAaC,IAAO,EAAMD,GAAM,KAExE/O,GAAKtD,IAAM,GAC5BI,GAAS,OAJTqF,GAAM4M,IAAO,EAAMC,GAAM,KAAaD,IAAO,EAAMC,GAAM,IAAYD,IAAO,GAI3DhS,GAAKoF,IAAM,GAG5B4M,EAAKE,GAAIxH,EAAE,IAAI,IAKEzH,IAFjBtD,IAFAsS,EAAKG,GAAI1H,EAAE,IAAI,OAEF,GAAOsH,GAAM,KAAcA,IAAO,GAAYC,GAAM,IAAmBA,IAAO,EAAMD,GAAM,OAE3E,GAC5BjS,GAAS,OAJTqF,GAAM4M,IAAO,GAAOC,GAAM,KAAcA,IAAO,GAAYD,GAAM,GAAkBA,IAAO,GAIzEhS,GAAKoF,IAAM,GAI5BpF,IADAD,IADAkD,IAHAiH,GAAS,MAAJvK,KAGM,MACA,MACA,GAEXuS,EAAGxH,GAAU,MAAJ3K,EAAeC,GAAK,GAC7BoS,EAAG1H,GAAU,MAAJR,EAAejH,GAAK,GASnCiH,EAAQ,OAFRvK,EAAIkT,GAEY5P,EAAItD,IAAM,GAC1BI,EAAQ,OAJRqF,EAAIiN,GAIYrS,EAAIoF,IAAM,GAE1BA,EAAI0L,EAAG,GAGU7N,IAFjBtD,EAAIoR,EAAG,MAEqB,GAC5BhR,GAAS,MAAJqF,EAAYpF,GAAKoF,IAAM,GAI5BpF,IADAD,IADAkD,IAHAiH,GAAS,MAAJvK,KAGM,MACA,MACA,GAEXmR,EAAG,GAAKuB,EAAW,MAAJtS,EAAeC,GAAK,GACnC+Q,EAAG,GAAK8B,EAAW,MAAJ3I,EAAejH,GAAK,GAKnCiH,EAAQ,OAFRvK,EAAImT,GAEY7P,EAAItD,IAAM,GAC1BI,EAAQ,OAJRqF,EAAIkN,GAIYtS,EAAIoF,IAAM,GAE1BA,EAAI0L,EAAG,GAGU7N,IAFjBtD,EAAIoR,EAAG,MAEqB,GAC5BhR,GAAS,MAAJqF,EAAYpF,GAAKoF,IAAM,GAI5BpF,IADAD,IADAkD,IAHAiH,GAAS,MAAJvK,KAGM,MACA,MACA,GAEXmR,EAAG,GAAKwB,EAAW,MAAJvS,EAAeC,GAAK,GACnC+Q,EAAG,GAAK+B,EAAW,MAAJ5I,EAAejH,GAAK,GAKnCiH,EAAQ,OAFRvK,EAAIoT,GAEY9P,EAAItD,IAAM,GAC1BI,EAAQ,OAJRqF,EAAImN,GAIYvS,EAAIoF,IAAM,GAE1BA,EAAI0L,EAAG,GAGU7N,IAFjBtD,EAAIoR,EAAG,MAEqB,GAC5BhR,GAAS,MAAJqF,EAAYpF,GAAKoF,IAAM,GAI5BpF,IADAD,IADAkD,IAHAiH,GAAS,MAAJvK,KAGM,MACA,MACA,GAEXmR,EAAG,GAAKyB,EAAW,MAAJxS,EAAeC,GAAK,GACnC+Q,EAAG,GAAKgC,EAAW,MAAJ7I,EAAejH,GAAK,GAKnCiH,EAAQ,OAFRvK,EAAIqT,GAEY/P,EAAItD,IAAM,GAC1BI,EAAQ,OAJRqF,EAAIoN,GAIYxS,EAAIoF,IAAM,GAE1BA,EAAI0L,EAAG,GAGU7N,IAFjBtD,EAAIoR,EAAG,MAEqB,GAC5BhR,GAAS,MAAJqF,EAAYpF,GAAKoF,IAAM,GAI5BpF,IADAD,IADAkD,IAHAiH,GAAS,MAAJvK,KAGM,MACA,MACA,GAEXmR,EAAG,GAAK0B,EAAW,MAAJzS,EAAeC,GAAK,GACnC+Q,EAAG,GAAKiC,EAAW,MAAJ9I,EAAejH,GAAK,GAKnCiH,EAAQ,OAFRvK,EAAIsT,GAEYhQ,EAAItD,IAAM,GAC1BI,EAAQ,OAJRqF,EAAIqN,GAIYzS,EAAIoF,IAAM,GAE1BA,EAAI0L,EAAG,GAGU7N,IAFjBtD,EAAIoR,EAAG,MAEqB,GAC5BhR,GAAS,MAAJqF,EAAYpF,GAAKoF,IAAM,GAI5BpF,IADAD,IADAkD,IAHAiH,GAAS,MAAJvK,KAGM,MACA,MACA,GAEXmR,EAAG,GAAK2B,EAAW,MAAJ1S,EAAeC,GAAK,GACnC+Q,EAAG,GAAKkC,EAAW,MAAJ/I,EAAejH,GAAK,GAKnCiH,EAAQ,OAFRvK,EAAIuT,GAEYjQ,EAAItD,IAAM,GAC1BI,EAAQ,OAJRqF,EAAIsN,GAIY1S,EAAIoF,IAAM,GAE1BA,EAAI0L,EAAG,GAGU7N,IAFjBtD,EAAIoR,EAAG,MAEqB,GAC5BhR,GAAS,MAAJqF,EAAYpF,GAAKoF,IAAM,GAI5BpF,IADAD,IADAkD,IAHAiH,GAAS,MAAJvK,KAGM,MACA,MACA,GAEXmR,EAAG,GAAK4B,EAAW,MAAJ3S,EAAeC,GAAK,GACnC+Q,EAAG,GAAKmC,EAAW,MAAJhJ,EAAejH,GAAK,GAKnCiH,EAAQ,OAFRvK,EAAIwT,GAEYlQ,EAAItD,IAAM,GAC1BI,EAAQ,OAJRqF,EAAIuN,GAIY3S,EAAIoF,IAAM,GAE1BA,EAAI0L,EAAG,GAGU7N,IAFjBtD,EAAIoR,EAAG,MAEqB,GAC5BhR,GAAS,MAAJqF,EAAYpF,GAAKoF,IAAM,GAI5BpF,IADAD,IADAkD,IAHAiH,GAAS,MAAJvK,KAGM,MACA,MACA,GAEXmR,EAAG,GAAK6B,EAAW,MAAJ5S,EAAeC,GAAK,GACnC+Q,EAAG,GAAKoC,EAAW,MAAJjJ,EAAejH,GAAK,GAKnCiH,EAAQ,OAFRvK,EAAIyT,GAEYnQ,EAAItD,IAAM,GAC1BI,EAAQ,OAJRqF,EAAIwN,GAIY5S,EAAIoF,IAAM,GAE1BA,EAAI0L,EAAG,GAGU7N,IAFjBtD,EAAIoR,EAAG,MAEqB,GAC5BhR,GAAS,MAAJqF,EAAYpF,GAAKoF,IAAM,GAI5BpF,IADAD,IADAkD,IAHAiH,GAAS,MAAJvK,KAGM,MACA,MACA,GAEXmR,EAAG,GAAK8B,EAAW,MAAJ7S,EAAeC,GAAK,GACnC+Q,EAAG,GAAKqC,EAAW,MAAJlJ,EAAejH,GAAK,GAEnCU,GAAO,IACPxC,GAAK,IAGP,OAAOA,EAGT,SAASkS,EAAYtR,EAAKjC,EAAGqB,GAC3B,IAGIzB,EAHAoR,EAAK,IAAIqB,WAAW,GACpBpB,EAAK,IAAIoB,WAAW,GACpBhN,EAAI,IAAI7C,WAAW,KAChBW,EAAI9B,EAuBX,IArBA2P,EAAG,GAAK,WACRA,EAAG,GAAK,WACRA,EAAG,GAAK,WACRA,EAAG,GAAK,WACRA,EAAG,GAAK,WACRA,EAAG,GAAK,WACRA,EAAG,GAAK,UACRA,EAAG,GAAK,WAERC,EAAG,GAAK,WACRA,EAAG,GAAK,WACRA,EAAG,GAAK,WACRA,EAAG,GAAK,WACRA,EAAG,GAAK,WACRA,EAAG,GAAK,UACRA,EAAG,GAAK,WACRA,EAAG,GAAK,UAERF,EAAqBC,EAAIC,EAAIjR,EAAGqB,GAChCA,GAAK,IAEAzB,EAAI,EAAGA,EAAIyB,EAAGzB,IAAKyF,EAAEzF,GAAKI,EAAEmD,EAAE9B,EAAEzB,GAQrC,IAPAyF,EAAEhE,GAAK,IAGPgE,GADAhE,EAAI,IAAI,KAAKA,EAAE,IAAI,EAAE,IACjB,GAAK,EACT+D,EAAKC,EAAGhE,EAAE,EAAK8B,EAAI,UAAc,EAAGA,GAAK,GACzC4N,EAAqBC,EAAIC,EAAI5L,EAAGhE,GAE3BzB,EAAI,EAAGA,EAAI,EAAGA,IAAKwF,EAAKnD,EAAK,EAAErC,EAAGoR,EAAGpR,GAAIqR,EAAGrR,IAEjD,OAAO,EAGT,SAAS4T,EAAI9R,EAAGgJ,GACd,IAAIN,EAAI9F,IAAMnB,EAAImB,IAAMrE,EAAIqE,IACxBpE,EAAIoE,IAAMwJ,EAAIxJ,IAAMyJ,EAAIzJ,IACxBoM,EAAIpM,IAAMgB,EAAIhB,IAAMxD,EAAIwD,IAE5B2G,EAAEb,EAAG1I,EAAE,GAAIA,EAAE,IACbuJ,EAAEnK,EAAG4J,EAAE,GAAIA,EAAE,IACbQ,EAAEd,EAAGA,EAAGtJ,GACRkK,EAAE7H,EAAGzB,EAAE,GAAIA,EAAE,IACbsJ,EAAElK,EAAG4J,EAAE,GAAIA,EAAE,IACbQ,EAAE/H,EAAGA,EAAGrC,GACRoK,EAAEjL,EAAGyB,EAAE,GAAIgJ,EAAE,IACbQ,EAAEjL,EAAGA,EAAG+E,GACRkG,EAAEhL,EAAGwB,EAAE,GAAIgJ,EAAE,IACbM,EAAE9K,EAAGA,EAAGA,GACR+K,EAAE6C,EAAG3K,EAAGiH,GACRa,EAAE8C,EAAG7N,EAAGD,GACR+K,EAAE0F,EAAGxQ,EAAGD,GACR+K,EAAE1F,EAAGnC,EAAGiH,GAERc,EAAExJ,EAAE,GAAIoM,EAAGC,GACX7C,EAAExJ,EAAE,GAAI4D,EAAGoL,GACXxF,EAAExJ,EAAE,GAAIgP,EAAG3C,GACX7C,EAAExJ,EAAE,GAAIoM,EAAGxI,GAGb,SAASmO,EAAM/R,EAAGgJ,EAAGvH,GACnB,IAAIvD,EACJ,IAAKA,EAAI,EAAGA,EAAI,EAAGA,IACjB6K,EAAS/I,EAAE9B,GAAI8K,EAAE9K,GAAIuD,GAIzB,SAASuQ,GAAKhT,EAAGgB,GACf,IAAIiS,EAAKrP,IAAMsP,EAAKtP,IAAMuP,EAAKvP,IAC/BqJ,EAASkG,EAAInS,EAAE,IACfwJ,EAAEyI,EAAIjS,EAAE,GAAImS,GACZ3I,EAAE0I,EAAIlS,EAAE,GAAImS,GACZlJ,EAAUjK,EAAGkT,GACblT,EAAE,KAAOoK,EAAS6I,IAAO,EAG3B,SAASG,GAAWpS,EAAGgJ,EAAG/I,GACxB,IAAIwB,EAAGvD,EAKP,IAJAuK,EAASzI,EAAE,GAAIkD,GACfuF,EAASzI,EAAE,GAAImD,GACfsF,EAASzI,EAAE,GAAImD,GACfsF,EAASzI,EAAE,GAAIkD,GACVhF,EAAI,IAAKA,GAAK,IAAKA,EAEtB6T,EAAM/R,EAAGgJ,EADTvH,EAAKxB,EAAG/B,EAAE,EAAG,KAAS,EAAFA,GAAQ,GAE5B4T,EAAI9I,EAAGhJ,GACP8R,EAAI9R,EAAGA,GACP+R,EAAM/R,EAAGgJ,EAAGvH,GAIhB,SAAS4Q,GAAWrS,EAAGC,GACrB,IAAI+I,EAAI,CAACpG,IAAMA,IAAMA,IAAMA,KAC3B6F,EAASO,EAAE,GAAIzF,GACfkF,EAASO,EAAE,GAAIxF,GACfiF,EAASO,EAAE,GAAI7F,GACfqG,EAAER,EAAE,GAAIzF,EAAGC,GACX4O,GAAWpS,EAAGgJ,EAAG/I,GAGnB,SAASqS,GAAoBC,EAAIC,EAAIC,GACnC,IAEIvU,EAFAM,EAAI,IAAIsC,WAAW,IACnBd,EAAI,CAAC4C,IAAMA,IAAMA,IAAMA,KAY3B,IATK6P,GAAQ1P,EAAYyP,EAAI,IAC7BX,EAAYrT,EAAGgU,EAAI,IACnBhU,EAAE,IAAM,IACRA,EAAE,KAAO,IACTA,EAAE,KAAO,GAET6T,GAAWrS,EAAGxB,GACdwT,GAAKO,EAAIvS,GAEJ9B,EAAI,EAAGA,EAAI,GAAIA,IAAKsU,EAAGtU,EAAE,IAAMqU,EAAGrU,GACvC,OAAO,EAGT,IAAIwU,GAAI,IAAI5P,aAAa,CAAC,IAAM,IAAM,IAAM,GAAM,GAAM,GAAM,GAAM,GAAM,IAAM,IAAM,IAAM,IAAM,IAAM,IAAM,IAAM,GAAM,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,KAEvK,SAAS6P,GAAK3T,EAAG2E,GACf,IAAIiP,EAAO1U,EAAGgL,EAAG7E,EACjB,IAAKnG,EAAI,GAAIA,GAAK,KAAMA,EAAG,CAEzB,IADA0U,EAAQ,EACH1J,EAAIhL,EAAI,GAAImG,EAAInG,EAAI,GAAIgL,EAAI7E,IAAK6E,EACpCvF,EAAEuF,IAAM0J,EAAQ,GAAKjP,EAAEzF,GAAKwU,GAAExJ,GAAKhL,EAAI,KACvC0U,EAAQ/J,KAAKC,OAAOnF,EAAEuF,GAAK,KAAO,KAClCvF,EAAEuF,IAAc,IAAR0J,EAEVjP,EAAEuF,IAAM0J,EACRjP,EAAEzF,GAAK,EAGT,IADA0U,EAAQ,EACH1J,EAAI,EAAGA,EAAI,GAAIA,IAClBvF,EAAEuF,IAAM0J,GAASjP,EAAE,KAAO,GAAK+O,GAAExJ,GACjC0J,EAAQjP,EAAEuF,IAAM,EAChBvF,EAAEuF,IAAM,IAEV,IAAKA,EAAI,EAAGA,EAAI,GAAIA,IAAKvF,EAAEuF,IAAM0J,EAAQF,GAAExJ,GAC3C,IAAKhL,EAAI,EAAGA,EAAI,GAAIA,IAClByF,EAAEzF,EAAE,IAAMyF,EAAEzF,IAAM,EAClBc,EAAEd,GAAY,IAAPyF,EAAEzF,GAIb,SAAS2U,GAAO7T,GACd,IAA8Bd,EAA1ByF,EAAI,IAAIb,aAAa,IACzB,IAAK5E,EAAI,EAAGA,EAAI,GAAIA,IAAKyF,EAAEzF,GAAKc,EAAEd,GAClC,IAAKA,EAAI,EAAGA,EAAI,GAAIA,IAAKc,EAAEd,GAAK,EAChCyU,GAAK3T,EAAG2E,GAIV,SAASmP,GAAYC,EAAIzU,EAAGqB,EAAG6S,GAC7B,IACItU,EAAGgL,EADH1K,EAAI,IAAIsC,WAAW,IAAK8C,EAAI,IAAI9C,WAAW,IAAK9B,EAAI,IAAI8B,WAAW,IAC7D6C,EAAI,IAAIb,aAAa,IAC3B9C,EAAI,CAAC4C,IAAMA,IAAMA,IAAMA,KAE3BiP,EAAYrT,EAAGgU,EAAI,IACnBhU,EAAE,IAAM,IACRA,EAAE,KAAO,IACTA,EAAE,KAAO,GAET,IAAIwU,EAAQrT,EAAI,GAChB,IAAKzB,EAAI,EAAGA,EAAIyB,EAAGzB,IAAK6U,EAAG,GAAK7U,GAAKI,EAAEJ,GACvC,IAAKA,EAAI,EAAGA,EAAI,GAAIA,IAAK6U,EAAG,GAAK7U,GAAKM,EAAE,GAAKN,GAO7C,IALA2T,EAAY7S,EAAG+T,EAAGxG,SAAS,IAAK5M,EAAE,IAClCkT,GAAO7T,GACPqT,GAAWrS,EAAGhB,GACdgT,GAAKe,EAAI/S,GAEJ9B,EAAI,GAAIA,EAAI,GAAIA,IAAK6U,EAAG7U,GAAKsU,EAAGtU,GAIrC,IAHA2T,EAAYjO,EAAGmP,EAAIpT,EAAI,IACvBkT,GAAOjP,GAEF1F,EAAI,EAAGA,EAAI,GAAIA,IAAKyF,EAAEzF,GAAK,EAChC,IAAKA,EAAI,EAAGA,EAAI,GAAIA,IAAKyF,EAAEzF,GAAKc,EAAEd,GAClC,IAAKA,EAAI,EAAGA,EAAI,GAAIA,IAClB,IAAKgL,EAAI,EAAGA,EAAI,GAAIA,IAClBvF,EAAEzF,EAAEgL,IAAMtF,EAAE1F,GAAKM,EAAE0K,GAKvB,OADAyJ,GAAKI,EAAGxG,SAAS,IAAK5I,GACfqP,EAyCT,SAASC,GAAiB3U,EAAGyU,EAAIpT,EAAG4S,GAClC,IAAIrU,EACAkB,EAAI,IAAI0B,WAAW,IAAK8C,EAAI,IAAI9C,WAAW,IAC3Cd,EAAI,CAAC4C,IAAMA,IAAMA,IAAMA,KACvBoG,EAAI,CAACpG,IAAMA,IAAMA,IAAMA,KAE3B,GAAIjD,EAAI,GAAI,OAAQ,EAEpB,GA9CF,SAAmBX,EAAGgB,GACpB,IAAIZ,EAAIwD,IAAMsQ,EAAMtQ,IAAMuQ,EAAMvQ,IAC5BwQ,EAAMxQ,IAAMyQ,EAAOzQ,IAAM0Q,EAAO1Q,IAChC2Q,EAAO3Q,IA2BX,OAzBA6F,EAASzJ,EAAE,GAAImE,GACfkG,EAAYrK,EAAE,GAAIgB,GAClBgM,EAAEmH,EAAKnU,EAAE,IACTwK,EAAE4J,EAAKD,EAAK9P,GACZkG,EAAE4J,EAAKA,EAAKnU,EAAE,IACdsK,EAAE8J,EAAKpU,EAAE,GAAIoU,GAEbpH,EAAEqH,EAAMD,GACRpH,EAAEsH,EAAMD,GACR7J,EAAE+J,EAAMD,EAAMD,GACd7J,EAAEpK,EAAGmU,EAAMJ,GACX3J,EAAEpK,EAAGA,EAAGgU,GAERlH,EAAQ9M,EAAGA,GACXoK,EAAEpK,EAAGA,EAAG+T,GACR3J,EAAEpK,EAAGA,EAAGgU,GACR5J,EAAEpK,EAAGA,EAAGgU,GACR5J,EAAExK,EAAE,GAAII,EAAGgU,GAEXpH,EAAEkH,EAAKlU,EAAE,IACTwK,EAAE0J,EAAKA,EAAKE,GACRjK,EAAS+J,EAAKC,IAAM3J,EAAExK,EAAE,GAAIA,EAAE,GAAIyE,GAEtCuI,EAAEkH,EAAKlU,EAAE,IACTwK,EAAE0J,EAAKA,EAAKE,GACRjK,EAAS+J,EAAKC,IAAc,GAE5B/J,EAASpK,EAAE,MAASgB,EAAE,KAAK,GAAIuJ,EAAEvK,EAAE,GAAIkE,EAAKlE,EAAE,IAElDwK,EAAExK,EAAE,GAAIA,EAAE,GAAIA,EAAE,IACT,GAWHwU,CAAUxK,EAAGuJ,GAAK,OAAQ,EAE9B,IAAKrU,EAAI,EAAGA,EAAIyB,EAAGzB,IAAKI,EAAEJ,GAAK6U,EAAG7U,GAClC,IAAKA,EAAI,EAAGA,EAAI,GAAIA,IAAKI,EAAEJ,EAAE,IAAMqU,EAAGrU,GAUtC,GATA2T,EAAYjO,EAAGtF,EAAGqB,GAClBkT,GAAOjP,GACPwO,GAAWpS,EAAGgJ,EAAGpF,GAEjByO,GAAWrJ,EAAG+J,EAAGxG,SAAS,KAC1BuF,EAAI9R,EAAGgJ,GACPgJ,GAAK5S,EAAGY,GAERL,GAAK,GACDuE,EAAiB6O,EAAI,EAAG3T,EAAG,GAAI,CACjC,IAAKlB,EAAI,EAAGA,EAAIyB,EAAGzB,IAAKI,EAAEJ,GAAK,EAC/B,OAAQ,EAGV,IAAKA,EAAI,EAAGA,EAAIyB,EAAGzB,IAAKI,EAAEJ,GAAK6U,EAAG7U,EAAI,IACtC,OAAOyB,EAkFT,SAAS8T,GAAapP,EAAG1E,GACvB,GAhF8B,KAgF1B0E,EAAEjE,OAAsC,MAAM,IAAIoB,MAAM,gBAC5D,GAhFgC,KAgF5B7B,EAAES,OAAwC,MAAM,IAAIoB,MAAM,kBAQhE,SAASkS,KACP,IAAK,IAAIxV,EAAI,EAAGA,EAAIyV,UAAUvT,OAAQlC,IACpC,KAAMyV,UAAUzV,aAAc4C,YAC5B,MAAM,IAAI8S,UAAU,mCAI1B,SAASC,GAAQ3R,GACf,IAAK,IAAIhE,EAAI,EAAGA,EAAIgE,EAAI9B,OAAQlC,IAAKgE,EAAIhE,GAAK,EA/EhDyE,EAAKmR,SAAW,CACdtN,qBAAsBA,EACtBU,kBAAmBA,EACnBF,cAAeA,EACfL,0BAA2BA,EAC3BI,sBAAuBA,EACvBkB,mBAAoBA,EACpBI,0BAA2BA,EAC3BpE,iBAAkBA,EAClBC,iBAAkBA,EAClBqE,iBAAkBA,EAClBC,sBAAuBA,EACvB2D,kBAAmBA,EACnBM,uBAAwBA,EACxBE,oBAAqBA,EACrBuC,mBAAoBA,EACpB6E,WAxsBF,SAAoBxV,EAAGD,EAAGE,EAAGmB,EAAGoE,EAAGJ,GACjC,IAAIU,EAAI,IAAIvD,WAAW,IAEvB,OADA6L,EAAoBtI,EAAGN,EAAGJ,GACnBuL,EAAmB3Q,EAAGD,EAAGE,EAAGmB,EAAG0E,IAssBtC2P,gBAnsBF,SAAyB1V,EAAGC,EAAGC,EAAGmB,EAAGoE,EAAGJ,GACtC,IAAIU,EAAI,IAAIvD,WAAW,IAEvB,OADA6L,EAAoBtI,EAAGN,EAAGJ,GACnBwL,EAAwB7Q,EAAGC,EAAGC,EAAGmB,EAAG0E,IAisB3CqI,mBAAoBA,EACpBmF,YAAaA,EACbiB,YAAaA,GACbR,oBAAqBA,GACrBW,iBAAkBA,GAElBgB,0BA1C8B,GA2C9BC,4BA1CgC,GA2ChCC,2BA1C+B,GA2C/BC,8BA1CkC,GA2ClCC,wBA1C4B,GA2C5BC,8BA1CkC,GA2ClCC,0BA1C8B,GA2C9BC,0BA1C8B,GA2C9BC,yBA1C6B,GA2C7BC,sBAlDgC,GAmDhCC,qBAlD+B,GAmD/BC,wBAlDkC,GAmDlCC,kBA1CsB,GA2CtBC,2BA1C+B,GA2C/BC,2BA1C+B,GA2C/BC,sBA1C0B,GA2C1BC,kBA1CsB,GA4CtBrS,GAAIA,EACJS,EAAGA,EACHqP,EAAGA,GACHzJ,UAAWA,EACXI,YAAaA,EACbG,EAAGA,EACHF,EAAGA,EACH0C,EAAGA,EACHzC,EAAGA,EACH2C,QAASA,EACT4F,IAAKA,EACLrJ,SAAUA,EACVkK,KAAMA,GACNP,WAAYA,GACZC,WAAYA,IA0Bd1P,EAAKuS,YAAc,SAASvV,GAC1B,IAAI8B,EAAI,IAAIX,WAAWnB,GAEvB,OADAoD,EAAYtB,EAAG9B,GACR8B,GAGTkB,EAAKwS,UAAY,SAASC,EAAKC,EAAO5V,GACpCiU,GAAgB0B,EAAKC,EAAO5V,GAC5BgU,GAAahU,EAAK4V,GAGlB,IAFA,IAAI/W,EAAI,IAAIwC,WA3GmB,GA2GqBsU,EAAIhV,QACpD7B,EAAI,IAAIuC,WAAWxC,EAAE8B,QAChBlC,EAAI,EAAGA,EAAIkX,EAAIhV,OAAQlC,IAAKI,EAAEJ,EA7GR,IA6GwCkX,EAAIlX,GAE3E,OADAqK,EAAiBhK,EAAGD,EAAGA,EAAE8B,OAAQiV,EAAO5V,GACjClB,EAAEgO,SA9GyB,KAiHpC5J,EAAKwS,UAAUG,KAAO,SAASC,EAAKF,EAAO5V,GACzCiU,GAAgB6B,EAAKF,EAAO5V,GAC5BgU,GAAahU,EAAK4V,GAGlB,IAFA,IAAI9W,EAAI,IAAIuC,WApHsB,GAoHqByU,EAAInV,QACvD9B,EAAI,IAAIwC,WAAWvC,EAAE6B,QAChBlC,EAAI,EAAGA,EAAIqX,EAAInV,OAAQlC,IAAKK,EAAEL,EAtHL,IAsHwCqX,EAAIrX,GAC9E,OAAIK,EAAE6B,OAAS,IAC2C,IAAtDoI,EAAsBlK,EAAGC,EAAGA,EAAE6B,OAAQiV,EAAO5V,GADvB,KAEnBnB,EAAEiO,SA1HsB,KA6HjC5J,EAAKwS,UAAUK,UA/HiB,GAgIhC7S,EAAKwS,UAAUM,YA/HmB,GAgIlC9S,EAAKwS,UAAUO,eA9HqB,GAgIpC/S,EAAKgT,WAAa,SAAShW,EAAGK,GAE5B,GADA0T,GAAgB/T,EAAGK,GA/He,KAgI9BL,EAAES,OAA0C,MAAM,IAAIoB,MAAM,cAChE,GAlI4B,KAkIxBxB,EAAEI,OAAoC,MAAM,IAAIoB,MAAM,cAC1D,IAAIwH,EAAI,IAAIlI,WAnIgB,IAqI5B,OADAqL,EAAkBnD,EAAGrJ,EAAGK,GACjBgJ,GAGTrG,EAAKgT,WAAWC,KAAO,SAASjW,GAE9B,GADA+T,GAAgB/T,GAxIkB,KAyI9BA,EAAES,OAA0C,MAAM,IAAIoB,MAAM,cAChE,IAAIwH,EAAI,IAAIlI,WA3IgB,IA6I5B,OADA2L,EAAuBzD,EAAGrJ,GACnBqJ,GAGTrG,EAAKgT,WAAWE,aA/IoB,GAgJpClT,EAAKgT,WAAWG,mBAjJc,GAmJ9BnT,EAAK4S,IAAM,SAASH,EAAKC,EAAOU,EAAWC,GACzC,IAAI3R,EAAI1B,EAAK4S,IAAIU,OAAOF,EAAWC,GACnC,OAAOrT,EAAKwS,UAAUC,EAAKC,EAAOhR,IAGpC1B,EAAK4S,IAAIU,OAAS,SAASF,EAAWC,GACpCtC,GAAgBqC,EAAWC,GAzE7B,SAAyBzD,EAAIC,GAC3B,GA/E8B,KA+E1BD,EAAGnS,OAAsC,MAAM,IAAIoB,MAAM,uBAC7D,GA/E8B,KA+E1BgR,EAAGpS,OAAsC,MAAM,IAAIoB,MAAM,uBAwE7D0U,CAAgBH,EAAWC,GAC3B,IAAI3R,EAAI,IAAIvD,WAvJiB,IAyJ7B,OADA6L,EAAoBtI,EAAG0R,EAAWC,GAC3B3R,GAGT1B,EAAK4S,IAAIY,MAAQxT,EAAKwS,UAEtBxS,EAAK4S,IAAID,KAAO,SAASF,EAAKC,EAAOU,EAAWC,GAC9C,IAAI3R,EAAI1B,EAAK4S,IAAIU,OAAOF,EAAWC,GACnC,OAAOrT,EAAKwS,UAAUG,KAAKF,EAAKC,EAAOhR,IAGzC1B,EAAK4S,IAAID,KAAKa,MAAQxT,EAAKwS,UAAUG,KAErC3S,EAAK4S,IAAIa,QAAU,WACjB,IAAI7D,EAAK,IAAIzR,WAxKiB,IAyK1B0R,EAAK,IAAI1R,WAxKiB,IA0K9B,OADA4L,EAAmB6F,EAAIC,GAChB,CAACuD,UAAWxD,EAAIyD,UAAWxD,IAGpC7P,EAAK4S,IAAIa,QAAQC,cAAgB,SAASL,GAExC,GADAtC,GAAgBsC,GA9Kc,KA+K1BA,EAAU5V,OACZ,MAAM,IAAIoB,MAAM,uBAClB,IAAI+Q,EAAK,IAAIzR,WAlLiB,IAoL9B,OADA2L,EAAuB8F,EAAIyD,GACpB,CAACD,UAAWxD,EAAIyD,UAAW,IAAIlV,WAAWkV,KAGnDrT,EAAK4S,IAAIe,gBAvLuB,GAwLhC3T,EAAK4S,IAAIgB,gBAvLuB,GAwLhC5T,EAAK4S,IAAIiB,gBAvLsB,GAwL/B7T,EAAK4S,IAAIE,YA/LyB,GAgMlC9S,EAAK4S,IAAIG,eAAiB/S,EAAKwS,UAAUO,eAEzC/S,EAAK8T,KAAO,SAASrB,EAAKY,GAExB,GADAtC,GAAgB0B,EAAKY,GAtLU,KAuL3BA,EAAU5V,OACZ,MAAM,IAAIoB,MAAM,uBAClB,IAAIkV,EAAY,IAAI5V,WA3LE,GA2L2BsU,EAAIhV,QAErD,OADA0S,GAAY4D,EAAWtB,EAAKA,EAAIhV,OAAQ4V,GACjCU,GAGT/T,EAAK8T,KAAKnB,KAAO,SAASoB,EAAWX,GAEnC,GADArC,GAAgBgD,EAAWX,GAhMI,KAiM3BA,EAAU3V,OACZ,MAAM,IAAIoB,MAAM,uBAClB,IAAImV,EAAM,IAAI7V,WAAW4V,EAAUtW,QAC/BwW,EAAO3D,GAAiB0D,EAAKD,EAAWA,EAAUtW,OAAQ2V,GAC9D,GAAIa,EAAO,EAAG,OAAO,KAErB,IADA,IAAItY,EAAI,IAAIwC,WAAW8V,GACd1Y,EAAI,EAAGA,EAAII,EAAE8B,OAAQlC,IAAKI,EAAEJ,GAAKyY,EAAIzY,GAC9C,OAAOI,GAGTqE,EAAK8T,KAAKI,SAAW,SAASzB,EAAKY,GAGjC,IAFA,IAAIU,EAAY/T,EAAK8T,KAAKrB,EAAKY,GAC3Bc,EAAM,IAAIhW,WA9MQ,IA+Mb5C,EAAI,EAAGA,EAAI4Y,EAAI1W,OAAQlC,IAAK4Y,EAAI5Y,GAAKwY,EAAUxY,GACxD,OAAO4Y,GAGTnU,EAAK8T,KAAKI,SAASE,OAAS,SAAS3B,EAAK0B,EAAKf,GAE7C,GADArC,GAAgB0B,EAAK0B,EAAKf,GApNJ,KAqNlBe,EAAI1W,OACN,MAAM,IAAIoB,MAAM,sBAClB,GAtN+B,KAsN3BuU,EAAU3V,OACZ,MAAM,IAAIoB,MAAM,uBAClB,IAEItD,EAFA6U,EAAK,IAAIjS,WAzNS,GAyNsBsU,EAAIhV,QAC5C9B,EAAI,IAAIwC,WA1NU,GA0NqBsU,EAAIhV,QAE/C,IAAKlC,EAAI,EAAGA,EA5NU,GA4NaA,IAAK6U,EAAG7U,GAAK4Y,EAAI5Y,GACpD,IAAKA,EAAI,EAAGA,EAAIkX,EAAIhV,OAAQlC,IAAK6U,EAAG7U,EA7Nd,IA6NqCkX,EAAIlX,GAC/D,OAAQ+U,GAAiB3U,EAAGyU,EAAIA,EAAG3S,OAAQ2V,IAAc,GAG3DpT,EAAK8T,KAAKL,QAAU,WAClB,IAAI7D,EAAK,IAAIzR,WAjOkB,IAkO3B0R,EAAK,IAAI1R,WAjOkB,IAmO/B,OADAwR,GAAoBC,EAAIC,GACjB,CAACuD,UAAWxD,EAAIyD,UAAWxD,IAGpC7P,EAAK8T,KAAKL,QAAQC,cAAgB,SAASL,GAEzC,GADAtC,GAAgBsC,GAvOe,KAwO3BA,EAAU5V,OACZ,MAAM,IAAIoB,MAAM,uBAElB,IADA,IAAI+Q,EAAK,IAAIzR,WA3OkB,IA4OtB5C,EAAI,EAAGA,EAAIqU,EAAGnS,OAAQlC,IAAKqU,EAAGrU,GAAK8X,EAAU,GAAG9X,GACzD,MAAO,CAAC6X,UAAWxD,EAAIyD,UAAW,IAAIlV,WAAWkV,KAGnDrT,EAAK8T,KAAKL,QAAQY,SAAW,SAASC,GAEpC,GADAvD,GAAgBuD,GA/OU,KAgPtBA,EAAK7W,OACP,MAAM,IAAIoB,MAAM,iBAGlB,IAFA,IAAI+Q,EAAK,IAAIzR,WApPkB,IAqP3B0R,EAAK,IAAI1R,WApPkB,IAqPtB5C,EAAI,EAAGA,EAAI,GAAIA,IAAKsU,EAAGtU,GAAK+Y,EAAK/Y,GAE1C,OADAoU,GAAoBC,EAAIC,GAAI,GACrB,CAACuD,UAAWxD,EAAIyD,UAAWxD,IAGpC7P,EAAK8T,KAAKH,gBA3PuB,GA4PjC3T,EAAK8T,KAAKF,gBA3PuB,GA4PjC5T,EAAK8T,KAAKS,WA3PkB,GA4P5BvU,EAAK8T,KAAKU,gBA/Pc,GAiQxBxU,EAAKyU,KAAO,SAAShC,GACnB1B,GAAgB0B,GAChB,IAAIxR,EAAI,IAAI9C,WA/PU,IAiQtB,OADA+Q,EAAYjO,EAAGwR,EAAKA,EAAIhV,QACjBwD,GAGTjB,EAAKyU,KAAKC,WApQc,GAsQxB1U,EAAKoU,OAAS,SAASpT,EAAGI,GAGxB,OAFA2P,GAAgB/P,EAAGI,GAEF,IAAbJ,EAAEvD,QAA6B,IAAb2D,EAAE3D,SACpBuD,EAAEvD,SAAW2D,EAAE3D,QACkB,IAA7ByD,EAAGF,EAAG,EAAGI,EAAG,EAAGJ,EAAEvD,UAG3BuC,EAAK2U,QAAU,SAASC,GACtBxU,EAAcwU,GAGhB,WAGE,IAAIC,EAAyB,oBAATC,KAAwBA,KAAKD,QAAUC,KAAKC,SAAY,KAC5E,GAAIF,GAAUA,EAAOG,gBAAiB,CAGpChV,EAAK2U,SAAQ,SAAS3T,EAAGhE,GACvB,IAAIzB,EAAG0K,EAAI,IAAI9H,WAAWnB,GAC1B,IAAKzB,EAAI,EAAGA,EAAIyB,EAAGzB,GAHT,MAIRsZ,EAAOG,gBAAgB/O,EAAE2D,SAASrO,EAAGA,EAAI2K,KAAKxG,IAAI1C,EAAIzB,EAJ9C,SAMV,IAAKA,EAAI,EAAGA,EAAIyB,EAAGzB,IAAKyF,EAAEzF,GAAK0K,EAAE1K,GACjC2V,GAAQjL,WAIV4O,EAAS,EAAQ,KACHA,EAAOtC,aACnBvS,EAAK2U,SAAQ,SAAS3T,EAAGhE,GACvB,IAAIzB,EAAG0K,EAAI4O,EAAOtC,YAAYvV,GAC9B,IAAKzB,EAAI,EAAGA,EAAIyB,EAAGzB,IAAKyF,EAAEzF,GAAK0K,EAAE1K,GACjC2V,GAAQjL,MAtBhB,GA1zEA,CAs1EoCjL,EAAOD,QAAUC,EAAOD,QAAW+Z,KAAK9U,KAAO8U,KAAK9U,MAAQ,K,gBCt1EhGhF,EAAOD,QAAU,EAAQ,GAA4Bka,S,6FCUrD,IANA,IAAIhW,EAAeD,OAAOC,aAEtBiW,EACF,mEACEC,EAAS,GAEJ,EAAI,EAAG3Z,EAAI0Z,EAASzX,OAAQ,EAAIjC,EAAG,IAC1C2Z,EAAOD,EAASE,OAAO,IAAM,EAG/B,IAAIC,EAAU,SAAUzZ,GACtB,IAAI0Z,EAAK1Z,EAAEgD,WAAW,GACtB,OAAO0W,EAAK,IACR1Z,EACA0Z,EAAK,KACHrW,EAAa,IAAQqW,IAAO,GAAMrW,EAAa,IAAa,GAALqW,GACvDrW,EAAa,IAASqW,IAAO,GAAM,IACnCrW,EAAa,IAASqW,IAAO,EAAK,IAClCrW,EAAa,IAAa,GAALqW,IAGzBC,EAAO,SAAU5T,GACnB,OAAOA,EAAE6T,QAAQ,gBAAiBH,IAGhCI,EAAY,SAAUC,GACxB,IAAIC,EAAS,CAAC,EAAG,EAAG,GAAGD,EAAIjY,OAAS,GAChCmY,EACDF,EAAI9W,WAAW,IAAM,IACpB8W,EAAIjY,OAAS,EAAIiY,EAAI9W,WAAW,GAAK,IAAM,GAC5C8W,EAAIjY,OAAS,EAAIiY,EAAI9W,WAAW,GAAK,GAOxC,MANY,CACVsW,EAASE,OAAOQ,IAAQ,IACxBV,EAASE,OAAQQ,IAAQ,GAAM,IAC/BD,GAAU,EAAI,IAAMT,EAASE,OAAQQ,IAAQ,EAAK,IAClDD,GAAU,EAAI,IAAMT,EAASE,OAAa,GAANQ,IAEzB7V,KAAK,KAGhB8V,EACF,KAAOA,MACP,SAAU/W,GACR,OAAOA,EAAE0W,QAAQ,eAAgBC,ICTtB,MAnCf,MAIE,YACEK,EACAC,EACAC,EACAC,GAEA9a,KAAK4a,MAAQA,EACb5a,KAAK+a,MAAQJ,EAAI,KACX3a,KAAK+a,QACP/a,KAAK+a,MAAQD,EAAS9a,KAAK+a,SAE5BF,GAOL,YACE,OAAsB,OAAf7a,KAAK+a,MAId,gBACM/a,KAAK+a,QACP/a,KAAK4a,MAAM5a,KAAK+a,OAChB/a,KAAK+a,MAAQ,QC5BnB,SAAS,EAAaA,GACpB,KAAOC,aAAaD,GAEtB,SAAS,EAAcA,GACrB,KAAOE,cAAcF,GAQhB,MAAM,UAAoB,EAC/B,YAAYF,EAAcC,GACxBI,MAAMC,WAAY,EAAcN,GAAO,SAAUE,GAE/C,OADAD,IACO,SAUN,MAAM,UAAsB,EACjC,YAAYD,EAAcC,GACxBI,MAAME,YAAa,EAAeP,GAAO,SAAUE,GAEjD,OADAD,IACOC,MC/Bb,IA6Be,EA7BJ,CACTM,IAAG,IACGC,KAAKD,IACAC,KAAKD,OAEL,IAAIC,MAAOC,UAItBC,MAAMV,GACG,IAAI,EAAY,EAAGA,GAW5B,OAAOna,KAAiB8a,GACtB,IAAIC,EAAiBC,MAAM3Z,UAAU4Z,MAAMrb,KAAKsV,UAAW,GAC3D,OAAO,SAAU/T,GACf,OAAOA,EAAOnB,GAAMkb,MAAM/Z,EAAQ4Z,EAAeI,OAAOjG,eCXvD,SAASkG,EAAUC,KAAgBC,GACxC,IAAK,IAAI7b,EAAI,EAAGA,EAAI6b,EAAQ3Z,OAAQlC,IAAK,CACvC,IAAI8b,EAAaD,EAAQ7b,GACzB,IAAK,IAAI2B,KAAYma,EAEjBA,EAAWna,IACXma,EAAWna,GAAUoa,aACrBD,EAAWna,GAAUoa,cAAgBrb,OAErCkb,EAAOja,GAAYga,EAAOC,EAAOja,IAAa,GAAIma,EAAWna,IAE7Dia,EAAOja,GAAYma,EAAWna,GAIpC,OAAOia,EAGF,SAASI,IAEd,IADA,IAAI5b,EAAI,CAAC,UACAJ,EAAI,EAAGA,EAAIyV,UAAUvT,OAAQlC,IACR,iBAAjByV,UAAUzV,GACnBI,EAAEmE,KAAKkR,UAAUzV,IAEjBI,EAAEmE,KAAK0X,EAAkBxG,UAAUzV,KAGvC,OAAOI,EAAEoE,KAAK,OAGT,SAAS0X,EAAaC,EAAcC,GAEzC,IAAIC,EAAgBd,MAAM3Z,UAAU0a,QACpC,GAAc,OAAVH,EACF,OAAQ,EAEV,GAAIE,GAAiBF,EAAMG,UAAYD,EACrC,OAAOF,EAAMG,QAAQF,GAEvB,IAAK,IAAIpc,EAAI,EAAGC,EAAIkc,EAAMja,OAAQlC,EAAIC,EAAGD,IACvC,GAAImc,EAAMnc,KAAOoc,EACf,OAAOpc,EAGX,OAAQ,EAaH,SAASuc,EAAY7a,EAAayM,GACvC,IAAK,IAAI5M,KAAOG,EACVhB,OAAOkB,UAAUC,eAAe1B,KAAKuB,EAAQH,IAC/C4M,EAAEzM,EAAOH,GAAMA,EAAKG,GAUnB,SAAS8a,EAAK9a,GACnB,IAAI8a,EAAO,GAIX,OAHAD,EAAY7a,GAAQ,SAAU+a,EAAGlb,GAC/Bib,EAAKjY,KAAKhD,MAELib,EA0BF,SAASf,EAAMU,EAAchO,EAAauO,GAC/C,IAAK,IAAI1c,EAAI,EAAGA,EAAImc,EAAMja,OAAQlC,IAChCmO,EAAEhO,KAAKuc,GAAW,KAAQP,EAAMnc,GAAIA,EAAGmc,GAepC,SAASQ,EAAIR,EAAchO,GAEhC,IADA,IAAI3K,EAAS,GACJxD,EAAI,EAAGA,EAAImc,EAAMja,OAAQlC,IAChCwD,EAAOe,KAAK4J,EAAEgO,EAAMnc,GAAIA,EAAGmc,EAAO3Y,IAEpC,OAAOA,EAiCF,SAASoZ,EAAOT,EAAcU,GACnCA,EACEA,GACA,SAAU5b,GACR,QAASA,GAIb,IADA,IAAIuC,EAAS,GACJxD,EAAI,EAAGA,EAAImc,EAAMja,OAAQlC,IAC5B6c,EAAKV,EAAMnc,GAAIA,EAAGmc,EAAO3Y,IAC3BA,EAAOe,KAAK4X,EAAMnc,IAGtB,OAAOwD,EAcF,SAASsZ,EAAapb,EAAgBmb,GAC3C,IAAIrZ,EAAS,GAMb,OALA+Y,EAAY7a,GAAQ,SAAUT,EAAOM,IAC9Bsb,GAAQA,EAAK5b,EAAOM,EAAKG,EAAQ8B,IAAYuZ,QAAQ9b,MACxDuC,EAAOjC,GAAON,MAGXuC,EA0BF,SAASwZ,EAAIb,EAAcU,GAChC,IAAK,IAAI7c,EAAI,EAAGA,EAAImc,EAAMja,OAAQlC,IAChC,GAAI6c,EAAKV,EAAMnc,GAAIA,EAAGmc,GACpB,OAAO,EAGX,OAAO,EAsBF,SAASc,EAAmB7a,GACjC,OA5GqC+L,EA4Gd,SAAUlN,GAI/B,MAHqB,iBAAVA,IACTA,EAAQgb,EAAkBhb,IAErBic,oBJ1QoBnb,EI0QYd,EAAMkc,WJzQxC7C,EAAKN,EAAKjY,MADJ,IAAgBA,GI2JzByB,EAAS,GACb+Y,EA0GiBna,GA1GG,SAAUnB,EAAOM,GACnCiC,EAAOjC,GAAO4M,EAAElN,MAEXuC,EALF,IAAgC2K,EACjC3K,EAmHC,SAAS4Z,EAAiBhb,GAC/B,IAxDsBV,EAClB8B,EAuDA6Z,EAASP,EAAa1a,GAAM,SAAUnB,GACxC,YAAiBqc,IAAVrc,KAQT,OALY0b,GA5DUjb,EA6DZub,EAAmBI,GA5DzB7Z,EAAS,GACb+Y,EAAY7a,GAAQ,SAAUT,EAAOM,GACnCiC,EAAOe,KAAK,CAAChD,EAAKN,OAEbuC,GAyDL,EAAK+Z,OAAO,OAAQ,MACpB/Y,KAAK,KAoEF,SAASyX,EAAkBuB,GAChC,IACE,OAAOC,KAAKzB,UAAUwB,GACtB,MAAOtP,GACP,OAAOuP,KAAKzB,WAzDV0B,EAAU,GACZC,EAAQ,GAEH,SAAUC,EAAM3c,EAAO4c,GAC5B,IAAI7d,EAAGO,EAAMud,EAEb,cAAe7c,GACb,IAAK,SACH,IAAKA,EACH,OAAO,KAET,IAAKjB,EAAI,EAAGA,EAAI0d,EAAQxb,OAAQlC,GAAK,EACnC,GAAI0d,EAAQ1d,KAAOiB,EACjB,MAAO,CAAE8c,KAAMJ,EAAM3d,IAOzB,GAHA0d,EAAQnZ,KAAKtD,GACb0c,EAAMpZ,KAAKsZ,GAEoC,mBAA3Cnd,OAAOkB,UAAUub,SAAS1B,MAAMxa,GAElC,IADA6c,EAAK,GACA9d,EAAI,EAAGA,EAAIiB,EAAMiB,OAAQlC,GAAK,EACjC8d,EAAG9d,GAAK4d,EAAM3c,EAAMjB,GAAI6d,EAAO,IAAM7d,EAAI,UAI3C,IAAKO,KADLud,EAAK,GACQ7c,EACPP,OAAOkB,UAAUC,eAAe1B,KAAKc,EAAOV,KAC9Cud,EAAGvd,GAAQqd,EACT3c,EAAMV,GACNsd,EAAO,IAAMJ,KAAKzB,UAAUzb,GAAQ,MAK5C,OAAOud,EACT,IAAK,SACL,IAAK,SACL,IAAK,UACH,OAAO7c,GArCN,CAsD+Buc,EAf3B,OA3CN,IACDE,EACFC,ECxQJ,IAmCe,EAnCe,CAC5BK,QAAS,QACTC,SAAU,EAEVC,OAAQ,GACRC,QAAS,IACTC,OAAQ,GAERC,SAAU,oBACVC,SAAU,GACVC,UAAW,IACXC,SAAU,UAEVC,WAAY,mBAEZC,aAAc,eACdC,cAAe,OACfC,gBAAiB,KACjBC,YAAa,IACbC,mBAAoB,IACpBC,mBAAoB,CAClBC,SAAU,oBACVC,UAAW,QAEbC,qBAAsB,CACpBF,SAAU,eACVC,UAAW,QAIbE,SAAU,uBACVC,UAAW,wBACXC,kBAAmB,IC3DrB,SAASC,EACPC,EACAlC,EACAQ,GAIA,OAFa0B,GAAclC,EAAOmC,OAAS,IAAM,IAEjC,OADLnC,EAAOmC,OAASnC,EAAOoC,QAAUpC,EAAOqC,YACpB7B,EAGjC,SAAS8B,EAAepe,EAAaqe,GASnC,MARW,QAAUre,GAEnB,aACA,EAAS0c,SADT,sBAIA,EAASD,SACR4B,EAAc,IAAMA,EAAc,KAIhC,IAAIC,EAAgB,CACzBC,WAAY,SAAUve,EAAa8b,GAEjC,OAAOiC,EAAc,KAAMjC,GADfA,EAAOmB,UAAY,IAAMmB,EAAepe,EAAK,kBAKlDwe,EAAkB,CAC3BD,WAAY,SAAUve,EAAa8b,GAEjC,OAAOiC,EAAc,OAAQjC,GADjBA,EAAOmB,UAAY,WAAamB,EAAepe,MC9BhD,MAAM,EAGnB,cACE3B,KAAKogB,WAAa,GAGpB,IAAIzf,GACF,OAAOX,KAAKogB,WAAWC,EAAO1f,IAGhC,IAAIA,EAAcma,EAAoBgC,GACpC,IAAIwD,EAAoBD,EAAO1f,GAC/BX,KAAKogB,WAAWE,GACdtgB,KAAKogB,WAAWE,IAAsB,GACxCtgB,KAAKogB,WAAWE,GAAmB3b,KAAK,CACtC8U,GAAIqB,EACJgC,QAASA,IAIb,OAAOnc,EAAema,EAAqBgC,GACzC,GAAKnc,GAASma,GAAagC,EAA3B,CAKA,IAAIyD,EAAQ5f,EAAO,CAAC0f,EAAO1f,IAAS,EAAiBX,KAAKogB,YAEtDtF,GAAYgC,EACd9c,KAAKwgB,eAAeD,EAAOzF,EAAUgC,GAErC9c,KAAKygB,mBAAmBF,QATxBvgB,KAAKogB,WAAa,GAad,eAAeG,EAAiBzF,EAAoBgC,GAC1D,EACEyD,GACA,SAAU5f,GACRX,KAAKogB,WAAWzf,GAAQ,EACtBX,KAAKogB,WAAWzf,IAAS,IACzB,SAAU+f,GACR,OACG5F,GAAYA,IAAa4F,EAAQjH,IACjCqD,GAAWA,IAAY4D,EAAQ5D,WAID,IAAjC9c,KAAKogB,WAAWzf,GAAM2B,eACjBtC,KAAKogB,WAAWzf,KAG3BX,MAII,mBAAmBugB,GACzB,EACEA,GACA,SAAU5f,UACDX,KAAKogB,WAAWzf,KAEzBX,OAKN,SAASqgB,EAAO1f,GACd,MAAO,IAAMA,EChEA,MAAM,EAKnB,YAAYggB,GACV3gB,KAAK4gB,UAAY,IAAI,EACrB5gB,KAAK6gB,iBAAmB,GACxB7gB,KAAK2gB,YAAcA,EAGrB,KAAKG,EAAmBhG,EAAoBgC,GAE1C,OADA9c,KAAK4gB,UAAU5M,IAAI8M,EAAWhG,EAAUgC,GACjC9c,KAGT,YAAY8a,GAEV,OADA9a,KAAK6gB,iBAAiBlc,KAAKmW,GACpB9a,KAGT,OAAO8gB,EAAoBhG,EAAqBgC,GAE9C,OADA9c,KAAK4gB,UAAUG,OAAOD,EAAWhG,EAAUgC,GACpC9c,KAGT,cAAc8a,GACZ,OAAKA,GAKL9a,KAAK6gB,iBAAmB,EACtB7gB,KAAK6gB,kBAAoB,GACxBpgB,GAAMA,IAAMqa,GAGR9a,OATLA,KAAK6gB,iBAAmB,GACjB7gB,MAWX,aAGE,OAFAA,KAAKghB,SACLhhB,KAAKihB,gBACEjhB,KAGT,KAAK8gB,EAAmBte,EAAY0e,GAClC,IAAK,IAAI9gB,EAAI,EAAGA,EAAIJ,KAAK6gB,iBAAiBve,OAAQlC,IAChDJ,KAAK6gB,iBAAiBzgB,GAAG0gB,EAAWte,GAGtC,IAAIoe,EAAY5gB,KAAK4gB,UAAU3f,IAAI6f,GAC/BrF,EAAO,GAYX,GAVIyF,EAGFzF,EAAK9W,KAAKnC,EAAM0e,GACP1e,GAGTiZ,EAAK9W,KAAKnC,GAGRoe,GAAaA,EAAUte,OAAS,EAClC,IAASlC,EAAI,EAAGA,EAAIwgB,EAAUte,OAAQlC,IACpCwgB,EAAUxgB,GAAGqZ,GAAGoC,MAAM+E,EAAUxgB,GAAG0c,SAAW,KAAQrB,QAE/Czb,KAAK2gB,aACd3gB,KAAK2gB,YAAYG,EAAWte,GAG9B,OAAOxC,MC7BI,UAjDf,oBAaU,KAAAmhB,UAAaC,IACf,KAAOC,SAAW,KAAOA,QAAQC,KACnC,KAAOD,QAAQC,IAAIF,IAdvB,SAAS3F,GACPzb,KAAKshB,IAAIthB,KAAKmhB,UAAW1F,GAG3B,QAAQA,GACNzb,KAAKshB,IAAIthB,KAAKuhB,cAAe9F,GAG/B,SAASA,GACPzb,KAAKshB,IAAIthB,KAAKwhB,eAAgB/F,GASxB,cAAc2F,GAChB,KAAOC,SAAW,KAAOA,QAAQI,KACnC,KAAOJ,QAAQI,KAAKL,GAEpBphB,KAAKmhB,UAAUC,GAIX,eAAeA,GACjB,KAAOC,SAAW,KAAOA,QAAQK,MACnC,KAAOL,QAAQK,MAAMN,GAErBphB,KAAKuhB,cAAcH,GAIf,IACNO,KACGlG,GAEH,IAAI2F,EAAUhF,EAAUP,MAAM7b,KAAM6V,WACpC,GAAI,GAAOyL,IACT,GAAOA,IAAIF,QACN,GAAI,GAAOQ,aAAc,CAClBD,EAAuB/f,KAAK5B,KACxCshB,CAAIF,MCTK,MAAM,UAA4B,EAc/C,YACES,EACAlhB,EACAmhB,EACAngB,EACAogB,GAEA7G,QACAlb,KAAKgiB,WAAa,GAAQC,+BAC1BjiB,KAAK6hB,MAAQA,EACb7hB,KAAKW,KAAOA,EACZX,KAAK8hB,SAAWA,EAChB9hB,KAAK2B,IAAMA,EACX3B,KAAK+hB,QAAUA,EAEf/hB,KAAKkiB,MAAQ,MACbliB,KAAKmiB,SAAWJ,EAAQI,SACxBniB,KAAKgf,gBAAkB+C,EAAQ/C,gBAC/Bhf,KAAKoiB,GAAKpiB,KAAKmiB,SAASE,mBAO1B,wBACE,OAAOlF,QAAQnd,KAAK6hB,MAAMS,uBAO5B,eACE,OAAOnF,QAAQnd,KAAK6hB,MAAMU,cAO5B,UACE,GAAIviB,KAAKwiB,QAAyB,gBAAfxiB,KAAKkiB,MACtB,OAAO,EAGT,IAAIO,EAAMziB,KAAK6hB,MAAMa,KAAKxC,WAAWlgB,KAAK2B,IAAK3B,KAAK+hB,SACpD,IACE/hB,KAAKwiB,OAASxiB,KAAK6hB,MAAMc,UAAUF,EAAKziB,KAAK+hB,SAC7C,MAAOzT,GAKP,OAJA,EAAKkN,MAAM,KACTxb,KAAK4iB,QAAQtU,GACbtO,KAAK6iB,YAAY,aAEZ,EAOT,OAJA7iB,KAAK8iB,gBAEL,EAAOC,MAAM,aAAc,CAAE1D,UAAWrf,KAAKW,KAAM8hB,QACnDziB,KAAK6iB,YAAY,eACV,EAOT,QACE,QAAI7iB,KAAKwiB,SACPxiB,KAAKwiB,OAAOQ,SACL,GAWX,KAAKxgB,GACH,MAAmB,SAAfxC,KAAKkiB,QAEP,EAAK1G,MAAM,KACLxb,KAAKwiB,QACPxiB,KAAKwiB,OAAOS,KAAKzgB,MAGd,GAOX,OACqB,SAAfxC,KAAKkiB,OAAoBliB,KAAKuiB,gBAChCviB,KAAKwiB,OAAOU,OAIR,SACFljB,KAAK6hB,MAAMsB,YACbnjB,KAAK6hB,MAAMsB,WACTnjB,KAAKwiB,OACLxiB,KAAK6hB,MAAMa,KAAKU,QAAQpjB,KAAK2B,IAAK3B,KAAK+hB,UAG3C/hB,KAAK6iB,YAAY,QACjB7iB,KAAKwiB,OAAOa,YAAS3F,EAGf,QAAQgE,GACd1hB,KAAKsjB,KAAK,QAAS,CAAEC,KAAM,iBAAkB7B,MAAOA,IACpD1hB,KAAKmiB,SAAST,MAAM1hB,KAAKwjB,qBAAqB,CAAE9B,MAAOA,EAAMnE,cAGvD,QAAQkG,GACVA,EACFzjB,KAAK6iB,YAAY,SAAU,CACzBa,KAAMD,EAAWC,KACjBC,OAAQF,EAAWE,OACnBC,SAAUH,EAAWG,WAGvB5jB,KAAK6iB,YAAY,UAEnB7iB,KAAK6jB,kBACL7jB,KAAKwiB,YAAS9E,EAGR,UAAU0D,GAChBphB,KAAKsjB,KAAK,UAAWlC,GAGf,aACNphB,KAAKsjB,KAAK,YAGJ,gBACNtjB,KAAKwiB,OAAOa,OAAS,KACnBrjB,KAAK8jB,UAEP9jB,KAAKwiB,OAAOuB,QAAWrC,IACrB1hB,KAAK4iB,QAAQlB,IAEf1hB,KAAKwiB,OAAOwB,QAAWP,IACrBzjB,KAAKikB,QAAQR,IAEfzjB,KAAKwiB,OAAO0B,UAAa9C,IACvBphB,KAAKmkB,UAAU/C,IAGbphB,KAAKuiB,iBACPviB,KAAKwiB,OAAO4B,WAAa,KACvBpkB,KAAKqkB,eAKH,kBACFrkB,KAAKwiB,SACPxiB,KAAKwiB,OAAOa,YAAS3F,EACrB1d,KAAKwiB,OAAOuB,aAAUrG,EACtB1d,KAAKwiB,OAAOwB,aAAUtG,EACtB1d,KAAKwiB,OAAO0B,eAAYxG,EACpB1d,KAAKuiB,iBACPviB,KAAKwiB,OAAO4B,gBAAa1G,IAKvB,YAAYwE,EAAezE,GACjCzd,KAAKkiB,MAAQA,EACbliB,KAAKmiB,SAASmC,KACZtkB,KAAKwjB,qBAAqB,CACxBtB,MAAOA,EACPzE,OAAQA,KAGZzd,KAAKsjB,KAAKpB,EAAOzE,GAGnB,qBAAqB2D,GACnB,OAAO,EAAmB,CAAEmD,IAAKvkB,KAAKoiB,IAAMhB,ICzNjC,MAAM,EAGnB,YAAYS,GACV7hB,KAAK6hB,MAAQA,EAQf,YAAY2C,GACV,OAAOxkB,KAAK6hB,MAAM4C,YAAYD,GAWhC,iBACE7jB,EACAmhB,EACAngB,EACAogB,GAEA,OAAO,IAAI,EAAoB/hB,KAAK6hB,MAAOlhB,EAAMmhB,EAAUngB,EAAKogB,ICrCpE,IAAI2C,EAAc,IAAI,EAA0B,CAC9ChC,KAAM,EACNJ,uBAAuB,EACvBC,cAAc,EAEdoC,cAAe,WACb,OAAOxH,QAAQ,GAAQyH,oBAEzBH,YAAa,WACX,OAAOtH,QAAQ,GAAQyH,oBAEzBjC,UAAW,SAAUF,GACnB,OAAO,GAAQoC,gBAAgBpC,MAI/BqC,EAAoB,CACtBpC,KAAM,EACNJ,uBAAuB,EACvBC,cAAc,EACdoC,cAAe,WACb,OAAO,IAIAI,EAAyB,EAClC,CACEpC,UAAW,SAAUF,GACnB,OAAO,GAAQuC,YAAYC,sBAAsBxC,KAGrDqC,GAESI,EAAuB,EAChC,CACEvC,UAAW,SAAUF,GACnB,OAAO,GAAQuC,YAAYG,oBAAoB1C,KAGnDqC,GAGEM,EAAmB,CACrBX,YAAa,WACX,OAAO,GAAQY,mBAwBJ,EANmB,CAChCpF,GAAIyE,EACJY,cAf0B,IAAI,EAE5B,EAAmB,GAAIP,EAAwBK,IAcjDG,YATwB,IAAI,EAE1B,EAAmB,GAAIL,EAAsBE,KCpDlC,MAAM,EAOnB,YACEI,EACAnG,EACA0C,GAEA/hB,KAAKwlB,QAAUA,EACfxlB,KAAKqf,UAAYA,EACjBrf,KAAKylB,aAAe1D,EAAQ0D,aAC5BzlB,KAAK0lB,aAAe3D,EAAQ2D,aAC5B1lB,KAAK2lB,eAAYjI,EAanB,iBACE/c,EACAmhB,EACAngB,EACAogB,GAEAA,EAAU,EAAmB,GAAIA,EAAS,CACxC/C,gBAAiBhf,KAAK2lB,YAExB,IAAIC,EAAa5lB,KAAKqf,UAAUwG,iBAC9BllB,EACAmhB,EACAngB,EACAogB,GAGE+D,EAAgB,KAEhBhC,EAAS,WACX8B,EAAW5E,OAAO,OAAQ8C,GAC1B8B,EAAWhkB,KAAK,SAAUmkB,GAC1BD,EAAgB,EAAKzK,OAEnB0K,EAAYtC,IAGd,GAFAmC,EAAW5E,OAAO,SAAU+E,GAEJ,OAApBtC,EAAWC,MAAqC,OAApBD,EAAWC,KAEzC1jB,KAAKwlB,QAAQQ,mBACR,IAAKvC,EAAWG,UAAYkC,EAAe,CAEhD,IAAIG,EAAW,EAAK5K,MAAQyK,EACxBG,EAAW,EAAIjmB,KAAK0lB,eACtB1lB,KAAKwlB,QAAQQ,cACbhmB,KAAK2lB,UAAY5a,KAAKmb,IAAID,EAAW,EAAGjmB,KAAKylB,iBAMnD,OADAG,EAAWhkB,KAAK,OAAQkiB,GACjB8B,EAWT,YAAYpB,GACV,OAAOxkB,KAAKwlB,QAAQW,WAAanmB,KAAKqf,UAAUoF,YAAYD,IC/FhE,MAAM4B,EAAW,CAgBfC,cAAe,SAAUC,GACvB,IACE,IAAIC,EAAc1I,KAAK2I,MAAMF,EAAa9jB,MACtCikB,EAAkBF,EAAY/jB,KAClC,GAA+B,iBAApBikB,EACT,IACEA,EAAkB5I,KAAK2I,MAAMD,EAAY/jB,MACzC,MAAO8L,IAEX,IAAIoY,EAA2B,CAC7BC,MAAOJ,EAAYI,MACnBC,QAASL,EAAYK,QACrBpkB,KAAMikB,GAKR,OAHIF,EAAYM,UACdH,EAAYG,QAAUN,EAAYM,SAE7BH,EACP,MAAOpY,GACP,KAAM,CAAEiV,KAAM,oBAAqB7B,MAAOpT,EAAG9L,KAAM8jB,EAAa9jB,QAUpEskB,cAAe,SAAUH,GACvB,OAAO9I,KAAKzB,UAAUuK,IAiBxBI,iBAAkB,SAAUT,GAC1B,IAAIlF,EAAUgF,EAASC,cAAcC,GAErC,GAAsB,kCAAlBlF,EAAQuF,MAA2C,CACrD,IAAKvF,EAAQ5e,KAAKwkB,iBAChB,KAAM,6CAER,MAAO,CACLC,OAAQ,YACR7E,GAAIhB,EAAQ5e,KAAK0kB,UACjBlI,gBAAiD,IAAhCoC,EAAQ5e,KAAKwkB,kBAE3B,GAAsB,iBAAlB5F,EAAQuF,MAGjB,MAAO,CACLM,OAAQjnB,KAAKmnB,eAAe/F,EAAQ5e,MACpCkf,MAAO1hB,KAAKonB,cAAchG,EAAQ5e,OAGpC,KAAM,qBAcV2kB,eAAgB,SAAU1D,GACxB,OAAIA,EAAWC,KAAO,IAMhBD,EAAWC,MAAQ,MAAQD,EAAWC,MAAQ,KACzC,UAEA,KAEoB,MAApBD,EAAWC,KACb,WACED,EAAWC,KAAO,KACpB,UACED,EAAWC,KAAO,KACpB,UACED,EAAWC,KAAO,KACpB,QAGA,WAaX0D,cAAe,SAAU3D,GACvB,OAAwB,MAApBA,EAAWC,MAAqC,OAApBD,EAAWC,KAClC,CACLH,KAAM,cACN/gB,KAAM,CACJkhB,KAAMD,EAAWC,KACjBtC,QAASqC,EAAWE,QAAUF,EAAWrC,UAItC,OAKE,QClIA,MAAM,UAAmB,EAKtC,YAAYgB,EAAY/C,GACtBnE,QACAlb,KAAKoiB,GAAKA,EACVpiB,KAAKqf,UAAYA,EACjBrf,KAAKgf,gBAAkBK,EAAUL,gBACjChf,KAAK8iB,gBAOP,wBACE,OAAO9iB,KAAKqf,UAAUiD,wBAOxB,KAAK9f,GACH,OAAOxC,KAAKqf,UAAU4D,KAAKzgB,GAU7B,WAAW7B,EAAc6B,EAAWokB,GAClC,IAAID,EAAqB,CAAEA,MAAOhmB,EAAM6B,KAAMA,GAK9C,OAJIokB,IACFD,EAAMC,QAAUA,GAElB,EAAO7D,MAAM,aAAc4D,GACpB3mB,KAAKijB,KAAK,EAAS6D,cAAcH,IAQ1C,OACM3mB,KAAKqf,UAAUkD,eACjBviB,KAAKqf,UAAU6D,OAEfljB,KAAKqnB,WAAW,cAAe,IAKnC,QACErnB,KAAKqf,UAAU2D,QAGT,gBACN,IAAIsE,EAAY,CACdlG,QAAUkF,IACR,IAAII,EACJ,IACEA,EAAc,EAASL,cAAcC,GACrC,MAAOhY,GACPtO,KAAKsjB,KAAK,QAAS,CACjBC,KAAM,oBACN7B,MAAOpT,EACP9L,KAAM8jB,EAAa9jB,OAIvB,QAAoBkb,IAAhBgJ,EAA2B,CAG7B,OAFA,EAAO3D,MAAM,aAAc2D,GAEnBA,EAAYC,OAClB,IAAK,eACH3mB,KAAKsjB,KAAK,QAAS,CACjBC,KAAM,cACN/gB,KAAMkkB,EAAYlkB,OAEpB,MACF,IAAK,cACHxC,KAAKsjB,KAAK,QACV,MACF,IAAK,cACHtjB,KAAKsjB,KAAK,QAGdtjB,KAAKsjB,KAAK,UAAWoD,KAGzBa,SAAU,KACRvnB,KAAKsjB,KAAK,aAEZ5B,MAAQA,IACN1hB,KAAKsjB,KAAK,QAAS5B,IAErB8F,OAAS/D,IACPI,IAEIJ,GAAcA,EAAWC,MAC3B1jB,KAAKynB,iBAAiBhE,GAGxBzjB,KAAKqf,UAAY,KACjBrf,KAAKsjB,KAAK,YAIVO,EAAkB,KACpB,EAAwByD,EAAW,CAACI,EAAUf,KAC5C3mB,KAAKqf,UAAU2B,OAAO2F,EAAOe,MAIjC,EAAwBJ,EAAW,CAACI,EAAUf,KAC5C3mB,KAAKqf,UAAUzd,KAAK+kB,EAAOe,KAIvB,iBAAiBjE,GACvB,IAAIwD,EAAS,EAASE,eAAe1D,GACjC/B,EAAQ,EAAS0F,cAAc3D,GAC/B/B,GACF1hB,KAAKsjB,KAAK,QAAS5B,GAEjBuF,GACFjnB,KAAKsjB,KAAK2D,EAAQ,CAAEA,OAAQA,EAAQvF,MAAOA,KCrIlC,MAAM,EAMnB,YACErC,EACAvE,GAEA9a,KAAKqf,UAAYA,EACjBrf,KAAK8a,SAAWA,EAChB9a,KAAK8iB,gBAGP,QACE9iB,KAAK6jB,kBACL7jB,KAAKqf,UAAU2D,QAGT,gBACNhjB,KAAKmkB,UAAa3jB,IAGhB,IAAIoD,EAFJ5D,KAAK6jB,kBAGL,IACEjgB,EAAS,EAASmjB,iBAAiBvmB,GACnC,MAAO8N,GAGP,OAFAtO,KAAKsK,OAAO,QAAS,CAAEoX,MAAOpT,SAC9BtO,KAAKqf,UAAU2D,QAIK,cAAlBpf,EAAOqjB,OACTjnB,KAAKsK,OAAO,YAAa,CACvBsb,WAAY,IAAI,EAAWhiB,EAAOwe,GAAIpiB,KAAKqf,WAC3CL,gBAAiBpb,EAAOob,mBAG1Bhf,KAAKsK,OAAO1G,EAAOqjB,OAAQ,CAAEvF,MAAO9d,EAAO8d,QAC3C1hB,KAAKqf,UAAU2D,UAInBhjB,KAAK+lB,SAAYtC,IACfzjB,KAAK6jB,kBAEL,IAAIoD,EAAS,EAASE,eAAe1D,IAAe,UAChD/B,EAAQ,EAAS0F,cAAc3D,GACnCzjB,KAAKsK,OAAO2c,EAAQ,CAAEvF,MAAOA,KAG/B1hB,KAAKqf,UAAUzd,KAAK,UAAW5B,KAAKmkB,WACpCnkB,KAAKqf,UAAUzd,KAAK,SAAU5B,KAAK+lB,UAG7B,kBACN/lB,KAAKqf,UAAU2B,OAAO,UAAWhhB,KAAKmkB,WACtCnkB,KAAKqf,UAAU2B,OAAO,SAAUhhB,KAAK+lB,UAG/B,OAAOkB,EAAgBxJ,GAC7Bzd,KAAK8a,SACH,EAAmB,CAAEuE,UAAWrf,KAAKqf,UAAW4H,OAAQA,GAAUxJ,KC1EzD,MAAM,EAKnB,YAAY0E,EAAoBJ,GAC9B/hB,KAAKmiB,SAAWA,EAChBniB,KAAK+hB,QAAUA,GAAW,GAG5B,KAAKnC,EAAiB9E,GAChB9a,KAAKmiB,SAASwF,WAIlB3nB,KAAKmiB,SAASc,KACZ,GAAQ2E,kBAAkBC,SAAS7nB,KAAM4f,GACzC9E,IC3BC,MAAMgN,WAAqBpkB,MAChC,YAAY4T,GACV4D,MAAM5D,GAENxW,OAAOinB,eAAe/nB,gBAAiBgC,YAIpC,MAAMgmB,WAAuBtkB,MAClC,YAAY4T,GACV4D,MAAM5D,GAENxW,OAAOinB,eAAe/nB,gBAAiBgC,YAIN0B,MAO9B,MAAMukB,WAAgCvkB,MAC3C,YAAY4T,GACV4D,MAAM5D,GAENxW,OAAOinB,eAAe/nB,gBAAiBgC,YAGpC,MAAMkmB,WAAwBxkB,MACnC,YAAY4T,GACV4D,MAAM5D,GAENxW,OAAOinB,eAAe/nB,gBAAiBgC,YAGpC,MAAMmmB,WAA2BzkB,MACtC,YAAY4T,GACV4D,MAAM5D,GAENxW,OAAOinB,eAAe/nB,gBAAiBgC,YAGpC,MAAMomB,WAA6B1kB,MACxC,YAAY4T,GACV4D,MAAM5D,GAENxW,OAAOinB,eAAe/nB,gBAAiBgC,YAGpC,MAAMqmB,WAA4B3kB,MACvC,YAAY4T,GACV4D,MAAM5D,GAENxW,OAAOinB,eAAe/nB,gBAAiBgC,YAGpC,MAAMsmB,WAAsB5kB,MAEjC,YAAY6kB,EAAgBjR,GAC1B4D,MAAM5D,GACNtX,KAAKuoB,OAASA,EAEdznB,OAAOinB,eAAe/nB,gBAAiBgC,YC9D3C,MAAMwmB,GAAW,CACfC,QAAS,qBACT/F,KAAM,CACJgG,uBAAwB,CACtBzK,KAAM,kDAER0K,sBAAuB,CACrB1K,KAAM,gDAER2K,qBAAsB,CACpB3K,KAAM,gCAER4K,uBAAwB,CACtB5K,KAAM,uDAER6K,wBAAyB,CACvBC,QACE,iHA0BO,OAhBQ,SAAUpnB,GAC/B,MACMqnB,EAASR,GAAS9F,KAAK/gB,GAC7B,IAAKqnB,EAAQ,MAAO,GAEpB,IAAIvG,EAOJ,OANIuG,EAAOD,QACTtG,EAAMuG,EAAOD,QACJC,EAAO/K,OAChBwE,EAAM+F,GAASC,QAAUO,EAAO/K,MAG7BwE,EACE,QAAgBA,EADN,ICrBJ,MAAM,WAAgB,EAQnC,YAAY9hB,EAAcsoB,GACxB/N,OAAM,SAAUyL,EAAOnkB,GACrB,EAAOugB,MAAM,mBAAqBpiB,EAAO,QAAUgmB,MAGrD3mB,KAAKW,KAAOA,EACZX,KAAKipB,OAASA,EACdjpB,KAAKkpB,YAAa,EAClBlpB,KAAKmpB,qBAAsB,EAC3BnpB,KAAKopB,uBAAwB,EAO/B,UAAUC,EAAkBvO,GAC1B,OAAOA,EAAS,KAAM,CAAEwO,KAAM,KAIhC,QAAQ3C,EAAenkB,GACrB,GAAiC,IAA7BmkB,EAAMjK,QAAQ,WAChB,MAAM,IAAI,GACR,UAAYiK,EAAQ,mCAGxB,IAAK3mB,KAAKkpB,WAAY,CACpB,IAAIK,EAAS,GAAwB,0BACrC,EAAO9H,KACL,0EAA0E8H,GAG9E,OAAOvpB,KAAKipB,OAAO5B,WAAWV,EAAOnkB,EAAMxC,KAAKW,MAIlD,aACEX,KAAKkpB,YAAa,EAClBlpB,KAAKmpB,qBAAsB,EAO7B,YAAYxC,GACV,IAAI7F,EAAY6F,EAAMA,MAClBnkB,EAAOmkB,EAAMnkB,KACjB,GAAkB,2CAAdse,EACF9gB,KAAKwpB,iCAAiC7C,QACjC,GAAkB,uCAAd7F,EACT9gB,KAAKypB,6BAA6B9C,QAC7B,GAA8C,IAA1C7F,EAAUpE,QAAQ,oBAA2B,CAEtD1c,KAAKsjB,KAAKxC,EAAWte,EADI,KAK7B,iCAAiCmkB,GAC/B3mB,KAAKmpB,qBAAsB,EAC3BnpB,KAAKkpB,YAAa,EACdlpB,KAAKopB,sBACPppB,KAAKipB,OAAOS,YAAY1pB,KAAKW,MAE7BX,KAAKsjB,KAAK,gCAAiCqD,EAAMnkB,MAIrD,6BAA6BmkB,GACvBA,EAAMnkB,KAAKmnB,qBACb3pB,KAAK4pB,kBAAoBjD,EAAMnkB,KAAKmnB,oBAGtC3pB,KAAKsjB,KAAK,4BAA6BqD,EAAMnkB,MAI/C,YACMxC,KAAKkpB,aAGTlpB,KAAKmpB,qBAAsB,EAC3BnpB,KAAKopB,uBAAwB,EAC7BppB,KAAK6pB,UACH7pB,KAAKipB,OAAOrD,WAAWsB,UACvB,CAACxF,EAAqBlf,KAChBkf,GACF1hB,KAAKmpB,qBAAsB,EAI3B,EAAOzH,MAAMA,EAAMnE,YACnBvd,KAAKsjB,KACH,4BACAxiB,OAAOgpB,OACL,GACA,CACEvG,KAAM,YACN7B,MAAOA,EAAMN,SAEfM,aAAiB4G,GAAgB,CAAEC,OAAQ7G,EAAM6G,QAAW,MAIhEvoB,KAAKipB,OAAO5B,WAAW,mBAAoB,CACzCiC,KAAM9mB,EAAK8mB,KACXS,aAAcvnB,EAAKunB,aACnBnD,QAAS5mB,KAAKW,UAQxB,cACEX,KAAKkpB,YAAa,EAClBlpB,KAAKipB,OAAO5B,WAAW,qBAAsB,CAC3CT,QAAS5mB,KAAKW,OAKlB,qBACEX,KAAKopB,uBAAwB,EAI/B,wBACEppB,KAAKopB,uBAAwB,GCvJlB,MAAM,WAAuB,GAM1C,UAAUC,EAAkBvO,GAC1B,OAAO9a,KAAKipB,OAAOe,OAAOC,kBACxB,CACEC,YAAalqB,KAAKW,KAClB0oB,SAAUA,GAEZvO,IClBS,MAAM,GAMnB,cACE9a,KAAKmqB,QAUP,IAAI/H,GACF,OAAIthB,OAAOkB,UAAUC,eAAe1B,KAAKP,KAAKoqB,QAAShI,GAC9C,CACLA,GAAIA,EACJkC,KAAMtkB,KAAKoqB,QAAQhI,IAGd,KAQX,KAAKtH,GACH,EAAwB9a,KAAKoqB,QAAS,CAACC,EAAQjI,KAC7CtH,EAAS9a,KAAKiB,IAAImhB,MAKtB,QAAQA,GACNpiB,KAAKsqB,KAAOlI,EAId,eAAemI,GACbvqB,KAAKoqB,QAAUG,EAAiBC,SAASlR,KACzCtZ,KAAKyqB,MAAQF,EAAiBC,SAASC,MACvCzqB,KAAK0qB,GAAK1qB,KAAKiB,IAAIjB,KAAKsqB,MAI1B,UAAUK,GAKR,OAJqC,OAAjC3qB,KAAKiB,IAAI0pB,EAAW9D,UACtB7mB,KAAKyqB,QAEPzqB,KAAKoqB,QAAQO,EAAW9D,SAAW8D,EAAWC,UACvC5qB,KAAKiB,IAAI0pB,EAAW9D,SAI7B,aAAa8D,GACX,IAAIN,EAASrqB,KAAKiB,IAAI0pB,EAAW9D,SAKjC,OAJIwD,WACKrqB,KAAKoqB,QAAQO,EAAW9D,SAC/B7mB,KAAKyqB,SAEAJ,EAIT,QACErqB,KAAKoqB,QAAU,GACfpqB,KAAKyqB,MAAQ,EACbzqB,KAAKsqB,KAAO,KACZtqB,KAAK0qB,GAAK,M,2SCpEC,MAAM,WAAwB,GAQ3C,YAAY/pB,EAAcsoB,GACxB/N,MAAMva,EAAMsoB,GACZjpB,KAAKoqB,QAAU,IAAI,GAQrB,UAAUf,EAAkBvO,GAC1BI,MAAM2O,UAAUR,EAAU,CAAO3H,EAAOmJ,IAAa,GAAD,gCAClD,IAAKnJ,EAEH,GAA6B,OAD7BmJ,EAAWA,GACEd,aAAsB,CACjC,IAAIe,EAAcjN,KAAK2I,MAAMqE,EAASd,cACtC/pB,KAAKoqB,QAAQW,QAAQD,EAAYjE,aAC5B,CAEL,SADM7mB,KAAKipB,OAAO+B,KAAKC,kBACW,MAA9BjrB,KAAKipB,OAAO+B,KAAKE,UAId,CACL,IAAI3B,EAAS,GAAwB,yBAOrC,OANA,EAAO7H,MACL,sCAAsC1hB,KAAKW,yCACP4oB,4CAGtCzO,EAAS,yBART9a,KAAKoqB,QAAQW,QAAQ/qB,KAAKipB,OAAO+B,KAAKE,UAAU9I,IAatDtH,EAAS4G,EAAOmJ,OAQpB,YAAYlE,GACV,IAAI7F,EAAY6F,EAAMA,MACtB,GAA8C,IAA1C7F,EAAUpE,QAAQ,oBACpB1c,KAAKmrB,oBAAoBxE,OACpB,CACL,IAAInkB,EAAOmkB,EAAMnkB,KACb0e,EAAqB,GACrByF,EAAME,UACR3F,EAAS2F,QAAUF,EAAME,SAE3B7mB,KAAKsjB,KAAKxC,EAAWte,EAAM0e,IAG/B,oBAAoByF,GAClB,IAAI7F,EAAY6F,EAAMA,MAClBnkB,EAAOmkB,EAAMnkB,KACjB,OAAQse,GACN,IAAK,yCACH9gB,KAAKwpB,iCAAiC7C,GACtC,MACF,IAAK,qCACH3mB,KAAKypB,6BAA6B9C,GAClC,MACF,IAAK,+BACH,IAAIyE,EAAcprB,KAAKoqB,QAAQiB,UAAU7oB,GACzCxC,KAAKsjB,KAAK,sBAAuB8H,GACjC,MACF,IAAK,iCACH,IAAIE,EAAgBtrB,KAAKoqB,QAAQmB,aAAa/oB,GAC1C8oB,GACFtrB,KAAKsjB,KAAK,wBAAyBgI,IAM3C,iCAAiC3E,GAC/B3mB,KAAKmpB,qBAAsB,EAC3BnpB,KAAKkpB,YAAa,EACdlpB,KAAKopB,sBACPppB,KAAKipB,OAAOS,YAAY1pB,KAAKW,OAE7BX,KAAKoqB,QAAQoB,eAAe7E,EAAMnkB,MAClCxC,KAAKsjB,KAAK,gCAAiCtjB,KAAKoqB,UAKpD,aACEpqB,KAAKoqB,QAAQD,QACbjP,MAAMuQ,c,oBC3FK,MAAM,WAAyB,GAI5C,YAAY9qB,EAAcsoB,EAAgBpkB,GACxCqW,MAAMva,EAAMsoB,GAJd,KAAAtnB,IAAkB,KAKhB3B,KAAK6E,KAAOA,EAQd,UAAUwkB,EAAkBvO,GAC1BI,MAAM2O,UACJR,EACA,CAAC3H,EAAqBmJ,KACpB,GAAInJ,EAEF,YADA5G,EAAS4G,EAAOmJ,GAGlB,IAAIa,EAAeb,EAAwB,cACtCa,GASL1rB,KAAK2B,IAAM,kBAAa+pB,UACjBb,EAAwB,cAC/B/P,EAAS,KAAM+P,IAVb/P,EACE,IAAIpX,MACF,+DAA+D1D,KAAKW,MAEtE,QAWV,QAAQgmB,EAAenkB,GACrB,MAAM,IAAI,GACR,oEAQJ,YAAYmkB,GACV,IAAI7F,EAAY6F,EAAMA,MAClBnkB,EAAOmkB,EAAMnkB,KAE2B,IAA1Cse,EAAUpE,QAAQ,qBACe,IAAjCoE,EAAUpE,QAAQ,WAKpB1c,KAAK2rB,qBAAqB7K,EAAWte,GAHnC0Y,MAAM0Q,YAAYjF,GAMd,qBAAqBA,EAAenkB,GAC1C,IAAKxC,KAAK2B,IAIR,YAHA,EAAOohB,MACL,gFAIJ,IAAKvgB,EAAKqpB,aAAerpB,EAAK+U,MAK5B,YAJA,EAAOmK,MACL,qGACElf,GAIN,IAAIspB,EAAa,kBAAatpB,EAAKqpB,YACnC,GAAIC,EAAWxpB,OAAStC,KAAK6E,KAAKwS,UAAUO,eAI1C,YAHA,EAAO8J,MACL,oDAAoD1hB,KAAK6E,KAAKwS,UAAUO,wBAAwBkU,EAAWxpB,UAI/G,IAAIiV,EAAQ,kBAAa/U,EAAK+U,OAC9B,GAAIA,EAAMjV,OAAStC,KAAK6E,KAAKwS,UAAUM,YAIrC,YAHA,EAAO+J,MACL,+CAA+C1hB,KAAK6E,KAAKwS,UAAUM,qBAAqBJ,EAAMjV,UAKlG,IAAIyM,EAAQ/O,KAAK6E,KAAKwS,UAAUG,KAAKsU,EAAYvU,EAAOvX,KAAK2B,KAC7D,GAAc,OAAVoN,EAuBF,OAtBA,EAAOgU,MACL,wIAIF/iB,KAAK6pB,UAAU7pB,KAAKipB,OAAOrD,WAAWsB,UAAW,CAACxF,EAAOmJ,KACnDnJ,EACF,EAAOA,MACL,iDAAiDmJ,4DAIrD9b,EAAQ/O,KAAK6E,KAAKwS,UAAUG,KAAKsU,EAAYvU,EAAOvX,KAAK2B,KAC3C,OAAVoN,EAMJ/O,KAAKsjB,KAAKqD,EAAO3mB,KAAK+rB,cAAchd,IALlC,EAAO2S,MACL,qEASR1hB,KAAKsjB,KAAKqD,EAAO3mB,KAAK+rB,cAAchd,IAKtC,cAAcA,GACZ,IAAIid,EAAM,kBAAWjd,GACrB,IACE,OAAO8O,KAAK2I,MAAMwF,GAClB,SACA,OAAOA,ICpGE,MAAM,WAA0B,EAkB7C,YAAYrqB,EAAaogB,GACvB7G,QACAlb,KAAKkiB,MAAQ,cACbliB,KAAK4lB,WAAa,KAElB5lB,KAAK2B,IAAMA,EACX3B,KAAK+hB,QAAUA,EACf/hB,KAAKmiB,SAAWniB,KAAK+hB,QAAQI,SAC7BniB,KAAKisB,SAAWjsB,KAAK+hB,QAAQnC,OAE7B5f,KAAKksB,eAAiBlsB,KAAKmsB,sBAC3BnsB,KAAKosB,oBAAsBpsB,KAAKqsB,yBAC9BrsB,KAAKksB,gBAEPlsB,KAAKssB,mBAAqBtsB,KAAKusB,wBAAwBvsB,KAAKksB,gBAE5D,IAAIM,EAAU,GAAQC,aAEtBD,EAAQ5qB,KAAK,SAAU,KACrB5B,KAAKmiB,SAASmC,KAAK,CAAEoI,QAAS,WACX,eAAf1sB,KAAKkiB,OAAyC,gBAAfliB,KAAKkiB,OACtCliB,KAAK2sB,QAAQ,KAGjBH,EAAQ5qB,KAAK,UAAW,KACtB5B,KAAKmiB,SAASmC,KAAK,CAAEoI,QAAS,YAC1B1sB,KAAK4lB,YACP5lB,KAAK4sB,sBAIT5sB,KAAK6sB,iBAQP,UACM7sB,KAAK4lB,YAAc5lB,KAAK8sB,SAGvB9sB,KAAK+sB,SAAStI,eAInBzkB,KAAKgtB,YAAY,cACjBhtB,KAAKitB,kBACLjtB,KAAKktB,uBALHltB,KAAKgtB,YAAY,WAYrB,KAAKxqB,GACH,QAAIxC,KAAK4lB,YACA5lB,KAAK4lB,WAAW3C,KAAKzgB,GAahC,WAAW7B,EAAc6B,EAAWokB,GAClC,QAAI5mB,KAAK4lB,YACA5lB,KAAK4lB,WAAWyB,WAAW1mB,EAAM6B,EAAMokB,GAOlD,aACE5mB,KAAKmtB,uBACLntB,KAAKgtB,YAAY,gBAGnB,aACE,OAAOhtB,KAAKisB,SAGN,kBACN,IAAInR,EAAW,CAAC4G,EAAO0L,KACjB1L,EACF1hB,KAAK8sB,OAAS9sB,KAAK+sB,SAASM,QAAQ,EAAGvS,GAEd,UAArBsS,EAAUnG,QACZjnB,KAAKsjB,KAAK,QAAS,CACjBC,KAAM,iBACN7B,MAAO0L,EAAU1L,QAEnB1hB,KAAKmiB,SAAST,MAAM,CAAE4L,eAAgBF,EAAU1L,UAEhD1hB,KAAKutB,kBACLvtB,KAAKssB,mBAAmBc,EAAUnG,QAAQmG,KAIhDptB,KAAK8sB,OAAS9sB,KAAK+sB,SAASM,QAAQ,EAAGvS,GAGjC,kBACF9a,KAAK8sB,SACP9sB,KAAK8sB,OAAOU,QACZxtB,KAAK8sB,OAAS,MAIV,wBACN9sB,KAAKutB,kBACLvtB,KAAKytB,kBACLztB,KAAK0tB,wBACD1tB,KAAK4lB,aACU5lB,KAAK2tB,oBACX3K,QAIP,iBACNhjB,KAAK+sB,SAAW/sB,KAAK+hB,QAAQ6L,YAAY,CACvCjsB,IAAK3B,KAAK2B,IACVwgB,SAAUniB,KAAKmiB,SACfvC,OAAQ5f,KAAKisB,WAIT,QAAQpR,GACd7a,KAAKmiB,SAASmC,KAAK,CAAE2C,OAAQ,QAASpM,MAAOA,IACzCA,EAAQ,GACV7a,KAAKsjB,KAAK,gBAAiBvY,KAAK8iB,MAAMhT,EAAQ,MAEhD7a,KAAK8tB,WAAa,IAAI,EAAMjT,GAAS,EAAG,KACtC7a,KAAKmtB,uBACLntB,KAAKqtB,YAID,kBACFrtB,KAAK8tB,aACP9tB,KAAK8tB,WAAWC,gBAChB/tB,KAAK8tB,WAAa,MAId,sBACN9tB,KAAKguB,iBAAmB,IAAI,EAAMhuB,KAAK+hB,QAAQ7C,mBAAoB,KACjElf,KAAKgtB,YAAY,iBAIb,wBACFhtB,KAAKguB,kBACPhuB,KAAKguB,iBAAiBD,gBAIlB,oBACN/tB,KAAKiuB,oBACLjuB,KAAK4lB,WAAW1C,OAEhBljB,KAAKkuB,cAAgB,IAAI,EAAMluB,KAAK+hB,QAAQ9C,YAAa,KACvDjf,KAAKmiB,SAAST,MAAM,CAAEyM,eAAgBnuB,KAAK+hB,QAAQ9C,cACnDjf,KAAK2sB,QAAQ,KAIT,qBACN3sB,KAAKiuB,oBAEDjuB,KAAK4lB,aAAe5lB,KAAK4lB,WAAWtD,0BACtCtiB,KAAKkuB,cAAgB,IAAI,EAAMluB,KAAKgf,gBAAiB,KACnDhf,KAAK4sB,uBAKH,oBACF5sB,KAAKkuB,eACPluB,KAAKkuB,cAAcH,gBAIf,yBACN7B,GAEA,OAAO,EAAwC,GAAIA,EAAgB,CACjE9K,QAAUA,IAERphB,KAAKouB,qBACLpuB,KAAKsjB,KAAK,UAAWlC,IAEvB8B,KAAM,KACJljB,KAAKqnB,WAAW,cAAe,KAEjCE,SAAU,KACRvnB,KAAKouB,sBAEP1M,MAAQA,IAEN1hB,KAAKsjB,KAAK,QAAS5B,IAErB8F,OAAQ,KACNxnB,KAAK2tB,oBACD3tB,KAAKquB,eACPruB,KAAK2sB,QAAQ,QAMb,wBACNT,GAEA,OAAO,EAAuC,GAAIA,EAAgB,CAChEoC,UAAYlB,IACVptB,KAAKgf,gBAAkBjU,KAAKxG,IAC1BvE,KAAK+hB,QAAQ/C,gBACboO,EAAUpO,gBACVoO,EAAUxH,WAAW5G,iBAAmBuP,KAE1CvuB,KAAK0tB,wBACL1tB,KAAKwuB,cAAcpB,EAAUxH,YAC7B5lB,KAAKknB,UAAYlnB,KAAK4lB,WAAWxD,GACjCpiB,KAAKgtB,YAAY,YAAa,CAAE9F,UAAWlnB,KAAKknB,eAK9C,sBACN,IAAIuH,EAAoB3T,GACdlX,IACFA,EAAO8d,OACT1hB,KAAKsjB,KAAK,QAAS,CAAEC,KAAM,iBAAkB7B,MAAO9d,EAAO8d,QAE7D5G,EAASlX,IAIb,MAAO,CACL8qB,SAAUD,EAAiB,KACzBzuB,KAAKisB,UAAW,EAChBjsB,KAAK6sB,iBACL7sB,KAAK2sB,QAAQ,KAEfgC,QAASF,EAAiB,KACxBzuB,KAAKyrB,eAEPmD,QAASH,EAAiB,KACxBzuB,KAAK2sB,QAAQ,OAEfkC,MAAOJ,EAAiB,KACtBzuB,KAAK2sB,QAAQ,MAKX,cAAc/G,GAEpB,IAAK,IAAIe,KADT3mB,KAAK4lB,WAAaA,EACA5lB,KAAKosB,oBACrBpsB,KAAK4lB,WAAWhkB,KAAK+kB,EAAO3mB,KAAKosB,oBAAoBzF,IAEvD3mB,KAAKouB,qBAGC,oBACN,GAAKpuB,KAAK4lB,WAAV,CAIA,IAAK,IAAIe,KADT3mB,KAAKiuB,oBACajuB,KAAKosB,oBACrBpsB,KAAK4lB,WAAW5E,OAAO2F,EAAO3mB,KAAKosB,oBAAoBzF,IAEzD,IAAIf,EAAa5lB,KAAK4lB,WAEtB,OADA5lB,KAAK4lB,WAAa,KACXA,GAGD,YAAYkJ,EAAkBtsB,GACpC,IAAIusB,EAAgB/uB,KAAKkiB,MAEzB,GADAliB,KAAKkiB,MAAQ4M,EACTC,IAAkBD,EAAU,CAC9B,IAAIE,EAAsBF,EACE,cAAxBE,IACFA,GAAuB,uBAAyBxsB,EAAK0kB,WAEvD,EAAOnE,MACL,gBACAgM,EAAgB,OAASC,GAE3BhvB,KAAKmiB,SAASmC,KAAK,CAAEpC,MAAO4M,EAAUrR,OAAQjb,IAC9CxC,KAAKsjB,KAAK,eAAgB,CAAE2L,SAAUF,EAAeG,QAASJ,IAC9D9uB,KAAKsjB,KAAKwL,EAAUtsB,IAIhB,cACN,MAAsB,eAAfxC,KAAKkiB,OAAyC,cAAfliB,KAAKkiB,OCtWhC,MAAM,GAGnB,cACEliB,KAAKmvB,SAAW,GASlB,IAAIxuB,EAAcsoB,GAIhB,OAHKjpB,KAAKmvB,SAASxuB,KACjBX,KAAKmvB,SAASxuB,GAwCpB,SAAuBA,EAAcsoB,GACnC,GAA2C,IAAvCtoB,EAAK+b,QAAQ,sBAA6B,CAC5C,GAAIuM,EAAOe,OAAOnlB,KAChB,OAAO,GAAQuqB,uBAAuBzuB,EAAMsoB,EAAQA,EAAOe,OAAOnlB,MAEpE,IAAIwqB,EACF,0FACE9F,EAAS,GAAwB,2BACrC,MAAM,IAAI,GAA0B,GAAG8F,MAAW9F,KAC7C,GAAiC,IAA7B5oB,EAAK+b,QAAQ,YACtB,OAAO,GAAQ4S,qBAAqB3uB,EAAMsoB,GACrC,GAAkC,IAA9BtoB,EAAK+b,QAAQ,aACtB,OAAO,GAAQ6S,sBAAsB5uB,EAAMsoB,GACtC,GAA0B,IAAtBtoB,EAAK+b,QAAQ,KACtB,MAAM,IAAI,GACR,sCAAwC/b,EAAO,MAGjD,OAAO,GAAQ6uB,cAAc7uB,EAAMsoB,GA1DXuG,CAAc7uB,EAAMsoB,IAErCjpB,KAAKmvB,SAASxuB,GAOvB,MACE,OtBiEG,SAAgBmB,GACrB,IAAI2tB,EAAS,GAIb,OAHA9S,EAAY7a,GAAQ,SAAUT,GAC5BouB,EAAO9qB,KAAKtD,MAEPouB,EsBtEE,CAAmBzvB,KAAKmvB,UAQjC,KAAKxuB,GACH,OAAOX,KAAKmvB,SAASxuB,GAOvB,OAAOA,GACL,IAAIimB,EAAU5mB,KAAKmvB,SAASxuB,GAE5B,cADOX,KAAKmvB,SAASxuB,GACdimB,EAIT,aACE,EAAwB5mB,KAAKmvB,UAAU,SAAUvI,GAC/CA,EAAQ6E,iBClCd,IAoDe,GApDD,CACZiE,eAAc,IACL,IAAI,GAGbC,wBAAuB,CACrBhuB,EACAogB,IAEO,IAAI,GAAkBpgB,EAAKogB,GAGpCyN,cAAa,CAAC7uB,EAAcsoB,IACnB,IAAI,GAAQtoB,EAAMsoB,GAG3BqG,qBAAoB,CAAC3uB,EAAcsoB,IAC1B,IAAI,GAAetoB,EAAMsoB,GAGlCsG,sBAAqB,CAAC5uB,EAAcsoB,IAC3B,IAAI,GAAgBtoB,EAAMsoB,GAGnCmG,uBAAsB,CACpBzuB,EACAsoB,EACApkB,IAEO,IAAI,GAAiBlE,EAAMsoB,EAAQpkB,GAG5C+qB,qBAAoB,CAACzN,EAAoBJ,IAChC,IAAI,EAAeI,EAAUJ,GAGtC8N,gBAAe,CACbxQ,EACAvE,IAEO,IAAI,EAAUuE,EAAWvE,GAGlCgV,qCAAoC,CAClCtK,EACAnG,EACA0C,IAEO,IAAI,EAA+ByD,EAASnG,EAAW0C,ICxDnD,MAAM,GAInB,YAAYA,GACV/hB,KAAK+hB,QAAUA,GAAW,GAC1B/hB,KAAK+vB,UAAY/vB,KAAK+hB,QAAQiO,OAASzB,IAQzC,aAAalP,GACX,OAAO,GAAQyQ,qCAAqC9vB,KAAMqf,EAAW,CACnEoG,aAAczlB,KAAK+hB,QAAQ0D,aAC3BC,aAAc1lB,KAAK+hB,QAAQ2D,eAQ/B,UACE,OAAO1lB,KAAK+vB,UAAY,EAI1B,cACE/vB,KAAK+vB,WAAa,GCjCP,MAAM,GAOnB,YAAYE,EAAwBlO,GAClC/hB,KAAKiwB,WAAaA,EAClBjwB,KAAKkwB,KAAO/S,QAAQ4E,EAAQmO,MAC5BlwB,KAAKmwB,SAAWhT,QAAQ4E,EAAQoO,UAChCnwB,KAAKowB,QAAUrO,EAAQqO,QACvBpwB,KAAKqwB,aAAetO,EAAQsO,aAG9B,cACE,OAAO,EAAgBrwB,KAAKiwB,WAAY,EAAKtS,OAAO,gBAGtD,QAAQ2S,EAAqBxV,GAC3B,IAAImV,EAAajwB,KAAKiwB,WAClBf,EAAU,EACVkB,EAAUpwB,KAAKowB,QACftD,EAAS,KAETyD,EAAkB,CAAC7O,EAAO0L,KACxBA,EACFtS,EAAS,KAAMsS,IAEf8B,GAAoB,EAChBlvB,KAAKkwB,OACPhB,GAAoBe,EAAW3tB,QAG7B4sB,EAAUe,EAAW3tB,QACnB8tB,IACFA,GAAoB,EAChBpwB,KAAKqwB,eACPD,EAAUrlB,KAAKxG,IAAI6rB,EAASpwB,KAAKqwB,gBAGrCvD,EAAS9sB,KAAKwwB,YACZP,EAAWf,GACXoB,EACA,CAAEF,UAASD,SAAUnwB,KAAKmwB,UAC1BI,IAGFzV,GAAS,KAYf,OAPAgS,EAAS9sB,KAAKwwB,YACZP,EAAWf,GACXoB,EACA,CAAEF,QAASA,EAASD,SAAUnwB,KAAKmwB,UACnCI,GAGK,CACL/C,MAAO,WACLV,EAAOU,SAETiD,iBAAkB,SAAUvuB,GAC1BouB,EAAcpuB,EACV4qB,GACFA,EAAO2D,iBAAiBvuB,KAMxB,YACN6qB,EACAuD,EACAvO,EACAjH,GAEA,IAAIC,EAAQ,KACR+R,EAAS,KAoBb,OAlBI/K,EAAQqO,QAAU,IACpBrV,EAAQ,IAAI,EAAMgH,EAAQqO,SAAS,WACjCtD,EAAOU,QACP1S,GAAS,OAIbgS,EAASC,EAASM,QAAQiD,GAAa,SAAU5O,EAAO0L,GAClD1L,GAAS3G,GAASA,EAAM2V,cAAgB3O,EAAQoO,WAIhDpV,GACFA,EAAMgT,gBAERjT,EAAS4G,EAAO0L,OAGX,CACLI,MAAO,WACDzS,GACFA,EAAMgT,gBAERjB,EAAOU,SAETiD,iBAAkB,SAAUvuB,GAC1B4qB,EAAO2D,iBAAiBvuB,MCpHjB,MAAM,GAGnB,YAAY+tB,GACVjwB,KAAKiwB,WAAaA,EAGpB,cACE,OAAO,EAAgBjwB,KAAKiwB,WAAY,EAAKtS,OAAO,gBAGtD,QAAQ2S,EAAqBxV,GAC3B,OA6BJ,SACEmV,EACAK,EACAK,GAEA,IAAIC,EAAU,EAAgBX,GAAY,SAAUlD,EAAU3sB,EAAGyc,EAAGgU,GAClE,OAAO9D,EAASM,QAAQiD,EAAaK,EAAgBvwB,EAAGywB,OAE1D,MAAO,CACLrD,MAAO,WACL,EAAkBoD,EAASE,KAE7BL,iBAAkB,SAAUvuB,GAC1B,EAAkB0uB,GAAS,SAAU9D,GACnCA,EAAO2D,iBAAiBvuB,QA3CrBmrB,CAAQrtB,KAAKiwB,WAAYK,GAAa,SAAUlwB,EAAGwwB,GACxD,OAAO,SAAUlP,EAAO0L,GACtBwD,EAAQxwB,GAAGshB,MAAQA,EACfA,EA8CZ,SAA0BkP,GACxB,O1BsLK,SAAarU,EAAcU,GAChC,IAAK,IAAI7c,EAAI,EAAGA,EAAImc,EAAMja,OAAQlC,IAChC,IAAK6c,EAAKV,EAAMnc,GAAIA,EAAGmc,GACrB,OAAO,EAGX,OAAO,E0B5LA,CAAgBqU,GAAS,SAAU9D,GACxC,OAAO3P,QAAQ2P,EAAOpL,UA/CZqP,CAAiBH,IACnB9V,GAAS,IAIb,EAAkB8V,GAAS,SAAU9D,GACnCA,EAAO2D,iBAAiBrD,EAAU/N,UAAUyC,aAE9ChH,EAAS,KAAMsS,SA2CvB,SAAS0D,GAAYhE,GACdA,EAAOpL,OAAUoL,EAAOkE,UAC3BlE,EAAOU,QACPV,EAAOkE,SAAU,GC1DN,MAAM,GAOnB,YACEjE,EACAkE,EACAlP,GAEA/hB,KAAK+sB,SAAWA,EAChB/sB,KAAKixB,WAAaA,EAClBjxB,KAAKkxB,IAAMnP,EAAQmP,KAAO,KAC1BlxB,KAAKisB,SAAWlK,EAAQnC,OACxB5f,KAAKmiB,SAAWJ,EAAQI,SAG1B,cACE,OAAOniB,KAAK+sB,SAAStI,cAGvB,QAAQ6L,EAAqBxV,GAC3B,IAAImR,EAAWjsB,KAAKisB,SAChB3H,EAkER,SAA6B2H,GAC3B,IAAIkF,EAAU,GAAQC,kBACtB,GAAID,EACF,IACE,IAAIE,EAAkBF,EAAQG,GAAqBrF,IACnD,GAAIoF,EACF,OAAOxT,KAAK2I,MAAM6K,GAEpB,MAAO/iB,GACPijB,GAAoBtF,GAGxB,OAAO,KA9EMuF,CAAoBvF,GAC3BwF,EAAiBnN,GAAQA,EAAKmN,eAAiBnN,EAAKmN,eAAiB,EAErExB,EAAa,CAACjwB,KAAK+sB,UACvB,GAAIzI,GAAQA,EAAKoN,UAAY1xB,KAAKkxB,KAAO,EAAK7V,MAAO,CACnD,IAAIgE,EAAYrf,KAAKixB,WAAW3M,EAAKjF,WACjCA,IACE,CAAC,KAAM,OAAOsS,SAASrN,EAAKjF,YAAcoS,EAAiB,GAC7DzxB,KAAKmiB,SAASmC,KAAK,CACjBsN,QAAQ,EACRvS,UAAWiF,EAAKjF,UAChBwS,QAASvN,EAAKuN,UAEhB5B,EAAWtrB,KACT,IAAI,GAAmB,CAAC0a,GAAY,CAClC+Q,QAAwB,EAAf9L,EAAKuN,QAAc,IAC5B1B,UAAU,MAIdsB,KAKN,IAAIK,EAAiB,EAAKzW,MACtByR,EAASmD,EACV8B,MACA1E,QAAQiD,GAAa,SAAS0B,EAAGtQ,EAAO0L,GACnC1L,GACF6P,GAAoBtF,GAChBgE,EAAW3tB,OAAS,GACtBwvB,EAAiB,EAAKzW,MACtByR,EAASmD,EAAW8B,MAAM1E,QAAQiD,EAAa0B,IAE/ClX,EAAS4G,MA8CrB,SACEuK,EACA5M,EACAwS,EACAJ,GAEA,IAAIN,EAAU,GAAQC,kBACtB,GAAID,EACF,IACEA,EAAQG,GAAqBrF,IAAa,EAA8B,CACtEyF,UAAW,EAAKrW,MAChBgE,UAAWA,EACXwS,QAASA,EACTJ,eAAgBA,IAElB,MAAOnjB,KA1DH2jB,CACEhG,EACAmB,EAAU/N,UAAU1e,KACpB,EAAK0a,MAAQyW,EACbL,GAEF3W,EAAS,KAAMsS,OAIrB,MAAO,CACLI,MAAO,WACLV,EAAOU,SAETiD,iBAAkB,SAAUvuB,GAC1BouB,EAAcpuB,EACV4qB,GACFA,EAAO2D,iBAAiBvuB,MAOlC,SAASovB,GAAqBrF,GAC5B,MAAO,mBAAqBA,EAAW,MAAQ,UAuCjD,SAASsF,GAAoBtF,GAC3B,IAAIkF,EAAU,GAAQC,kBACtB,GAAID,EACF,WACSA,EAAQG,GAAqBrF,IACpC,MAAO3d,KC5IE,MAAM,GAInB,YAAYye,GAAsBlS,MAAOqX,IACvClyB,KAAK+sB,SAAWA,EAChB/sB,KAAK+hB,QAAU,CAAElH,MAAOqX,GAG1B,cACE,OAAOlyB,KAAK+sB,SAAStI,cAGvB,QAAQ6L,EAAqBxV,GAC3B,IACIgS,EADAC,EAAW/sB,KAAK+sB,SAEhBhS,EAAQ,IAAI,EAAM/a,KAAK+hB,QAAQlH,OAAO,WACxCiS,EAASC,EAASM,QAAQiD,EAAaxV,MAGzC,MAAO,CACL0S,MAAO,WACLzS,EAAMgT,gBACFjB,GACFA,EAAOU,SAGXiD,iBAAkB,SAAUvuB,GAC1BouB,EAAcpuB,EACV4qB,GACFA,EAAO2D,iBAAiBvuB,MCjCnB,MAAMiwB,GAKnB,YACElV,EACAmV,EACAC,GAEAryB,KAAKid,KAAOA,EACZjd,KAAKoyB,WAAaA,EAClBpyB,KAAKqyB,YAAcA,EAGrB,cAEE,OADaryB,KAAKid,OAASjd,KAAKoyB,WAAapyB,KAAKqyB,aACpC5N,cAGhB,QAAQ6L,EAAqBxV,GAE3B,OADa9a,KAAKid,OAASjd,KAAKoyB,WAAapyB,KAAKqyB,aACpChF,QAAQiD,EAAaxV,ICxBxB,MAAMwX,GAGnB,YAAYvF,GACV/sB,KAAK+sB,SAAWA,EAGlB,cACE,OAAO/sB,KAAK+sB,SAAStI,cAGvB,QAAQ6L,EAAqBxV,GAC3B,IAAIgS,EAAS9sB,KAAK+sB,SAASM,QACzBiD,GACA,SAAU5O,EAAO0L,GACXA,GACFN,EAAOU,QAET1S,EAAS4G,EAAO0L,MAGpB,OAAON,GCdX,SAASyF,GAAqBxF,GAC5B,OAAO,WACL,OAAOA,EAAStI,eAIpB,IAsIe,GAtIU,SACvBuF,EACAwI,EACAC,GAEA,IAAIC,EAAiD,GAErD,SAASC,EACPhyB,EACA4iB,EACAzB,EACAC,EACAyD,GAEA,IAAInG,EAAYoT,EACdzI,EACArpB,EACA4iB,EACAzB,EACAC,EACAyD,GAKF,OAFAkN,EAAkB/xB,GAAQ0e,EAEnBA,EAGT,IA+EIuT,EA/EAC,EAA8B/xB,OAAOgpB,OAAO,GAAI0I,EAAa,CAC/D1S,WAAYkK,EAAO8I,OAAS,IAAM9I,EAAO1L,OACzCuB,QAASmK,EAAO8I,OAAS,IAAM9I,EAAOzL,QACtCK,SAAUoL,EAAOxL,SAEfuU,EAA+B,EAAmB,GAAIF,EAAY,CACpEjT,QAAQ,IAENoT,EAAgClyB,OAAOgpB,OAAO,GAAI0I,EAAa,CACjE1S,WAAYkK,EAAOvL,SAAW,IAAMuL,EAAOtL,SAC3CmB,QAASmK,EAAOvL,SAAW,IAAMuL,EAAOrL,UACxCC,SAAUoL,EAAOpL,WAEfqU,EAAW,CACb/C,MAAM,EACNE,QAAS,KACTC,aAAc,KAGZ6C,EAAa,IAAI,GAAiB,CACpCzN,aAAc,IACdC,aAAcsE,EAAOhL,kBAEnBmU,EAAoB,IAAI,GAAiB,CAC3CnD,MAAO,EACPvK,aAAc,IACdC,aAAcsE,EAAOhL,kBAGnBoU,EAAeT,EACjB,KACA,KACA,EACAE,EACAK,GAEEG,EAAgBV,EAClB,MACA,KACA,EACAI,EACAG,GAEEI,EAA0BX,EAC5B,gBACA,gBACA,EACAK,EACAG,GAEEI,EAAwBZ,EAC1B,cACA,cACA,EACAK,GAGEQ,EAAU,IAAI,GAAmB,CAACJ,GAAeH,GACjDQ,EAAW,IAAI,GAAmB,CAACJ,GAAgBJ,GACnDS,EAAiB,IAAI,GACvB,CAACJ,GACDL,GAEEU,EAAe,IAAI,GAAmB,CAACJ,GAAwBN,GAE/DW,EAAY,IAAI,GAClB,CACE,IAAIzB,GACFI,GAAqBmB,GACrB,IAAI,GAA0B,CAC5BA,EACA,IAAI,GAAgBC,EAAc,CAAE9Y,MAAO,QAE7C8Y,IAGJV,GAiBF,OAZEL,EADEJ,EAAY5S,OACD,IAAI,GAA0B,CACzC4T,EACA,IAAI,GAAgBI,EAAW,CAAE/Y,MAAO,QAG7B,IAAI,GAA0B,CACzC2Y,EACA,IAAI,GAAgBC,EAAU,CAAE5Y,MAAO,MACvC,IAAI,GAAgB+Y,EAAW,CAAE/Y,MAAO,QAIrC,IAAI,GACT,IAAIyX,GACF,IAAIH,GAAWI,GAAqBa,GAAeR,EAAYgB,IAEjElB,EACA,CACExB,IAAK,KACL/O,SAAUqQ,EAAYrQ,SACtBvC,OAAQ4S,EAAY5S,UC9IX,MAAM,WAAoB,EAQvC,YAAYiC,EAAqBlE,EAAgB8E,GAC/CvH,QACAlb,KAAK6hB,MAAQA,EACb7hB,KAAK2d,OAASA,EACd3d,KAAKyiB,IAAMA,EAGb,MAAMoR,GACJ7zB,KAAK8zB,SAAW,EAChB9zB,KAAK+zB,IAAM/zB,KAAK6hB,MAAMmS,WAAWh0B,MAEjCA,KAAKi0B,SAAW,KACdj0B,KAAKgjB,SAEP,GAAQkR,kBAAkBl0B,KAAKi0B,UAE/Bj0B,KAAK+zB,IAAIvc,KAAKxX,KAAK2d,OAAQ3d,KAAKyiB,KAAK,GAEjCziB,KAAK+zB,IAAII,kBACXn0B,KAAK+zB,IAAII,iBAAiB,eAAgB,oBAE5Cn0B,KAAK+zB,IAAI9Q,KAAK4Q,GAGhB,QACM7zB,KAAKi0B,WACP,GAAQG,qBAAqBp0B,KAAKi0B,UAClCj0B,KAAKi0B,SAAW,MAEdj0B,KAAK+zB,MACP/zB,KAAK6hB,MAAMwS,aAAar0B,KAAK+zB,KAC7B/zB,KAAK+zB,IAAM,MAIf,QAAQxL,EAAgB/lB,GACtB,OAAa,CACX,IAAI8xB,EAAQt0B,KAAKu0B,cAAc/xB,GAC/B,IAAI8xB,EAGF,MAFAt0B,KAAKsjB,KAAK,QAAS,CAAEiF,OAAQA,EAAQ/lB,KAAM8xB,IAK3Ct0B,KAAKw0B,gBAAgBhyB,IACvBxC,KAAKsjB,KAAK,mBAIN,cAAcxZ,GACpB,IAAI2qB,EAAa3qB,EAAO8R,MAAM5b,KAAK8zB,UAC/BY,EAAoBD,EAAW/X,QAAQ,MAE3C,OAA2B,IAAvBgY,GACF10B,KAAK8zB,UAAYY,EAAoB,EAC9BD,EAAW7Y,MAAM,EAAG8Y,IAGpB,KAIH,gBAAgB5qB,GACtB,OAAO9J,KAAK8zB,WAAahqB,EAAOxH,QAAUwH,EAAOxH,OAzE3B,QCL1B,IAAKqyB,IAAL,SAAKA,GACH,+BACA,mBACA,uBAHF,CAAKA,QAAK,KAMK,UCGXC,GAAgB,EA0LpB,SAASC,GAAapS,GACpB,IAAIqS,GAAkC,IAAtBrS,EAAI/F,QAAQ,KAAc,IAAM,IAChD,OAAO+F,EAAMqS,EAAY,OAAQ,IAAIxZ,KAAS,MAAQsZ,KAQxD,SAASG,GAAa7O,GACpB,OAAO,GAAQ8O,UAAU9O,GAaZ,OAhNf,MAaE,YAAYrE,EAAoBY,GAC9BziB,KAAK6hB,MAAQA,EACb7hB,KAAKi1B,QAAUF,GAAa,KAAQ,IAuLxC,SAAsBzyB,GAGpB,IAFA,IAAIsB,EAAS,GAEJxD,EAAI,EAAGA,EAAIkC,EAAQlC,IAC1BwD,EAAOe,KAAKowB,GAAa,IAAIxX,SAAS,KAGxC,OAAO3Z,EAAOgB,KAAK,IA9LyBswB,CAAa,GACvDl1B,KAAKm1B,SA4JT,SAAqB1S,GACnB,IAAI2S,EAAQ,qBAAqBC,KAAK5S,GACtC,MAAO,CACL3K,KAAMsd,EAAM,GACZpV,YAAaoV,EAAM,IAhKHE,CAAY7S,GAC5BziB,KAAKu1B,WAAa,GAAMC,WACxBx1B,KAAKy1B,aAGP,KAAK5B,GACH,OAAO7zB,KAAK01B,QAAQ7X,KAAKzB,UAAU,CAACyX,KAGtC,OACE7zB,KAAK6hB,MAAM8T,cAAc31B,MAG3B,MAAM0jB,EAAWC,GACf3jB,KAAKikB,QAAQP,EAAMC,GAAQ,GAI7B,QAAQkQ,GACN,GAAI7zB,KAAKu1B,aAAe,GAAMK,KAW5B,OAAO,EAVP,IAKE,OAJA,GAAQC,oBACN,OACAhB,IA6IUpS,EA7IcziB,KAAKm1B,SA6IDF,EA7IWj1B,KAAKi1B,QA8I7CxS,EAAI3K,KAAO,IAAMmd,EAAU,eA7I1Ba,MAAMjC,IACD,EACP,MAAOvlB,GACP,OAAO,EAyIf,IAAoBmU,EAAkBwS,EAjIpC,YACEj1B,KAAK+1B,cACL/1B,KAAKy1B,aAIP,QAAQ/R,EAAMC,EAAQC,GACpB5jB,KAAK+1B,cACL/1B,KAAKu1B,WAAa,GAAMS,OACpBh2B,KAAKgkB,SACPhkB,KAAKgkB,QAAQ,CACXN,KAAMA,EACNC,OAAQA,EACRC,SAAUA,IAKR,QAAQ0Q,GAQd,IAAIT,EAPJ,GAAqB,MAAjBS,EAAM/L,OASV,OANIvoB,KAAKu1B,aAAe,GAAMK,MAC5B51B,KAAKqkB,aAIIiQ,EAAM9xB,KAAKoZ,MAAM,EAAG,IAE7B,IAAK,IACHiY,EAAUhW,KAAK2I,MAAM8N,EAAM9xB,KAAKoZ,MAAM,IAAM,MAC5C5b,KAAK8jB,OAAO+P,GACZ,MACF,IAAK,IACHA,EAAUhW,KAAK2I,MAAM8N,EAAM9xB,KAAKoZ,MAAM,IAAM,MAC5C,IAAK,IAAIxb,EAAI,EAAGA,EAAIyzB,EAAQvxB,OAAQlC,IAClCJ,KAAKi2B,QAAQpC,EAAQzzB,IAEvB,MACF,IAAK,IACHyzB,EAAUhW,KAAK2I,MAAM8N,EAAM9xB,KAAKoZ,MAAM,IAAM,QAC5C5b,KAAKi2B,QAAQpC,GACb,MACF,IAAK,IACH7zB,KAAK6hB,MAAMqU,YAAYl2B,MACvB,MACF,IAAK,IACH6zB,EAAUhW,KAAK2I,MAAM8N,EAAM9xB,KAAKoZ,MAAM,IAAM,MAC5C5b,KAAKikB,QAAQ4P,EAAQ,GAAIA,EAAQ,IAAI,IAKnC,OAAO9R,GAqFjB,IAAqBU,EAAa0T,EAC5BC,EArFEp2B,KAAKu1B,aAAe,GAAMC,YACxBzT,GAAWA,EAAQoU,WACrBn2B,KAAKm1B,SAASrd,MAkFD2K,EAlFoBziB,KAAKm1B,SAASrd,KAkFrBqe,EAlF2BpU,EAAQoU,UAmF/DC,EAAW,oCAAoCf,KAAK5S,IACxC,GAAK0T,EAAWC,EAAS,KAlFrCp2B,KAAKu1B,WAAa,GAAMK,KAEpB51B,KAAKqjB,QACPrjB,KAAKqjB,UAGPrjB,KAAKikB,QAAQ,KAAM,uBAAuB,GAItC,QAAQ0C,GACV3mB,KAAKu1B,aAAe,GAAMK,MAAQ51B,KAAKkkB,WACzClkB,KAAKkkB,UAAU,CAAE1hB,KAAMmkB,IAInB,aACF3mB,KAAKokB,YACPpkB,KAAKokB,aAID,QAAQ1C,GACV1hB,KAAK+jB,SACP/jB,KAAK+jB,QAAQrC,GAIT,aACN1hB,KAAKq2B,OAAS,GAAQR,oBACpB,OACAhB,GAAa70B,KAAK6hB,MAAMyU,cAAct2B,KAAKm1B,SAAUn1B,KAAKi1B,WAG5Dj1B,KAAKq2B,OAAOz0B,KAAK,QAAU0yB,IACzBt0B,KAAKu2B,QAAQjC,KAEft0B,KAAKq2B,OAAOz0B,KAAK,WAAa2mB,IAC5BvoB,KAAK6hB,MAAM2U,WAAWx2B,KAAMuoB,KAE9BvoB,KAAKq2B,OAAOz0B,KAAK,kBAAmB,KAClC5B,KAAKy2B,cAGP,IACEz2B,KAAKq2B,OAAOP,QACZ,MAAOpU,GACP,EAAKlG,MAAM,KACTxb,KAAK4iB,QAAQlB,GACb1hB,KAAKikB,QAAQ,KAAM,6BAA6B,MAK9C,cACFjkB,KAAKq2B,SACPr2B,KAAKq2B,OAAOK,aACZ12B,KAAKq2B,OAAOrT,QACZhjB,KAAKq2B,OAAS,QChKL,GAfU,CACvBC,cAAe,SAAU7T,EAAKwS,GAC5B,OAAOxS,EAAI3K,KAAO,IAAMmd,EAAU,iBAAmBxS,EAAIzC,aAE3DkW,YAAa,SAAU1T,GACrBA,EAAOkT,QAAQ,OAEjBC,cAAe,SAAUnT,GACvBA,EAAOkT,QAAQ,OAEjBc,WAAY,SAAUhU,EAAQ+F,GAC5B/F,EAAOyB,QAAQ,KAAM,2BAA6BsE,EAAS,KAAK,KCSrD,GAnBU,CACvB+N,cAAe,SAAU7T,EAAkBwS,GACzC,OAAOxS,EAAI3K,KAAO,IAAMmd,EAAU,OAASxS,EAAIzC,aAEjDkW,YAAa,aAGbP,cAAe,SAAUnT,GACvBA,EAAOkT,QAAQ,OAEjBc,WAAY,SAAUhU,EAAQ+F,GACb,MAAXA,EACF/F,EAAOiU,YAEPjU,EAAOyB,QAAQ,KAAM,2BAA6BsE,EAAS,KAAK,KCgBvD,GA7BW,CACxByL,WAAY,SAAUxR,GACpB,IACIuR,EAAM,IADQ,GAAQ4C,aAmB1B,OAjBA5C,EAAI6C,mBAAqB7C,EAAI8C,WAAa,WACxC,OAAQ9C,EAAIwB,YACV,KAAK,EACCxB,EAAI+C,cAAgB/C,EAAI+C,aAAax0B,OAAS,GAChDkgB,EAAO+T,QAAQxC,EAAIxL,OAAQwL,EAAI+C,cAEjC,MACF,KAAK,EAEC/C,EAAI+C,cAAgB/C,EAAI+C,aAAax0B,OAAS,GAChDkgB,EAAO+T,QAAQxC,EAAIxL,OAAQwL,EAAI+C,cAEjCtU,EAAOc,KAAK,WAAYyQ,EAAIxL,QAC5B/F,EAAOQ,UAIN+Q,GAETM,aAAc,SAAUN,GACtBA,EAAI6C,mBAAqB,KACzB7C,EAAIvG,UC+BO,GApDO,CACpBuJ,mBAAA,GACAC,WAA6B,EAC7B/U,+BCRa,WACFjiB,KAENmiB,SAASmC,KAFHtkB,KAGJwjB,qBAAqB,CACxBnE,UAJOrf,KAISW,MAJTX,KAIsB+hB,QAAQnC,OAAS,IAAM,OAJ7C5f,KAQF6hB,MAAM8C,gBARJ3kB,KASJ6iB,YAAY,eATR7iB,KAWJikB,WDHPe,YEJsB,CACtB,sBAAsBvC,GACpB,OAAOziB,KAAKi3B,aAAa,GAAgBxU,IAG3C,oBAAoBA,GAClB,OAAOziB,KAAKi3B,aAAa,GAAcxU,IAGzCwU,aAAY,CAACpV,EAAoBY,IACxB,IAAI,GAAWZ,EAAOY,GAG/B,UAAU9E,EAAgB8E,GACxB,OAAOziB,KAAKk3B,cAAc,GAAUvZ,EAAQ8E,IAG9CyU,cAAa,CAACrV,EAAqBlE,EAAgB8E,IAC1C,IAAI,GAAYZ,EAAOlE,EAAQ8E,IFZxC,MAAM0U,GACJA,EAAYC,SAGd,oBAIAC,kBAAiB,IACR,EACL,EAAyB,CAAEpX,GAAI,EAAWA,KAAM,SAAU3e,GACxD,OAAOA,EAAEmjB,YAAY,QAK3B6S,YAAW,IACF,QAGTjS,eAAc,KACL,EAGT,oBAAoB1H,EAAgB8E,GAClC,GAAIziB,KAAKqlB,iBACP,OAAOrlB,KAAKglB,YAAYuS,UAAU5Z,EAAQ8E,GAE1C,KAAM,gDAIV,YAEE,OAAO,IADWziB,KAAK22B,cAIzB,gBAAgBlU,GAEd,OAAO,IADWziB,KAAK4kB,kBAChB,CAAgBnC,IAGzB,kBAAkBiF,KAClB,qBAAqBA,MGjDhB,IAAI,GAAU,IANd,cAAsB,EAC3B,WACE,OAAO,IC+DI,GA3DgB,SAC7B5K,EACA0a,EACAC,EACAC,EACA5c,GAEA,IAAI6c,EAAU,IAAIC,QAGlB,IAAK,IAAIC,KAFTF,EAAQhd,IAAI,eAAgB,qCAEL8c,EAAYE,QACjCA,EAAQhd,IAAIkd,EAAYJ,EAAYE,QAAQE,IAG9C,GAAmC,MAA/BJ,EAAYK,gBAAyB,CACvC,MAAMC,EAAiBN,EAAYK,kBACnC,IAAK,IAAID,KAAcE,EACrBJ,EAAQhd,IAAIkd,EAAYE,EAAeF,IAI3C,IAAIG,EAAOR,EACPS,EAAU,IAAIC,QAAQT,EAAYrY,SAAU,CAC9CuY,UACAK,OACAG,YAAa,cACbxa,OAAQ,SAGV,OAAOya,MAAMH,GACVI,KAAMC,IACL,IAAI,OAAE/P,GAAW+P,EACjB,GAAe,MAAX/P,EAGF,OAAO+P,EAASC,OAElB,MAAM,IAAIjQ,GACRC,EACA,iBAAiBmP,EAAgBna,oDAAoDgL,OAGxF8P,KAAM71B,IACL,IAAIg2B,EACJ,IACEA,EAAa3a,KAAK2I,MAAMhkB,GACxB,MAAO8L,GACP,MAAM,IAAIga,GACR,IACA,sBAAsBoP,EAAgBna,uEAAuE/a,KAGjHsY,EAAS,KAAM0d,KAEhBC,MAAOC,IACN5d,EAAS4d,EAAK,SC1BL,GALK,CAClB/3B,KAAM,MACNknB,SA5Ba,SAAU8Q,EAAwB/Y,GAC/C,OAAO,SAAUpd,EAAWsY,GAC1B,IACI2H,EADS,QAAU7C,EAAS,IAAM,IAAM,OAEhC+Y,EAAOC,MAAQD,EAAO5W,QAAQ6W,MAAQD,EAAO5W,QAAQ9D,KAC7DuZ,EAAQ,EAA6Bh1B,GAGzC41B,MAFA3V,GAAO,MAAgB+U,GAGpBa,KAAMC,IACL,GAAwB,MAApBA,EAAS/P,OACX,KAAM,YAAY+P,EAAS/P,+BAE7B,OAAO+P,EAASO,SAEjBR,KAAK,EAAGO,WACHA,IACFD,EAAOC,KAAOA,KAGjBH,MAAOC,IACN,EAAO3V,MAAM,yBAA0B2V,QClB/C,MACE3B,mBAAkB,GAClBC,WAAU,SACV8B,GAAK,YACLxB,GAAW,eACXjS,GAAc,gBACd+L,GAAe,UACfmG,GAAS,gBACT1S,GAAe,kBACfqP,GAAiB,qBACjBE,GAAoB,+BACpBnS,GAA8B,oBAC9B4T,GAAmB,YACnB7Q,IACE,GAkDW,IC1EV+T,GD0EU,GAhDS,CACtBhC,mBAAkB,GAClBC,WAAU,GACV8B,SACAxB,eACAjS,kBACA+L,mBACAmG,aACA1S,mBACAqP,qBACAE,wBACAnS,kCACA4T,uBACA7Q,eAEA4C,kBAAmB,GAEnBoR,eAAc,KACL,CAAEC,KAAM,KAGjBrU,gBAAe,IACNsU,UAGTvC,UAAS,IACAwC,eAGT1M,WAAU,IACD,GAGTuI,UAAU9O,GAWDnb,KAAKC,OANKouB,WAAW1f,QAAU0f,WAAqB,UACnCvf,gBAAgB,IAAIwf,YAAY,IAAI,GAE1C,WAAK,IAGMnT,KCtEjC,SAAK6S,GACH,qBACA,mBACA,qBAHF,CAAKA,QAAa,KAMH,UCOA,MAAM,GAQnB,YAAYp3B,EAAaszB,EAAiBlT,GACxC/hB,KAAK2B,IAAMA,EACX3B,KAAKi1B,QAAUA,EACfj1B,KAAKs5B,OAAS,GACdt5B,KAAK+hB,QAAUA,GAAW,GAC1B/hB,KAAKu5B,KAAO,EACZv5B,KAAKw5B,SAAW,EAGlB,IAAIC,EAAO9S,GACL8S,GAASz5B,KAAK+hB,QAAQ0X,QACxBz5B,KAAKs5B,OAAO30B,KACV,EAAmB,GAAIgiB,EAAO,CAAE+K,UAAW,EAAKrW,SAE9Crb,KAAK+hB,QAAQ2X,OAAS15B,KAAKs5B,OAAOh3B,OAAStC,KAAK+hB,QAAQ2X,OAC1D15B,KAAKs5B,OAAOK,SAKlB,MAAMhT,GACJ3mB,KAAKshB,IAAI,GAAMsY,MAAOjT,GAGxB,KAAKA,GACH3mB,KAAKshB,IAAI,GAAMuY,KAAMlT,GAGvB,MAAMA,GACJ3mB,KAAKshB,IAAI,GAAMwY,MAAOnT,GAGxB,UACE,OAA8B,IAAvB3mB,KAAKs5B,OAAOh3B,OAGrB,KAAKy3B,EAAQjf,GACX,IAAItY,EAAO,EACT,CACEyyB,QAASj1B,KAAKi1B,QACd+E,OAAQh6B,KAAKu5B,KAAO,EACpB53B,IAAK3B,KAAK2B,IACVs4B,IAAK,KACLC,QAASl6B,KAAK+hB,QAAQmY,QACtBC,QAASn6B,KAAK+hB,QAAQoY,QACtBC,SAAUp6B,KAAK+hB,QAAQqY,SACvBjY,SAAUniB,KAAKs5B,QAEjBt5B,KAAK+hB,QAAQtE,QAaf,OAVAzd,KAAKs5B,OAAS,GACdS,EAAOv3B,EAAM,CAACkf,EAAO9d,KACd8d,GACH1hB,KAAKu5B,OAEHze,GACFA,EAAS4G,EAAO9d,MAIb,EAGT,mBAEE,OADA5D,KAAKw5B,WACEx5B,KAAKw5B,UCvED,MAAM,GAMnB,YACE74B,EACAmhB,EACAzC,EACA0C,GAEA/hB,KAAKW,KAAOA,EACZX,KAAK8hB,SAAWA,EAChB9hB,KAAKqf,UAAYA,EACjBrf,KAAK+hB,QAAUA,GAAW,GAO5B,cACE,OAAO/hB,KAAKqf,UAAUoF,YAAY,CAChC7E,OAAQ5f,KAAK+hB,QAAQnC,SASzB,QAAQ0Q,EAAqBxV,GAC3B,IAAK9a,KAAKykB,cACR,OAAO4V,GAAY,IAAI,GAA8Bvf,GAChD,GAAI9a,KAAK8hB,SAAWwO,EACzB,OAAO+J,GAAY,IAAI,GAAkCvf,GAG3D,IAAIwT,GAAY,EACZjP,EAAYrf,KAAKqf,UAAUwG,iBAC7B7lB,KAAKW,KACLX,KAAK8hB,SACL9hB,KAAK+hB,QAAQpgB,IACb3B,KAAK+hB,SAEHqL,EAAY,KAEZkN,EAAgB,WAClBjb,EAAU2B,OAAO,cAAesZ,GAChCjb,EAAUgO,WAERvJ,EAAS,WACXsJ,EAAY,GAAQyC,gBAAgBxQ,GAAW,SAAUzb,GACvD0qB,GAAY,EACZzK,IACA/I,EAAS,KAAMlX,OAGfgf,EAAU,SAAUlB,GACtBmC,IACA/I,EAAS4G,IAEPqE,EAAW,WAEb,IAAIwU,EADJ1W,IAOA0W,EAAsB,EAA8Blb,GACpDvE,EAAS,IAAI,GAAuByf,KAGlC1W,EAAkB,WACpBxE,EAAU2B,OAAO,cAAesZ,GAChCjb,EAAU2B,OAAO,OAAQ8C,GACzBzE,EAAU2B,OAAO,QAAS4B,GAC1BvD,EAAU2B,OAAO,SAAU+E,IAW7B,OARA1G,EAAUzd,KAAK,cAAe04B,GAC9Bjb,EAAUzd,KAAK,OAAQkiB,GACvBzE,EAAUzd,KAAK,QAASghB,GACxBvD,EAAUzd,KAAK,SAAUmkB,GAGzB1G,EAAU2C,aAEH,CACLwL,MAAO,KACDc,IAGJzK,IACIuJ,EACFA,EAAUpK,QAEV3D,EAAU2D,UAGdyN,iBAAmBvuB,IACbosB,GAGAtuB,KAAK8hB,SAAW5f,IACdkrB,EACFA,EAAUpK,QAEV3D,EAAU2D,YAQtB,SAASqX,GAAY3Y,EAAc5G,GAIjC,OAHA,EAAKU,OAAM,WACTV,EAAS4G,MAEJ,CACL8L,MAAO,aACPiD,iBAAkB,cCnItB,MAAQuG,WAAU,IAAK,GAEhB,ICZKwD,GDYD,GAAkB,SAC3BxQ,EACArpB,EACA4iB,EACAzB,EACAC,EACAyD,GAEA,IAWInG,EAXAob,EAAiB,GAAWlX,GAChC,IAAKkX,EACH,MAAM,IAAI,GAA4BlX,GA0BxC,QAtBIyG,EAAO0Q,oBACuD,IAA9D,EAAyB1Q,EAAO0Q,kBAAmB/5B,IACnDqpB,EAAO2Q,qBACwD,IAA/D,EAAyB3Q,EAAO2Q,mBAAoBh6B,KAItDohB,EAAUjhB,OAAOgpB,OACf,CAAE8Q,iBAAkB5Q,EAAO4Q,kBAC3B7Y,GAGF1C,EAAY,IAAI,GACd1e,EACAmhB,EACA0D,EAAUA,EAAQqV,aAAaJ,GAAkBA,EACjD1Y,IAGF1C,EAAY,GAGPA,GAGL,GAAgC,CAClCoF,YAAa,WACX,OAAO,GAET4I,QAAS,SAAUxQ,EAAG/B,GACpB,IAAIggB,EAAW,EAAKtf,OAAM,WACxBV,EAAS,IAAI,OAEf,MAAO,CACL0S,MAAO,WACLsN,EAAS/M,iBAEX0C,iBAAkB,gBEjBjB,SAASsK,GAAgBhZ,GAC9B,GAAe,MAAXA,EACF,KAAM,kCAER,GAAuB,MAAnBA,EAAQoY,QACV,KAAM,wCAEJ,iBAAkBpY,GACpB,EAAON,KACL,kEDvDN,SAAY+Y,GACV,2CACA,+CAFF,CAAYA,QAAe,KE6DZ,OAtBb/C,IAEA,QAA+D,IAApD,GAAQuB,iBAAiBvB,EAAYpY,WAC9C,KAAM,IAAIoY,EAAYpY,gDAGxB,MAAO,CACL5B,EACA3C,KAEA,MAAM0c,EAvCkB,EAC1B/Z,EACAga,KAEA,IAAID,EAAQ,aAAela,mBAAmBG,EAAO4L,UAErD,IAAK,IAAI1nB,KAAO81B,EAAYha,OAC1B+Z,GACE,IACAla,mBAAmB3b,GACnB,IACA2b,mBAAmBma,EAAYha,OAAO9b,IAG1C,GAAkC,MAA9B81B,EAAYuD,eAAwB,CACtC,IAAIC,EAAgBxD,EAAYuD,iBAChC,IAAK,IAAIr5B,KAAOs5B,EACdzD,GACE,IACAla,mBAAmB3b,GACnB,IACA2b,mBAAmB2d,EAAct5B,IAIvC,OAAO61B,GAcS0D,CAAoBzd,EAAQga,GAE1C,GAAQuB,iBAAiBvB,EAAYpY,WACnC,GACAmY,EACAC,EACA+C,GAAgBW,mBAChBrgB,KCOS,OAtBb2c,IAEA,QAA+D,IAApD,GAAQuB,iBAAiBvB,EAAYpY,WAC9C,KAAM,IAAIoY,EAAYpY,gDAGxB,MAAO,CACL5B,EACA3C,KAEA,MAAM0c,EAzCkB,EAC1B/Z,EACAga,KAEA,IAAID,EAAQ,aAAela,mBAAmBG,EAAO4L,UAIrD,IAAK,IAAI1nB,KAFT61B,GAAS,iBAAmBla,mBAAmBG,EAAOyM,aAEtCuN,EAAYha,OAC1B+Z,GACE,IACAla,mBAAmB3b,GACnB,IACA2b,mBAAmBma,EAAYha,OAAO9b,IAG1C,GAAkC,MAA9B81B,EAAYuD,eAAwB,CACtC,IAAIC,EAAgBxD,EAAYuD,iBAChC,IAAK,IAAIr5B,KAAOs5B,EACdzD,GACE,IACAla,mBAAmB3b,GACnB,IACA2b,mBAAmB2d,EAAct5B,IAIvC,OAAO61B,GAcS,CAAoB/Z,EAAQga,GAE1C,GAAQuB,iBAAiBvB,EAAYpY,WACnC,GACAmY,EACAC,EACA+C,GAAgBY,qBAChBtgB,KCgCN,SAASugB,GAAYC,GACnB,OAAIA,EAAK7c,SACA6c,EAAK7c,SAEV6c,EAAKnB,QACA,UAAUmB,EAAKnB,qBAEjB,EAAS1b,SAGlB,SAAS8c,GAAiBD,GACxB,OAAIA,EAAKxI,OACAwI,EAAKxI,OAMP,MAJ4BwI,EAAKnB,qBAO1C,SAASqB,GAAaF,GACpB,MAA8B,WAA1B,GAAQhE,gBAEiB,IAAlBgE,EAAKG,SASlB,SAASC,GAAqBJ,GAC5B,MAAI,gBAAiBA,EACZA,EAAKK,YAEV,iBAAkBL,IACZA,EAAKM,aAKjB,SAASC,GAAuBP,GAC9B,MAAMnc,EAAqB,OAAH,wBACnB,EAASA,oBACTmc,EAAKnc,oBAEV,MACE,kBAAmBA,GACoB,MAAvCA,EAAkC,cAE3BA,EAAkC,cAGpC,GAAkBA,GA8B3B,SAAS2c,GACPR,EACArS,GAEA,MAAM3J,EA/BR,SAA0Bgc,EAAerS,GACvC,IAAI3J,EAuBJ,MAtBI,yBAA0Bgc,EAC5Bhc,EAAuB,OAAH,wBACf,EAASA,sBACTgc,EAAKhc,uBAGVA,EAAuB,CACrBD,UAAWic,EAAKvc,eAAiB,EAASA,cAC1CK,SAAUkc,EAAKxc,cAAgB,EAASA,cAEtC,SAAUwc,IACR,WAAYA,EAAKhS,OAAMhK,EAAqB7B,OAAS6d,EAAKhS,KAAK7L,QAC/D,YAAa6d,EAAKhS,OACpBhK,EAAqBqY,QAAU2D,EAAKhS,KAAKqO,UAEzC,eAAgB2D,IAClBhc,EAAqByc,cCxIW,EACpC9S,EACAwO,EACAuE,KAEA,MAAMC,EAA2D,CAC/Dld,cAAe0Y,EAAYpY,UAC3BP,aAAc2Y,EAAYrY,SAC1BkK,KAAM,CACJ7L,OAAQga,EAAYha,OACpBka,QAASF,EAAYE,UAGzB,MAAO,CACLla,EACA3C,KAEA,MAAM8L,EAAUqC,EAAOrC,QAAQnJ,EAAOyM,aAKpC8R,EAA2BpV,EAASqV,GACpBpS,UAAUpM,EAAO4L,SAAUvO,KDiHNohB,CACnCjT,EACA3J,EACAgc,EAAKa,cAGJ7c,EAOsB8c,CAAiBd,EAAMrS,GACpD,MACE,kBAAmB3J,GACsB,MAAzCA,EAAoC,cAE7BA,EAAoC,cAGtC,GAAkBA,GEvLZ,MAAM,WAAwB,EAG3C,YAAmB2J,GACjB/N,OAAM,SAAU4F,EAAWte,GACzB,EAAOugB,MAAM,wCAAwCjC,MAGvD9gB,KAAKipB,OAASA,EACdjpB,KAAKq8B,6BAGP,YAAY3V,GACVA,EAAYlkB,KAAK82B,OAAOgD,QAASC,IAC/Bv8B,KAAKsjB,KAAKiZ,EAAe57B,KAAM47B,KAI3B,6BACNv8B,KAAKipB,OAAOrD,WAAWhkB,KAAK,UAAY8kB,IAEpB,qCADFA,EAAYC,OAE1B3mB,KAAK4rB,YAAYlF,MCjBV,OATf,WACE,IAAI8V,EAASC,EAKb,MAAO,CAAEC,QAJO,IAAIC,QAAQ,CAACC,EAAKC,KAChCL,EAAUI,EACVH,EAASI,IAEOL,UAASC,WCKd,MAAM,WAAmB,EAStC,YAAmBxT,GACjB/N,OAAM,SAAU4F,EAAWte,GACzB,EAAOugB,MAAM,4BAA8BjC,MAT/C,KAAAgc,kBAA4B,EAC5B,KAAA5R,UAAiB,KACjB,KAAA6R,oBAA+B,KAC/B,KAAA9R,kBAAkC,KAE1B,KAAA+R,mBAA+B,KA8D/B,KAAAC,aAA2C,CACjDvE,EACA7N,KAEA,GAAI6N,EAGF,OAFA,EAAOjX,KAAK,wBAAwBiX,QACpC14B,KAAKk9B,WAIPl9B,KAAKipB,OAAO5B,WAAW,gBAAiB,CACtCiC,KAAMuB,EAASvB,KACf4B,UAAWL,EAASK,aApEtBlrB,KAAKipB,OAASA,EACdjpB,KAAKipB,OAAOrD,WAAWhkB,KAAK,eAAgB,EAAGqtB,WAAUC,cACtC,cAAbD,GAAwC,cAAZC,GAC9BlvB,KAAKm9B,UAEU,cAAblO,GAAwC,cAAZC,IAC9BlvB,KAAKk9B,WACLl9B,KAAKo9B,+BAITp9B,KAAKq9B,UAAY,IAAI,GAAgBpU,GAErCjpB,KAAKipB,OAAOrD,WAAWhkB,KAAK,UAAY+kB,IAEpB,0BADFA,EAAMA,OAEpB3mB,KAAKs9B,iBAAiB3W,EAAMnkB,MAG5BxC,KAAK+8B,qBACL/8B,KAAK+8B,oBAAoBp8B,OAASgmB,EAAMC,SAExC5mB,KAAK+8B,oBAAoBnR,YAAYjF,KAKpC,SACD3mB,KAAK88B,mBAIT98B,KAAK88B,kBAAmB,EACxB98B,KAAKm9B,WAGC,UACDn9B,KAAK88B,mBAIV98B,KAAKo9B,4BAEgC,cAAjCp9B,KAAKipB,OAAOrD,WAAW1D,OAK3BliB,KAAKipB,OAAOe,OAAOuT,kBACjB,CACElU,SAAUrpB,KAAKipB,OAAOrD,WAAWsB,WAEnClnB,KAAKi9B,eAsBD,iBAAiBz6B,GACvB,IACExC,KAAKkrB,UAAYrN,KAAK2I,MAAMhkB,EAAK0oB,WACjC,MAAO5c,GAGP,OAFA,EAAOoT,MAAM,0CAA0Clf,EAAK0oB,gBAC5DlrB,KAAKk9B,WAIP,GAAiC,iBAAtBl9B,KAAKkrB,UAAU9I,IAAyC,KAAtBpiB,KAAKkrB,UAAU9I,GAK1D,OAJA,EAAOV,MACL,+CAA+C1hB,KAAKkrB,gBAEtDlrB,KAAKk9B,WAKPl9B,KAAKg9B,qBACLh9B,KAAKw9B,qBAGC,qBAYNx9B,KAAK+8B,oBAAsB,IAAI,GAC7B,mBAAmB/8B,KAAKkrB,UAAU9I,GAClCpiB,KAAKipB,QAEPjpB,KAAK+8B,oBAAoBU,YAAY,CAAC3c,EAAWte,KAEH,IAA1Cse,EAAUpE,QAAQ,qBACe,IAAjCoE,EAAUpE,QAAQ,YAKpB1c,KAAKsjB,KAAKxC,EAAWte,KAvBG,CAACokB,IACrBA,EAAQuC,qBAAuBvC,EAAQwC,sBACzCxC,EAAQ8W,wBAEP9W,EAAQuC,qBACwB,cAAjCnpB,KAAKipB,OAAOrD,WAAW1D,OAEvB0E,EAAQ+W,aAkBZC,CAAkB59B,KAAK+8B,qBAGjB,WACN/8B,KAAKkrB,UAAY,KACblrB,KAAK+8B,sBACP/8B,KAAK+8B,oBAAoBrG,aACzB12B,KAAK+8B,oBAAoBtR,aACzBzrB,KAAK+8B,oBAAsB,MAGzB/8B,KAAK88B,kBAGP98B,KAAKg9B,qBAID,4BACN,IAAKh9B,KAAK88B,iBACR,OAIF,GAAI98B,KAAKirB,oBAAuBjrB,KAAKirB,kBAA0B4S,KAC7D,OAKF,MAAM,QAAEnB,EAAO,QAAEF,EAASC,OAAQ5f,GAAM,KACvC6f,EAAgBmB,MAAO,EACxB,MAAMC,EAAU,KACbpB,EAAgBmB,MAAO,GAE1BnB,EAAQrE,KAAKyF,GAASrF,MAAMqF,GAC5B99B,KAAKirB,kBAAoByR,EACzB18B,KAAKg9B,mBAAqBR,GC/J9B,MAAqB,GAYnB,eACE,GAAOuB,SAAU,EACjB,IAAK,IAAI39B,EAAI,EAAGC,EAAI,GAAO29B,UAAU17B,OAAQlC,EAAIC,EAAGD,IAClD,GAAO49B,UAAU59B,GAAGitB,UAMhB,2BACN,OAAO,EACL,EAAyB,CAAEpN,GAAI,GAAQ+W,WAAW/W,KAAM,SAAU3e,GAChE,OAAOA,EAAEmjB,YAAY,QAgB3B,YAAYwZ,EAAiBlc,IAsL/B,SAAqBpgB,GACnB,GAAIA,QACF,KAAM,0DAvLNu8B,CAAYD,GACZlD,GAAgBhZ,GAChB/hB,KAAK2B,IAAMs8B,EACXj+B,KAAKgqB,OLfF,SAAmBsR,EAAerS,GACvC,IAAIe,EAAiB,CACnBhL,gBAAiBsc,EAAKtc,iBAAmB,EAASA,gBAClDmb,QAASmB,EAAKnB,QACdvb,SAAU0c,EAAK1c,UAAY,EAASA,SACpCF,SAAU4c,EAAK5c,UAAY,EAASA,SACpCC,UAAW2c,EAAK3c,WAAa,EAASA,UACtCM,YAAaqc,EAAKrc,aAAe,EAASA,YAC1Ckf,UAAW7C,EAAK6C,WAAa,EAAStf,WACtCK,mBAAoBoc,EAAKpc,oBAAsB,EAASA,mBACxDV,OAAQ8c,EAAK9c,QAAU,EAASA,OAChCF,OAAQgd,EAAKhd,QAAU,EAASA,OAChCC,QAAS+c,EAAK/c,SAAW,EAASA,QAElCod,YAAaD,GAAqBJ,GAClC7c,SAAU4c,GAAYC,GACtB1b,OAAQ4b,GAAaF,GACrBxI,OAAQyI,GAAiBD,GAEzBiC,kBAAmB1B,GAAuBP,GAC1CrR,kBAAmB6R,GAAuBR,EAAMrS,IAclD,MAXI,uBAAwBqS,IAC1BtR,EAAO2Q,mBAAqBW,EAAKX,oBAC/B,sBAAuBW,IACzBtR,EAAO0Q,kBAAoBY,EAAKZ,mBAC9B,qBAAsBY,IACxBtR,EAAO4Q,iBAAmBU,EAAKV,kBAC7B,mBAAoBU,IAAMtR,EAAOoU,eAAiB9C,EAAK8C,gBACvD,SAAU9C,IACZtR,EAAOnlB,KAAOy2B,EAAKz2B,MAGdmlB,EKnBSqU,CAAUtc,EAAS/hB,MAEjCA,KAAKmvB,SAAW,GAAQO,iBACxB1vB,KAAKs+B,eAAiB,IAAI,EAC1Bt+B,KAAKu+B,UAAY,GAAQvJ,UAAU,KAEnCh1B,KAAKmiB,SAAW,IAAI,GAASniB,KAAK2B,IAAK3B,KAAKu+B,UAAW,CACrDpE,QAASn6B,KAAKgqB,OAAOmQ,QACrBC,SAAU,GAAO/C,oBACjB5Z,OAAQzd,KAAKgqB,OAAOoU,gBAAkB,GACtC1E,MAAO,GACPD,MAAO,GAAcI,KACrBK,QAAS,EAAS9b,UAEhBpe,KAAKgqB,OAAO2R,cACd37B,KAAKw+B,eAAiB,GAAQ5O,qBAAqB5vB,KAAKmiB,SAAU,CAChEyW,KAAM54B,KAAKgqB,OAAOmU,UAClBlgB,KAAM,gBAAkB,GAAQ2J,kBAAkBjnB,QAQtDX,KAAK4lB,WAAa,GAAQ+J,wBAAwB3vB,KAAK2B,IAAK,CAC1DisB,YALiB7L,GACV,GAAQgV,mBAAmB/2B,KAAKgqB,OAAQjI,EAAS,IAKxDI,SAAUniB,KAAKmiB,SACfnD,gBAAiBhf,KAAKgqB,OAAOhL,gBAC7BC,YAAajf,KAAKgqB,OAAO/K,YACzBC,mBAAoBlf,KAAKgqB,OAAO9K,mBAChCU,OAAQzC,QAAQnd,KAAKgqB,OAAOpK,UAG9B5f,KAAK4lB,WAAWhkB,KAAK,YAAa,KAChC5B,KAAKy+B,eACDz+B,KAAKw+B,gBACPx+B,KAAKw+B,eAAevb,KAAKjjB,KAAK4lB,WAAW8Y,gBAI7C1+B,KAAK4lB,WAAWhkB,KAAK,UAAY+kB,IAC/B,IACIgY,EAAqD,IADzChY,EAAMA,MACGjK,QAAQ,oBACjC,GAAIiK,EAAMC,QAAS,CACjB,IAAIA,EAAU5mB,KAAK4mB,QAAQD,EAAMC,SAC7BA,GACFA,EAAQgF,YAAYjF,GAInBgY,GACH3+B,KAAKs+B,eAAehb,KAAKqD,EAAMA,MAAOA,EAAMnkB,QAGhDxC,KAAK4lB,WAAWhkB,KAAK,aAAc,KACjC5B,KAAKmvB,SAAS1D,eAEhBzrB,KAAK4lB,WAAWhkB,KAAK,eAAgB,KACnC5B,KAAKmvB,SAAS1D,eAEhBzrB,KAAK4lB,WAAWhkB,KAAK,QAAU82B,IAC7B,EAAOjX,KAAKiX,KAGd,GAAOsF,UAAUr5B,KAAK3E,MACtBA,KAAKmiB,SAASmC,KAAK,CAAE0Z,UAAW,GAAOA,UAAU17B,SAEjDtC,KAAKgrB,KAAO,IAAI,GAAWhrB,MAEvB,GAAO+9B,SACT/9B,KAAKqtB,UAIT,QAAQ1sB,GACN,OAAOX,KAAKmvB,SAASyP,KAAKj+B,GAG5B,cACE,OAAOX,KAAKmvB,SAAS0P,MAGvB,UAGE,GAFA7+B,KAAK4lB,WAAWyH,UAEZrtB,KAAKw+B,iBACFx+B,KAAK8+B,oBAAqB,CAC7B,IAAI7S,EAAWjsB,KAAK4lB,WAAW8Y,aAC3BF,EAAiBx+B,KAAKw+B,eAC1Bx+B,KAAK8+B,oBAAsB,IAAI,EAAc,KAAO,WAClDN,EAAevb,KAAKgJ,OAM5B,aACEjsB,KAAK4lB,WAAW6F,aAEZzrB,KAAK8+B,sBACP9+B,KAAK8+B,oBAAoB/Q,gBACzB/tB,KAAK8+B,oBAAsB,MAI/B,KAAKC,EAAoBjkB,EAAoBgC,GAE3C,OADA9c,KAAKs+B,eAAe18B,KAAKm9B,EAAYjkB,EAAUgC,GACxC9c,KAGT,OAAO++B,EAAqBjkB,EAAqBgC,GAE/C,OADA9c,KAAKs+B,eAAetd,OAAO+d,EAAYjkB,EAAUgC,GAC1C9c,KAGT,YAAY8a,GAEV,OADA9a,KAAKs+B,eAAeb,YAAY3iB,GACzB9a,KAGT,cAAc8a,GAEZ,OADA9a,KAAKs+B,eAAerd,cAAcnG,GAC3B9a,KAGT,WAAW8a,GAET,OADA9a,KAAKs+B,eAAe5H,aACb12B,KAGT,eACE,IAAIkqB,EACJ,IAAKA,KAAelqB,KAAKmvB,SAASA,SAC5BnvB,KAAKmvB,SAASA,SAASltB,eAAeioB,IACxClqB,KAAK29B,UAAUzT,GAKrB,UAAU8U,GACR,IAAIpY,EAAU5mB,KAAKmvB,SAASnb,IAAIgrB,EAAch/B,MAS9C,OARI4mB,EAAQuC,qBAAuBvC,EAAQwC,sBACzCxC,EAAQ8W,wBAEP9W,EAAQuC,qBACiB,cAA1BnpB,KAAK4lB,WAAW1D,OAEhB0E,EAAQ+W,YAEH/W,EAGT,YAAYoY,GACV,IAAIpY,EAAU5mB,KAAKmvB,SAASyP,KAAKI,GAC7BpY,GAAWA,EAAQuC,oBACrBvC,EAAQqY,sBAERrY,EAAU5mB,KAAKmvB,SAASpO,OAAOie,KAChBpY,EAAQsC,YACrBtC,EAAQ8C,cAKd,WAAWqV,EAAoBv8B,EAAWokB,GACxC,OAAO5mB,KAAK4lB,WAAWyB,WAAW0X,EAAYv8B,EAAMokB,GAGtD,eACE,OAAO5mB,KAAKgqB,OAAOpK,OAGrB,SACE5f,KAAKgrB,KAAKkU,UAxNL,GAAAlB,UAAsB,GACtB,GAAAD,SAAmB,EACnB,GAAAnc,cAAwB,EAGxB,GAAAud,QAA2B,GAC3B,GAAAC,gBAA6B,GAASA,gBACtC,GAAAC,sBAAmC,GAASA,sBAC5C,GAAAC,eAA4B,GAASA,eAVzB,UAoOrB,GAAQxG,MAAM,I,YCxPC,MAAM,WAA6B,GAChD,YAAYmF,EAAiBlc,GAC3B,GAAOH,aAAe,GAAqBA,aAC3C,GAAON,IAAM,GAAqBA,IAElCyZ,GAAgBhZ,GAChBA,EAAQld,KAAO,GACfqW,MAAM+iB,EAASlc", "file": "pusher-with-encryption.worker.min.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"Pusher\"] = factory();\n\telse\n\t\troot[\"Pusher\"] = factory();\n})(this, function() {\nreturn ", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 3);\n", "// Copyright (C) 2016 <PERSON>\n// MIT License. See LICENSE file for details.\n\n/**\n * Package base64 implements Base64 encoding and decoding.\n */\n\n// Invalid character used in decoding to indicate\n// that the character to decode is out of range of\n// alphabet and cannot be decoded.\nconst INVALID_BYTE = 256;\n\n/**\n * Implements standard Base64 encoding.\n *\n * Operates in constant time.\n */\nexport class Coder {\n    // TODO(dchest): methods to encode chunk-by-chunk.\n\n    constructor(private _padding<PERSON>haracter = \"=\") { }\n\n    encodedLength(length: number): number {\n        if (!this._padding<PERSON>haracter) {\n            return (length * 8 + 5) / 6 | 0;\n        }\n        return (length + 2) / 3 * 4 | 0;\n    }\n\n    encode(data: Uint8Array): string {\n        let out = \"\";\n\n        let i = 0;\n        for (; i < data.length - 2; i += 3) {\n            let c = (data[i] << 16) | (data[i + 1] << 8) | (data[i + 2]);\n            out += this._encodeByte((c >>> 3 * 6) & 63);\n            out += this._encodeByte((c >>> 2 * 6) & 63);\n            out += this._encodeByte((c >>> 1 * 6) & 63);\n            out += this._encodeByte((c >>> 0 * 6) & 63);\n        }\n\n        const left = data.length - i;\n        if (left > 0) {\n            let c = (data[i] << 16) | (left === 2 ? data[i + 1] << 8 : 0);\n            out += this._encodeByte((c >>> 3 * 6) & 63);\n            out += this._encodeByte((c >>> 2 * 6) & 63);\n            if (left === 2) {\n                out += this._encodeByte((c >>> 1 * 6) & 63);\n            } else {\n                out += this._paddingCharacter || \"\";\n            }\n            out += this._paddingCharacter || \"\";\n        }\n\n        return out;\n    }\n\n    maxDecodedLength(length: number): number {\n        if (!this._paddingCharacter) {\n            return (length * 6 + 7) / 8 | 0;\n        }\n        return length / 4 * 3 | 0;\n    }\n\n    decodedLength(s: string): number {\n        return this.maxDecodedLength(s.length - this._getPaddingLength(s));\n    }\n\n    decode(s: string): Uint8Array {\n        if (s.length === 0) {\n            return new Uint8Array(0);\n        }\n        const paddingLength = this._getPaddingLength(s);\n        const length = s.length - paddingLength;\n        const out = new Uint8Array(this.maxDecodedLength(length));\n        let op = 0;\n        let i = 0;\n        let haveBad = 0;\n        let v0 = 0, v1 = 0, v2 = 0, v3 = 0;\n        for (; i < length - 4; i += 4) {\n            v0 = this._decodeChar(s.charCodeAt(i + 0));\n            v1 = this._decodeChar(s.charCodeAt(i + 1));\n            v2 = this._decodeChar(s.charCodeAt(i + 2));\n            v3 = this._decodeChar(s.charCodeAt(i + 3));\n            out[op++] = (v0 << 2) | (v1 >>> 4);\n            out[op++] = (v1 << 4) | (v2 >>> 2);\n            out[op++] = (v2 << 6) | v3;\n            haveBad |= v0 & INVALID_BYTE;\n            haveBad |= v1 & INVALID_BYTE;\n            haveBad |= v2 & INVALID_BYTE;\n            haveBad |= v3 & INVALID_BYTE;\n        }\n        if (i < length - 1) {\n            v0 = this._decodeChar(s.charCodeAt(i));\n            v1 = this._decodeChar(s.charCodeAt(i + 1));\n            out[op++] = (v0 << 2) | (v1 >>> 4);\n            haveBad |= v0 & INVALID_BYTE;\n            haveBad |= v1 & INVALID_BYTE;\n        }\n        if (i < length - 2) {\n            v2 = this._decodeChar(s.charCodeAt(i + 2));\n            out[op++] = (v1 << 4) | (v2 >>> 2);\n            haveBad |= v2 & INVALID_BYTE;\n        }\n        if (i < length - 3) {\n            v3 = this._decodeChar(s.charCodeAt(i + 3));\n            out[op++] = (v2 << 6) | v3;\n            haveBad |= v3 & INVALID_BYTE;\n        }\n        if (haveBad !== 0) {\n            throw new Error(\"Base64Coder: incorrect characters for decoding\");\n        }\n        return out;\n    }\n\n    // Standard encoding have the following encoded/decoded ranges,\n    // which we need to convert between.\n    //\n    // ABCDEFGHIJKLMNOPQRSTUVWXYZ abcdefghijklmnopqrstuvwxyz 0123456789  +   /\n    // Index:   0 - 25                    26 - 51              52 - 61   62  63\n    // ASCII:  65 - 90                    97 - 122             48 - 57   43  47\n    //\n\n    // Encode 6 bits in b into a new character.\n    protected _encodeByte(b: number): string {\n        // Encoding uses constant time operations as follows:\n        //\n        // 1. Define comparison of A with B using (A - B) >>> 8:\n        //          if A > B, then result is positive integer\n        //          if A <= B, then result is 0\n        //\n        // 2. Define selection of C or 0 using bitwise AND: X & C:\n        //          if X == 0, then result is 0\n        //          if X != 0, then result is C\n        //\n        // 3. Start with the smallest comparison (b >= 0), which is always\n        //    true, so set the result to the starting ASCII value (65).\n        //\n        // 4. Continue comparing b to higher ASCII values, and selecting\n        //    zero if comparison isn't true, otherwise selecting a value\n        //    to add to result, which:\n        //\n        //          a) undoes the previous addition\n        //          b) provides new value to add\n        //\n        let result = b;\n        // b >= 0\n        result += 65;\n        // b > 25\n        result += ((25 - b) >>> 8) & ((0 - 65) - 26 + 97);\n        // b > 51\n        result += ((51 - b) >>> 8) & ((26 - 97) - 52 + 48);\n        // b > 61\n        result += ((61 - b) >>> 8) & ((52 - 48) - 62 + 43);\n        // b > 62\n        result += ((62 - b) >>> 8) & ((62 - 43) - 63 + 47);\n\n        return String.fromCharCode(result);\n    }\n\n    // Decode a character code into a byte.\n    // Must return 256 if character is out of alphabet range.\n    protected _decodeChar(c: number): number {\n        // Decoding works similar to encoding: using the same comparison\n        // function, but now it works on ranges: result is always incremented\n        // by value, but this value becomes zero if the range is not\n        // satisfied.\n        //\n        // Decoding starts with invalid value, 256, which is then\n        // subtracted when the range is satisfied. If none of the ranges\n        // apply, the function returns 256, which is then checked by\n        // the caller to throw error.\n        let result = INVALID_BYTE; // start with invalid character\n\n        // c == 43 (c > 42 and c < 44)\n        result += (((42 - c) & (c - 44)) >>> 8) & (-INVALID_BYTE + c - 43 + 62);\n        // c == 47 (c > 46 and c < 48)\n        result += (((46 - c) & (c - 48)) >>> 8) & (-INVALID_BYTE + c - 47 + 63);\n        // c > 47 and c < 58\n        result += (((47 - c) & (c - 58)) >>> 8) & (-INVALID_BYTE + c - 48 + 52);\n        // c > 64 and c < 91\n        result += (((64 - c) & (c - 91)) >>> 8) & (-INVALID_BYTE + c - 65 + 0);\n        // c > 96 and c < 123\n        result += (((96 - c) & (c - 123)) >>> 8) & (-INVALID_BYTE + c - 97 + 26);\n\n        return result;\n    }\n\n    private _getPaddingLength(s: string): number {\n        let paddingLength = 0;\n        if (this._paddingCharacter) {\n            for (let i = s.length - 1; i >= 0; i--) {\n                if (s[i] !== this._paddingCharacter) {\n                    break;\n                }\n                paddingLength++;\n            }\n            if (s.length < 4 || paddingLength > 2) {\n                throw new Error(\"Base64Coder: incorrect padding\");\n            }\n        }\n        return paddingLength;\n    }\n\n}\n\nconst stdCoder = new Coder();\n\nexport function encode(data: Uint8Array): string {\n    return stdCoder.encode(data);\n}\n\nexport function decode(s: string): Uint8Array {\n    return stdCoder.decode(s);\n}\n\n/**\n * Implements URL-safe Base64 encoding.\n * (Same as Base64, but '+' is replaced with '-', and '/' with '_').\n *\n * Operates in constant time.\n */\nexport class URLSafeCoder extends Coder {\n    // URL-safe encoding have the following encoded/decoded ranges:\n    //\n    // ABCDEFGHIJKLMNOPQRSTUVWXYZ abcdefghijklmnopqrstuvwxyz 0123456789  -   _\n    // Index:   0 - 25                    26 - 51              52 - 61   62  63\n    // ASCII:  65 - 90                    97 - 122             48 - 57   45  95\n    //\n\n    protected _encodeByte(b: number): string {\n        let result = b;\n        // b >= 0\n        result += 65;\n        // b > 25\n        result += ((25 - b) >>> 8) & ((0 - 65) - 26 + 97);\n        // b > 51\n        result += ((51 - b) >>> 8) & ((26 - 97) - 52 + 48);\n        // b > 61\n        result += ((61 - b) >>> 8) & ((52 - 48) - 62 + 45);\n        // b > 62\n        result += ((62 - b) >>> 8) & ((62 - 45) - 63 + 95);\n\n        return String.fromCharCode(result);\n    }\n\n    protected _decodeChar(c: number): number {\n        let result = INVALID_BYTE;\n\n        // c == 45 (c > 44 and c < 46)\n        result += (((44 - c) & (c - 46)) >>> 8) & (-INVALID_BYTE + c - 45 + 62);\n        // c == 95 (c > 94 and c < 96)\n        result += (((94 - c) & (c - 96)) >>> 8) & (-INVALID_BYTE + c - 95 + 63);\n        // c > 47 and c < 58\n        result += (((47 - c) & (c - 58)) >>> 8) & (-INVALID_BYTE + c - 48 + 52);\n        // c > 64 and c < 91\n        result += (((64 - c) & (c - 91)) >>> 8) & (-INVALID_BYTE + c - 65 + 0);\n        // c > 96 and c < 123\n        result += (((96 - c) & (c - 123)) >>> 8) & (-INVALID_BYTE + c - 97 + 26);\n\n        return result;\n    }\n}\n\nconst urlSafeCoder = new URLSafeCoder();\n\nexport function encodeURLSafe(data: Uint8Array): string {\n    return urlSafeCoder.encode(data);\n}\n\nexport function decodeURLSafe(s: string): Uint8Array {\n    return urlSafeCoder.decode(s);\n}\n\n\nexport const encodedLength = (length: number) =>\n    stdCoder.encodedLength(length);\n\nexport const maxDecodedLength = (length: number) =>\n    stdCoder.maxDecodedLength(length);\n\nexport const decodedLength = (s: string) =>\n    stdCoder.decodedLength(s);\n", "// Copyright (C) 2016 <PERSON>\n// MIT License. See LICENSE file for details.\n\n/**\n * Package utf8 implements UTF-8 encoding and decoding.\n */\n\nconst INVALID_UTF16 = \"utf8: invalid string\";\nconst INVALID_UTF8 = \"utf8: invalid source encoding\";\n\n/**\n * Encodes the given string into UTF-8 byte array.\n * Throws if the source string has invalid UTF-16 encoding.\n */\nexport function encode(s: string): Uint8Array {\n    // Calculate result length and allocate output array.\n    // encodedLength() also validates string and throws errors,\n    // so we don't need repeat validation here.\n    const arr = new Uint8Array(encodedLength(s));\n\n    let pos = 0;\n    for (let i = 0; i < s.length; i++) {\n        let c = s.charCodeAt(i);\n        if (c < 0x80) {\n            arr[pos++] = c;\n        } else if (c < 0x800) {\n            arr[pos++] = 0xc0 | c >> 6;\n            arr[pos++] = 0x80 | c & 0x3f;\n        } else if (c < 0xd800) {\n            arr[pos++] = 0xe0 | c >> 12;\n            arr[pos++] = 0x80 | (c >> 6) & 0x3f;\n            arr[pos++] = 0x80 | c & 0x3f;\n        } else {\n            i++; // get one more character\n            c = (c & 0x3ff) << 10;\n            c |= s.charCodeAt(i) & 0x3ff;\n            c += 0x10000;\n\n            arr[pos++] = 0xf0 | c >> 18;\n            arr[pos++] = 0x80 | (c >> 12) & 0x3f;\n            arr[pos++] = 0x80 | (c >> 6) & 0x3f;\n            arr[pos++] = 0x80 | c & 0x3f;\n        }\n    }\n    return arr;\n}\n\n/**\n * Returns the number of bytes required to encode the given string into UTF-8.\n * Throws if the source string has invalid UTF-16 encoding.\n */\nexport function encodedLength(s: string): number {\n    let result = 0;\n    for (let i = 0; i < s.length; i++) {\n        const c = s.charCodeAt(i);\n        if (c < 0x80) {\n            result += 1;\n        } else if (c < 0x800) {\n            result += 2;\n        } else if (c < 0xd800) {\n            result += 3;\n        } else if (c <= 0xdfff) {\n            if (i >= s.length - 1) {\n                throw new Error(INVALID_UTF16);\n            }\n            i++; // \"eat\" next character\n            result += 4;\n        } else {\n            throw new Error(INVALID_UTF16);\n        }\n    }\n    return result;\n}\n\n/**\n * Decodes the given byte array from UTF-8 into a string.\n * Throws if encoding is invalid.\n */\nexport function decode(arr: Uint8Array): string {\n    const chars: string[] = [];\n    for (let i = 0; i < arr.length; i++) {\n        let b = arr[i];\n\n        if (b & 0x80) {\n            let min;\n            if (b < 0xe0) {\n                // Need 1 more byte.\n                if (i >= arr.length) {\n                    throw new Error(INVALID_UTF8);\n                }\n                const n1 = arr[++i];\n                if ((n1 & 0xc0) !== 0x80) {\n                    throw new Error(INVALID_UTF8);\n                }\n                b = (b & 0x1f) << 6 | (n1 & 0x3f);\n                min = 0x80;\n            } else if (b < 0xf0) {\n                // Need 2 more bytes.\n                if (i >= arr.length - 1) {\n                    throw new Error(INVALID_UTF8);\n                }\n                const n1 = arr[++i];\n                const n2 = arr[++i];\n                if ((n1 & 0xc0) !== 0x80 || (n2 & 0xc0) !== 0x80) {\n                    throw new Error(INVALID_UTF8);\n                }\n                b = (b & 0x0f) << 12 | (n1 & 0x3f) << 6 | (n2 & 0x3f);\n                min = 0x800;\n            } else if (b < 0xf8) {\n                // Need 3 more bytes.\n                if (i >= arr.length - 2) {\n                    throw new Error(INVALID_UTF8);\n                }\n                const n1 = arr[++i];\n                const n2 = arr[++i];\n                const n3 = arr[++i];\n                if ((n1 & 0xc0) !== 0x80 || (n2 & 0xc0) !== 0x80 || (n3 & 0xc0) !== 0x80) {\n                    throw new Error(INVALID_UTF8);\n                }\n                b = (b & 0x0f) << 18 | (n1 & 0x3f) << 12 | (n2 & 0x3f) << 6 | (n3 & 0x3f);\n                min = 0x10000;\n            } else {\n                throw new Error(INVALID_UTF8);\n            }\n\n            if (b < min || (b >= 0xd800 && b <= 0xdfff)) {\n                throw new Error(INVALID_UTF8);\n            }\n\n            if (b >= 0x10000) {\n                // Surrogate pair.\n                if (b > 0x10ffff) {\n                    throw new Error(INVALID_UTF8);\n                }\n                b -= 0x10000;\n                chars.push(String.fromCharCode(0xd800 | (b >> 10)));\n                b = 0xdc00 | (b & 0x3ff);\n            }\n        }\n\n        chars.push(String.fromCharCode(b));\n    }\n    return chars.join(\"\");\n}\n", "(function(nacl) {\n'use strict';\n\n// Ported in 2014 by <PERSON> and <PERSON>.\n// Public domain.\n//\n// Implementation derived from TweetNaCl version 20140427.\n// See for details: http://tweetnacl.cr.yp.to/\n\nvar gf = function(init) {\n  var i, r = new Float64Array(16);\n  if (init) for (i = 0; i < init.length; i++) r[i] = init[i];\n  return r;\n};\n\n//  Pluggable, initialized in high-level API below.\nvar randombytes = function(/* x, n */) { throw new Error('no PRNG'); };\n\nvar _0 = new Uint8Array(16);\nvar _9 = new Uint8Array(32); _9[0] = 9;\n\nvar gf0 = gf(),\n    gf1 = gf([1]),\n    _121665 = gf([0xdb41, 1]),\n    D = gf([0x78a3, 0x1359, 0x4dca, 0x75eb, 0xd8ab, 0x4141, 0x0a4d, 0x0070, 0xe898, 0x7779, 0x4079, 0x8cc7, 0xfe73, 0x2b6f, 0x6cee, 0x5203]),\n    D2 = gf([0xf159, 0x26b2, 0x9b94, 0xebd6, 0xb156, 0x8283, 0x149a, 0x00e0, 0xd130, 0xeef3, 0x80f2, 0x198e, 0xfce7, 0x56df, 0xd9dc, 0x2406]),\n    X = gf([0xd51a, 0x8f25, 0x2d60, 0xc956, 0xa7b2, 0x9525, 0xc760, 0x692c, 0xdc5c, 0xfdd6, 0xe231, 0xc0a4, 0x53fe, 0xcd6e, 0x36d3, 0x2169]),\n    Y = gf([0x6658, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666, 0x6666]),\n    I = gf([0xa0b0, 0x4a0e, 0x1b27, 0xc4ee, 0xe478, 0xad2f, 0x1806, 0x2f43, 0xd7a7, 0x3dfb, 0x0099, 0x2b4d, 0xdf0b, 0x4fc1, 0x2480, 0x2b83]);\n\nfunction ts64(x, i, h, l) {\n  x[i]   = (h >> 24) & 0xff;\n  x[i+1] = (h >> 16) & 0xff;\n  x[i+2] = (h >>  8) & 0xff;\n  x[i+3] = h & 0xff;\n  x[i+4] = (l >> 24)  & 0xff;\n  x[i+5] = (l >> 16)  & 0xff;\n  x[i+6] = (l >>  8)  & 0xff;\n  x[i+7] = l & 0xff;\n}\n\nfunction vn(x, xi, y, yi, n) {\n  var i,d = 0;\n  for (i = 0; i < n; i++) d |= x[xi+i]^y[yi+i];\n  return (1 & ((d - 1) >>> 8)) - 1;\n}\n\nfunction crypto_verify_16(x, xi, y, yi) {\n  return vn(x,xi,y,yi,16);\n}\n\nfunction crypto_verify_32(x, xi, y, yi) {\n  return vn(x,xi,y,yi,32);\n}\n\nfunction core_salsa20(o, p, k, c) {\n  var j0  = c[ 0] & 0xff | (c[ 1] & 0xff)<<8 | (c[ 2] & 0xff)<<16 | (c[ 3] & 0xff)<<24,\n      j1  = k[ 0] & 0xff | (k[ 1] & 0xff)<<8 | (k[ 2] & 0xff)<<16 | (k[ 3] & 0xff)<<24,\n      j2  = k[ 4] & 0xff | (k[ 5] & 0xff)<<8 | (k[ 6] & 0xff)<<16 | (k[ 7] & 0xff)<<24,\n      j3  = k[ 8] & 0xff | (k[ 9] & 0xff)<<8 | (k[10] & 0xff)<<16 | (k[11] & 0xff)<<24,\n      j4  = k[12] & 0xff | (k[13] & 0xff)<<8 | (k[14] & 0xff)<<16 | (k[15] & 0xff)<<24,\n      j5  = c[ 4] & 0xff | (c[ 5] & 0xff)<<8 | (c[ 6] & 0xff)<<16 | (c[ 7] & 0xff)<<24,\n      j6  = p[ 0] & 0xff | (p[ 1] & 0xff)<<8 | (p[ 2] & 0xff)<<16 | (p[ 3] & 0xff)<<24,\n      j7  = p[ 4] & 0xff | (p[ 5] & 0xff)<<8 | (p[ 6] & 0xff)<<16 | (p[ 7] & 0xff)<<24,\n      j8  = p[ 8] & 0xff | (p[ 9] & 0xff)<<8 | (p[10] & 0xff)<<16 | (p[11] & 0xff)<<24,\n      j9  = p[12] & 0xff | (p[13] & 0xff)<<8 | (p[14] & 0xff)<<16 | (p[15] & 0xff)<<24,\n      j10 = c[ 8] & 0xff | (c[ 9] & 0xff)<<8 | (c[10] & 0xff)<<16 | (c[11] & 0xff)<<24,\n      j11 = k[16] & 0xff | (k[17] & 0xff)<<8 | (k[18] & 0xff)<<16 | (k[19] & 0xff)<<24,\n      j12 = k[20] & 0xff | (k[21] & 0xff)<<8 | (k[22] & 0xff)<<16 | (k[23] & 0xff)<<24,\n      j13 = k[24] & 0xff | (k[25] & 0xff)<<8 | (k[26] & 0xff)<<16 | (k[27] & 0xff)<<24,\n      j14 = k[28] & 0xff | (k[29] & 0xff)<<8 | (k[30] & 0xff)<<16 | (k[31] & 0xff)<<24,\n      j15 = c[12] & 0xff | (c[13] & 0xff)<<8 | (c[14] & 0xff)<<16 | (c[15] & 0xff)<<24;\n\n  var x0 = j0, x1 = j1, x2 = j2, x3 = j3, x4 = j4, x5 = j5, x6 = j6, x7 = j7,\n      x8 = j8, x9 = j9, x10 = j10, x11 = j11, x12 = j12, x13 = j13, x14 = j14,\n      x15 = j15, u;\n\n  for (var i = 0; i < 20; i += 2) {\n    u = x0 + x12 | 0;\n    x4 ^= u<<7 | u>>>(32-7);\n    u = x4 + x0 | 0;\n    x8 ^= u<<9 | u>>>(32-9);\n    u = x8 + x4 | 0;\n    x12 ^= u<<13 | u>>>(32-13);\n    u = x12 + x8 | 0;\n    x0 ^= u<<18 | u>>>(32-18);\n\n    u = x5 + x1 | 0;\n    x9 ^= u<<7 | u>>>(32-7);\n    u = x9 + x5 | 0;\n    x13 ^= u<<9 | u>>>(32-9);\n    u = x13 + x9 | 0;\n    x1 ^= u<<13 | u>>>(32-13);\n    u = x1 + x13 | 0;\n    x5 ^= u<<18 | u>>>(32-18);\n\n    u = x10 + x6 | 0;\n    x14 ^= u<<7 | u>>>(32-7);\n    u = x14 + x10 | 0;\n    x2 ^= u<<9 | u>>>(32-9);\n    u = x2 + x14 | 0;\n    x6 ^= u<<13 | u>>>(32-13);\n    u = x6 + x2 | 0;\n    x10 ^= u<<18 | u>>>(32-18);\n\n    u = x15 + x11 | 0;\n    x3 ^= u<<7 | u>>>(32-7);\n    u = x3 + x15 | 0;\n    x7 ^= u<<9 | u>>>(32-9);\n    u = x7 + x3 | 0;\n    x11 ^= u<<13 | u>>>(32-13);\n    u = x11 + x7 | 0;\n    x15 ^= u<<18 | u>>>(32-18);\n\n    u = x0 + x3 | 0;\n    x1 ^= u<<7 | u>>>(32-7);\n    u = x1 + x0 | 0;\n    x2 ^= u<<9 | u>>>(32-9);\n    u = x2 + x1 | 0;\n    x3 ^= u<<13 | u>>>(32-13);\n    u = x3 + x2 | 0;\n    x0 ^= u<<18 | u>>>(32-18);\n\n    u = x5 + x4 | 0;\n    x6 ^= u<<7 | u>>>(32-7);\n    u = x6 + x5 | 0;\n    x7 ^= u<<9 | u>>>(32-9);\n    u = x7 + x6 | 0;\n    x4 ^= u<<13 | u>>>(32-13);\n    u = x4 + x7 | 0;\n    x5 ^= u<<18 | u>>>(32-18);\n\n    u = x10 + x9 | 0;\n    x11 ^= u<<7 | u>>>(32-7);\n    u = x11 + x10 | 0;\n    x8 ^= u<<9 | u>>>(32-9);\n    u = x8 + x11 | 0;\n    x9 ^= u<<13 | u>>>(32-13);\n    u = x9 + x8 | 0;\n    x10 ^= u<<18 | u>>>(32-18);\n\n    u = x15 + x14 | 0;\n    x12 ^= u<<7 | u>>>(32-7);\n    u = x12 + x15 | 0;\n    x13 ^= u<<9 | u>>>(32-9);\n    u = x13 + x12 | 0;\n    x14 ^= u<<13 | u>>>(32-13);\n    u = x14 + x13 | 0;\n    x15 ^= u<<18 | u>>>(32-18);\n  }\n   x0 =  x0 +  j0 | 0;\n   x1 =  x1 +  j1 | 0;\n   x2 =  x2 +  j2 | 0;\n   x3 =  x3 +  j3 | 0;\n   x4 =  x4 +  j4 | 0;\n   x5 =  x5 +  j5 | 0;\n   x6 =  x6 +  j6 | 0;\n   x7 =  x7 +  j7 | 0;\n   x8 =  x8 +  j8 | 0;\n   x9 =  x9 +  j9 | 0;\n  x10 = x10 + j10 | 0;\n  x11 = x11 + j11 | 0;\n  x12 = x12 + j12 | 0;\n  x13 = x13 + j13 | 0;\n  x14 = x14 + j14 | 0;\n  x15 = x15 + j15 | 0;\n\n  o[ 0] = x0 >>>  0 & 0xff;\n  o[ 1] = x0 >>>  8 & 0xff;\n  o[ 2] = x0 >>> 16 & 0xff;\n  o[ 3] = x0 >>> 24 & 0xff;\n\n  o[ 4] = x1 >>>  0 & 0xff;\n  o[ 5] = x1 >>>  8 & 0xff;\n  o[ 6] = x1 >>> 16 & 0xff;\n  o[ 7] = x1 >>> 24 & 0xff;\n\n  o[ 8] = x2 >>>  0 & 0xff;\n  o[ 9] = x2 >>>  8 & 0xff;\n  o[10] = x2 >>> 16 & 0xff;\n  o[11] = x2 >>> 24 & 0xff;\n\n  o[12] = x3 >>>  0 & 0xff;\n  o[13] = x3 >>>  8 & 0xff;\n  o[14] = x3 >>> 16 & 0xff;\n  o[15] = x3 >>> 24 & 0xff;\n\n  o[16] = x4 >>>  0 & 0xff;\n  o[17] = x4 >>>  8 & 0xff;\n  o[18] = x4 >>> 16 & 0xff;\n  o[19] = x4 >>> 24 & 0xff;\n\n  o[20] = x5 >>>  0 & 0xff;\n  o[21] = x5 >>>  8 & 0xff;\n  o[22] = x5 >>> 16 & 0xff;\n  o[23] = x5 >>> 24 & 0xff;\n\n  o[24] = x6 >>>  0 & 0xff;\n  o[25] = x6 >>>  8 & 0xff;\n  o[26] = x6 >>> 16 & 0xff;\n  o[27] = x6 >>> 24 & 0xff;\n\n  o[28] = x7 >>>  0 & 0xff;\n  o[29] = x7 >>>  8 & 0xff;\n  o[30] = x7 >>> 16 & 0xff;\n  o[31] = x7 >>> 24 & 0xff;\n\n  o[32] = x8 >>>  0 & 0xff;\n  o[33] = x8 >>>  8 & 0xff;\n  o[34] = x8 >>> 16 & 0xff;\n  o[35] = x8 >>> 24 & 0xff;\n\n  o[36] = x9 >>>  0 & 0xff;\n  o[37] = x9 >>>  8 & 0xff;\n  o[38] = x9 >>> 16 & 0xff;\n  o[39] = x9 >>> 24 & 0xff;\n\n  o[40] = x10 >>>  0 & 0xff;\n  o[41] = x10 >>>  8 & 0xff;\n  o[42] = x10 >>> 16 & 0xff;\n  o[43] = x10 >>> 24 & 0xff;\n\n  o[44] = x11 >>>  0 & 0xff;\n  o[45] = x11 >>>  8 & 0xff;\n  o[46] = x11 >>> 16 & 0xff;\n  o[47] = x11 >>> 24 & 0xff;\n\n  o[48] = x12 >>>  0 & 0xff;\n  o[49] = x12 >>>  8 & 0xff;\n  o[50] = x12 >>> 16 & 0xff;\n  o[51] = x12 >>> 24 & 0xff;\n\n  o[52] = x13 >>>  0 & 0xff;\n  o[53] = x13 >>>  8 & 0xff;\n  o[54] = x13 >>> 16 & 0xff;\n  o[55] = x13 >>> 24 & 0xff;\n\n  o[56] = x14 >>>  0 & 0xff;\n  o[57] = x14 >>>  8 & 0xff;\n  o[58] = x14 >>> 16 & 0xff;\n  o[59] = x14 >>> 24 & 0xff;\n\n  o[60] = x15 >>>  0 & 0xff;\n  o[61] = x15 >>>  8 & 0xff;\n  o[62] = x15 >>> 16 & 0xff;\n  o[63] = x15 >>> 24 & 0xff;\n}\n\nfunction core_hsalsa20(o,p,k,c) {\n  var j0  = c[ 0] & 0xff | (c[ 1] & 0xff)<<8 | (c[ 2] & 0xff)<<16 | (c[ 3] & 0xff)<<24,\n      j1  = k[ 0] & 0xff | (k[ 1] & 0xff)<<8 | (k[ 2] & 0xff)<<16 | (k[ 3] & 0xff)<<24,\n      j2  = k[ 4] & 0xff | (k[ 5] & 0xff)<<8 | (k[ 6] & 0xff)<<16 | (k[ 7] & 0xff)<<24,\n      j3  = k[ 8] & 0xff | (k[ 9] & 0xff)<<8 | (k[10] & 0xff)<<16 | (k[11] & 0xff)<<24,\n      j4  = k[12] & 0xff | (k[13] & 0xff)<<8 | (k[14] & 0xff)<<16 | (k[15] & 0xff)<<24,\n      j5  = c[ 4] & 0xff | (c[ 5] & 0xff)<<8 | (c[ 6] & 0xff)<<16 | (c[ 7] & 0xff)<<24,\n      j6  = p[ 0] & 0xff | (p[ 1] & 0xff)<<8 | (p[ 2] & 0xff)<<16 | (p[ 3] & 0xff)<<24,\n      j7  = p[ 4] & 0xff | (p[ 5] & 0xff)<<8 | (p[ 6] & 0xff)<<16 | (p[ 7] & 0xff)<<24,\n      j8  = p[ 8] & 0xff | (p[ 9] & 0xff)<<8 | (p[10] & 0xff)<<16 | (p[11] & 0xff)<<24,\n      j9  = p[12] & 0xff | (p[13] & 0xff)<<8 | (p[14] & 0xff)<<16 | (p[15] & 0xff)<<24,\n      j10 = c[ 8] & 0xff | (c[ 9] & 0xff)<<8 | (c[10] & 0xff)<<16 | (c[11] & 0xff)<<24,\n      j11 = k[16] & 0xff | (k[17] & 0xff)<<8 | (k[18] & 0xff)<<16 | (k[19] & 0xff)<<24,\n      j12 = k[20] & 0xff | (k[21] & 0xff)<<8 | (k[22] & 0xff)<<16 | (k[23] & 0xff)<<24,\n      j13 = k[24] & 0xff | (k[25] & 0xff)<<8 | (k[26] & 0xff)<<16 | (k[27] & 0xff)<<24,\n      j14 = k[28] & 0xff | (k[29] & 0xff)<<8 | (k[30] & 0xff)<<16 | (k[31] & 0xff)<<24,\n      j15 = c[12] & 0xff | (c[13] & 0xff)<<8 | (c[14] & 0xff)<<16 | (c[15] & 0xff)<<24;\n\n  var x0 = j0, x1 = j1, x2 = j2, x3 = j3, x4 = j4, x5 = j5, x6 = j6, x7 = j7,\n      x8 = j8, x9 = j9, x10 = j10, x11 = j11, x12 = j12, x13 = j13, x14 = j14,\n      x15 = j15, u;\n\n  for (var i = 0; i < 20; i += 2) {\n    u = x0 + x12 | 0;\n    x4 ^= u<<7 | u>>>(32-7);\n    u = x4 + x0 | 0;\n    x8 ^= u<<9 | u>>>(32-9);\n    u = x8 + x4 | 0;\n    x12 ^= u<<13 | u>>>(32-13);\n    u = x12 + x8 | 0;\n    x0 ^= u<<18 | u>>>(32-18);\n\n    u = x5 + x1 | 0;\n    x9 ^= u<<7 | u>>>(32-7);\n    u = x9 + x5 | 0;\n    x13 ^= u<<9 | u>>>(32-9);\n    u = x13 + x9 | 0;\n    x1 ^= u<<13 | u>>>(32-13);\n    u = x1 + x13 | 0;\n    x5 ^= u<<18 | u>>>(32-18);\n\n    u = x10 + x6 | 0;\n    x14 ^= u<<7 | u>>>(32-7);\n    u = x14 + x10 | 0;\n    x2 ^= u<<9 | u>>>(32-9);\n    u = x2 + x14 | 0;\n    x6 ^= u<<13 | u>>>(32-13);\n    u = x6 + x2 | 0;\n    x10 ^= u<<18 | u>>>(32-18);\n\n    u = x15 + x11 | 0;\n    x3 ^= u<<7 | u>>>(32-7);\n    u = x3 + x15 | 0;\n    x7 ^= u<<9 | u>>>(32-9);\n    u = x7 + x3 | 0;\n    x11 ^= u<<13 | u>>>(32-13);\n    u = x11 + x7 | 0;\n    x15 ^= u<<18 | u>>>(32-18);\n\n    u = x0 + x3 | 0;\n    x1 ^= u<<7 | u>>>(32-7);\n    u = x1 + x0 | 0;\n    x2 ^= u<<9 | u>>>(32-9);\n    u = x2 + x1 | 0;\n    x3 ^= u<<13 | u>>>(32-13);\n    u = x3 + x2 | 0;\n    x0 ^= u<<18 | u>>>(32-18);\n\n    u = x5 + x4 | 0;\n    x6 ^= u<<7 | u>>>(32-7);\n    u = x6 + x5 | 0;\n    x7 ^= u<<9 | u>>>(32-9);\n    u = x7 + x6 | 0;\n    x4 ^= u<<13 | u>>>(32-13);\n    u = x4 + x7 | 0;\n    x5 ^= u<<18 | u>>>(32-18);\n\n    u = x10 + x9 | 0;\n    x11 ^= u<<7 | u>>>(32-7);\n    u = x11 + x10 | 0;\n    x8 ^= u<<9 | u>>>(32-9);\n    u = x8 + x11 | 0;\n    x9 ^= u<<13 | u>>>(32-13);\n    u = x9 + x8 | 0;\n    x10 ^= u<<18 | u>>>(32-18);\n\n    u = x15 + x14 | 0;\n    x12 ^= u<<7 | u>>>(32-7);\n    u = x12 + x15 | 0;\n    x13 ^= u<<9 | u>>>(32-9);\n    u = x13 + x12 | 0;\n    x14 ^= u<<13 | u>>>(32-13);\n    u = x14 + x13 | 0;\n    x15 ^= u<<18 | u>>>(32-18);\n  }\n\n  o[ 0] = x0 >>>  0 & 0xff;\n  o[ 1] = x0 >>>  8 & 0xff;\n  o[ 2] = x0 >>> 16 & 0xff;\n  o[ 3] = x0 >>> 24 & 0xff;\n\n  o[ 4] = x5 >>>  0 & 0xff;\n  o[ 5] = x5 >>>  8 & 0xff;\n  o[ 6] = x5 >>> 16 & 0xff;\n  o[ 7] = x5 >>> 24 & 0xff;\n\n  o[ 8] = x10 >>>  0 & 0xff;\n  o[ 9] = x10 >>>  8 & 0xff;\n  o[10] = x10 >>> 16 & 0xff;\n  o[11] = x10 >>> 24 & 0xff;\n\n  o[12] = x15 >>>  0 & 0xff;\n  o[13] = x15 >>>  8 & 0xff;\n  o[14] = x15 >>> 16 & 0xff;\n  o[15] = x15 >>> 24 & 0xff;\n\n  o[16] = x6 >>>  0 & 0xff;\n  o[17] = x6 >>>  8 & 0xff;\n  o[18] = x6 >>> 16 & 0xff;\n  o[19] = x6 >>> 24 & 0xff;\n\n  o[20] = x7 >>>  0 & 0xff;\n  o[21] = x7 >>>  8 & 0xff;\n  o[22] = x7 >>> 16 & 0xff;\n  o[23] = x7 >>> 24 & 0xff;\n\n  o[24] = x8 >>>  0 & 0xff;\n  o[25] = x8 >>>  8 & 0xff;\n  o[26] = x8 >>> 16 & 0xff;\n  o[27] = x8 >>> 24 & 0xff;\n\n  o[28] = x9 >>>  0 & 0xff;\n  o[29] = x9 >>>  8 & 0xff;\n  o[30] = x9 >>> 16 & 0xff;\n  o[31] = x9 >>> 24 & 0xff;\n}\n\nfunction crypto_core_salsa20(out,inp,k,c) {\n  core_salsa20(out,inp,k,c);\n}\n\nfunction crypto_core_hsalsa20(out,inp,k,c) {\n  core_hsalsa20(out,inp,k,c);\n}\n\nvar sigma = new Uint8Array([101, 120, 112, 97, 110, 100, 32, 51, 50, 45, 98, 121, 116, 101, 32, 107]);\n            // \"expand 32-byte k\"\n\nfunction crypto_stream_salsa20_xor(c,cpos,m,mpos,b,n,k) {\n  var z = new Uint8Array(16), x = new Uint8Array(64);\n  var u, i;\n  for (i = 0; i < 16; i++) z[i] = 0;\n  for (i = 0; i < 8; i++) z[i] = n[i];\n  while (b >= 64) {\n    crypto_core_salsa20(x,z,k,sigma);\n    for (i = 0; i < 64; i++) c[cpos+i] = m[mpos+i] ^ x[i];\n    u = 1;\n    for (i = 8; i < 16; i++) {\n      u = u + (z[i] & 0xff) | 0;\n      z[i] = u & 0xff;\n      u >>>= 8;\n    }\n    b -= 64;\n    cpos += 64;\n    mpos += 64;\n  }\n  if (b > 0) {\n    crypto_core_salsa20(x,z,k,sigma);\n    for (i = 0; i < b; i++) c[cpos+i] = m[mpos+i] ^ x[i];\n  }\n  return 0;\n}\n\nfunction crypto_stream_salsa20(c,cpos,b,n,k) {\n  var z = new Uint8Array(16), x = new Uint8Array(64);\n  var u, i;\n  for (i = 0; i < 16; i++) z[i] = 0;\n  for (i = 0; i < 8; i++) z[i] = n[i];\n  while (b >= 64) {\n    crypto_core_salsa20(x,z,k,sigma);\n    for (i = 0; i < 64; i++) c[cpos+i] = x[i];\n    u = 1;\n    for (i = 8; i < 16; i++) {\n      u = u + (z[i] & 0xff) | 0;\n      z[i] = u & 0xff;\n      u >>>= 8;\n    }\n    b -= 64;\n    cpos += 64;\n  }\n  if (b > 0) {\n    crypto_core_salsa20(x,z,k,sigma);\n    for (i = 0; i < b; i++) c[cpos+i] = x[i];\n  }\n  return 0;\n}\n\nfunction crypto_stream(c,cpos,d,n,k) {\n  var s = new Uint8Array(32);\n  crypto_core_hsalsa20(s,n,k,sigma);\n  var sn = new Uint8Array(8);\n  for (var i = 0; i < 8; i++) sn[i] = n[i+16];\n  return crypto_stream_salsa20(c,cpos,d,sn,s);\n}\n\nfunction crypto_stream_xor(c,cpos,m,mpos,d,n,k) {\n  var s = new Uint8Array(32);\n  crypto_core_hsalsa20(s,n,k,sigma);\n  var sn = new Uint8Array(8);\n  for (var i = 0; i < 8; i++) sn[i] = n[i+16];\n  return crypto_stream_salsa20_xor(c,cpos,m,mpos,d,sn,s);\n}\n\n/*\n* Port of Andrew Moon's Poly1305-donna-16. Public domain.\n* https://github.com/floodyberry/poly1305-donna\n*/\n\nvar poly1305 = function(key) {\n  this.buffer = new Uint8Array(16);\n  this.r = new Uint16Array(10);\n  this.h = new Uint16Array(10);\n  this.pad = new Uint16Array(8);\n  this.leftover = 0;\n  this.fin = 0;\n\n  var t0, t1, t2, t3, t4, t5, t6, t7;\n\n  t0 = key[ 0] & 0xff | (key[ 1] & 0xff) << 8; this.r[0] = ( t0                     ) & 0x1fff;\n  t1 = key[ 2] & 0xff | (key[ 3] & 0xff) << 8; this.r[1] = ((t0 >>> 13) | (t1 <<  3)) & 0x1fff;\n  t2 = key[ 4] & 0xff | (key[ 5] & 0xff) << 8; this.r[2] = ((t1 >>> 10) | (t2 <<  6)) & 0x1f03;\n  t3 = key[ 6] & 0xff | (key[ 7] & 0xff) << 8; this.r[3] = ((t2 >>>  7) | (t3 <<  9)) & 0x1fff;\n  t4 = key[ 8] & 0xff | (key[ 9] & 0xff) << 8; this.r[4] = ((t3 >>>  4) | (t4 << 12)) & 0x00ff;\n  this.r[5] = ((t4 >>>  1)) & 0x1ffe;\n  t5 = key[10] & 0xff | (key[11] & 0xff) << 8; this.r[6] = ((t4 >>> 14) | (t5 <<  2)) & 0x1fff;\n  t6 = key[12] & 0xff | (key[13] & 0xff) << 8; this.r[7] = ((t5 >>> 11) | (t6 <<  5)) & 0x1f81;\n  t7 = key[14] & 0xff | (key[15] & 0xff) << 8; this.r[8] = ((t6 >>>  8) | (t7 <<  8)) & 0x1fff;\n  this.r[9] = ((t7 >>>  5)) & 0x007f;\n\n  this.pad[0] = key[16] & 0xff | (key[17] & 0xff) << 8;\n  this.pad[1] = key[18] & 0xff | (key[19] & 0xff) << 8;\n  this.pad[2] = key[20] & 0xff | (key[21] & 0xff) << 8;\n  this.pad[3] = key[22] & 0xff | (key[23] & 0xff) << 8;\n  this.pad[4] = key[24] & 0xff | (key[25] & 0xff) << 8;\n  this.pad[5] = key[26] & 0xff | (key[27] & 0xff) << 8;\n  this.pad[6] = key[28] & 0xff | (key[29] & 0xff) << 8;\n  this.pad[7] = key[30] & 0xff | (key[31] & 0xff) << 8;\n};\n\npoly1305.prototype.blocks = function(m, mpos, bytes) {\n  var hibit = this.fin ? 0 : (1 << 11);\n  var t0, t1, t2, t3, t4, t5, t6, t7, c;\n  var d0, d1, d2, d3, d4, d5, d6, d7, d8, d9;\n\n  var h0 = this.h[0],\n      h1 = this.h[1],\n      h2 = this.h[2],\n      h3 = this.h[3],\n      h4 = this.h[4],\n      h5 = this.h[5],\n      h6 = this.h[6],\n      h7 = this.h[7],\n      h8 = this.h[8],\n      h9 = this.h[9];\n\n  var r0 = this.r[0],\n      r1 = this.r[1],\n      r2 = this.r[2],\n      r3 = this.r[3],\n      r4 = this.r[4],\n      r5 = this.r[5],\n      r6 = this.r[6],\n      r7 = this.r[7],\n      r8 = this.r[8],\n      r9 = this.r[9];\n\n  while (bytes >= 16) {\n    t0 = m[mpos+ 0] & 0xff | (m[mpos+ 1] & 0xff) << 8; h0 += ( t0                     ) & 0x1fff;\n    t1 = m[mpos+ 2] & 0xff | (m[mpos+ 3] & 0xff) << 8; h1 += ((t0 >>> 13) | (t1 <<  3)) & 0x1fff;\n    t2 = m[mpos+ 4] & 0xff | (m[mpos+ 5] & 0xff) << 8; h2 += ((t1 >>> 10) | (t2 <<  6)) & 0x1fff;\n    t3 = m[mpos+ 6] & 0xff | (m[mpos+ 7] & 0xff) << 8; h3 += ((t2 >>>  7) | (t3 <<  9)) & 0x1fff;\n    t4 = m[mpos+ 8] & 0xff | (m[mpos+ 9] & 0xff) << 8; h4 += ((t3 >>>  4) | (t4 << 12)) & 0x1fff;\n    h5 += ((t4 >>>  1)) & 0x1fff;\n    t5 = m[mpos+10] & 0xff | (m[mpos+11] & 0xff) << 8; h6 += ((t4 >>> 14) | (t5 <<  2)) & 0x1fff;\n    t6 = m[mpos+12] & 0xff | (m[mpos+13] & 0xff) << 8; h7 += ((t5 >>> 11) | (t6 <<  5)) & 0x1fff;\n    t7 = m[mpos+14] & 0xff | (m[mpos+15] & 0xff) << 8; h8 += ((t6 >>>  8) | (t7 <<  8)) & 0x1fff;\n    h9 += ((t7 >>> 5)) | hibit;\n\n    c = 0;\n\n    d0 = c;\n    d0 += h0 * r0;\n    d0 += h1 * (5 * r9);\n    d0 += h2 * (5 * r8);\n    d0 += h3 * (5 * r7);\n    d0 += h4 * (5 * r6);\n    c = (d0 >>> 13); d0 &= 0x1fff;\n    d0 += h5 * (5 * r5);\n    d0 += h6 * (5 * r4);\n    d0 += h7 * (5 * r3);\n    d0 += h8 * (5 * r2);\n    d0 += h9 * (5 * r1);\n    c += (d0 >>> 13); d0 &= 0x1fff;\n\n    d1 = c;\n    d1 += h0 * r1;\n    d1 += h1 * r0;\n    d1 += h2 * (5 * r9);\n    d1 += h3 * (5 * r8);\n    d1 += h4 * (5 * r7);\n    c = (d1 >>> 13); d1 &= 0x1fff;\n    d1 += h5 * (5 * r6);\n    d1 += h6 * (5 * r5);\n    d1 += h7 * (5 * r4);\n    d1 += h8 * (5 * r3);\n    d1 += h9 * (5 * r2);\n    c += (d1 >>> 13); d1 &= 0x1fff;\n\n    d2 = c;\n    d2 += h0 * r2;\n    d2 += h1 * r1;\n    d2 += h2 * r0;\n    d2 += h3 * (5 * r9);\n    d2 += h4 * (5 * r8);\n    c = (d2 >>> 13); d2 &= 0x1fff;\n    d2 += h5 * (5 * r7);\n    d2 += h6 * (5 * r6);\n    d2 += h7 * (5 * r5);\n    d2 += h8 * (5 * r4);\n    d2 += h9 * (5 * r3);\n    c += (d2 >>> 13); d2 &= 0x1fff;\n\n    d3 = c;\n    d3 += h0 * r3;\n    d3 += h1 * r2;\n    d3 += h2 * r1;\n    d3 += h3 * r0;\n    d3 += h4 * (5 * r9);\n    c = (d3 >>> 13); d3 &= 0x1fff;\n    d3 += h5 * (5 * r8);\n    d3 += h6 * (5 * r7);\n    d3 += h7 * (5 * r6);\n    d3 += h8 * (5 * r5);\n    d3 += h9 * (5 * r4);\n    c += (d3 >>> 13); d3 &= 0x1fff;\n\n    d4 = c;\n    d4 += h0 * r4;\n    d4 += h1 * r3;\n    d4 += h2 * r2;\n    d4 += h3 * r1;\n    d4 += h4 * r0;\n    c = (d4 >>> 13); d4 &= 0x1fff;\n    d4 += h5 * (5 * r9);\n    d4 += h6 * (5 * r8);\n    d4 += h7 * (5 * r7);\n    d4 += h8 * (5 * r6);\n    d4 += h9 * (5 * r5);\n    c += (d4 >>> 13); d4 &= 0x1fff;\n\n    d5 = c;\n    d5 += h0 * r5;\n    d5 += h1 * r4;\n    d5 += h2 * r3;\n    d5 += h3 * r2;\n    d5 += h4 * r1;\n    c = (d5 >>> 13); d5 &= 0x1fff;\n    d5 += h5 * r0;\n    d5 += h6 * (5 * r9);\n    d5 += h7 * (5 * r8);\n    d5 += h8 * (5 * r7);\n    d5 += h9 * (5 * r6);\n    c += (d5 >>> 13); d5 &= 0x1fff;\n\n    d6 = c;\n    d6 += h0 * r6;\n    d6 += h1 * r5;\n    d6 += h2 * r4;\n    d6 += h3 * r3;\n    d6 += h4 * r2;\n    c = (d6 >>> 13); d6 &= 0x1fff;\n    d6 += h5 * r1;\n    d6 += h6 * r0;\n    d6 += h7 * (5 * r9);\n    d6 += h8 * (5 * r8);\n    d6 += h9 * (5 * r7);\n    c += (d6 >>> 13); d6 &= 0x1fff;\n\n    d7 = c;\n    d7 += h0 * r7;\n    d7 += h1 * r6;\n    d7 += h2 * r5;\n    d7 += h3 * r4;\n    d7 += h4 * r3;\n    c = (d7 >>> 13); d7 &= 0x1fff;\n    d7 += h5 * r2;\n    d7 += h6 * r1;\n    d7 += h7 * r0;\n    d7 += h8 * (5 * r9);\n    d7 += h9 * (5 * r8);\n    c += (d7 >>> 13); d7 &= 0x1fff;\n\n    d8 = c;\n    d8 += h0 * r8;\n    d8 += h1 * r7;\n    d8 += h2 * r6;\n    d8 += h3 * r5;\n    d8 += h4 * r4;\n    c = (d8 >>> 13); d8 &= 0x1fff;\n    d8 += h5 * r3;\n    d8 += h6 * r2;\n    d8 += h7 * r1;\n    d8 += h8 * r0;\n    d8 += h9 * (5 * r9);\n    c += (d8 >>> 13); d8 &= 0x1fff;\n\n    d9 = c;\n    d9 += h0 * r9;\n    d9 += h1 * r8;\n    d9 += h2 * r7;\n    d9 += h3 * r6;\n    d9 += h4 * r5;\n    c = (d9 >>> 13); d9 &= 0x1fff;\n    d9 += h5 * r4;\n    d9 += h6 * r3;\n    d9 += h7 * r2;\n    d9 += h8 * r1;\n    d9 += h9 * r0;\n    c += (d9 >>> 13); d9 &= 0x1fff;\n\n    c = (((c << 2) + c)) | 0;\n    c = (c + d0) | 0;\n    d0 = c & 0x1fff;\n    c = (c >>> 13);\n    d1 += c;\n\n    h0 = d0;\n    h1 = d1;\n    h2 = d2;\n    h3 = d3;\n    h4 = d4;\n    h5 = d5;\n    h6 = d6;\n    h7 = d7;\n    h8 = d8;\n    h9 = d9;\n\n    mpos += 16;\n    bytes -= 16;\n  }\n  this.h[0] = h0;\n  this.h[1] = h1;\n  this.h[2] = h2;\n  this.h[3] = h3;\n  this.h[4] = h4;\n  this.h[5] = h5;\n  this.h[6] = h6;\n  this.h[7] = h7;\n  this.h[8] = h8;\n  this.h[9] = h9;\n};\n\npoly1305.prototype.finish = function(mac, macpos) {\n  var g = new Uint16Array(10);\n  var c, mask, f, i;\n\n  if (this.leftover) {\n    i = this.leftover;\n    this.buffer[i++] = 1;\n    for (; i < 16; i++) this.buffer[i] = 0;\n    this.fin = 1;\n    this.blocks(this.buffer, 0, 16);\n  }\n\n  c = this.h[1] >>> 13;\n  this.h[1] &= 0x1fff;\n  for (i = 2; i < 10; i++) {\n    this.h[i] += c;\n    c = this.h[i] >>> 13;\n    this.h[i] &= 0x1fff;\n  }\n  this.h[0] += (c * 5);\n  c = this.h[0] >>> 13;\n  this.h[0] &= 0x1fff;\n  this.h[1] += c;\n  c = this.h[1] >>> 13;\n  this.h[1] &= 0x1fff;\n  this.h[2] += c;\n\n  g[0] = this.h[0] + 5;\n  c = g[0] >>> 13;\n  g[0] &= 0x1fff;\n  for (i = 1; i < 10; i++) {\n    g[i] = this.h[i] + c;\n    c = g[i] >>> 13;\n    g[i] &= 0x1fff;\n  }\n  g[9] -= (1 << 13);\n\n  mask = (c ^ 1) - 1;\n  for (i = 0; i < 10; i++) g[i] &= mask;\n  mask = ~mask;\n  for (i = 0; i < 10; i++) this.h[i] = (this.h[i] & mask) | g[i];\n\n  this.h[0] = ((this.h[0]       ) | (this.h[1] << 13)                    ) & 0xffff;\n  this.h[1] = ((this.h[1] >>>  3) | (this.h[2] << 10)                    ) & 0xffff;\n  this.h[2] = ((this.h[2] >>>  6) | (this.h[3] <<  7)                    ) & 0xffff;\n  this.h[3] = ((this.h[3] >>>  9) | (this.h[4] <<  4)                    ) & 0xffff;\n  this.h[4] = ((this.h[4] >>> 12) | (this.h[5] <<  1) | (this.h[6] << 14)) & 0xffff;\n  this.h[5] = ((this.h[6] >>>  2) | (this.h[7] << 11)                    ) & 0xffff;\n  this.h[6] = ((this.h[7] >>>  5) | (this.h[8] <<  8)                    ) & 0xffff;\n  this.h[7] = ((this.h[8] >>>  8) | (this.h[9] <<  5)                    ) & 0xffff;\n\n  f = this.h[0] + this.pad[0];\n  this.h[0] = f & 0xffff;\n  for (i = 1; i < 8; i++) {\n    f = (((this.h[i] + this.pad[i]) | 0) + (f >>> 16)) | 0;\n    this.h[i] = f & 0xffff;\n  }\n\n  mac[macpos+ 0] = (this.h[0] >>> 0) & 0xff;\n  mac[macpos+ 1] = (this.h[0] >>> 8) & 0xff;\n  mac[macpos+ 2] = (this.h[1] >>> 0) & 0xff;\n  mac[macpos+ 3] = (this.h[1] >>> 8) & 0xff;\n  mac[macpos+ 4] = (this.h[2] >>> 0) & 0xff;\n  mac[macpos+ 5] = (this.h[2] >>> 8) & 0xff;\n  mac[macpos+ 6] = (this.h[3] >>> 0) & 0xff;\n  mac[macpos+ 7] = (this.h[3] >>> 8) & 0xff;\n  mac[macpos+ 8] = (this.h[4] >>> 0) & 0xff;\n  mac[macpos+ 9] = (this.h[4] >>> 8) & 0xff;\n  mac[macpos+10] = (this.h[5] >>> 0) & 0xff;\n  mac[macpos+11] = (this.h[5] >>> 8) & 0xff;\n  mac[macpos+12] = (this.h[6] >>> 0) & 0xff;\n  mac[macpos+13] = (this.h[6] >>> 8) & 0xff;\n  mac[macpos+14] = (this.h[7] >>> 0) & 0xff;\n  mac[macpos+15] = (this.h[7] >>> 8) & 0xff;\n};\n\npoly1305.prototype.update = function(m, mpos, bytes) {\n  var i, want;\n\n  if (this.leftover) {\n    want = (16 - this.leftover);\n    if (want > bytes)\n      want = bytes;\n    for (i = 0; i < want; i++)\n      this.buffer[this.leftover + i] = m[mpos+i];\n    bytes -= want;\n    mpos += want;\n    this.leftover += want;\n    if (this.leftover < 16)\n      return;\n    this.blocks(this.buffer, 0, 16);\n    this.leftover = 0;\n  }\n\n  if (bytes >= 16) {\n    want = bytes - (bytes % 16);\n    this.blocks(m, mpos, want);\n    mpos += want;\n    bytes -= want;\n  }\n\n  if (bytes) {\n    for (i = 0; i < bytes; i++)\n      this.buffer[this.leftover + i] = m[mpos+i];\n    this.leftover += bytes;\n  }\n};\n\nfunction crypto_onetimeauth(out, outpos, m, mpos, n, k) {\n  var s = new poly1305(k);\n  s.update(m, mpos, n);\n  s.finish(out, outpos);\n  return 0;\n}\n\nfunction crypto_onetimeauth_verify(h, hpos, m, mpos, n, k) {\n  var x = new Uint8Array(16);\n  crypto_onetimeauth(x,0,m,mpos,n,k);\n  return crypto_verify_16(h,hpos,x,0);\n}\n\nfunction crypto_secretbox(c,m,d,n,k) {\n  var i;\n  if (d < 32) return -1;\n  crypto_stream_xor(c,0,m,0,d,n,k);\n  crypto_onetimeauth(c, 16, c, 32, d - 32, c);\n  for (i = 0; i < 16; i++) c[i] = 0;\n  return 0;\n}\n\nfunction crypto_secretbox_open(m,c,d,n,k) {\n  var i;\n  var x = new Uint8Array(32);\n  if (d < 32) return -1;\n  crypto_stream(x,0,32,n,k);\n  if (crypto_onetimeauth_verify(c, 16,c, 32,d - 32,x) !== 0) return -1;\n  crypto_stream_xor(m,0,c,0,d,n,k);\n  for (i = 0; i < 32; i++) m[i] = 0;\n  return 0;\n}\n\nfunction set25519(r, a) {\n  var i;\n  for (i = 0; i < 16; i++) r[i] = a[i]|0;\n}\n\nfunction car25519(o) {\n  var i, v, c = 1;\n  for (i = 0; i < 16; i++) {\n    v = o[i] + c + 65535;\n    c = Math.floor(v / 65536);\n    o[i] = v - c * 65536;\n  }\n  o[0] += c-1 + 37 * (c-1);\n}\n\nfunction sel25519(p, q, b) {\n  var t, c = ~(b-1);\n  for (var i = 0; i < 16; i++) {\n    t = c & (p[i] ^ q[i]);\n    p[i] ^= t;\n    q[i] ^= t;\n  }\n}\n\nfunction pack25519(o, n) {\n  var i, j, b;\n  var m = gf(), t = gf();\n  for (i = 0; i < 16; i++) t[i] = n[i];\n  car25519(t);\n  car25519(t);\n  car25519(t);\n  for (j = 0; j < 2; j++) {\n    m[0] = t[0] - 0xffed;\n    for (i = 1; i < 15; i++) {\n      m[i] = t[i] - 0xffff - ((m[i-1]>>16) & 1);\n      m[i-1] &= 0xffff;\n    }\n    m[15] = t[15] - 0x7fff - ((m[14]>>16) & 1);\n    b = (m[15]>>16) & 1;\n    m[14] &= 0xffff;\n    sel25519(t, m, 1-b);\n  }\n  for (i = 0; i < 16; i++) {\n    o[2*i] = t[i] & 0xff;\n    o[2*i+1] = t[i]>>8;\n  }\n}\n\nfunction neq25519(a, b) {\n  var c = new Uint8Array(32), d = new Uint8Array(32);\n  pack25519(c, a);\n  pack25519(d, b);\n  return crypto_verify_32(c, 0, d, 0);\n}\n\nfunction par25519(a) {\n  var d = new Uint8Array(32);\n  pack25519(d, a);\n  return d[0] & 1;\n}\n\nfunction unpack25519(o, n) {\n  var i;\n  for (i = 0; i < 16; i++) o[i] = n[2*i] + (n[2*i+1] << 8);\n  o[15] &= 0x7fff;\n}\n\nfunction A(o, a, b) {\n  for (var i = 0; i < 16; i++) o[i] = a[i] + b[i];\n}\n\nfunction Z(o, a, b) {\n  for (var i = 0; i < 16; i++) o[i] = a[i] - b[i];\n}\n\nfunction M(o, a, b) {\n  var v, c,\n     t0 = 0,  t1 = 0,  t2 = 0,  t3 = 0,  t4 = 0,  t5 = 0,  t6 = 0,  t7 = 0,\n     t8 = 0,  t9 = 0, t10 = 0, t11 = 0, t12 = 0, t13 = 0, t14 = 0, t15 = 0,\n    t16 = 0, t17 = 0, t18 = 0, t19 = 0, t20 = 0, t21 = 0, t22 = 0, t23 = 0,\n    t24 = 0, t25 = 0, t26 = 0, t27 = 0, t28 = 0, t29 = 0, t30 = 0,\n    b0 = b[0],\n    b1 = b[1],\n    b2 = b[2],\n    b3 = b[3],\n    b4 = b[4],\n    b5 = b[5],\n    b6 = b[6],\n    b7 = b[7],\n    b8 = b[8],\n    b9 = b[9],\n    b10 = b[10],\n    b11 = b[11],\n    b12 = b[12],\n    b13 = b[13],\n    b14 = b[14],\n    b15 = b[15];\n\n  v = a[0];\n  t0 += v * b0;\n  t1 += v * b1;\n  t2 += v * b2;\n  t3 += v * b3;\n  t4 += v * b4;\n  t5 += v * b5;\n  t6 += v * b6;\n  t7 += v * b7;\n  t8 += v * b8;\n  t9 += v * b9;\n  t10 += v * b10;\n  t11 += v * b11;\n  t12 += v * b12;\n  t13 += v * b13;\n  t14 += v * b14;\n  t15 += v * b15;\n  v = a[1];\n  t1 += v * b0;\n  t2 += v * b1;\n  t3 += v * b2;\n  t4 += v * b3;\n  t5 += v * b4;\n  t6 += v * b5;\n  t7 += v * b6;\n  t8 += v * b7;\n  t9 += v * b8;\n  t10 += v * b9;\n  t11 += v * b10;\n  t12 += v * b11;\n  t13 += v * b12;\n  t14 += v * b13;\n  t15 += v * b14;\n  t16 += v * b15;\n  v = a[2];\n  t2 += v * b0;\n  t3 += v * b1;\n  t4 += v * b2;\n  t5 += v * b3;\n  t6 += v * b4;\n  t7 += v * b5;\n  t8 += v * b6;\n  t9 += v * b7;\n  t10 += v * b8;\n  t11 += v * b9;\n  t12 += v * b10;\n  t13 += v * b11;\n  t14 += v * b12;\n  t15 += v * b13;\n  t16 += v * b14;\n  t17 += v * b15;\n  v = a[3];\n  t3 += v * b0;\n  t4 += v * b1;\n  t5 += v * b2;\n  t6 += v * b3;\n  t7 += v * b4;\n  t8 += v * b5;\n  t9 += v * b6;\n  t10 += v * b7;\n  t11 += v * b8;\n  t12 += v * b9;\n  t13 += v * b10;\n  t14 += v * b11;\n  t15 += v * b12;\n  t16 += v * b13;\n  t17 += v * b14;\n  t18 += v * b15;\n  v = a[4];\n  t4 += v * b0;\n  t5 += v * b1;\n  t6 += v * b2;\n  t7 += v * b3;\n  t8 += v * b4;\n  t9 += v * b5;\n  t10 += v * b6;\n  t11 += v * b7;\n  t12 += v * b8;\n  t13 += v * b9;\n  t14 += v * b10;\n  t15 += v * b11;\n  t16 += v * b12;\n  t17 += v * b13;\n  t18 += v * b14;\n  t19 += v * b15;\n  v = a[5];\n  t5 += v * b0;\n  t6 += v * b1;\n  t7 += v * b2;\n  t8 += v * b3;\n  t9 += v * b4;\n  t10 += v * b5;\n  t11 += v * b6;\n  t12 += v * b7;\n  t13 += v * b8;\n  t14 += v * b9;\n  t15 += v * b10;\n  t16 += v * b11;\n  t17 += v * b12;\n  t18 += v * b13;\n  t19 += v * b14;\n  t20 += v * b15;\n  v = a[6];\n  t6 += v * b0;\n  t7 += v * b1;\n  t8 += v * b2;\n  t9 += v * b3;\n  t10 += v * b4;\n  t11 += v * b5;\n  t12 += v * b6;\n  t13 += v * b7;\n  t14 += v * b8;\n  t15 += v * b9;\n  t16 += v * b10;\n  t17 += v * b11;\n  t18 += v * b12;\n  t19 += v * b13;\n  t20 += v * b14;\n  t21 += v * b15;\n  v = a[7];\n  t7 += v * b0;\n  t8 += v * b1;\n  t9 += v * b2;\n  t10 += v * b3;\n  t11 += v * b4;\n  t12 += v * b5;\n  t13 += v * b6;\n  t14 += v * b7;\n  t15 += v * b8;\n  t16 += v * b9;\n  t17 += v * b10;\n  t18 += v * b11;\n  t19 += v * b12;\n  t20 += v * b13;\n  t21 += v * b14;\n  t22 += v * b15;\n  v = a[8];\n  t8 += v * b0;\n  t9 += v * b1;\n  t10 += v * b2;\n  t11 += v * b3;\n  t12 += v * b4;\n  t13 += v * b5;\n  t14 += v * b6;\n  t15 += v * b7;\n  t16 += v * b8;\n  t17 += v * b9;\n  t18 += v * b10;\n  t19 += v * b11;\n  t20 += v * b12;\n  t21 += v * b13;\n  t22 += v * b14;\n  t23 += v * b15;\n  v = a[9];\n  t9 += v * b0;\n  t10 += v * b1;\n  t11 += v * b2;\n  t12 += v * b3;\n  t13 += v * b4;\n  t14 += v * b5;\n  t15 += v * b6;\n  t16 += v * b7;\n  t17 += v * b8;\n  t18 += v * b9;\n  t19 += v * b10;\n  t20 += v * b11;\n  t21 += v * b12;\n  t22 += v * b13;\n  t23 += v * b14;\n  t24 += v * b15;\n  v = a[10];\n  t10 += v * b0;\n  t11 += v * b1;\n  t12 += v * b2;\n  t13 += v * b3;\n  t14 += v * b4;\n  t15 += v * b5;\n  t16 += v * b6;\n  t17 += v * b7;\n  t18 += v * b8;\n  t19 += v * b9;\n  t20 += v * b10;\n  t21 += v * b11;\n  t22 += v * b12;\n  t23 += v * b13;\n  t24 += v * b14;\n  t25 += v * b15;\n  v = a[11];\n  t11 += v * b0;\n  t12 += v * b1;\n  t13 += v * b2;\n  t14 += v * b3;\n  t15 += v * b4;\n  t16 += v * b5;\n  t17 += v * b6;\n  t18 += v * b7;\n  t19 += v * b8;\n  t20 += v * b9;\n  t21 += v * b10;\n  t22 += v * b11;\n  t23 += v * b12;\n  t24 += v * b13;\n  t25 += v * b14;\n  t26 += v * b15;\n  v = a[12];\n  t12 += v * b0;\n  t13 += v * b1;\n  t14 += v * b2;\n  t15 += v * b3;\n  t16 += v * b4;\n  t17 += v * b5;\n  t18 += v * b6;\n  t19 += v * b7;\n  t20 += v * b8;\n  t21 += v * b9;\n  t22 += v * b10;\n  t23 += v * b11;\n  t24 += v * b12;\n  t25 += v * b13;\n  t26 += v * b14;\n  t27 += v * b15;\n  v = a[13];\n  t13 += v * b0;\n  t14 += v * b1;\n  t15 += v * b2;\n  t16 += v * b3;\n  t17 += v * b4;\n  t18 += v * b5;\n  t19 += v * b6;\n  t20 += v * b7;\n  t21 += v * b8;\n  t22 += v * b9;\n  t23 += v * b10;\n  t24 += v * b11;\n  t25 += v * b12;\n  t26 += v * b13;\n  t27 += v * b14;\n  t28 += v * b15;\n  v = a[14];\n  t14 += v * b0;\n  t15 += v * b1;\n  t16 += v * b2;\n  t17 += v * b3;\n  t18 += v * b4;\n  t19 += v * b5;\n  t20 += v * b6;\n  t21 += v * b7;\n  t22 += v * b8;\n  t23 += v * b9;\n  t24 += v * b10;\n  t25 += v * b11;\n  t26 += v * b12;\n  t27 += v * b13;\n  t28 += v * b14;\n  t29 += v * b15;\n  v = a[15];\n  t15 += v * b0;\n  t16 += v * b1;\n  t17 += v * b2;\n  t18 += v * b3;\n  t19 += v * b4;\n  t20 += v * b5;\n  t21 += v * b6;\n  t22 += v * b7;\n  t23 += v * b8;\n  t24 += v * b9;\n  t25 += v * b10;\n  t26 += v * b11;\n  t27 += v * b12;\n  t28 += v * b13;\n  t29 += v * b14;\n  t30 += v * b15;\n\n  t0  += 38 * t16;\n  t1  += 38 * t17;\n  t2  += 38 * t18;\n  t3  += 38 * t19;\n  t4  += 38 * t20;\n  t5  += 38 * t21;\n  t6  += 38 * t22;\n  t7  += 38 * t23;\n  t8  += 38 * t24;\n  t9  += 38 * t25;\n  t10 += 38 * t26;\n  t11 += 38 * t27;\n  t12 += 38 * t28;\n  t13 += 38 * t29;\n  t14 += 38 * t30;\n  // t15 left as is\n\n  // first car\n  c = 1;\n  v =  t0 + c + 65535; c = Math.floor(v / 65536);  t0 = v - c * 65536;\n  v =  t1 + c + 65535; c = Math.floor(v / 65536);  t1 = v - c * 65536;\n  v =  t2 + c + 65535; c = Math.floor(v / 65536);  t2 = v - c * 65536;\n  v =  t3 + c + 65535; c = Math.floor(v / 65536);  t3 = v - c * 65536;\n  v =  t4 + c + 65535; c = Math.floor(v / 65536);  t4 = v - c * 65536;\n  v =  t5 + c + 65535; c = Math.floor(v / 65536);  t5 = v - c * 65536;\n  v =  t6 + c + 65535; c = Math.floor(v / 65536);  t6 = v - c * 65536;\n  v =  t7 + c + 65535; c = Math.floor(v / 65536);  t7 = v - c * 65536;\n  v =  t8 + c + 65535; c = Math.floor(v / 65536);  t8 = v - c * 65536;\n  v =  t9 + c + 65535; c = Math.floor(v / 65536);  t9 = v - c * 65536;\n  v = t10 + c + 65535; c = Math.floor(v / 65536); t10 = v - c * 65536;\n  v = t11 + c + 65535; c = Math.floor(v / 65536); t11 = v - c * 65536;\n  v = t12 + c + 65535; c = Math.floor(v / 65536); t12 = v - c * 65536;\n  v = t13 + c + 65535; c = Math.floor(v / 65536); t13 = v - c * 65536;\n  v = t14 + c + 65535; c = Math.floor(v / 65536); t14 = v - c * 65536;\n  v = t15 + c + 65535; c = Math.floor(v / 65536); t15 = v - c * 65536;\n  t0 += c-1 + 37 * (c-1);\n\n  // second car\n  c = 1;\n  v =  t0 + c + 65535; c = Math.floor(v / 65536);  t0 = v - c * 65536;\n  v =  t1 + c + 65535; c = Math.floor(v / 65536);  t1 = v - c * 65536;\n  v =  t2 + c + 65535; c = Math.floor(v / 65536);  t2 = v - c * 65536;\n  v =  t3 + c + 65535; c = Math.floor(v / 65536);  t3 = v - c * 65536;\n  v =  t4 + c + 65535; c = Math.floor(v / 65536);  t4 = v - c * 65536;\n  v =  t5 + c + 65535; c = Math.floor(v / 65536);  t5 = v - c * 65536;\n  v =  t6 + c + 65535; c = Math.floor(v / 65536);  t6 = v - c * 65536;\n  v =  t7 + c + 65535; c = Math.floor(v / 65536);  t7 = v - c * 65536;\n  v =  t8 + c + 65535; c = Math.floor(v / 65536);  t8 = v - c * 65536;\n  v =  t9 + c + 65535; c = Math.floor(v / 65536);  t9 = v - c * 65536;\n  v = t10 + c + 65535; c = Math.floor(v / 65536); t10 = v - c * 65536;\n  v = t11 + c + 65535; c = Math.floor(v / 65536); t11 = v - c * 65536;\n  v = t12 + c + 65535; c = Math.floor(v / 65536); t12 = v - c * 65536;\n  v = t13 + c + 65535; c = Math.floor(v / 65536); t13 = v - c * 65536;\n  v = t14 + c + 65535; c = Math.floor(v / 65536); t14 = v - c * 65536;\n  v = t15 + c + 65535; c = Math.floor(v / 65536); t15 = v - c * 65536;\n  t0 += c-1 + 37 * (c-1);\n\n  o[ 0] = t0;\n  o[ 1] = t1;\n  o[ 2] = t2;\n  o[ 3] = t3;\n  o[ 4] = t4;\n  o[ 5] = t5;\n  o[ 6] = t6;\n  o[ 7] = t7;\n  o[ 8] = t8;\n  o[ 9] = t9;\n  o[10] = t10;\n  o[11] = t11;\n  o[12] = t12;\n  o[13] = t13;\n  o[14] = t14;\n  o[15] = t15;\n}\n\nfunction S(o, a) {\n  M(o, a, a);\n}\n\nfunction inv25519(o, i) {\n  var c = gf();\n  var a;\n  for (a = 0; a < 16; a++) c[a] = i[a];\n  for (a = 253; a >= 0; a--) {\n    S(c, c);\n    if(a !== 2 && a !== 4) M(c, c, i);\n  }\n  for (a = 0; a < 16; a++) o[a] = c[a];\n}\n\nfunction pow2523(o, i) {\n  var c = gf();\n  var a;\n  for (a = 0; a < 16; a++) c[a] = i[a];\n  for (a = 250; a >= 0; a--) {\n      S(c, c);\n      if(a !== 1) M(c, c, i);\n  }\n  for (a = 0; a < 16; a++) o[a] = c[a];\n}\n\nfunction crypto_scalarmult(q, n, p) {\n  var z = new Uint8Array(32);\n  var x = new Float64Array(80), r, i;\n  var a = gf(), b = gf(), c = gf(),\n      d = gf(), e = gf(), f = gf();\n  for (i = 0; i < 31; i++) z[i] = n[i];\n  z[31]=(n[31]&127)|64;\n  z[0]&=248;\n  unpack25519(x,p);\n  for (i = 0; i < 16; i++) {\n    b[i]=x[i];\n    d[i]=a[i]=c[i]=0;\n  }\n  a[0]=d[0]=1;\n  for (i=254; i>=0; --i) {\n    r=(z[i>>>3]>>>(i&7))&1;\n    sel25519(a,b,r);\n    sel25519(c,d,r);\n    A(e,a,c);\n    Z(a,a,c);\n    A(c,b,d);\n    Z(b,b,d);\n    S(d,e);\n    S(f,a);\n    M(a,c,a);\n    M(c,b,e);\n    A(e,a,c);\n    Z(a,a,c);\n    S(b,a);\n    Z(c,d,f);\n    M(a,c,_121665);\n    A(a,a,d);\n    M(c,c,a);\n    M(a,d,f);\n    M(d,b,x);\n    S(b,e);\n    sel25519(a,b,r);\n    sel25519(c,d,r);\n  }\n  for (i = 0; i < 16; i++) {\n    x[i+16]=a[i];\n    x[i+32]=c[i];\n    x[i+48]=b[i];\n    x[i+64]=d[i];\n  }\n  var x32 = x.subarray(32);\n  var x16 = x.subarray(16);\n  inv25519(x32,x32);\n  M(x16,x16,x32);\n  pack25519(q,x16);\n  return 0;\n}\n\nfunction crypto_scalarmult_base(q, n) {\n  return crypto_scalarmult(q, n, _9);\n}\n\nfunction crypto_box_keypair(y, x) {\n  randombytes(x, 32);\n  return crypto_scalarmult_base(y, x);\n}\n\nfunction crypto_box_beforenm(k, y, x) {\n  var s = new Uint8Array(32);\n  crypto_scalarmult(s, x, y);\n  return crypto_core_hsalsa20(k, _0, s, sigma);\n}\n\nvar crypto_box_afternm = crypto_secretbox;\nvar crypto_box_open_afternm = crypto_secretbox_open;\n\nfunction crypto_box(c, m, d, n, y, x) {\n  var k = new Uint8Array(32);\n  crypto_box_beforenm(k, y, x);\n  return crypto_box_afternm(c, m, d, n, k);\n}\n\nfunction crypto_box_open(m, c, d, n, y, x) {\n  var k = new Uint8Array(32);\n  crypto_box_beforenm(k, y, x);\n  return crypto_box_open_afternm(m, c, d, n, k);\n}\n\nvar K = [\n  0x428a2f98, 0xd728ae22, 0x71374491, 0x23ef65cd,\n  0xb5c0fbcf, 0xec4d3b2f, 0xe9b5dba5, 0x8189dbbc,\n  0x3956c25b, 0xf348b538, 0x59f111f1, 0xb605d019,\n  0x923f82a4, 0xaf194f9b, 0xab1c5ed5, 0xda6d8118,\n  0xd807aa98, 0xa3030242, 0x12835b01, 0x45706fbe,\n  0x243185be, 0x4ee4b28c, 0x550c7dc3, 0xd5ffb4e2,\n  0x72be5d74, 0xf27b896f, 0x80deb1fe, 0x3b1696b1,\n  0x9bdc06a7, 0x25c71235, 0xc19bf174, 0xcf692694,\n  0xe49b69c1, 0x9ef14ad2, 0xefbe4786, 0x384f25e3,\n  0x0fc19dc6, 0x8b8cd5b5, 0x240ca1cc, 0x77ac9c65,\n  0x2de92c6f, 0x592b0275, 0x4a7484aa, 0x6ea6e483,\n  0x5cb0a9dc, 0xbd41fbd4, 0x76f988da, 0x831153b5,\n  0x983e5152, 0xee66dfab, 0xa831c66d, 0x2db43210,\n  0xb00327c8, 0x98fb213f, 0xbf597fc7, 0xbeef0ee4,\n  0xc6e00bf3, 0x3da88fc2, 0xd5a79147, 0x930aa725,\n  0x06ca6351, 0xe003826f, 0x14292967, 0x0a0e6e70,\n  0x27b70a85, 0x46d22ffc, 0x2e1b2138, 0x5c26c926,\n  0x4d2c6dfc, 0x5ac42aed, 0x53380d13, 0x9d95b3df,\n  0x650a7354, 0x8baf63de, 0x766a0abb, 0x3c77b2a8,\n  0x81c2c92e, 0x47edaee6, 0x92722c85, 0x1482353b,\n  0xa2bfe8a1, 0x4cf10364, 0xa81a664b, 0xbc423001,\n  0xc24b8b70, 0xd0f89791, 0xc76c51a3, 0x0654be30,\n  0xd192e819, 0xd6ef5218, 0xd6990624, 0x5565a910,\n  0xf40e3585, 0x5771202a, 0x106aa070, 0x32bbd1b8,\n  0x19a4c116, 0xb8d2d0c8, 0x1e376c08, 0x5141ab53,\n  0x2748774c, 0xdf8eeb99, 0x34b0bcb5, 0xe19b48a8,\n  0x391c0cb3, 0xc5c95a63, 0x4ed8aa4a, 0xe3418acb,\n  0x5b9cca4f, 0x7763e373, 0x682e6ff3, 0xd6b2b8a3,\n  0x748f82ee, 0x5defb2fc, 0x78a5636f, 0x43172f60,\n  0x84c87814, 0xa1f0ab72, 0x8cc70208, 0x1a6439ec,\n  0x90befffa, 0x23631e28, 0xa4506ceb, 0xde82bde9,\n  0xbef9a3f7, 0xb2c67915, 0xc67178f2, 0xe372532b,\n  0xca273ece, 0xea26619c, 0xd186b8c7, 0x21c0c207,\n  0xeada7dd6, 0xcde0eb1e, 0xf57d4f7f, 0xee6ed178,\n  0x06f067aa, 0x72176fba, 0x0a637dc5, 0xa2c898a6,\n  0x113f9804, 0xbef90dae, 0x1b710b35, 0x131c471b,\n  0x28db77f5, 0x23047d84, 0x32caab7b, 0x40c72493,\n  0x3c9ebe0a, 0x15c9bebc, 0x431d67c4, 0x9c100d4c,\n  0x4cc5d4be, 0xcb3e42b6, 0x597f299c, 0xfc657e2a,\n  0x5fcb6fab, 0x3ad6faec, 0x6c44198c, 0x4a475817\n];\n\nfunction crypto_hashblocks_hl(hh, hl, m, n) {\n  var wh = new Int32Array(16), wl = new Int32Array(16),\n      bh0, bh1, bh2, bh3, bh4, bh5, bh6, bh7,\n      bl0, bl1, bl2, bl3, bl4, bl5, bl6, bl7,\n      th, tl, i, j, h, l, a, b, c, d;\n\n  var ah0 = hh[0],\n      ah1 = hh[1],\n      ah2 = hh[2],\n      ah3 = hh[3],\n      ah4 = hh[4],\n      ah5 = hh[5],\n      ah6 = hh[6],\n      ah7 = hh[7],\n\n      al0 = hl[0],\n      al1 = hl[1],\n      al2 = hl[2],\n      al3 = hl[3],\n      al4 = hl[4],\n      al5 = hl[5],\n      al6 = hl[6],\n      al7 = hl[7];\n\n  var pos = 0;\n  while (n >= 128) {\n    for (i = 0; i < 16; i++) {\n      j = 8 * i + pos;\n      wh[i] = (m[j+0] << 24) | (m[j+1] << 16) | (m[j+2] << 8) | m[j+3];\n      wl[i] = (m[j+4] << 24) | (m[j+5] << 16) | (m[j+6] << 8) | m[j+7];\n    }\n    for (i = 0; i < 80; i++) {\n      bh0 = ah0;\n      bh1 = ah1;\n      bh2 = ah2;\n      bh3 = ah3;\n      bh4 = ah4;\n      bh5 = ah5;\n      bh6 = ah6;\n      bh7 = ah7;\n\n      bl0 = al0;\n      bl1 = al1;\n      bl2 = al2;\n      bl3 = al3;\n      bl4 = al4;\n      bl5 = al5;\n      bl6 = al6;\n      bl7 = al7;\n\n      // add\n      h = ah7;\n      l = al7;\n\n      a = l & 0xffff; b = l >>> 16;\n      c = h & 0xffff; d = h >>> 16;\n\n      // Sigma1\n      h = ((ah4 >>> 14) | (al4 << (32-14))) ^ ((ah4 >>> 18) | (al4 << (32-18))) ^ ((al4 >>> (41-32)) | (ah4 << (32-(41-32))));\n      l = ((al4 >>> 14) | (ah4 << (32-14))) ^ ((al4 >>> 18) | (ah4 << (32-18))) ^ ((ah4 >>> (41-32)) | (al4 << (32-(41-32))));\n\n      a += l & 0xffff; b += l >>> 16;\n      c += h & 0xffff; d += h >>> 16;\n\n      // Ch\n      h = (ah4 & ah5) ^ (~ah4 & ah6);\n      l = (al4 & al5) ^ (~al4 & al6);\n\n      a += l & 0xffff; b += l >>> 16;\n      c += h & 0xffff; d += h >>> 16;\n\n      // K\n      h = K[i*2];\n      l = K[i*2+1];\n\n      a += l & 0xffff; b += l >>> 16;\n      c += h & 0xffff; d += h >>> 16;\n\n      // w\n      h = wh[i%16];\n      l = wl[i%16];\n\n      a += l & 0xffff; b += l >>> 16;\n      c += h & 0xffff; d += h >>> 16;\n\n      b += a >>> 16;\n      c += b >>> 16;\n      d += c >>> 16;\n\n      th = c & 0xffff | d << 16;\n      tl = a & 0xffff | b << 16;\n\n      // add\n      h = th;\n      l = tl;\n\n      a = l & 0xffff; b = l >>> 16;\n      c = h & 0xffff; d = h >>> 16;\n\n      // Sigma0\n      h = ((ah0 >>> 28) | (al0 << (32-28))) ^ ((al0 >>> (34-32)) | (ah0 << (32-(34-32)))) ^ ((al0 >>> (39-32)) | (ah0 << (32-(39-32))));\n      l = ((al0 >>> 28) | (ah0 << (32-28))) ^ ((ah0 >>> (34-32)) | (al0 << (32-(34-32)))) ^ ((ah0 >>> (39-32)) | (al0 << (32-(39-32))));\n\n      a += l & 0xffff; b += l >>> 16;\n      c += h & 0xffff; d += h >>> 16;\n\n      // Maj\n      h = (ah0 & ah1) ^ (ah0 & ah2) ^ (ah1 & ah2);\n      l = (al0 & al1) ^ (al0 & al2) ^ (al1 & al2);\n\n      a += l & 0xffff; b += l >>> 16;\n      c += h & 0xffff; d += h >>> 16;\n\n      b += a >>> 16;\n      c += b >>> 16;\n      d += c >>> 16;\n\n      bh7 = (c & 0xffff) | (d << 16);\n      bl7 = (a & 0xffff) | (b << 16);\n\n      // add\n      h = bh3;\n      l = bl3;\n\n      a = l & 0xffff; b = l >>> 16;\n      c = h & 0xffff; d = h >>> 16;\n\n      h = th;\n      l = tl;\n\n      a += l & 0xffff; b += l >>> 16;\n      c += h & 0xffff; d += h >>> 16;\n\n      b += a >>> 16;\n      c += b >>> 16;\n      d += c >>> 16;\n\n      bh3 = (c & 0xffff) | (d << 16);\n      bl3 = (a & 0xffff) | (b << 16);\n\n      ah1 = bh0;\n      ah2 = bh1;\n      ah3 = bh2;\n      ah4 = bh3;\n      ah5 = bh4;\n      ah6 = bh5;\n      ah7 = bh6;\n      ah0 = bh7;\n\n      al1 = bl0;\n      al2 = bl1;\n      al3 = bl2;\n      al4 = bl3;\n      al5 = bl4;\n      al6 = bl5;\n      al7 = bl6;\n      al0 = bl7;\n\n      if (i%16 === 15) {\n        for (j = 0; j < 16; j++) {\n          // add\n          h = wh[j];\n          l = wl[j];\n\n          a = l & 0xffff; b = l >>> 16;\n          c = h & 0xffff; d = h >>> 16;\n\n          h = wh[(j+9)%16];\n          l = wl[(j+9)%16];\n\n          a += l & 0xffff; b += l >>> 16;\n          c += h & 0xffff; d += h >>> 16;\n\n          // sigma0\n          th = wh[(j+1)%16];\n          tl = wl[(j+1)%16];\n          h = ((th >>> 1) | (tl << (32-1))) ^ ((th >>> 8) | (tl << (32-8))) ^ (th >>> 7);\n          l = ((tl >>> 1) | (th << (32-1))) ^ ((tl >>> 8) | (th << (32-8))) ^ ((tl >>> 7) | (th << (32-7)));\n\n          a += l & 0xffff; b += l >>> 16;\n          c += h & 0xffff; d += h >>> 16;\n\n          // sigma1\n          th = wh[(j+14)%16];\n          tl = wl[(j+14)%16];\n          h = ((th >>> 19) | (tl << (32-19))) ^ ((tl >>> (61-32)) | (th << (32-(61-32)))) ^ (th >>> 6);\n          l = ((tl >>> 19) | (th << (32-19))) ^ ((th >>> (61-32)) | (tl << (32-(61-32)))) ^ ((tl >>> 6) | (th << (32-6)));\n\n          a += l & 0xffff; b += l >>> 16;\n          c += h & 0xffff; d += h >>> 16;\n\n          b += a >>> 16;\n          c += b >>> 16;\n          d += c >>> 16;\n\n          wh[j] = (c & 0xffff) | (d << 16);\n          wl[j] = (a & 0xffff) | (b << 16);\n        }\n      }\n    }\n\n    // add\n    h = ah0;\n    l = al0;\n\n    a = l & 0xffff; b = l >>> 16;\n    c = h & 0xffff; d = h >>> 16;\n\n    h = hh[0];\n    l = hl[0];\n\n    a += l & 0xffff; b += l >>> 16;\n    c += h & 0xffff; d += h >>> 16;\n\n    b += a >>> 16;\n    c += b >>> 16;\n    d += c >>> 16;\n\n    hh[0] = ah0 = (c & 0xffff) | (d << 16);\n    hl[0] = al0 = (a & 0xffff) | (b << 16);\n\n    h = ah1;\n    l = al1;\n\n    a = l & 0xffff; b = l >>> 16;\n    c = h & 0xffff; d = h >>> 16;\n\n    h = hh[1];\n    l = hl[1];\n\n    a += l & 0xffff; b += l >>> 16;\n    c += h & 0xffff; d += h >>> 16;\n\n    b += a >>> 16;\n    c += b >>> 16;\n    d += c >>> 16;\n\n    hh[1] = ah1 = (c & 0xffff) | (d << 16);\n    hl[1] = al1 = (a & 0xffff) | (b << 16);\n\n    h = ah2;\n    l = al2;\n\n    a = l & 0xffff; b = l >>> 16;\n    c = h & 0xffff; d = h >>> 16;\n\n    h = hh[2];\n    l = hl[2];\n\n    a += l & 0xffff; b += l >>> 16;\n    c += h & 0xffff; d += h >>> 16;\n\n    b += a >>> 16;\n    c += b >>> 16;\n    d += c >>> 16;\n\n    hh[2] = ah2 = (c & 0xffff) | (d << 16);\n    hl[2] = al2 = (a & 0xffff) | (b << 16);\n\n    h = ah3;\n    l = al3;\n\n    a = l & 0xffff; b = l >>> 16;\n    c = h & 0xffff; d = h >>> 16;\n\n    h = hh[3];\n    l = hl[3];\n\n    a += l & 0xffff; b += l >>> 16;\n    c += h & 0xffff; d += h >>> 16;\n\n    b += a >>> 16;\n    c += b >>> 16;\n    d += c >>> 16;\n\n    hh[3] = ah3 = (c & 0xffff) | (d << 16);\n    hl[3] = al3 = (a & 0xffff) | (b << 16);\n\n    h = ah4;\n    l = al4;\n\n    a = l & 0xffff; b = l >>> 16;\n    c = h & 0xffff; d = h >>> 16;\n\n    h = hh[4];\n    l = hl[4];\n\n    a += l & 0xffff; b += l >>> 16;\n    c += h & 0xffff; d += h >>> 16;\n\n    b += a >>> 16;\n    c += b >>> 16;\n    d += c >>> 16;\n\n    hh[4] = ah4 = (c & 0xffff) | (d << 16);\n    hl[4] = al4 = (a & 0xffff) | (b << 16);\n\n    h = ah5;\n    l = al5;\n\n    a = l & 0xffff; b = l >>> 16;\n    c = h & 0xffff; d = h >>> 16;\n\n    h = hh[5];\n    l = hl[5];\n\n    a += l & 0xffff; b += l >>> 16;\n    c += h & 0xffff; d += h >>> 16;\n\n    b += a >>> 16;\n    c += b >>> 16;\n    d += c >>> 16;\n\n    hh[5] = ah5 = (c & 0xffff) | (d << 16);\n    hl[5] = al5 = (a & 0xffff) | (b << 16);\n\n    h = ah6;\n    l = al6;\n\n    a = l & 0xffff; b = l >>> 16;\n    c = h & 0xffff; d = h >>> 16;\n\n    h = hh[6];\n    l = hl[6];\n\n    a += l & 0xffff; b += l >>> 16;\n    c += h & 0xffff; d += h >>> 16;\n\n    b += a >>> 16;\n    c += b >>> 16;\n    d += c >>> 16;\n\n    hh[6] = ah6 = (c & 0xffff) | (d << 16);\n    hl[6] = al6 = (a & 0xffff) | (b << 16);\n\n    h = ah7;\n    l = al7;\n\n    a = l & 0xffff; b = l >>> 16;\n    c = h & 0xffff; d = h >>> 16;\n\n    h = hh[7];\n    l = hl[7];\n\n    a += l & 0xffff; b += l >>> 16;\n    c += h & 0xffff; d += h >>> 16;\n\n    b += a >>> 16;\n    c += b >>> 16;\n    d += c >>> 16;\n\n    hh[7] = ah7 = (c & 0xffff) | (d << 16);\n    hl[7] = al7 = (a & 0xffff) | (b << 16);\n\n    pos += 128;\n    n -= 128;\n  }\n\n  return n;\n}\n\nfunction crypto_hash(out, m, n) {\n  var hh = new Int32Array(8),\n      hl = new Int32Array(8),\n      x = new Uint8Array(256),\n      i, b = n;\n\n  hh[0] = 0x6a09e667;\n  hh[1] = 0xbb67ae85;\n  hh[2] = 0x3c6ef372;\n  hh[3] = 0xa54ff53a;\n  hh[4] = 0x510e527f;\n  hh[5] = 0x9b05688c;\n  hh[6] = 0x1f83d9ab;\n  hh[7] = 0x5be0cd19;\n\n  hl[0] = 0xf3bcc908;\n  hl[1] = 0x84caa73b;\n  hl[2] = 0xfe94f82b;\n  hl[3] = 0x5f1d36f1;\n  hl[4] = 0xade682d1;\n  hl[5] = 0x2b3e6c1f;\n  hl[6] = 0xfb41bd6b;\n  hl[7] = 0x137e2179;\n\n  crypto_hashblocks_hl(hh, hl, m, n);\n  n %= 128;\n\n  for (i = 0; i < n; i++) x[i] = m[b-n+i];\n  x[n] = 128;\n\n  n = 256-128*(n<112?1:0);\n  x[n-9] = 0;\n  ts64(x, n-8,  (b / 0x20000000) | 0, b << 3);\n  crypto_hashblocks_hl(hh, hl, x, n);\n\n  for (i = 0; i < 8; i++) ts64(out, 8*i, hh[i], hl[i]);\n\n  return 0;\n}\n\nfunction add(p, q) {\n  var a = gf(), b = gf(), c = gf(),\n      d = gf(), e = gf(), f = gf(),\n      g = gf(), h = gf(), t = gf();\n\n  Z(a, p[1], p[0]);\n  Z(t, q[1], q[0]);\n  M(a, a, t);\n  A(b, p[0], p[1]);\n  A(t, q[0], q[1]);\n  M(b, b, t);\n  M(c, p[3], q[3]);\n  M(c, c, D2);\n  M(d, p[2], q[2]);\n  A(d, d, d);\n  Z(e, b, a);\n  Z(f, d, c);\n  A(g, d, c);\n  A(h, b, a);\n\n  M(p[0], e, f);\n  M(p[1], h, g);\n  M(p[2], g, f);\n  M(p[3], e, h);\n}\n\nfunction cswap(p, q, b) {\n  var i;\n  for (i = 0; i < 4; i++) {\n    sel25519(p[i], q[i], b);\n  }\n}\n\nfunction pack(r, p) {\n  var tx = gf(), ty = gf(), zi = gf();\n  inv25519(zi, p[2]);\n  M(tx, p[0], zi);\n  M(ty, p[1], zi);\n  pack25519(r, ty);\n  r[31] ^= par25519(tx) << 7;\n}\n\nfunction scalarmult(p, q, s) {\n  var b, i;\n  set25519(p[0], gf0);\n  set25519(p[1], gf1);\n  set25519(p[2], gf1);\n  set25519(p[3], gf0);\n  for (i = 255; i >= 0; --i) {\n    b = (s[(i/8)|0] >> (i&7)) & 1;\n    cswap(p, q, b);\n    add(q, p);\n    add(p, p);\n    cswap(p, q, b);\n  }\n}\n\nfunction scalarbase(p, s) {\n  var q = [gf(), gf(), gf(), gf()];\n  set25519(q[0], X);\n  set25519(q[1], Y);\n  set25519(q[2], gf1);\n  M(q[3], X, Y);\n  scalarmult(p, q, s);\n}\n\nfunction crypto_sign_keypair(pk, sk, seeded) {\n  var d = new Uint8Array(64);\n  var p = [gf(), gf(), gf(), gf()];\n  var i;\n\n  if (!seeded) randombytes(sk, 32);\n  crypto_hash(d, sk, 32);\n  d[0] &= 248;\n  d[31] &= 127;\n  d[31] |= 64;\n\n  scalarbase(p, d);\n  pack(pk, p);\n\n  for (i = 0; i < 32; i++) sk[i+32] = pk[i];\n  return 0;\n}\n\nvar L = new Float64Array([0xed, 0xd3, 0xf5, 0x5c, 0x1a, 0x63, 0x12, 0x58, 0xd6, 0x9c, 0xf7, 0xa2, 0xde, 0xf9, 0xde, 0x14, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0x10]);\n\nfunction modL(r, x) {\n  var carry, i, j, k;\n  for (i = 63; i >= 32; --i) {\n    carry = 0;\n    for (j = i - 32, k = i - 12; j < k; ++j) {\n      x[j] += carry - 16 * x[i] * L[j - (i - 32)];\n      carry = Math.floor((x[j] + 128) / 256);\n      x[j] -= carry * 256;\n    }\n    x[j] += carry;\n    x[i] = 0;\n  }\n  carry = 0;\n  for (j = 0; j < 32; j++) {\n    x[j] += carry - (x[31] >> 4) * L[j];\n    carry = x[j] >> 8;\n    x[j] &= 255;\n  }\n  for (j = 0; j < 32; j++) x[j] -= carry * L[j];\n  for (i = 0; i < 32; i++) {\n    x[i+1] += x[i] >> 8;\n    r[i] = x[i] & 255;\n  }\n}\n\nfunction reduce(r) {\n  var x = new Float64Array(64), i;\n  for (i = 0; i < 64; i++) x[i] = r[i];\n  for (i = 0; i < 64; i++) r[i] = 0;\n  modL(r, x);\n}\n\n// Note: difference from C - smlen returned, not passed as argument.\nfunction crypto_sign(sm, m, n, sk) {\n  var d = new Uint8Array(64), h = new Uint8Array(64), r = new Uint8Array(64);\n  var i, j, x = new Float64Array(64);\n  var p = [gf(), gf(), gf(), gf()];\n\n  crypto_hash(d, sk, 32);\n  d[0] &= 248;\n  d[31] &= 127;\n  d[31] |= 64;\n\n  var smlen = n + 64;\n  for (i = 0; i < n; i++) sm[64 + i] = m[i];\n  for (i = 0; i < 32; i++) sm[32 + i] = d[32 + i];\n\n  crypto_hash(r, sm.subarray(32), n+32);\n  reduce(r);\n  scalarbase(p, r);\n  pack(sm, p);\n\n  for (i = 32; i < 64; i++) sm[i] = sk[i];\n  crypto_hash(h, sm, n + 64);\n  reduce(h);\n\n  for (i = 0; i < 64; i++) x[i] = 0;\n  for (i = 0; i < 32; i++) x[i] = r[i];\n  for (i = 0; i < 32; i++) {\n    for (j = 0; j < 32; j++) {\n      x[i+j] += h[i] * d[j];\n    }\n  }\n\n  modL(sm.subarray(32), x);\n  return smlen;\n}\n\nfunction unpackneg(r, p) {\n  var t = gf(), chk = gf(), num = gf(),\n      den = gf(), den2 = gf(), den4 = gf(),\n      den6 = gf();\n\n  set25519(r[2], gf1);\n  unpack25519(r[1], p);\n  S(num, r[1]);\n  M(den, num, D);\n  Z(num, num, r[2]);\n  A(den, r[2], den);\n\n  S(den2, den);\n  S(den4, den2);\n  M(den6, den4, den2);\n  M(t, den6, num);\n  M(t, t, den);\n\n  pow2523(t, t);\n  M(t, t, num);\n  M(t, t, den);\n  M(t, t, den);\n  M(r[0], t, den);\n\n  S(chk, r[0]);\n  M(chk, chk, den);\n  if (neq25519(chk, num)) M(r[0], r[0], I);\n\n  S(chk, r[0]);\n  M(chk, chk, den);\n  if (neq25519(chk, num)) return -1;\n\n  if (par25519(r[0]) === (p[31]>>7)) Z(r[0], gf0, r[0]);\n\n  M(r[3], r[0], r[1]);\n  return 0;\n}\n\nfunction crypto_sign_open(m, sm, n, pk) {\n  var i;\n  var t = new Uint8Array(32), h = new Uint8Array(64);\n  var p = [gf(), gf(), gf(), gf()],\n      q = [gf(), gf(), gf(), gf()];\n\n  if (n < 64) return -1;\n\n  if (unpackneg(q, pk)) return -1;\n\n  for (i = 0; i < n; i++) m[i] = sm[i];\n  for (i = 0; i < 32; i++) m[i+32] = pk[i];\n  crypto_hash(h, m, n);\n  reduce(h);\n  scalarmult(p, q, h);\n\n  scalarbase(q, sm.subarray(32));\n  add(p, q);\n  pack(t, p);\n\n  n -= 64;\n  if (crypto_verify_32(sm, 0, t, 0)) {\n    for (i = 0; i < n; i++) m[i] = 0;\n    return -1;\n  }\n\n  for (i = 0; i < n; i++) m[i] = sm[i + 64];\n  return n;\n}\n\nvar crypto_secretbox_KEYBYTES = 32,\n    crypto_secretbox_NONCEBYTES = 24,\n    crypto_secretbox_ZEROBYTES = 32,\n    crypto_secretbox_BOXZEROBYTES = 16,\n    crypto_scalarmult_BYTES = 32,\n    crypto_scalarmult_SCALARBYTES = 32,\n    crypto_box_PUBLICKEYBYTES = 32,\n    crypto_box_SECRETKEYBYTES = 32,\n    crypto_box_BEFORENMBYTES = 32,\n    crypto_box_NONCEBYTES = crypto_secretbox_NONCEBYTES,\n    crypto_box_ZEROBYTES = crypto_secretbox_ZEROBYTES,\n    crypto_box_BOXZEROBYTES = crypto_secretbox_BOXZEROBYTES,\n    crypto_sign_BYTES = 64,\n    crypto_sign_PUBLICKEYBYTES = 32,\n    crypto_sign_SECRETKEYBYTES = 64,\n    crypto_sign_SEEDBYTES = 32,\n    crypto_hash_BYTES = 64;\n\nnacl.lowlevel = {\n  crypto_core_hsalsa20: crypto_core_hsalsa20,\n  crypto_stream_xor: crypto_stream_xor,\n  crypto_stream: crypto_stream,\n  crypto_stream_salsa20_xor: crypto_stream_salsa20_xor,\n  crypto_stream_salsa20: crypto_stream_salsa20,\n  crypto_onetimeauth: crypto_onetimeauth,\n  crypto_onetimeauth_verify: crypto_onetimeauth_verify,\n  crypto_verify_16: crypto_verify_16,\n  crypto_verify_32: crypto_verify_32,\n  crypto_secretbox: crypto_secretbox,\n  crypto_secretbox_open: crypto_secretbox_open,\n  crypto_scalarmult: crypto_scalarmult,\n  crypto_scalarmult_base: crypto_scalarmult_base,\n  crypto_box_beforenm: crypto_box_beforenm,\n  crypto_box_afternm: crypto_box_afternm,\n  crypto_box: crypto_box,\n  crypto_box_open: crypto_box_open,\n  crypto_box_keypair: crypto_box_keypair,\n  crypto_hash: crypto_hash,\n  crypto_sign: crypto_sign,\n  crypto_sign_keypair: crypto_sign_keypair,\n  crypto_sign_open: crypto_sign_open,\n\n  crypto_secretbox_KEYBYTES: crypto_secretbox_KEYBYTES,\n  crypto_secretbox_NONCEBYTES: crypto_secretbox_NONCEBYTES,\n  crypto_secretbox_ZEROBYTES: crypto_secretbox_ZEROBYTES,\n  crypto_secretbox_BOXZEROBYTES: crypto_secretbox_BOXZEROBYTES,\n  crypto_scalarmult_BYTES: crypto_scalarmult_BYTES,\n  crypto_scalarmult_SCALARBYTES: crypto_scalarmult_SCALARBYTES,\n  crypto_box_PUBLICKEYBYTES: crypto_box_PUBLICKEYBYTES,\n  crypto_box_SECRETKEYBYTES: crypto_box_SECRETKEYBYTES,\n  crypto_box_BEFORENMBYTES: crypto_box_BEFORENMBYTES,\n  crypto_box_NONCEBYTES: crypto_box_NONCEBYTES,\n  crypto_box_ZEROBYTES: crypto_box_ZEROBYTES,\n  crypto_box_BOXZEROBYTES: crypto_box_BOXZEROBYTES,\n  crypto_sign_BYTES: crypto_sign_BYTES,\n  crypto_sign_PUBLICKEYBYTES: crypto_sign_PUBLICKEYBYTES,\n  crypto_sign_SECRETKEYBYTES: crypto_sign_SECRETKEYBYTES,\n  crypto_sign_SEEDBYTES: crypto_sign_SEEDBYTES,\n  crypto_hash_BYTES: crypto_hash_BYTES,\n\n  gf: gf,\n  D: D,\n  L: L,\n  pack25519: pack25519,\n  unpack25519: unpack25519,\n  M: M,\n  A: A,\n  S: S,\n  Z: Z,\n  pow2523: pow2523,\n  add: add,\n  set25519: set25519,\n  modL: modL,\n  scalarmult: scalarmult,\n  scalarbase: scalarbase,\n};\n\n/* High-level API */\n\nfunction checkLengths(k, n) {\n  if (k.length !== crypto_secretbox_KEYBYTES) throw new Error('bad key size');\n  if (n.length !== crypto_secretbox_NONCEBYTES) throw new Error('bad nonce size');\n}\n\nfunction checkBoxLengths(pk, sk) {\n  if (pk.length !== crypto_box_PUBLICKEYBYTES) throw new Error('bad public key size');\n  if (sk.length !== crypto_box_SECRETKEYBYTES) throw new Error('bad secret key size');\n}\n\nfunction checkArrayTypes() {\n  for (var i = 0; i < arguments.length; i++) {\n    if (!(arguments[i] instanceof Uint8Array))\n      throw new TypeError('unexpected type, use Uint8Array');\n  }\n}\n\nfunction cleanup(arr) {\n  for (var i = 0; i < arr.length; i++) arr[i] = 0;\n}\n\nnacl.randomBytes = function(n) {\n  var b = new Uint8Array(n);\n  randombytes(b, n);\n  return b;\n};\n\nnacl.secretbox = function(msg, nonce, key) {\n  checkArrayTypes(msg, nonce, key);\n  checkLengths(key, nonce);\n  var m = new Uint8Array(crypto_secretbox_ZEROBYTES + msg.length);\n  var c = new Uint8Array(m.length);\n  for (var i = 0; i < msg.length; i++) m[i+crypto_secretbox_ZEROBYTES] = msg[i];\n  crypto_secretbox(c, m, m.length, nonce, key);\n  return c.subarray(crypto_secretbox_BOXZEROBYTES);\n};\n\nnacl.secretbox.open = function(box, nonce, key) {\n  checkArrayTypes(box, nonce, key);\n  checkLengths(key, nonce);\n  var c = new Uint8Array(crypto_secretbox_BOXZEROBYTES + box.length);\n  var m = new Uint8Array(c.length);\n  for (var i = 0; i < box.length; i++) c[i+crypto_secretbox_BOXZEROBYTES] = box[i];\n  if (c.length < 32) return null;\n  if (crypto_secretbox_open(m, c, c.length, nonce, key) !== 0) return null;\n  return m.subarray(crypto_secretbox_ZEROBYTES);\n};\n\nnacl.secretbox.keyLength = crypto_secretbox_KEYBYTES;\nnacl.secretbox.nonceLength = crypto_secretbox_NONCEBYTES;\nnacl.secretbox.overheadLength = crypto_secretbox_BOXZEROBYTES;\n\nnacl.scalarMult = function(n, p) {\n  checkArrayTypes(n, p);\n  if (n.length !== crypto_scalarmult_SCALARBYTES) throw new Error('bad n size');\n  if (p.length !== crypto_scalarmult_BYTES) throw new Error('bad p size');\n  var q = new Uint8Array(crypto_scalarmult_BYTES);\n  crypto_scalarmult(q, n, p);\n  return q;\n};\n\nnacl.scalarMult.base = function(n) {\n  checkArrayTypes(n);\n  if (n.length !== crypto_scalarmult_SCALARBYTES) throw new Error('bad n size');\n  var q = new Uint8Array(crypto_scalarmult_BYTES);\n  crypto_scalarmult_base(q, n);\n  return q;\n};\n\nnacl.scalarMult.scalarLength = crypto_scalarmult_SCALARBYTES;\nnacl.scalarMult.groupElementLength = crypto_scalarmult_BYTES;\n\nnacl.box = function(msg, nonce, publicKey, secretKey) {\n  var k = nacl.box.before(publicKey, secretKey);\n  return nacl.secretbox(msg, nonce, k);\n};\n\nnacl.box.before = function(publicKey, secretKey) {\n  checkArrayTypes(publicKey, secretKey);\n  checkBoxLengths(publicKey, secretKey);\n  var k = new Uint8Array(crypto_box_BEFORENMBYTES);\n  crypto_box_beforenm(k, publicKey, secretKey);\n  return k;\n};\n\nnacl.box.after = nacl.secretbox;\n\nnacl.box.open = function(msg, nonce, publicKey, secretKey) {\n  var k = nacl.box.before(publicKey, secretKey);\n  return nacl.secretbox.open(msg, nonce, k);\n};\n\nnacl.box.open.after = nacl.secretbox.open;\n\nnacl.box.keyPair = function() {\n  var pk = new Uint8Array(crypto_box_PUBLICKEYBYTES);\n  var sk = new Uint8Array(crypto_box_SECRETKEYBYTES);\n  crypto_box_keypair(pk, sk);\n  return {publicKey: pk, secretKey: sk};\n};\n\nnacl.box.keyPair.fromSecretKey = function(secretKey) {\n  checkArrayTypes(secretKey);\n  if (secretKey.length !== crypto_box_SECRETKEYBYTES)\n    throw new Error('bad secret key size');\n  var pk = new Uint8Array(crypto_box_PUBLICKEYBYTES);\n  crypto_scalarmult_base(pk, secretKey);\n  return {publicKey: pk, secretKey: new Uint8Array(secretKey)};\n};\n\nnacl.box.publicKeyLength = crypto_box_PUBLICKEYBYTES;\nnacl.box.secretKeyLength = crypto_box_SECRETKEYBYTES;\nnacl.box.sharedKeyLength = crypto_box_BEFORENMBYTES;\nnacl.box.nonceLength = crypto_box_NONCEBYTES;\nnacl.box.overheadLength = nacl.secretbox.overheadLength;\n\nnacl.sign = function(msg, secretKey) {\n  checkArrayTypes(msg, secretKey);\n  if (secretKey.length !== crypto_sign_SECRETKEYBYTES)\n    throw new Error('bad secret key size');\n  var signedMsg = new Uint8Array(crypto_sign_BYTES+msg.length);\n  crypto_sign(signedMsg, msg, msg.length, secretKey);\n  return signedMsg;\n};\n\nnacl.sign.open = function(signedMsg, publicKey) {\n  checkArrayTypes(signedMsg, publicKey);\n  if (publicKey.length !== crypto_sign_PUBLICKEYBYTES)\n    throw new Error('bad public key size');\n  var tmp = new Uint8Array(signedMsg.length);\n  var mlen = crypto_sign_open(tmp, signedMsg, signedMsg.length, publicKey);\n  if (mlen < 0) return null;\n  var m = new Uint8Array(mlen);\n  for (var i = 0; i < m.length; i++) m[i] = tmp[i];\n  return m;\n};\n\nnacl.sign.detached = function(msg, secretKey) {\n  var signedMsg = nacl.sign(msg, secretKey);\n  var sig = new Uint8Array(crypto_sign_BYTES);\n  for (var i = 0; i < sig.length; i++) sig[i] = signedMsg[i];\n  return sig;\n};\n\nnacl.sign.detached.verify = function(msg, sig, publicKey) {\n  checkArrayTypes(msg, sig, publicKey);\n  if (sig.length !== crypto_sign_BYTES)\n    throw new Error('bad signature size');\n  if (publicKey.length !== crypto_sign_PUBLICKEYBYTES)\n    throw new Error('bad public key size');\n  var sm = new Uint8Array(crypto_sign_BYTES + msg.length);\n  var m = new Uint8Array(crypto_sign_BYTES + msg.length);\n  var i;\n  for (i = 0; i < crypto_sign_BYTES; i++) sm[i] = sig[i];\n  for (i = 0; i < msg.length; i++) sm[i+crypto_sign_BYTES] = msg[i];\n  return (crypto_sign_open(m, sm, sm.length, publicKey) >= 0);\n};\n\nnacl.sign.keyPair = function() {\n  var pk = new Uint8Array(crypto_sign_PUBLICKEYBYTES);\n  var sk = new Uint8Array(crypto_sign_SECRETKEYBYTES);\n  crypto_sign_keypair(pk, sk);\n  return {publicKey: pk, secretKey: sk};\n};\n\nnacl.sign.keyPair.fromSecretKey = function(secretKey) {\n  checkArrayTypes(secretKey);\n  if (secretKey.length !== crypto_sign_SECRETKEYBYTES)\n    throw new Error('bad secret key size');\n  var pk = new Uint8Array(crypto_sign_PUBLICKEYBYTES);\n  for (var i = 0; i < pk.length; i++) pk[i] = secretKey[32+i];\n  return {publicKey: pk, secretKey: new Uint8Array(secretKey)};\n};\n\nnacl.sign.keyPair.fromSeed = function(seed) {\n  checkArrayTypes(seed);\n  if (seed.length !== crypto_sign_SEEDBYTES)\n    throw new Error('bad seed size');\n  var pk = new Uint8Array(crypto_sign_PUBLICKEYBYTES);\n  var sk = new Uint8Array(crypto_sign_SECRETKEYBYTES);\n  for (var i = 0; i < 32; i++) sk[i] = seed[i];\n  crypto_sign_keypair(pk, sk, true);\n  return {publicKey: pk, secretKey: sk};\n};\n\nnacl.sign.publicKeyLength = crypto_sign_PUBLICKEYBYTES;\nnacl.sign.secretKeyLength = crypto_sign_SECRETKEYBYTES;\nnacl.sign.seedLength = crypto_sign_SEEDBYTES;\nnacl.sign.signatureLength = crypto_sign_BYTES;\n\nnacl.hash = function(msg) {\n  checkArrayTypes(msg);\n  var h = new Uint8Array(crypto_hash_BYTES);\n  crypto_hash(h, msg, msg.length);\n  return h;\n};\n\nnacl.hash.hashLength = crypto_hash_BYTES;\n\nnacl.verify = function(x, y) {\n  checkArrayTypes(x, y);\n  // Zero length arguments are considered not equal.\n  if (x.length === 0 || y.length === 0) return false;\n  if (x.length !== y.length) return false;\n  return (vn(x, 0, y, 0, x.length) === 0) ? true : false;\n};\n\nnacl.setPRNG = function(fn) {\n  randombytes = fn;\n};\n\n(function() {\n  // Initialize PRNG if environment provides CSPRNG.\n  // If not, methods calling randombytes will throw.\n  var crypto = typeof self !== 'undefined' ? (self.crypto || self.msCrypto) : null;\n  if (crypto && crypto.getRandomValues) {\n    // Browsers.\n    var QUOTA = 65536;\n    nacl.setPRNG(function(x, n) {\n      var i, v = new Uint8Array(n);\n      for (i = 0; i < n; i += QUOTA) {\n        crypto.getRandomValues(v.subarray(i, i + Math.min(n - i, QUOTA)));\n      }\n      for (i = 0; i < n; i++) x[i] = v[i];\n      cleanup(v);\n    });\n  } else if (typeof require !== 'undefined') {\n    // Node.js.\n    crypto = require('crypto');\n    if (crypto && crypto.randomBytes) {\n      nacl.setPRNG(function(x, n) {\n        var i, v = crypto.randomBytes(n);\n        for (i = 0; i < n; i++) x[i] = v[i];\n        cleanup(v);\n      });\n    }\n  }\n})();\n\n})(typeof module !== 'undefined' && module.exports ? module.exports : (self.nacl = self.nacl || {}));\n", "module.exports = require('./pusher-with-encryption').default;\n", "export default function encode(s: any): string {\n  return btoa(utob(s));\n}\n\nvar fromCharCode = String.fromCharCode;\n\nvar b64chars =\n  'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\nvar b64tab = {};\n\nfor (var i = 0, l = b64chars.length; i < l; i++) {\n  b64tab[b64chars.charAt(i)] = i;\n}\n\nvar cb_utob = function (c) {\n  var cc = c.charCodeAt(0);\n  return cc < 0x80\n    ? c\n    : cc < 0x800\n      ? fromCharCode(0xc0 | (cc >>> 6)) + fromCharCode(0x80 | (cc & 0x3f))\n      : fromCharCode(0xe0 | ((cc >>> 12) & 0x0f)) +\n        fromCharCode(0x80 | ((cc >>> 6) & 0x3f)) +\n        fromCharCode(0x80 | (cc & 0x3f));\n};\n\nvar utob = function (u) {\n  return u.replace(/[^\\x00-\\x7F]/g, cb_utob);\n};\n\nvar cb_encode = function (ccc) {\n  var padlen = [0, 2, 1][ccc.length % 3];\n  var ord =\n    (ccc.charCodeAt(0) << 16) |\n    ((ccc.length > 1 ? ccc.charCodeAt(1) : 0) << 8) |\n    (ccc.length > 2 ? ccc.charCodeAt(2) : 0);\n  var chars = [\n    b64chars.charAt(ord >>> 18),\n    b64chars.charAt((ord >>> 12) & 63),\n    padlen >= 2 ? '=' : b64chars.charAt((ord >>> 6) & 63),\n    padlen >= 1 ? '=' : b64chars.charAt(ord & 63),\n  ];\n  return chars.join('');\n};\n\nvar btoa =\n  global.btoa ||\n  function (b) {\n    return b.replace(/[\\s\\S]{1,3}/g, cb_encode);\n  };\n", "import TimedCallback from './timed_callback';\nimport { Delay, Scheduler, Canceller } from './scheduling';\n\nabstract class Timer {\n  protected clear: Canceller;\n  protected timer: number | void;\n\n  constructor(\n    set: Scheduler,\n    clear: Canceller,\n    delay: Delay,\n    callback: TimedCallback,\n  ) {\n    this.clear = clear;\n    this.timer = set(() => {\n      if (this.timer) {\n        this.timer = callback(this.timer);\n      }\n    }, delay);\n  }\n\n  /** Returns whether the timer is still running.\n   *\n   * @return {Boolean}\n   */\n  isRunning(): boolean {\n    return this.timer !== null;\n  }\n\n  /** Aborts a timer when it's running. */\n  ensureAborted() {\n    if (this.timer) {\n      this.clear(this.timer);\n      this.timer = null;\n    }\n  }\n}\n\nexport default Timer;\n", "import Timer from './abstract_timer';\nimport TimedCallback from './timed_callback';\nimport { Delay } from './scheduling';\n\n// We need to bind clear functions this way to avoid exceptions on IE8\nfunction clearTimeout(timer) {\n  global.clearTimeout(timer);\n}\nfunction clearInterval(timer) {\n  global.clearInterval(timer);\n}\n\n/** Cross-browser compatible one-off timer abstraction.\n *\n * @param {Number} delay\n * @param {Function} callback\n */\nexport class OneOffTimer extends Timer {\n  constructor(delay: Delay, callback: TimedCallback) {\n    super(setTimeout, clearTimeout, delay, function (timer) {\n      callback();\n      return null;\n    });\n  }\n}\n\n/** Cross-browser compatible periodic timer abstraction.\n *\n * @param {Number} delay\n * @param {Function} callback\n */\nexport class PeriodicTimer extends Timer {\n  constructor(delay: Delay, callback: TimedCallback) {\n    super(setInterval, clearInterval, delay, function (timer) {\n      callback();\n      return timer;\n    });\n  }\n}\n", "import * as Collections from './utils/collections';\nimport TimedCallback from './utils/timers/timed_callback';\nimport { OneOffTimer, PeriodicTimer } from './utils/timers';\n\nvar Util = {\n  now(): number {\n    if (Date.now) {\n      return Date.now();\n    } else {\n      return new Date().valueOf();\n    }\n  },\n\n  defer(callback: TimedCallback): OneOffTimer {\n    return new OneOffTimer(0, callback);\n  },\n\n  /** Builds a function that will proxy a method call to its first argument.\n   *\n   * Allows partial application of arguments, so additional arguments are\n   * prepended to the argument list.\n   *\n   * @param  {String} name method name\n   * @return {Function} proxy function\n   */\n  method(name: string, ...args: any[]): Function {\n    var boundArguments = Array.prototype.slice.call(arguments, 1);\n    return function (object) {\n      return object[name].apply(object, boundArguments.concat(arguments));\n    };\n  },\n};\n\nexport default Util;\n", "import base64encode from '../base64';\nimport Util from '../util';\n\n/** Merges multiple objects into the target argument.\n *\n * For properties that are plain Objects, performs a deep-merge. For the\n * rest it just copies the value of the property.\n *\n * To extend prototypes use it as following:\n *   Pusher.Util.extend(Target.prototype, Base.prototype)\n *\n * You can also use it to merge objects without altering them:\n *   Pusher.Util.extend({}, object1, object2)\n *\n * @param  {Object} target\n * @return {Object} the target argument\n */\nexport function extend<T>(target: any, ...sources: any[]): T {\n  for (var i = 0; i < sources.length; i++) {\n    var extensions = sources[i];\n    for (var property in extensions) {\n      if (\n        extensions[property] &&\n        extensions[property].constructor &&\n        extensions[property].constructor === Object\n      ) {\n        target[property] = extend(target[property] || {}, extensions[property]);\n      } else {\n        target[property] = extensions[property];\n      }\n    }\n  }\n  return target;\n}\n\nexport function stringify(): string {\n  var m = ['Pusher'];\n  for (var i = 0; i < arguments.length; i++) {\n    if (typeof arguments[i] === 'string') {\n      m.push(arguments[i]);\n    } else {\n      m.push(safeJSONStringify(arguments[i]));\n    }\n  }\n  return m.join(' : ');\n}\n\nexport function arrayIndexOf(array: any[], item: any): number {\n  // MSIE doesn't have array.indexOf\n  var nativeIndexOf = Array.prototype.indexOf;\n  if (array === null) {\n    return -1;\n  }\n  if (nativeIndexOf && array.indexOf === nativeIndexOf) {\n    return array.indexOf(item);\n  }\n  for (var i = 0, l = array.length; i < l; i++) {\n    if (array[i] === item) {\n      return i;\n    }\n  }\n  return -1;\n}\n\n/** Applies a function f to all properties of an object.\n *\n * Function f gets 3 arguments passed:\n * - element from the object\n * - key of the element\n * - reference to the object\n *\n * @param {Object} object\n * @param {Function} f\n */\nexport function objectApply(object: any, f: Function) {\n  for (var key in object) {\n    if (Object.prototype.hasOwnProperty.call(object, key)) {\n      f(object[key], key, object);\n    }\n  }\n}\n\n/** Return a list of objects own proerty keys\n *\n * @param {Object} object\n * @returns {Array}\n */\nexport function keys(object: any): string[] {\n  var keys = [];\n  objectApply(object, function (_, key) {\n    keys.push(key);\n  });\n  return keys;\n}\n\n/** Return a list of object's own property values\n *\n * @param {Object} object\n * @returns {Array}\n */\nexport function values(object: any): any[] {\n  var values = [];\n  objectApply(object, function (value) {\n    values.push(value);\n  });\n  return values;\n}\n\n/** Applies a function f to all elements of an array.\n *\n * Function f gets 3 arguments passed:\n * - element from the array\n * - index of the element\n * - reference to the array\n *\n * @param {Array} array\n * @param {Function} f\n */\nexport function apply(array: any[], f: Function, context?: any) {\n  for (var i = 0; i < array.length; i++) {\n    f.call(context || global, array[i], i, array);\n  }\n}\n\n/** Maps all elements of the array and returns the result.\n *\n * Function f gets 4 arguments passed:\n * - element from the array\n * - index of the element\n * - reference to the source array\n * - reference to the destination array\n *\n * @param {Array} array\n * @param {Function} f\n */\nexport function map(array: any[], f: Function): any[] {\n  var result = [];\n  for (var i = 0; i < array.length; i++) {\n    result.push(f(array[i], i, array, result));\n  }\n  return result;\n}\n\n/** Maps all elements of the object and returns the result.\n *\n * Function f gets 4 arguments passed:\n * - element from the object\n * - key of the element\n * - reference to the source object\n * - reference to the destination object\n *\n * @param {Object} object\n * @param {Function} f\n */\nexport function mapObject(object: any, f: Function): any {\n  var result = {};\n  objectApply(object, function (value, key) {\n    result[key] = f(value);\n  });\n  return result;\n}\n\n/** Filters elements of the array using a test function.\n *\n * Function test gets 4 arguments passed:\n * - element from the array\n * - index of the element\n * - reference to the source array\n * - reference to the destination array\n *\n * @param {Array} array\n * @param {Function} f\n */\nexport function filter(array: any[], test: Function): any[] {\n  test =\n    test ||\n    function (value) {\n      return !!value;\n    };\n\n  var result = [];\n  for (var i = 0; i < array.length; i++) {\n    if (test(array[i], i, array, result)) {\n      result.push(array[i]);\n    }\n  }\n  return result;\n}\n\n/** Filters properties of the object using a test function.\n *\n * Function test gets 4 arguments passed:\n * - element from the object\n * - key of the element\n * - reference to the source object\n * - reference to the destination object\n *\n * @param {Object} object\n * @param {Function} f\n */\nexport function filterObject(object: Object, test: Function) {\n  var result = {};\n  objectApply(object, function (value, key) {\n    if ((test && test(value, key, object, result)) || Boolean(value)) {\n      result[key] = value;\n    }\n  });\n  return result;\n}\n\n/** Flattens an object into a two-dimensional array.\n *\n * @param  {Object} object\n * @return {Array} resulting array of [key, value] pairs\n */\nexport function flatten(object: Object): any[] {\n  var result = [];\n  objectApply(object, function (value, key) {\n    result.push([key, value]);\n  });\n  return result;\n}\n\n/** Checks whether any element of the array passes the test.\n *\n * Function test gets 3 arguments passed:\n * - element from the array\n * - index of the element\n * - reference to the source array\n *\n * @param {Array} array\n * @param {Function} f\n */\nexport function any(array: any[], test: Function): boolean {\n  for (var i = 0; i < array.length; i++) {\n    if (test(array[i], i, array)) {\n      return true;\n    }\n  }\n  return false;\n}\n\n/** Checks whether all elements of the array pass the test.\n *\n * Function test gets 3 arguments passed:\n * - element from the array\n * - index of the element\n * - reference to the source array\n *\n * @param {Array} array\n * @param {Function} f\n */\nexport function all(array: any[], test: Function): boolean {\n  for (var i = 0; i < array.length; i++) {\n    if (!test(array[i], i, array)) {\n      return false;\n    }\n  }\n  return true;\n}\n\nexport function encodeParamsObject(data): string {\n  return mapObject(data, function (value) {\n    if (typeof value === 'object') {\n      value = safeJSONStringify(value);\n    }\n    return encodeURIComponent(base64encode(value.toString()));\n  });\n}\n\nexport function buildQueryString(data: any): string {\n  var params = filterObject(data, function (value) {\n    return value !== undefined;\n  });\n\n  var query = map(\n    flatten(encodeParamsObject(params)),\n    Util.method('join', '='),\n  ).join('&');\n\n  return query;\n}\n\n/**\n * See https://github.com/douglascrockford/JSON-js/blob/master/cycle.js\n *\n * Remove circular references from an object. Required for JSON.stringify in\n * React Native, which tends to blow up a lot.\n *\n * @param  {any} object\n * @return {any}        Decycled object\n */\nexport function decycleObject(object: any): any {\n  var objects = [],\n    paths = [];\n\n  return (function derez(value, path) {\n    var i, name, nu;\n\n    switch (typeof value) {\n      case 'object':\n        if (!value) {\n          return null;\n        }\n        for (i = 0; i < objects.length; i += 1) {\n          if (objects[i] === value) {\n            return { $ref: paths[i] };\n          }\n        }\n\n        objects.push(value);\n        paths.push(path);\n\n        if (Object.prototype.toString.apply(value) === '[object Array]') {\n          nu = [];\n          for (i = 0; i < value.length; i += 1) {\n            nu[i] = derez(value[i], path + '[' + i + ']');\n          }\n        } else {\n          nu = {};\n          for (name in value) {\n            if (Object.prototype.hasOwnProperty.call(value, name)) {\n              nu[name] = derez(\n                value[name],\n                path + '[' + JSON.stringify(name) + ']',\n              );\n            }\n          }\n        }\n        return nu;\n      case 'number':\n      case 'string':\n      case 'boolean':\n        return value;\n    }\n  })(object, '$');\n}\n\n/**\n * Provides a cross-browser and cross-platform way to safely stringify objects\n * into JSON. This is particularly necessary for ReactNative, where circular JSON\n * structures throw an exception.\n *\n * @param  {any}    source The object to stringify\n * @return {string}        The serialized output.\n */\nexport function safeJSONStringify(source: any): string {\n  try {\n    return JSON.stringify(source);\n  } catch (e) {\n    return JSON.stringify(decycleObject(source));\n  }\n}\n", "import {\n  ChannelAuthorizationOptions,\n  UserAuthenticationOptions,\n} from './auth/options';\nimport { AuthTransport } from './config';\n\nexport interface DefaultConfig {\n  VERSION: string;\n  PROTOCOL: number;\n  wsPort: number;\n  wssPort: number;\n  wsPath: string;\n  httpHost: string;\n  httpPort: number;\n  httpsPort: number;\n  httpPath: string;\n  stats_host: string;\n  authEndpoint: string;\n  authTransport: AuthTransport;\n  activityTimeout: number;\n  pongTimeout: number;\n  unavailableTimeout: number;\n  userAuthentication: UserAuthenticationOptions;\n  channelAuthorization: ChannelAuthorizationOptions;\n\n  cdn_http?: string;\n  cdn_https?: string;\n  dependency_suffix?: string;\n}\n\nvar Defaults: DefaultConfig = {\n  VERSION: VERSION,\n  PROTOCOL: 7,\n\n  wsPort: 80,\n  wssPort: 443,\n  wsPath: '',\n  // DEPRECATED: SockJS fallback parameters\n  httpHost: 'sockjs.pusher.com',\n  httpPort: 80,\n  httpsPort: 443,\n  httpPath: '/pusher',\n  // DEPRECATED: Stats\n  stats_host: 'stats.pusher.com',\n  // DEPRECATED: Other settings\n  authEndpoint: '/pusher/auth',\n  authTransport: 'ajax',\n  activityTimeout: 120000,\n  pongTimeout: 30000,\n  unavailableTimeout: 10000,\n  userAuthentication: {\n    endpoint: '/pusher/user-auth',\n    transport: 'ajax',\n  },\n  channelAuthorization: {\n    endpoint: '/pusher/auth',\n    transport: 'ajax',\n  },\n\n  // CDN configuration\n  cdn_http: CDN_HTTP,\n  cdn_https: CDN_HTTPS,\n  dependency_suffix: DEPENDENCY_SUFFIX,\n};\n\nexport default Defaults;\n", "import Defaults from '../defaults';\nimport { default as URLScheme, URLSchemeParams } from './url_scheme';\n\nfunction getGenericURL(\n  baseScheme: string,\n  params: URLSchemeParams,\n  path: string,\n): string {\n  var scheme = baseScheme + (params.useTLS ? 's' : '');\n  var host = params.useTLS ? params.hostTLS : params.hostNonTLS;\n  return scheme + '://' + host + path;\n}\n\nfunction getGenericPath(key: string, queryString?: string): string {\n  var path = '/app/' + key;\n  var query =\n    '?protocol=' +\n    Defaults.PROTOCOL +\n    '&client=js' +\n    '&version=' +\n    Defaults.VERSION +\n    (queryString ? '&' + queryString : '');\n  return path + query;\n}\n\nexport var ws: URLScheme = {\n  getInitial: function (key: string, params: URLSchemeParams): string {\n    var path = (params.httpPath || '') + getGenericPath(key, 'flash=false');\n    return getGenericURL('ws', params, path);\n  },\n};\n\nexport var http: URLScheme = {\n  getInitial: function (key: string, params: URLSchemeParams): string {\n    var path = (params.httpPath || '/pusher') + getGenericPath(key);\n    return getGenericURL('http', params, path);\n  },\n};\n\nexport var sockjs: URLScheme = {\n  getInitial: function (key: string, params: URLSchemeParams): string {\n    return getGenericURL('http', params, params.httpPath || '/pusher');\n  },\n  getPath: function (key: string, params: URLSchemeParams): string {\n    return getGenericPath(key);\n  },\n};\n", "import Callback from './callback';\nimport * as Collections from '../utils/collections';\nimport CallbackTable from './callback_table';\n\nexport default class CallbackRegistry {\n  _callbacks: CallbackTable;\n\n  constructor() {\n    this._callbacks = {};\n  }\n\n  get(name: string): Callback[] {\n    return this._callbacks[prefix(name)];\n  }\n\n  add(name: string, callback: Function, context: any) {\n    var prefixedEventName = prefix(name);\n    this._callbacks[prefixedEventName] =\n      this._callbacks[prefixedEventName] || [];\n    this._callbacks[prefixedEventName].push({\n      fn: callback,\n      context: context,\n    });\n  }\n\n  remove(name?: string, callback?: Function, context?: any) {\n    if (!name && !callback && !context) {\n      this._callbacks = {};\n      return;\n    }\n\n    var names = name ? [prefix(name)] : Collections.keys(this._callbacks);\n\n    if (callback || context) {\n      this.removeCallback(names, callback, context);\n    } else {\n      this.removeAllCallbacks(names);\n    }\n  }\n\n  private removeCallback(names: string[], callback: Function, context: any) {\n    Collections.apply(\n      names,\n      function (name) {\n        this._callbacks[name] = Collections.filter(\n          this._callbacks[name] || [],\n          function (binding) {\n            return (\n              (callback && callback !== binding.fn) ||\n              (context && context !== binding.context)\n            );\n          },\n        );\n        if (this._callbacks[name].length === 0) {\n          delete this._callbacks[name];\n        }\n      },\n      this,\n    );\n  }\n\n  private removeAllCallbacks(names: string[]) {\n    Collections.apply(\n      names,\n      function (name) {\n        delete this._callbacks[name];\n      },\n      this,\n    );\n  }\n}\n\nfunction prefix(name: string): string {\n  return '_' + name;\n}\n", "import * as Collections from '../utils/collections';\nimport Callback from './callback';\nimport Metadata from '../channels/metadata';\nimport CallbackRegistry from './callback_registry';\n\n/** Manages callback bindings and event emitting.\n *\n * @param Function failThrough called when no listeners are bound to an event\n */\nexport default class Dispatcher {\n  callbacks: CallbackRegistry;\n  global_callbacks: Function[];\n  failThrough: Function;\n\n  constructor(failThrough?: Function) {\n    this.callbacks = new CallbackRegistry();\n    this.global_callbacks = [];\n    this.failThrough = failThrough;\n  }\n\n  bind(eventName: string, callback: Function, context?: any) {\n    this.callbacks.add(eventName, callback, context);\n    return this;\n  }\n\n  bind_global(callback: Function) {\n    this.global_callbacks.push(callback);\n    return this;\n  }\n\n  unbind(eventName?: string, callback?: Function, context?: any) {\n    this.callbacks.remove(eventName, callback, context);\n    return this;\n  }\n\n  unbind_global(callback?: Function) {\n    if (!callback) {\n      this.global_callbacks = [];\n      return this;\n    }\n\n    this.global_callbacks = Collections.filter(\n      this.global_callbacks || [],\n      (c) => c !== callback,\n    );\n\n    return this;\n  }\n\n  unbind_all() {\n    this.unbind();\n    this.unbind_global();\n    return this;\n  }\n\n  emit(eventName: string, data?: any, metadata?: Metadata): Dispatcher {\n    for (var i = 0; i < this.global_callbacks.length; i++) {\n      this.global_callbacks[i](eventName, data);\n    }\n\n    var callbacks = this.callbacks.get(eventName);\n    var args = [];\n\n    if (metadata) {\n      // if there's a metadata argument, we need to call the callback with both\n      // data and metadata regardless of whether data is undefined\n      args.push(data, metadata);\n    } else if (data) {\n      // metadata is undefined, so we only need to call the callback with data\n      // if data exists\n      args.push(data);\n    }\n\n    if (callbacks && callbacks.length > 0) {\n      for (var i = 0; i < callbacks.length; i++) {\n        callbacks[i].fn.apply(callbacks[i].context || global, args);\n      }\n    } else if (this.failThrough) {\n      this.failThrough(eventName, data);\n    }\n\n    return this;\n  }\n}\n", "import { stringify } from './utils/collections';\nimport Pusher from './pusher';\n\nclass Logger {\n  debug(...args: any[]) {\n    this.log(this.globalLog, args);\n  }\n\n  warn(...args: any[]) {\n    this.log(this.globalLogWarn, args);\n  }\n\n  error(...args: any[]) {\n    this.log(this.globalLogError, args);\n  }\n\n  private globalLog = (message: string) => {\n    if (global.console && global.console.log) {\n      global.console.log(message);\n    }\n  };\n\n  private globalLogWarn(message: string) {\n    if (global.console && global.console.warn) {\n      global.console.warn(message);\n    } else {\n      this.globalLog(message);\n    }\n  }\n\n  private globalLogError(message: string) {\n    if (global.console && global.console.error) {\n      global.console.error(message);\n    } else {\n      this.globalLogWarn(message);\n    }\n  }\n\n  private log(\n    defaultLoggingFunction: (message: string) => void,\n    ...args: any[]\n  ) {\n    var message = stringify.apply(this, arguments);\n    if (Pusher.log) {\n      Pusher.log(message);\n    } else if (Pusher.logToConsole) {\n      const log = defaultLoggingFunction.bind(this);\n      log(message);\n    }\n  }\n}\n\nexport default new Logger();\n", "import Util from '../util';\nimport * as Collections from '../utils/collections';\nimport { default as EventsDispatcher } from '../events/dispatcher';\nimport Logger from '../logger';\nimport TransportHooks from './transport_hooks';\nimport Socket from '../socket';\nimport Runtime from 'runtime';\nimport Timeline from '../timeline/timeline';\nimport TransportConnectionOptions from './transport_connection_options';\n\n/** Provides universal API for transport connections.\n *\n * Transport connection is a low-level object that wraps a connection method\n * and exposes a simple evented interface for the connection state and\n * messaging. It does not implement Pusher-specific WebSocket protocol.\n *\n * Additionally, it fetches resources needed for transport to work and exposes\n * an interface for querying transport features.\n *\n * States:\n * - new - initial state after constructing the object\n * - initializing - during initialization phase, usually fetching resources\n * - intialized - ready to establish a connection\n * - connection - when connection is being established\n * - open - when connection ready to be used\n * - closed - after connection was closed be either side\n *\n * Emits:\n * - error - after the connection raised an error\n *\n * Options:\n * - useTLS - whether connection should be over TLS\n * - hostTLS - host to connect to when connection is over TLS\n * - hostNonTLS - host to connect to when connection is over TLS\n *\n * @param {String} key application key\n * @param {Object} options\n */\nexport default class TransportConnection extends EventsDispatcher {\n  hooks: TransportHooks;\n  name: string;\n  priority: number;\n  key: string;\n  options: TransportConnectionOptions;\n  state: string;\n  timeline: Timeline;\n  activityTimeout: number;\n  id: number;\n  socket: Socket;\n  beforeOpen: Function;\n  initialize: Function;\n\n  constructor(\n    hooks: TransportHooks,\n    name: string,\n    priority: number,\n    key: string,\n    options: TransportConnectionOptions,\n  ) {\n    super();\n    this.initialize = Runtime.transportConnectionInitializer;\n    this.hooks = hooks;\n    this.name = name;\n    this.priority = priority;\n    this.key = key;\n    this.options = options;\n\n    this.state = 'new';\n    this.timeline = options.timeline;\n    this.activityTimeout = options.activityTimeout;\n    this.id = this.timeline.generateUniqueID();\n  }\n\n  /** Checks whether the transport handles activity checks by itself.\n   *\n   * @return {Boolean}\n   */\n  handlesActivityChecks(): boolean {\n    return Boolean(this.hooks.handlesActivityChecks);\n  }\n\n  /** Checks whether the transport supports the ping/pong API.\n   *\n   * @return {Boolean}\n   */\n  supportsPing(): boolean {\n    return Boolean(this.hooks.supportsPing);\n  }\n\n  /** Tries to establish a connection.\n   *\n   * @returns {Boolean} false if transport is in invalid state\n   */\n  connect(): boolean {\n    if (this.socket || this.state !== 'initialized') {\n      return false;\n    }\n\n    var url = this.hooks.urls.getInitial(this.key, this.options);\n    try {\n      this.socket = this.hooks.getSocket(url, this.options);\n    } catch (e) {\n      Util.defer(() => {\n        this.onError(e);\n        this.changeState('closed');\n      });\n      return false;\n    }\n\n    this.bindListeners();\n\n    Logger.debug('Connecting', { transport: this.name, url });\n    this.changeState('connecting');\n    return true;\n  }\n\n  /** Closes the connection.\n   *\n   * @return {Boolean} true if there was a connection to close\n   */\n  close(): boolean {\n    if (this.socket) {\n      this.socket.close();\n      return true;\n    } else {\n      return false;\n    }\n  }\n\n  /** Sends data over the open connection.\n   *\n   * @param {String} data\n   * @return {Boolean} true only when in the \"open\" state\n   */\n  send(data: any): boolean {\n    if (this.state === 'open') {\n      // Workaround for MobileSafari bug (see https://gist.github.com/2052006)\n      Util.defer(() => {\n        if (this.socket) {\n          this.socket.send(data);\n        }\n      });\n      return true;\n    } else {\n      return false;\n    }\n  }\n\n  /** Sends a ping if the connection is open and transport supports it. */\n  ping() {\n    if (this.state === 'open' && this.supportsPing()) {\n      this.socket.ping();\n    }\n  }\n\n  private onOpen() {\n    if (this.hooks.beforeOpen) {\n      this.hooks.beforeOpen(\n        this.socket,\n        this.hooks.urls.getPath(this.key, this.options),\n      );\n    }\n    this.changeState('open');\n    this.socket.onopen = undefined;\n  }\n\n  private onError(error) {\n    this.emit('error', { type: 'WebSocketError', error: error });\n    this.timeline.error(this.buildTimelineMessage({ error: error.toString() }));\n  }\n\n  private onClose(closeEvent?: any) {\n    if (closeEvent) {\n      this.changeState('closed', {\n        code: closeEvent.code,\n        reason: closeEvent.reason,\n        wasClean: closeEvent.wasClean,\n      });\n    } else {\n      this.changeState('closed');\n    }\n    this.unbindListeners();\n    this.socket = undefined;\n  }\n\n  private onMessage(message) {\n    this.emit('message', message);\n  }\n\n  private onActivity() {\n    this.emit('activity');\n  }\n\n  private bindListeners() {\n    this.socket.onopen = () => {\n      this.onOpen();\n    };\n    this.socket.onerror = (error) => {\n      this.onError(error);\n    };\n    this.socket.onclose = (closeEvent) => {\n      this.onClose(closeEvent);\n    };\n    this.socket.onmessage = (message) => {\n      this.onMessage(message);\n    };\n\n    if (this.supportsPing()) {\n      this.socket.onactivity = () => {\n        this.onActivity();\n      };\n    }\n  }\n\n  private unbindListeners() {\n    if (this.socket) {\n      this.socket.onopen = undefined;\n      this.socket.onerror = undefined;\n      this.socket.onclose = undefined;\n      this.socket.onmessage = undefined;\n      if (this.supportsPing()) {\n        this.socket.onactivity = undefined;\n      }\n    }\n  }\n\n  private changeState(state: string, params?: any) {\n    this.state = state;\n    this.timeline.info(\n      this.buildTimelineMessage({\n        state: state,\n        params: params,\n      }),\n    );\n    this.emit(state, params);\n  }\n\n  buildTimelineMessage(message): any {\n    return Collections.extend({ cid: this.id }, message);\n  }\n}\n", "import Factory from '../utils/factory';\nimport TransportHooks from './transport_hooks';\nimport TransportConnection from './transport_connection';\nimport TransportConnectionOptions from './transport_connection_options';\n\n/** Provides interface for transport connection instantiation.\n *\n * Takes transport-specific hooks as the only argument, which allow checking\n * for transport support and creating its connections.\n *\n * Supported hooks: * - file - the name of the file to be fetched during initialization\n * - urls - URL scheme to be used by transport\n * - handlesActivityCheck - true when the transport handles activity checks\n * - supportsPing - true when the transport has a ping/activity API\n * - isSupported - tells whether the transport is supported in the environment\n * - getSocket - creates a WebSocket-compatible transport socket\n *\n * See transports.js for specific implementations.\n *\n * @param {Object} hooks object containing all needed transport hooks\n */\nexport default class Transport {\n  hooks: TransportHooks;\n\n  constructor(hooks: TransportHooks) {\n    this.hooks = hooks;\n  }\n\n  /** Returns whether the transport is supported in the environment.\n   *\n   * @param {Object} envronment te environment details (encryption, settings)\n   * @returns {Boolean} true when the transport is supported\n   */\n  isSupported(environment: any): boolean {\n    return this.hooks.isSupported(environment);\n  }\n\n  /** Creates a transport connection.\n   *\n   * @param {String} name\n   * @param {Number} priority\n   * @param {String} key the application key\n   * @param {Object} options\n   * @returns {TransportConnection}\n   */\n  createConnection(\n    name: string,\n    priority: number,\n    key: string,\n    options: any,\n  ): TransportConnection {\n    return new TransportConnection(this.hooks, name, priority, key, options);\n  }\n}\n", "import * as URLSchemes from 'core/transports/url_schemes';\nimport URLScheme from 'core/transports/url_scheme';\nimport Transport from 'core/transports/transport';\nimport Util from 'core/util';\nimport * as Collections from 'core/utils/collections';\nimport TransportHooks from 'core/transports/transport_hooks';\nimport TransportsTable from 'core/transports/transports_table';\nimport Runtime from 'runtime';\n\n/** WebSocket transport.\n *\n * Uses native WebSocket implementation, including MozWebSocket supported by\n * earlier Firefox versions.\n */\nvar WSTransport = new Transport(<TransportHooks>{\n  urls: URLSchemes.ws,\n  handlesActivityChecks: false,\n  supportsPing: false,\n\n  isInitialized: function () {\n    return Boolean(Runtime.getWebSocketAPI());\n  },\n  isSupported: function (): boolean {\n    return Boolean(Runtime.getWebSocketAPI());\n  },\n  getSocket: function (url) {\n    return Runtime.createWebSocket(url);\n  },\n});\n\nvar httpConfiguration = {\n  urls: URLSchemes.http,\n  handlesActivityChecks: false,\n  supportsPing: true,\n  isInitialized: function () {\n    return true;\n  },\n};\n\nexport var streamingConfiguration = Collections.extend(\n  {\n    getSocket: function (url) {\n      return Runtime.HTTPFactory.createStreamingSocket(url);\n    },\n  },\n  httpConfiguration,\n);\nexport var pollingConfiguration = Collections.extend(\n  {\n    getSocket: function (url) {\n      return Runtime.HTTPFactory.createPollingSocket(url);\n    },\n  },\n  httpConfiguration,\n);\n\nvar xhrConfiguration = {\n  isSupported: function (): boolean {\n    return Runtime.isXHRSupported();\n  },\n};\n\n/** HTTP streaming transport using CORS-enabled XMLHttpRequest. */\nvar XHRStreamingTransport = new Transport(\n  <TransportHooks>(\n    Collections.extend({}, streamingConfiguration, xhrConfiguration)\n  ),\n);\n\n/** HTTP long-polling transport using CORS-enabled XMLHttpRequest. */\nvar XHRPollingTransport = new Transport(\n  <TransportHooks>(\n    Collections.extend({}, pollingConfiguration, xhrConfiguration)\n  ),\n);\n\nvar Transports: TransportsTable = {\n  ws: WSTransport,\n  xhr_streaming: XHRStreamingTransport,\n  xhr_polling: XHRPollingTransport,\n};\n\nexport default Transports;\n", "import Util from '../util';\nimport * as Collections from '../utils/collections';\nimport TransportManager from './transport_manager';\nimport TransportConnection from './transport_connection';\nimport Transport from './transport';\nimport PingDelayOptions from './ping_delay_options';\n\n/** Creates transport connections monitored by a transport manager.\n *\n * When a transport is closed, it might mean the environment does not support\n * it. It's possible that messages get stuck in an intermediate buffer or\n * proxies terminate inactive connections. To combat these problems,\n * assistants monitor the connection lifetime, report unclean exits and\n * adjust ping timeouts to keep the connection active. The decision to disable\n * a transport is the manager's responsibility.\n *\n * @param {TransportManager} manager\n * @param {TransportConnection} transport\n * @param {Object} options\n */\nexport default class AssistantToTheTransportManager {\n  manager: TransportManager;\n  transport: Transport;\n  minPingDelay: number;\n  maxPingDelay: number;\n  pingDelay: number;\n\n  constructor(\n    manager: TransportManager,\n    transport: Transport,\n    options: PingDelayOptions,\n  ) {\n    this.manager = manager;\n    this.transport = transport;\n    this.minPingDelay = options.minPingDelay;\n    this.maxPingDelay = options.maxPingDelay;\n    this.pingDelay = undefined;\n  }\n\n  /** Creates a transport connection.\n   *\n   * This function has the same API as Transport#createConnection.\n   *\n   * @param {String} name\n   * @param {Number} priority\n   * @param {String} key the application key\n   * @param {Object} options\n   * @returns {TransportConnection}\n   */\n  createConnection(\n    name: string,\n    priority: number,\n    key: string,\n    options: Object,\n  ): TransportConnection {\n    options = Collections.extend({}, options, {\n      activityTimeout: this.pingDelay,\n    });\n    var connection = this.transport.createConnection(\n      name,\n      priority,\n      key,\n      options,\n    );\n\n    var openTimestamp = null;\n\n    var onOpen = function () {\n      connection.unbind('open', onOpen);\n      connection.bind('closed', onClosed);\n      openTimestamp = Util.now();\n    };\n    var onClosed = (closeEvent) => {\n      connection.unbind('closed', onClosed);\n\n      if (closeEvent.code === 1002 || closeEvent.code === 1003) {\n        // we don't want to use transports not obeying the protocol\n        this.manager.reportDeath();\n      } else if (!closeEvent.wasClean && openTimestamp) {\n        // report deaths only for short-living transport\n        var lifespan = Util.now() - openTimestamp;\n        if (lifespan < 2 * this.maxPingDelay) {\n          this.manager.reportDeath();\n          this.pingDelay = Math.max(lifespan / 2, this.minPingDelay);\n        }\n      }\n    };\n\n    connection.bind('open', onOpen);\n    return connection;\n  }\n\n  /** Returns whether the transport is supported in the environment.\n   *\n   * This function has the same API as Transport#isSupported. Might return false\n   * when the manager decides to kill the transport.\n   *\n   * @param {Object} environment the environment details (encryption, settings)\n   * @returns {Boolean} true when the transport is supported\n   */\n  isSupported(environment: string): boolean {\n    return this.manager.isAlive() && this.transport.isSupported(environment);\n  }\n}\n", "import Action from './action';\nimport { PusherEvent } from './message-types';\n/**\n * Provides functions for handling Pusher protocol-specific messages.\n */\n\nconst Protocol = {\n  /**\n   * Decodes a message in a Pusher format.\n   *\n   * The MessageEvent we receive from the transport should contain a pusher event\n   * (https://pusher.com/docs/pusher_protocol#events) serialized as JSO<PERSON> in the\n   * data field\n   *\n   * The pusher event may contain a data field too, and it may also be\n   * serialised as JSON\n   *\n   * Throws errors when messages are not parse'able.\n   *\n   * @param  {MessageEvent} messageEvent\n   * @return {PusherEvent}\n   */\n  decodeMessage: function (messageEvent: MessageEvent): PusherEvent {\n    try {\n      var messageData = JSON.parse(messageEvent.data);\n      var pusherEventData = messageData.data;\n      if (typeof pusherEventData === 'string') {\n        try {\n          pusherEventData = JSON.parse(messageData.data);\n        } catch (e) {}\n      }\n      var pusherEvent: PusherEvent = {\n        event: messageData.event,\n        channel: messageData.channel,\n        data: pusherEventData,\n      };\n      if (messageData.user_id) {\n        pusherEvent.user_id = messageData.user_id;\n      }\n      return pusherEvent;\n    } catch (e) {\n      throw { type: 'MessageParseError', error: e, data: messageEvent.data };\n    }\n  },\n\n  /**\n   * Encodes a message to be sent.\n   *\n   * @param  {PusherEvent} event\n   * @return {String}\n   */\n  encodeMessage: function (event: PusherEvent): string {\n    return JSON.stringify(event);\n  },\n\n  /**\n   * Processes a handshake message and returns appropriate actions.\n   *\n   * Returns an object with an 'action' and other action-specific properties.\n   *\n   * There are three outcomes when calling this function. First is a successful\n   * connection attempt, when pusher:connection_established is received, which\n   * results in a 'connected' action with an 'id' property. When passed a\n   * pusher:error event, it returns a result with action appropriate to the\n   * close code and an error. Otherwise, it raises an exception.\n   *\n   * @param {String} message\n   * @result Object\n   */\n  processHandshake: function (messageEvent: MessageEvent): Action {\n    var message = Protocol.decodeMessage(messageEvent);\n\n    if (message.event === 'pusher:connection_established') {\n      if (!message.data.activity_timeout) {\n        throw 'No activity timeout specified in handshake';\n      }\n      return {\n        action: 'connected',\n        id: message.data.socket_id,\n        activityTimeout: message.data.activity_timeout * 1000,\n      };\n    } else if (message.event === 'pusher:error') {\n      // From protocol 6 close codes are sent only once, so this only\n      // happens when connection does not support close codes\n      return {\n        action: this.getCloseAction(message.data),\n        error: this.getCloseError(message.data),\n      };\n    } else {\n      throw 'Invalid handshake';\n    }\n  },\n\n  /**\n   * Dispatches the close event and returns an appropriate action name.\n   *\n   * See:\n   * 1. https://developer.mozilla.org/en-US/docs/WebSockets/WebSockets_reference/CloseEvent\n   * 2. http://pusher.com/docs/pusher_protocol\n   *\n   * @param  {CloseEvent} closeEvent\n   * @return {String} close action name\n   */\n  getCloseAction: function (closeEvent): string {\n    if (closeEvent.code < 4000) {\n      // ignore 1000 CLOSE_NORMAL, 1001 CLOSE_GOING_AWAY,\n      //        1005 CLOSE_NO_STATUS, 1006 CLOSE_ABNORMAL\n      // ignore 1007...3999\n      // handle 1002 CLOSE_PROTOCOL_ERROR, 1003 CLOSE_UNSUPPORTED,\n      //        1004 CLOSE_TOO_LARGE\n      if (closeEvent.code >= 1002 && closeEvent.code <= 1004) {\n        return 'backoff';\n      } else {\n        return null;\n      }\n    } else if (closeEvent.code === 4000) {\n      return 'tls_only';\n    } else if (closeEvent.code < 4100) {\n      return 'refused';\n    } else if (closeEvent.code < 4200) {\n      return 'backoff';\n    } else if (closeEvent.code < 4300) {\n      return 'retry';\n    } else {\n      // unknown error\n      return 'refused';\n    }\n  },\n\n  /**\n   * Returns an error or null basing on the close event.\n   *\n   * Null is returned when connection was closed cleanly. Otherwise, an object\n   * with error details is returned.\n   *\n   * @param  {CloseEvent} closeEvent\n   * @return {Object} error object\n   */\n  getCloseError: function (closeEvent): any {\n    if (closeEvent.code !== 1000 && closeEvent.code !== 1001) {\n      return {\n        type: 'PusherError',\n        data: {\n          code: closeEvent.code,\n          message: closeEvent.reason || closeEvent.message,\n        },\n      };\n    } else {\n      return null;\n    }\n  },\n};\n\nexport default Protocol;\n", "import * as Collections from '../utils/collections';\nimport { default as EventsDispatcher } from '../events/dispatcher';\nimport Protocol from './protocol/protocol';\nimport { PusherEvent } from './protocol/message-types';\nimport Logger from '../logger';\nimport TransportConnection from '../transports/transport_connection';\nimport Socket from '../socket';\n/**\n * Provides Pusher protocol interface for transports.\n *\n * Emits following events:\n * - message - on received messages\n * - ping - on ping requests\n * - pong - on pong responses\n * - error - when the transport emits an error\n * - closed - after closing the transport\n *\n * It also emits more events when connection closes with a code.\n * See Protocol.getCloseAction to get more details.\n *\n * @param {Number} id\n * @param {AbstractTransport} transport\n */\nexport default class Connection extends EventsDispatcher implements Socket {\n  id: string;\n  transport: TransportConnection;\n  activityTimeout: number;\n\n  constructor(id: string, transport: TransportConnection) {\n    super();\n    this.id = id;\n    this.transport = transport;\n    this.activityTimeout = transport.activityTimeout;\n    this.bindListeners();\n  }\n\n  /** Returns whether used transport handles activity checks by itself\n   *\n   * @returns {Boolean} true if activity checks are handled by the transport\n   */\n  handlesActivityChecks() {\n    return this.transport.handlesActivityChecks();\n  }\n\n  /** Sends raw data.\n   *\n   * @param {String} data\n   */\n  send(data: any): boolean {\n    return this.transport.send(data);\n  }\n\n  /** Sends an event.\n   *\n   * @param {String} name\n   * @param {String} data\n   * @param {String} [channel]\n   * @returns {Boolean} whether message was sent or not\n   */\n  send_event(name: string, data: any, channel?: string): boolean {\n    var event: PusherEvent = { event: name, data: data };\n    if (channel) {\n      event.channel = channel;\n    }\n    Logger.debug('Event sent', event);\n    return this.send(Protocol.encodeMessage(event));\n  }\n\n  /** Sends a ping message to the server.\n   *\n   * Basing on the underlying transport, it might send either transport's\n   * protocol-specific ping or pusher:ping event.\n   */\n  ping() {\n    if (this.transport.supportsPing()) {\n      this.transport.ping();\n    } else {\n      this.send_event('pusher:ping', {});\n    }\n  }\n\n  /** Closes the connection. */\n  close() {\n    this.transport.close();\n  }\n\n  private bindListeners() {\n    var listeners = {\n      message: (messageEvent: MessageEvent) => {\n        var pusherEvent;\n        try {\n          pusherEvent = Protocol.decodeMessage(messageEvent);\n        } catch (e) {\n          this.emit('error', {\n            type: 'MessageParseError',\n            error: e,\n            data: messageEvent.data,\n          });\n        }\n\n        if (pusherEvent !== undefined) {\n          Logger.debug('Event recd', pusherEvent);\n\n          switch (pusherEvent.event) {\n            case 'pusher:error':\n              this.emit('error', {\n                type: 'PusherError',\n                data: pusherEvent.data,\n              });\n              break;\n            case 'pusher:ping':\n              this.emit('ping');\n              break;\n            case 'pusher:pong':\n              this.emit('pong');\n              break;\n          }\n          this.emit('message', pusherEvent);\n        }\n      },\n      activity: () => {\n        this.emit('activity');\n      },\n      error: (error) => {\n        this.emit('error', error);\n      },\n      closed: (closeEvent) => {\n        unbindListeners();\n\n        if (closeEvent && closeEvent.code) {\n          this.handleCloseEvent(closeEvent);\n        }\n\n        this.transport = null;\n        this.emit('closed');\n      },\n    };\n\n    var unbindListeners = () => {\n      Collections.objectApply(listeners, (listener, event) => {\n        this.transport.unbind(event, listener);\n      });\n    };\n\n    Collections.objectApply(listeners, (listener, event) => {\n      this.transport.bind(event, listener);\n    });\n  }\n\n  private handleCloseEvent(closeEvent: any) {\n    var action = Protocol.getCloseAction(closeEvent);\n    var error = Protocol.getCloseError(closeEvent);\n    if (error) {\n      this.emit('error', error);\n    }\n    if (action) {\n      this.emit(action, { action: action, error: error });\n    }\n  }\n}\n", "import Util from '../../util';\nimport * as Collections from '../../utils/collections';\nimport Protocol from '../protocol/protocol';\nimport Connection from '../connection';\nimport TransportConnection from '../../transports/transport_connection';\nimport HandshakePayload from './handshake_payload';\n\n/**\n * Handles Pusher protocol handshakes for transports.\n *\n * Calls back with a result object after handshake is completed. Results\n * always have two fields:\n * - action - string describing action to be taken after the handshake\n * - transport - the transport object passed to the constructor\n *\n * Different actions can set different additional properties on the result.\n * In the case of 'connected' action, there will be a 'connection' property\n * containing a Connection object for the transport. Other actions should\n * carry an 'error' property.\n *\n * @param {AbstractTransport} transport\n * @param {Function} callback\n */\nexport default class Handshake {\n  transport: TransportConnection;\n  callback: (HandshakePayload) => void;\n  onMessage: Function;\n  onClosed: Function;\n\n  constructor(\n    transport: TransportConnection,\n    callback: (HandshakePayload) => void,\n  ) {\n    this.transport = transport;\n    this.callback = callback;\n    this.bindListeners();\n  }\n\n  close() {\n    this.unbindListeners();\n    this.transport.close();\n  }\n\n  private bindListeners() {\n    this.onMessage = (m) => {\n      this.unbindListeners();\n\n      var result;\n      try {\n        result = Protocol.processHandshake(m);\n      } catch (e) {\n        this.finish('error', { error: e });\n        this.transport.close();\n        return;\n      }\n\n      if (result.action === 'connected') {\n        this.finish('connected', {\n          connection: new Connection(result.id, this.transport),\n          activityTimeout: result.activityTimeout,\n        });\n      } else {\n        this.finish(result.action, { error: result.error });\n        this.transport.close();\n      }\n    };\n\n    this.onClosed = (closeEvent) => {\n      this.unbindListeners();\n\n      var action = Protocol.getCloseAction(closeEvent) || 'backoff';\n      var error = Protocol.getCloseError(closeEvent);\n      this.finish(action, { error: error });\n    };\n\n    this.transport.bind('message', this.onMessage);\n    this.transport.bind('closed', this.onClosed);\n  }\n\n  private unbindListeners() {\n    this.transport.unbind('message', this.onMessage);\n    this.transport.unbind('closed', this.onClosed);\n  }\n\n  private finish(action: string, params: any) {\n    this.callback(\n      Collections.extend({ transport: this.transport, action: action }, params),\n    );\n  }\n}\n", "import * as Collections from '../utils/collections';\nimport Util from '../util';\nimport base64encode from '../base64';\nimport Timeline from './timeline';\nimport Runtime from 'runtime';\n\nexport interface TimelineSenderOptions {\n  host?: string;\n  port?: number;\n  path?: string;\n}\n\nexport default class TimelineSender {\n  timeline: Timeline;\n  options: TimelineSenderOptions;\n  host: string;\n\n  constructor(timeline: Timeline, options: TimelineSenderOptions) {\n    this.timeline = timeline;\n    this.options = options || {};\n  }\n\n  send(useTLS: boolean, callback?: Function) {\n    if (this.timeline.isEmpty()) {\n      return;\n    }\n\n    this.timeline.send(\n      Runtime.TimelineTransport.getAgent(this, useTLS),\n      callback,\n    );\n  }\n}\n", "/** Error classes used throughout the library. */\n// https://github.com/Microsoft/TypeScript-wiki/blob/master/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\nexport class BadEventName extends Error {\n  constructor(msg?: string) {\n    super(msg);\n\n    Object.setPrototypeOf(this, new.target.prototype);\n  }\n}\n\nexport class BadChannelName extends Error {\n  constructor(msg?: string) {\n    super(msg);\n\n    Object.setPrototypeOf(this, new.target.prototype);\n  }\n}\n\nexport class RequestTimedOut extends Error {\n  constructor(msg?: string) {\n    super(msg);\n\n    Object.setPrototypeOf(this, new.target.prototype);\n  }\n}\nexport class TransportPriorityTooLow extends Error {\n  constructor(msg?: string) {\n    super(msg);\n\n    Object.setPrototypeOf(this, new.target.prototype);\n  }\n}\nexport class TransportClosed extends Error {\n  constructor(msg?: string) {\n    super(msg);\n\n    Object.setPrototypeOf(this, new.target.prototype);\n  }\n}\nexport class UnsupportedFeature extends Error {\n  constructor(msg?: string) {\n    super(msg);\n\n    Object.setPrototypeOf(this, new.target.prototype);\n  }\n}\nexport class UnsupportedTransport extends Error {\n  constructor(msg?: string) {\n    super(msg);\n\n    Object.setPrototypeOf(this, new.target.prototype);\n  }\n}\nexport class UnsupportedStrategy extends Error {\n  constructor(msg?: string) {\n    super(msg);\n\n    Object.setPrototypeOf(this, new.target.prototype);\n  }\n}\nexport class HTTPAuthError extends Error {\n  status: number;\n  constructor(status: number, msg?: string) {\n    super(msg);\n    this.status = status;\n\n    Object.setPrototypeOf(this, new.target.prototype);\n  }\n}\n", "/**\n * A place to store help URLs for error messages etc\n */\n\nconst urlStore = {\n  baseUrl: 'https://pusher.com',\n  urls: {\n    authenticationEndpoint: {\n      path: '/docs/channels/server_api/authenticating_users',\n    },\n    authorizationEndpoint: {\n      path: '/docs/channels/server_api/authorizing-users/',\n    },\n    javascriptQuickStart: {\n      path: '/docs/javascript_quick_start',\n    },\n    triggeringClientEvents: {\n      path: '/docs/client_api_guide/client_events#trigger-events',\n    },\n    encryptedChannelSupport: {\n      fullUrl:\n        'https://github.com/pusher/pusher-js/tree/cc491015371a4bde5743d1c87a0fbac0feb53195#encrypted-channel-support',\n    },\n  },\n};\n\n/** Builds a consistent string with links to pusher documentation\n *\n * @param {string} key - relevant key in the url_store.urls object\n * @return {string} suffix string to append to log message\n */\nconst buildLogSuffix = function (key: string): string {\n  const urlPrefix = 'See:';\n  const urlObj = urlStore.urls[key];\n  if (!urlObj) return '';\n\n  let url;\n  if (urlObj.fullUrl) {\n    url = urlObj.fullUrl;\n  } else if (urlObj.path) {\n    url = urlStore.baseUrl + urlObj.path;\n  }\n\n  if (!url) return '';\n  return `${urlPrefix} ${url}`;\n};\n\nexport default { buildLogSuffix };\n", "import { default as EventsDispatcher } from '../events/dispatcher';\nimport * as Errors from '../errors';\nimport Logger from '../logger';\nimport Pusher from '../pusher';\nimport { PusherEvent } from '../connection/protocol/message-types';\nimport Metada<PERSON> from './metadata';\nimport UrlStore from '../utils/url_store';\nimport {\n  ChannelAuthorizationData,\n  ChannelAuthorizationCallback,\n} from '../auth/options';\nimport { HTTPAuthError } from '../errors';\n\n/** Provides base public channel interface with an event emitter.\n *\n * Emits:\n * - pusher:subscription_succeeded - after subscribing successfully\n * - other non-internal events\n *\n * @param {String} name\n * @param {Pusher} pusher\n */\nexport default class Channel extends EventsDispatcher {\n  name: string;\n  pusher: Pusher;\n  subscribed: boolean;\n  subscriptionPending: boolean;\n  subscriptionCancelled: boolean;\n  subscriptionCount: null;\n\n  constructor(name: string, pusher: Pusher) {\n    super(function (event, data) {\n      Logger.debug('No callbacks on ' + name + ' for ' + event);\n    });\n\n    this.name = name;\n    this.pusher = pusher;\n    this.subscribed = false;\n    this.subscriptionPending = false;\n    this.subscriptionCancelled = false;\n  }\n\n  /** Skips authorization, since public channels don't require it.\n   *\n   * @param {Function} callback\n   */\n  authorize(socketId: string, callback: ChannelAuthorizationCallback) {\n    return callback(null, { auth: '' });\n  }\n\n  /** Triggers an event */\n  trigger(event: string, data: any) {\n    if (event.indexOf('client-') !== 0) {\n      throw new Errors.BadEventName(\n        \"Event '\" + event + \"' does not start with 'client-'\",\n      );\n    }\n    if (!this.subscribed) {\n      var suffix = UrlStore.buildLogSuffix('triggeringClientEvents');\n      Logger.warn(\n        `Client event triggered before channel 'subscription_succeeded' event . ${suffix}`,\n      );\n    }\n    return this.pusher.send_event(event, data, this.name);\n  }\n\n  /** Signals disconnection to the channel. For internal use only. */\n  disconnect() {\n    this.subscribed = false;\n    this.subscriptionPending = false;\n  }\n\n  /** Handles a PusherEvent. For internal use only.\n   *\n   * @param {PusherEvent} event\n   */\n  handleEvent(event: PusherEvent) {\n    var eventName = event.event;\n    var data = event.data;\n    if (eventName === 'pusher_internal:subscription_succeeded') {\n      this.handleSubscriptionSucceededEvent(event);\n    } else if (eventName === 'pusher_internal:subscription_count') {\n      this.handleSubscriptionCountEvent(event);\n    } else if (eventName.indexOf('pusher_internal:') !== 0) {\n      var metadata: Metadata = {};\n      this.emit(eventName, data, metadata);\n    }\n  }\n\n  handleSubscriptionSucceededEvent(event: PusherEvent) {\n    this.subscriptionPending = false;\n    this.subscribed = true;\n    if (this.subscriptionCancelled) {\n      this.pusher.unsubscribe(this.name);\n    } else {\n      this.emit('pusher:subscription_succeeded', event.data);\n    }\n  }\n\n  handleSubscriptionCountEvent(event: PusherEvent) {\n    if (event.data.subscription_count) {\n      this.subscriptionCount = event.data.subscription_count;\n    }\n\n    this.emit('pusher:subscription_count', event.data);\n  }\n\n  /** Sends a subscription request. For internal use only. */\n  subscribe() {\n    if (this.subscribed) {\n      return;\n    }\n    this.subscriptionPending = true;\n    this.subscriptionCancelled = false;\n    this.authorize(\n      this.pusher.connection.socket_id,\n      (error: Error | null, data: ChannelAuthorizationData) => {\n        if (error) {\n          this.subscriptionPending = false;\n          // Why not bind to 'pusher:subscription_error' a level up, and log there?\n          // Binding to this event would cause the warning about no callbacks being\n          // bound (see constructor) to be suppressed, that's not what we want.\n          Logger.error(error.toString());\n          this.emit(\n            'pusher:subscription_error',\n            Object.assign(\n              {},\n              {\n                type: 'AuthError',\n                error: error.message,\n              },\n              error instanceof HTTPAuthError ? { status: error.status } : {},\n            ),\n          );\n        } else {\n          this.pusher.send_event('pusher:subscribe', {\n            auth: data.auth,\n            channel_data: data.channel_data,\n            channel: this.name,\n          });\n        }\n      },\n    );\n  }\n\n  /** Sends an unsubscription request. For internal use only. */\n  unsubscribe() {\n    this.subscribed = false;\n    this.pusher.send_event('pusher:unsubscribe', {\n      channel: this.name,\n    });\n  }\n\n  /** Cancels an in progress subscription. For internal use only. */\n  cancelSubscription() {\n    this.subscriptionCancelled = true;\n  }\n\n  /** Reinstates an in progress subscripiton. For internal use only. */\n  reinstateSubscription() {\n    this.subscriptionCancelled = false;\n  }\n}\n", "import Factory from '../utils/factory';\nimport Channel from './channel';\nimport { ChannelAuthorizationCallback } from '../auth/options';\n\n/** Extends public channels to provide private channel interface.\n *\n * @param {String} name\n * @param {Pusher} pusher\n */\nexport default class PrivateChannel extends Channel {\n  /** Authorizes the connection to use the channel.\n   *\n   * @param  {String} socketId\n   * @param  {Function} callback\n   */\n  authorize(socketId: string, callback: ChannelAuthorizationCallback) {\n    return this.pusher.config.channelAuthorizer(\n      {\n        channelName: this.name,\n        socketId: socketId,\n      },\n      callback,\n    );\n  }\n}\n", "import * as Collections from '../utils/collections';\n\n/** Represents a collection of members of a presence channel. */\nexport default class Members {\n  members: any;\n  count: number;\n  myID: any;\n  me: any;\n\n  constructor() {\n    this.reset();\n  }\n\n  /** Returns member's info for given id.\n   *\n   * Resulting object containts two fields - id and info.\n   *\n   * @param {Number} id\n   * @return {Object} member's info or null\n   */\n  get(id: string): any {\n    if (Object.prototype.hasOwnProperty.call(this.members, id)) {\n      return {\n        id: id,\n        info: this.members[id],\n      };\n    } else {\n      return null;\n    }\n  }\n\n  /** Calls back for each member in unspecified order.\n   *\n   * @param  {Function} callback\n   */\n  each(callback: Function) {\n    Collections.objectApply(this.members, (member, id) => {\n      callback(this.get(id));\n    });\n  }\n\n  /** Updates the id for connected member. For internal use only. */\n  setMyID(id: string) {\n    this.myID = id;\n  }\n\n  /** Handles subscription data. For internal use only. */\n  onSubscription(subscriptionData: any) {\n    this.members = subscriptionData.presence.hash;\n    this.count = subscriptionData.presence.count;\n    this.me = this.get(this.myID);\n  }\n\n  /** Adds a new member to the collection. For internal use only. */\n  addMember(memberData: any) {\n    if (this.get(memberData.user_id) === null) {\n      this.count++;\n    }\n    this.members[memberData.user_id] = memberData.user_info;\n    return this.get(memberData.user_id);\n  }\n\n  /** Adds a member from the collection. For internal use only. */\n  removeMember(memberData: any) {\n    var member = this.get(memberData.user_id);\n    if (member) {\n      delete this.members[memberData.user_id];\n      this.count--;\n    }\n    return member;\n  }\n\n  /** Resets the collection to the initial state. For internal use only. */\n  reset() {\n    this.members = {};\n    this.count = 0;\n    this.myID = null;\n    this.me = null;\n  }\n}\n", "import PrivateChannel from './private_channel';\nimport Logger from '../logger';\nimport Members from './members';\nimport Pusher from '../pusher';\nimport UrlStore from 'core/utils/url_store';\nimport { PusherEvent } from '../connection/protocol/message-types';\nimport Metadata from './metadata';\nimport { ChannelAuthorizationData } from '../auth/options';\n\nexport default class PresenceChannel extends PrivateChannel {\n  members: Members;\n\n  /** Adds presence channel functionality to private channels.\n   *\n   * @param {String} name\n   * @param {Pusher} pusher\n   */\n  constructor(name: string, pusher: Pusher) {\n    super(name, pusher);\n    this.members = new Members();\n  }\n\n  /** Authorizes the connection as a member of the channel.\n   *\n   * @param  {String} socketId\n   * @param  {Function} callback\n   */\n  authorize(socketId: string, callback: Function) {\n    super.authorize(socketId, async (error, authData) => {\n      if (!error) {\n        authData = authData as ChannelAuthorizationData;\n        if (authData.channel_data != null) {\n          var channelData = JSON.parse(authData.channel_data);\n          this.members.setMyID(channelData.user_id);\n        } else {\n          await this.pusher.user.signinDonePromise;\n          if (this.pusher.user.user_data != null) {\n            // If the user is signed in, get the id of the authenticated user\n            // and allow the presence authorization to continue.\n            this.members.setMyID(this.pusher.user.user_data.id);\n          } else {\n            let suffix = UrlStore.buildLogSuffix('authorizationEndpoint');\n            Logger.error(\n              `Invalid auth response for channel '${this.name}', ` +\n                `expected 'channel_data' field. ${suffix}, ` +\n                `or the user should be signed in.`,\n            );\n            callback('Invalid auth response');\n            return;\n          }\n        }\n      }\n      callback(error, authData);\n    });\n  }\n\n  /** Handles presence and subscription events. For internal use only.\n   *\n   * @param {PusherEvent} event\n   */\n  handleEvent(event: PusherEvent) {\n    var eventName = event.event;\n    if (eventName.indexOf('pusher_internal:') === 0) {\n      this.handleInternalEvent(event);\n    } else {\n      var data = event.data;\n      var metadata: Metadata = {};\n      if (event.user_id) {\n        metadata.user_id = event.user_id;\n      }\n      this.emit(eventName, data, metadata);\n    }\n  }\n  handleInternalEvent(event: PusherEvent) {\n    var eventName = event.event;\n    var data = event.data;\n    switch (eventName) {\n      case 'pusher_internal:subscription_succeeded':\n        this.handleSubscriptionSucceededEvent(event);\n        break;\n      case 'pusher_internal:subscription_count':\n        this.handleSubscriptionCountEvent(event);\n        break;\n      case 'pusher_internal:member_added':\n        var addedMember = this.members.addMember(data);\n        this.emit('pusher:member_added', addedMember);\n        break;\n      case 'pusher_internal:member_removed':\n        var removedMember = this.members.removeMember(data);\n        if (removedMember) {\n          this.emit('pusher:member_removed', removedMember);\n        }\n        break;\n    }\n  }\n\n  handleSubscriptionSucceededEvent(event: PusherEvent) {\n    this.subscriptionPending = false;\n    this.subscribed = true;\n    if (this.subscriptionCancelled) {\n      this.pusher.unsubscribe(this.name);\n    } else {\n      this.members.onSubscription(event.data);\n      this.emit('pusher:subscription_succeeded', this.members);\n    }\n  }\n\n  /** Resets the channel state, including members map. For internal use only. */\n  disconnect() {\n    this.members.reset();\n    super.disconnect();\n  }\n}\n", "import PrivateChannel from './private_channel';\nimport * as Errors from '../errors';\nimport Logger from '../logger';\nimport Pusher from '../pusher';\nimport { decode as encodeUTF8 } from '@stablelib/utf8';\nimport { decode as decodeBase64 } from '@stablelib/base64';\nimport Dispatcher from '../events/dispatcher';\nimport { PusherEvent } from '../connection/protocol/message-types';\nimport {\n  ChannelAuthorizationData,\n  ChannelAuthorizationCallback,\n} from '../auth/options';\nimport * as nacl from 'tweetnacl';\n\n/** Extends private channels to provide encrypted channel interface.\n *\n * @param {String} name\n * @param {Pusher} pusher\n */\nexport default class EncryptedChannel extends PrivateChannel {\n  key: Uint8Array = null;\n  nacl: nacl;\n\n  constructor(name: string, pusher: Pusher, nacl: nacl) {\n    super(name, pusher);\n    this.nacl = nacl;\n  }\n\n  /** Authorizes the connection to use the channel.\n   *\n   * @param  {String} socketId\n   * @param  {Function} callback\n   */\n  authorize(socketId: string, callback: ChannelAuthorizationCallback) {\n    super.authorize(\n      socketId,\n      (error: Error | null, authData: ChannelAuthorizationData) => {\n        if (error) {\n          callback(error, authData);\n          return;\n        }\n        let sharedSecret = authData['shared_secret'];\n        if (!sharedSecret) {\n          callback(\n            new Error(\n              `No shared_secret key in auth payload for encrypted channel: ${this.name}`,\n            ),\n            null,\n          );\n          return;\n        }\n        this.key = decodeBase64(sharedSecret);\n        delete authData['shared_secret'];\n        callback(null, authData);\n      },\n    );\n  }\n\n  trigger(event: string, data: any): boolean {\n    throw new Errors.UnsupportedFeature(\n      'Client events are not currently supported for encrypted channels',\n    );\n  }\n\n  /** Handles an event. For internal use only.\n   *\n   * @param {PusherEvent} event\n   */\n  handleEvent(event: PusherEvent) {\n    var eventName = event.event;\n    var data = event.data;\n    if (\n      eventName.indexOf('pusher_internal:') === 0 ||\n      eventName.indexOf('pusher:') === 0\n    ) {\n      super.handleEvent(event);\n      return;\n    }\n    this.handleEncryptedEvent(eventName, data);\n  }\n\n  private handleEncryptedEvent(event: string, data: any): void {\n    if (!this.key) {\n      Logger.debug(\n        'Received encrypted event before key has been retrieved from the authEndpoint',\n      );\n      return;\n    }\n    if (!data.ciphertext || !data.nonce) {\n      Logger.error(\n        'Unexpected format for encrypted event, expected object with `ciphertext` and `nonce` fields, got: ' +\n          data,\n      );\n      return;\n    }\n    let cipherText = decodeBase64(data.ciphertext);\n    if (cipherText.length < this.nacl.secretbox.overheadLength) {\n      Logger.error(\n        `Expected encrypted event ciphertext length to be ${this.nacl.secretbox.overheadLength}, got: ${cipherText.length}`,\n      );\n      return;\n    }\n    let nonce = decodeBase64(data.nonce);\n    if (nonce.length < this.nacl.secretbox.nonceLength) {\n      Logger.error(\n        `Expected encrypted event nonce length to be ${this.nacl.secretbox.nonceLength}, got: ${nonce.length}`,\n      );\n      return;\n    }\n\n    let bytes = this.nacl.secretbox.open(cipherText, nonce, this.key);\n    if (bytes === null) {\n      Logger.debug(\n        'Failed to decrypt an event, probably because it was encrypted with a different key. Fetching a new key from the authEndpoint...',\n      );\n      // Try a single time to retrieve a new auth key and decrypt the event with it\n      // If this fails, a new key will be requested when a new message is received\n      this.authorize(this.pusher.connection.socket_id, (error, authData) => {\n        if (error) {\n          Logger.error(\n            `Failed to make a request to the authEndpoint: ${authData}. Unable to fetch new key, so dropping encrypted event`,\n          );\n          return;\n        }\n        bytes = this.nacl.secretbox.open(cipherText, nonce, this.key);\n        if (bytes === null) {\n          Logger.error(\n            `Failed to decrypt event with new key. Dropping encrypted event`,\n          );\n          return;\n        }\n        this.emit(event, this.getDataToEmit(bytes));\n        return;\n      });\n      return;\n    }\n    this.emit(event, this.getDataToEmit(bytes));\n  }\n\n  // Try and parse the decrypted bytes as JSON. If we can't parse it, just\n  // return the utf-8 string\n  getDataToEmit(bytes: Uint8Array): string {\n    let raw = encodeUTF8(bytes);\n    try {\n      return JSON.parse(raw);\n    } catch {\n      return raw;\n    }\n  }\n}\n", "import { default as EventsDispatcher } from '../events/dispatcher';\nimport { OneOffTimer as Timer } from '../utils/timers';\nimport { Config } from '../config';\nimport Logger from '../logger';\nimport HandshakePayload from './handshake/handshake_payload';\nimport Connection from './connection';\nimport Strategy from '../strategies/strategy';\nimport StrategyRunner from '../strategies/strategy_runner';\nimport * as Collections from '../utils/collections';\nimport Timeline from '../timeline/timeline';\nimport ConnectionManagerOptions from './connection_manager_options';\nimport Runtime from 'runtime';\n\nimport {\n  ErrorCallbacks,\n  HandshakeCallbacks,\n  ConnectionCallbacks,\n} from './callbacks';\nimport Action from './protocol/action';\n\n/** Manages connection to Pusher.\n *\n * Uses a strategy (currently only default), timers and network availability\n * info to establish a connection and export its state. In case of failures,\n * manages reconnection attempts.\n *\n * Exports state changes as following events:\n * - \"state_change\", { previous: p, current: state }\n * - state\n *\n * States:\n * - initialized - initial state, never transitioned to\n * - connecting - connection is being established\n * - connected - connection has been fully established\n * - disconnected - on requested disconnection\n * - unavailable - after connection timeout or when there's no network\n * - failed - when the connection strategy is not supported\n *\n * Options:\n * - unavailableTimeout - time to transition to unavailable state\n * - activityTimeout - time after which ping message should be sent\n * - pongTimeout - time for Pusher to respond with pong before reconnecting\n *\n * @param {String} key application key\n * @param {Object} options\n */\nexport default class ConnectionManager extends EventsDispatcher {\n  key: string;\n  options: ConnectionManagerOptions;\n  state: string;\n  connection: Connection;\n  usingTLS: boolean;\n  timeline: Timeline;\n  socket_id: string;\n  unavailableTimer: Timer;\n  activityTimer: Timer;\n  retryTimer: Timer;\n  activityTimeout: number;\n  strategy: Strategy;\n  runner: StrategyRunner;\n  errorCallbacks: ErrorCallbacks;\n  handshakeCallbacks: HandshakeCallbacks;\n  connectionCallbacks: ConnectionCallbacks;\n\n  constructor(key: string, options: ConnectionManagerOptions) {\n    super();\n    this.state = 'initialized';\n    this.connection = null;\n\n    this.key = key;\n    this.options = options;\n    this.timeline = this.options.timeline;\n    this.usingTLS = this.options.useTLS;\n\n    this.errorCallbacks = this.buildErrorCallbacks();\n    this.connectionCallbacks = this.buildConnectionCallbacks(\n      this.errorCallbacks,\n    );\n    this.handshakeCallbacks = this.buildHandshakeCallbacks(this.errorCallbacks);\n\n    var Network = Runtime.getNetwork();\n\n    Network.bind('online', () => {\n      this.timeline.info({ netinfo: 'online' });\n      if (this.state === 'connecting' || this.state === 'unavailable') {\n        this.retryIn(0);\n      }\n    });\n    Network.bind('offline', () => {\n      this.timeline.info({ netinfo: 'offline' });\n      if (this.connection) {\n        this.sendActivityCheck();\n      }\n    });\n\n    this.updateStrategy();\n  }\n\n  /** Establishes a connection to Pusher.\n   *\n   * Does nothing when connection is already established. See top-level doc\n   * to find events emitted on connection attempts.\n   */\n  connect() {\n    if (this.connection || this.runner) {\n      return;\n    }\n    if (!this.strategy.isSupported()) {\n      this.updateState('failed');\n      return;\n    }\n    this.updateState('connecting');\n    this.startConnecting();\n    this.setUnavailableTimer();\n  }\n\n  /** Sends raw data.\n   *\n   * @param {String} data\n   */\n  send(data) {\n    if (this.connection) {\n      return this.connection.send(data);\n    } else {\n      return false;\n    }\n  }\n\n  /** Sends an event.\n   *\n   * @param {String} name\n   * @param {String} data\n   * @param {String} [channel]\n   * @returns {Boolean} whether message was sent or not\n   */\n  send_event(name: string, data: any, channel?: string) {\n    if (this.connection) {\n      return this.connection.send_event(name, data, channel);\n    } else {\n      return false;\n    }\n  }\n\n  /** Closes the connection. */\n  disconnect() {\n    this.disconnectInternally();\n    this.updateState('disconnected');\n  }\n\n  isUsingTLS() {\n    return this.usingTLS;\n  }\n\n  private startConnecting() {\n    var callback = (error, handshake) => {\n      if (error) {\n        this.runner = this.strategy.connect(0, callback);\n      } else {\n        if (handshake.action === 'error') {\n          this.emit('error', {\n            type: 'HandshakeError',\n            error: handshake.error,\n          });\n          this.timeline.error({ handshakeError: handshake.error });\n        } else {\n          this.abortConnecting(); // we don't support switching connections yet\n          this.handshakeCallbacks[handshake.action](handshake);\n        }\n      }\n    };\n    this.runner = this.strategy.connect(0, callback);\n  }\n\n  private abortConnecting() {\n    if (this.runner) {\n      this.runner.abort();\n      this.runner = null;\n    }\n  }\n\n  private disconnectInternally() {\n    this.abortConnecting();\n    this.clearRetryTimer();\n    this.clearUnavailableTimer();\n    if (this.connection) {\n      var connection = this.abandonConnection();\n      connection.close();\n    }\n  }\n\n  private updateStrategy() {\n    this.strategy = this.options.getStrategy({\n      key: this.key,\n      timeline: this.timeline,\n      useTLS: this.usingTLS,\n    });\n  }\n\n  private retryIn(delay) {\n    this.timeline.info({ action: 'retry', delay: delay });\n    if (delay > 0) {\n      this.emit('connecting_in', Math.round(delay / 1000));\n    }\n    this.retryTimer = new Timer(delay || 0, () => {\n      this.disconnectInternally();\n      this.connect();\n    });\n  }\n\n  private clearRetryTimer() {\n    if (this.retryTimer) {\n      this.retryTimer.ensureAborted();\n      this.retryTimer = null;\n    }\n  }\n\n  private setUnavailableTimer() {\n    this.unavailableTimer = new Timer(this.options.unavailableTimeout, () => {\n      this.updateState('unavailable');\n    });\n  }\n\n  private clearUnavailableTimer() {\n    if (this.unavailableTimer) {\n      this.unavailableTimer.ensureAborted();\n    }\n  }\n\n  private sendActivityCheck() {\n    this.stopActivityCheck();\n    this.connection.ping();\n    // wait for pong response\n    this.activityTimer = new Timer(this.options.pongTimeout, () => {\n      this.timeline.error({ pong_timed_out: this.options.pongTimeout });\n      this.retryIn(0);\n    });\n  }\n\n  private resetActivityCheck() {\n    this.stopActivityCheck();\n    // send ping after inactivity\n    if (this.connection && !this.connection.handlesActivityChecks()) {\n      this.activityTimer = new Timer(this.activityTimeout, () => {\n        this.sendActivityCheck();\n      });\n    }\n  }\n\n  private stopActivityCheck() {\n    if (this.activityTimer) {\n      this.activityTimer.ensureAborted();\n    }\n  }\n\n  private buildConnectionCallbacks(\n    errorCallbacks: ErrorCallbacks,\n  ): ConnectionCallbacks {\n    return Collections.extend<ConnectionCallbacks>({}, errorCallbacks, {\n      message: (message) => {\n        // includes pong messages from server\n        this.resetActivityCheck();\n        this.emit('message', message);\n      },\n      ping: () => {\n        this.send_event('pusher:pong', {});\n      },\n      activity: () => {\n        this.resetActivityCheck();\n      },\n      error: (error) => {\n        // just emit error to user - socket will already be closed by browser\n        this.emit('error', error);\n      },\n      closed: () => {\n        this.abandonConnection();\n        if (this.shouldRetry()) {\n          this.retryIn(1000);\n        }\n      },\n    });\n  }\n\n  private buildHandshakeCallbacks(\n    errorCallbacks: ErrorCallbacks,\n  ): HandshakeCallbacks {\n    return Collections.extend<HandshakeCallbacks>({}, errorCallbacks, {\n      connected: (handshake: HandshakePayload) => {\n        this.activityTimeout = Math.min(\n          this.options.activityTimeout,\n          handshake.activityTimeout,\n          handshake.connection.activityTimeout || Infinity,\n        );\n        this.clearUnavailableTimer();\n        this.setConnection(handshake.connection);\n        this.socket_id = this.connection.id;\n        this.updateState('connected', { socket_id: this.socket_id });\n      },\n    });\n  }\n\n  private buildErrorCallbacks(): ErrorCallbacks {\n    let withErrorEmitted = (callback) => {\n      return (result: Action | HandshakePayload) => {\n        if (result.error) {\n          this.emit('error', { type: 'WebSocketError', error: result.error });\n        }\n        callback(result);\n      };\n    };\n\n    return {\n      tls_only: withErrorEmitted(() => {\n        this.usingTLS = true;\n        this.updateStrategy();\n        this.retryIn(0);\n      }),\n      refused: withErrorEmitted(() => {\n        this.disconnect();\n      }),\n      backoff: withErrorEmitted(() => {\n        this.retryIn(1000);\n      }),\n      retry: withErrorEmitted(() => {\n        this.retryIn(0);\n      }),\n    };\n  }\n\n  private setConnection(connection) {\n    this.connection = connection;\n    for (var event in this.connectionCallbacks) {\n      this.connection.bind(event, this.connectionCallbacks[event]);\n    }\n    this.resetActivityCheck();\n  }\n\n  private abandonConnection() {\n    if (!this.connection) {\n      return;\n    }\n    this.stopActivityCheck();\n    for (var event in this.connectionCallbacks) {\n      this.connection.unbind(event, this.connectionCallbacks[event]);\n    }\n    var connection = this.connection;\n    this.connection = null;\n    return connection;\n  }\n\n  private updateState(newState: string, data?: any) {\n    var previousState = this.state;\n    this.state = newState;\n    if (previousState !== newState) {\n      var newStateDescription = newState;\n      if (newStateDescription === 'connected') {\n        newStateDescription += ' with new socket ID ' + data.socket_id;\n      }\n      Logger.debug(\n        'State changed',\n        previousState + ' -> ' + newStateDescription,\n      );\n      this.timeline.info({ state: newState, params: data });\n      this.emit('state_change', { previous: previousState, current: newState });\n      this.emit(newState, data);\n    }\n  }\n\n  private shouldRetry(): boolean {\n    return this.state === 'connecting' || this.state === 'connected';\n  }\n}\n", "import Channel from './channel';\nimport * as Collections from '../utils/collections';\nimport ChannelTable from './channel_table';\nimport Factory from '../utils/factory';\nimport Pusher from '../pusher';\nimport Logger from '../logger';\nimport * as Errors from '../errors';\nimport urlStore from '../utils/url_store';\n\n/** Handles a channel map. */\nexport default class Channels {\n  channels: ChannelTable;\n\n  constructor() {\n    this.channels = {};\n  }\n\n  /** Creates or retrieves an existing channel by its name.\n   *\n   * @param {String} name\n   * @param {Pusher} pusher\n   * @return {Channel}\n   */\n  add(name: string, pusher: Pusher) {\n    if (!this.channels[name]) {\n      this.channels[name] = createChannel(name, pusher);\n    }\n    return this.channels[name];\n  }\n\n  /** Returns a list of all channels\n   *\n   * @return {Array}\n   */\n  all(): Channel[] {\n    return Collections.values(this.channels);\n  }\n\n  /** Finds a channel by its name.\n   *\n   * @param {String} name\n   * @return {Channel} channel or null if it doesn't exist\n   */\n  find(name: string) {\n    return this.channels[name];\n  }\n\n  /** Removes a channel from the map.\n   *\n   * @param {String} name\n   */\n  remove(name: string) {\n    var channel = this.channels[name];\n    delete this.channels[name];\n    return channel;\n  }\n\n  /** Proxies disconnection signal to all channels. */\n  disconnect() {\n    Collections.objectApply(this.channels, function (channel) {\n      channel.disconnect();\n    });\n  }\n}\n\nfunction createChannel(name: string, pusher: Pusher): Channel {\n  if (name.indexOf('private-encrypted-') === 0) {\n    if (pusher.config.nacl) {\n      return Factory.createEncryptedChannel(name, pusher, pusher.config.nacl);\n    }\n    let errMsg =\n      'Tried to subscribe to a private-encrypted- channel but no nacl implementation available';\n    let suffix = urlStore.buildLogSuffix('encryptedChannelSupport');\n    throw new Errors.UnsupportedFeature(`${errMsg}. ${suffix}`);\n  } else if (name.indexOf('private-') === 0) {\n    return Factory.createPrivateChannel(name, pusher);\n  } else if (name.indexOf('presence-') === 0) {\n    return Factory.createPresenceChannel(name, pusher);\n  } else if (name.indexOf('#') === 0) {\n    throw new Errors.BadChannelName(\n      'Cannot create a channel with name \"' + name + '\".',\n    );\n  } else {\n    return Factory.createChannel(name, pusher);\n  }\n}\n", "import AssistantToTheTransportManager from '../transports/assistant_to_the_transport_manager';\nimport PingDelayOptions from '../transports/ping_delay_options';\nimport Transport from '../transports/transport';\nimport TransportManager from '../transports/transport_manager';\nimport Handshake from '../connection/handshake';\nimport TransportConnection from '../transports/transport_connection';\nimport SocketHooks from '../http/socket_hooks';\nimport HTTPSocket from '../http/http_socket';\n\nimport Timeline from '../timeline/timeline';\nimport {\n  default as TimelineSender,\n  TimelineSenderOptions,\n} from '../timeline/timeline_sender';\nimport PresenceChannel from '../channels/presence_channel';\nimport PrivateChannel from '../channels/private_channel';\nimport EncryptedChannel from '../channels/encrypted_channel';\nimport Channel from '../channels/channel';\nimport ConnectionManager from '../connection/connection_manager';\nimport ConnectionManagerOptions from '../connection/connection_manager_options';\nimport Ajax from '../http/ajax';\nimport Channels from '../channels/channels';\nimport Pusher from '../pusher';\nimport { Config } from '../config';\nimport * as nacl from 'tweetnacl';\n\nvar Factory = {\n  createChannels(): Channels {\n    return new Channels();\n  },\n\n  createConnectionManager(\n    key: string,\n    options: ConnectionManagerOptions,\n  ): ConnectionManager {\n    return new ConnectionManager(key, options);\n  },\n\n  createChannel(name: string, pusher: Pusher): Channel {\n    return new Channel(name, pusher);\n  },\n\n  createPrivateChannel(name: string, pusher: Pusher): PrivateChannel {\n    return new PrivateChannel(name, pusher);\n  },\n\n  createPresenceChannel(name: string, pusher: Pusher): PresenceChannel {\n    return new PresenceChannel(name, pusher);\n  },\n\n  createEncryptedChannel(\n    name: string,\n    pusher: Pusher,\n    nacl: nacl,\n  ): EncryptedChannel {\n    return new EncryptedChannel(name, pusher, nacl);\n  },\n\n  createTimelineSender(timeline: Timeline, options: TimelineSenderOptions) {\n    return new TimelineSender(timeline, options);\n  },\n\n  createHandshake(\n    transport: TransportConnection,\n    callback: (HandshakePayload) => void,\n  ): Handshake {\n    return new Handshake(transport, callback);\n  },\n\n  createAssistantToTheTransportManager(\n    manager: TransportManager,\n    transport: Transport,\n    options: PingDelayOptions,\n  ): AssistantToTheTransportManager {\n    return new AssistantToTheTransportManager(manager, transport, options);\n  },\n};\n\nexport default Factory;\n", "import AssistantToTheTransportManager from './assistant_to_the_transport_manager';\nimport Transport from './transport';\nimport PingDelayOptions from './ping_delay_options';\nimport Factory from '../utils/factory';\n\nexport interface TransportManagerOptions extends PingDelayOptions {\n  lives?: number;\n}\n\n/** Keeps track of the number of lives left for a transport.\n *\n * In the beginning of a session, transports may be assigned a number of\n * lives. When an AssistantToTheTransportManager instance reports a transport\n * connection closed uncleanly, the transport loses a life. When the number\n * of lives drops to zero, the transport gets disabled by its manager.\n *\n * @param {Object} options\n */\nexport default class TransportManager {\n  options: TransportManagerOptions;\n  livesLeft: number;\n\n  constructor(options: TransportManagerOptions) {\n    this.options = options || {};\n    this.livesLeft = this.options.lives || Infinity;\n  }\n\n  /** Creates a assistant for the transport.\n   *\n   * @param {Transport} transport\n   * @returns {AssistantToTheTransportManager}\n   */\n  getAssistant(transport: Transport): AssistantToTheTransportManager {\n    return Factory.createAssistantToTheTransportManager(this, transport, {\n      minPingDelay: this.options.minPingDelay,\n      maxPingDelay: this.options.maxPingDelay,\n    });\n  }\n\n  /** Returns whether the transport has any lives left.\n   *\n   * @returns {Boolean}\n   */\n  isAlive(): boolean {\n    return this.livesLeft > 0;\n  }\n\n  /** Takes one life from the transport. */\n  reportDeath() {\n    this.livesLeft -= 1;\n  }\n}\n", "import * as Collections from '../utils/collections';\nimport Util from '../util';\nimport { OneOffTimer as Timer } from '../utils/timers';\nimport Strategy from './strategy';\nimport StrategyOptions from './strategy_options';\n\n/** Loops through strategies with optional timeouts.\n *\n * Options:\n * - loop - whether it should loop through the substrategy list\n * - timeout - initial timeout for a single substrategy\n * - timeoutLimit - maximum timeout\n *\n * @param {Strategy[]} strategies\n * @param {Object} options\n */\nexport default class SequentialStrategy implements Strategy {\n  strategies: Strategy[];\n  loop: boolean;\n  failFast: boolean;\n  timeout: number;\n  timeoutLimit: number;\n\n  constructor(strategies: Strategy[], options: StrategyOptions) {\n    this.strategies = strategies;\n    this.loop = Boolean(options.loop);\n    this.failFast = Boolean(options.failFast);\n    this.timeout = options.timeout;\n    this.timeoutLimit = options.timeoutLimit;\n  }\n\n  isSupported(): boolean {\n    return Collections.any(this.strategies, Util.method('isSupported'));\n  }\n\n  connect(minPriority: number, callback: Function) {\n    var strategies = this.strategies;\n    var current = 0;\n    var timeout = this.timeout;\n    var runner = null;\n\n    var tryNextStrategy = (error, handshake) => {\n      if (handshake) {\n        callback(null, handshake);\n      } else {\n        current = current + 1;\n        if (this.loop) {\n          current = current % strategies.length;\n        }\n\n        if (current < strategies.length) {\n          if (timeout) {\n            timeout = timeout * 2;\n            if (this.timeoutLimit) {\n              timeout = Math.min(timeout, this.timeoutLimit);\n            }\n          }\n          runner = this.tryStrategy(\n            strategies[current],\n            minPriority,\n            { timeout, failFast: this.failFast },\n            tryNextStrategy,\n          );\n        } else {\n          callback(true);\n        }\n      }\n    };\n\n    runner = this.tryStrategy(\n      strategies[current],\n      minPriority,\n      { timeout: timeout, failFast: this.failFast },\n      tryNextStrategy,\n    );\n\n    return {\n      abort: function () {\n        runner.abort();\n      },\n      forceMinPriority: function (p) {\n        minPriority = p;\n        if (runner) {\n          runner.forceMinPriority(p);\n        }\n      },\n    };\n  }\n\n  private tryStrategy(\n    strategy: Strategy,\n    minPriority: number,\n    options: StrategyOptions,\n    callback: Function,\n  ) {\n    var timer = null;\n    var runner = null;\n\n    if (options.timeout > 0) {\n      timer = new Timer(options.timeout, function () {\n        runner.abort();\n        callback(true);\n      });\n    }\n\n    runner = strategy.connect(minPriority, function (error, handshake) {\n      if (error && timer && timer.isRunning() && !options.failFast) {\n        // advance to the next strategy after the timeout\n        return;\n      }\n      if (timer) {\n        timer.ensureAborted();\n      }\n      callback(error, handshake);\n    });\n\n    return {\n      abort: function () {\n        if (timer) {\n          timer.ensureAborted();\n        }\n        runner.abort();\n      },\n      forceMinPriority: function (p) {\n        runner.forceMinPriority(p);\n      },\n    };\n  }\n}\n", "import * as Collections from '../utils/collections';\nimport Util from '../util';\nimport Strategy from './strategy';\n\n/** Launches all substrategies and emits prioritized connected transports.\n *\n * @param {Array} strategies\n */\nexport default class BestConnectedEverStrategy implements Strategy {\n  strategies: Strategy[];\n\n  constructor(strategies: Strategy[]) {\n    this.strategies = strategies;\n  }\n\n  isSupported(): boolean {\n    return Collections.any(this.strategies, Util.method('isSupported'));\n  }\n\n  connect(minPriority: number, callback: Function) {\n    return connect(this.strategies, minPriority, function (i, runners) {\n      return function (error, handshake) {\n        runners[i].error = error;\n        if (error) {\n          if (allRunnersFailed(runners)) {\n            callback(true);\n          }\n          return;\n        }\n        Collections.apply(runners, function (runner) {\n          runner.forceMinPriority(handshake.transport.priority);\n        });\n        callback(null, handshake);\n      };\n    });\n  }\n}\n\n/** Connects to all strategies in parallel.\n *\n * Callback builder should be a function that takes two arguments: index\n * and a list of runners. It should return another function that will be\n * passed to the substrategy with given index. Runners can be aborted using\n * abortRunner(s) functions from this class.\n *\n * @param  {Array} strategies\n * @param  {Function} callbackBuilder\n * @return {Object} strategy runner\n */\nfunction connect(\n  strategies: Strategy[],\n  minPriority: number,\n  callbackBuilder: Function,\n) {\n  var runners = Collections.map(strategies, function (strategy, i, _, rs) {\n    return strategy.connect(minPriority, callbackBuilder(i, rs));\n  });\n  return {\n    abort: function () {\n      Collections.apply(runners, abortRunner);\n    },\n    forceMinPriority: function (p) {\n      Collections.apply(runners, function (runner) {\n        runner.forceMinPriority(p);\n      });\n    },\n  };\n}\n\nfunction allRunnersFailed(runners): boolean {\n  return Collections.all(runners, function (runner) {\n    return Boolean(runner.error);\n  });\n}\n\nfunction abortRunner(runner) {\n  if (!runner.error && !runner.aborted) {\n    runner.abort();\n    runner.aborted = true;\n  }\n}\n", "import Util from '../util';\nimport Runtime from 'runtime';\nimport Strategy from './strategy';\nimport SequentialStrategy from './sequential_strategy';\nimport StrategyOptions from './strategy_options';\nimport TransportStrategy from './transport_strategy';\nimport Timeline from '../timeline/timeline';\nimport * as Collections from '../utils/collections';\n\nexport interface TransportStrategyDictionary {\n  [key: string]: TransportStrategy;\n}\n\n/** Caches the last successful transport and, after the first few attempts,\n *  uses the cached transport for subsequent attempts.\n *\n * @param {Strategy} strategy\n * @param {Object} transports\n * @param {Object} options\n */\nexport default class WebSocketPrioritizedCachedStrategy implements Strategy {\n  strategy: Strategy;\n  transports: TransportStrategyDictionary;\n  ttl: number;\n  usingTLS: boolean;\n  timeline: Timeline;\n\n  constructor(\n    strategy: Strategy,\n    transports: TransportStrategyDictionary,\n    options: StrategyOptions,\n  ) {\n    this.strategy = strategy;\n    this.transports = transports;\n    this.ttl = options.ttl || 1800 * 1000;\n    this.usingTLS = options.useTLS;\n    this.timeline = options.timeline;\n  }\n\n  isSupported(): boolean {\n    return this.strategy.isSupported();\n  }\n\n  connect(minPriority: number, callback: Function) {\n    var usingTLS = this.usingTLS;\n    var info = fetchTransportCache(usingTLS);\n    var cacheSkipCount = info && info.cacheSkipCount ? info.cacheSkipCount : 0;\n\n    var strategies = [this.strategy];\n    if (info && info.timestamp + this.ttl >= Util.now()) {\n      var transport = this.transports[info.transport];\n      if (transport) {\n        if (['ws', 'wss'].includes(info.transport) || cacheSkipCount > 3) {\n          this.timeline.info({\n            cached: true,\n            transport: info.transport,\n            latency: info.latency,\n          });\n          strategies.push(\n            new SequentialStrategy([transport], {\n              timeout: info.latency * 2 + 1000,\n              failFast: true,\n            }),\n          );\n        } else {\n          cacheSkipCount++;\n        }\n      }\n    }\n\n    var startTimestamp = Util.now();\n    var runner = strategies\n      .pop()\n      .connect(minPriority, function cb(error, handshake) {\n        if (error) {\n          flushTransportCache(usingTLS);\n          if (strategies.length > 0) {\n            startTimestamp = Util.now();\n            runner = strategies.pop().connect(minPriority, cb);\n          } else {\n            callback(error);\n          }\n        } else {\n          storeTransportCache(\n            usingTLS,\n            handshake.transport.name,\n            Util.now() - startTimestamp,\n            cacheSkipCount,\n          );\n          callback(null, handshake);\n        }\n      });\n\n    return {\n      abort: function () {\n        runner.abort();\n      },\n      forceMinPriority: function (p) {\n        minPriority = p;\n        if (runner) {\n          runner.forceMinPriority(p);\n        }\n      },\n    };\n  }\n}\n\nfunction getTransportCacheKey(usingTLS: boolean): string {\n  return 'pusherTransport' + (usingTLS ? 'TLS' : 'NonTLS');\n}\n\nfunction fetchTransportCache(usingTLS: boolean): any {\n  var storage = Runtime.getLocalStorage();\n  if (storage) {\n    try {\n      var serializedCache = storage[getTransportCacheKey(usingTLS)];\n      if (serializedCache) {\n        return JSON.parse(serializedCache);\n      }\n    } catch (e) {\n      flushTransportCache(usingTLS);\n    }\n  }\n  return null;\n}\n\nfunction storeTransportCache(\n  usingTLS: boolean,\n  transport: TransportStrategy,\n  latency: number,\n  cacheSkipCount: number,\n) {\n  var storage = Runtime.getLocalStorage();\n  if (storage) {\n    try {\n      storage[getTransportCacheKey(usingTLS)] = Collections.safeJSONStringify({\n        timestamp: Util.now(),\n        transport: transport,\n        latency: latency,\n        cacheSkipCount: cacheSkipCount,\n      });\n    } catch (e) {\n      // catch over quota exceptions raised by localStorage\n    }\n  }\n}\n\nfunction flushTransportCache(usingTLS: boolean) {\n  var storage = Runtime.getLocalStorage();\n  if (storage) {\n    try {\n      delete storage[getTransportCacheKey(usingTLS)];\n    } catch (e) {\n      // catch exceptions raised by localStorage\n    }\n  }\n}\n", "import { OneOffTimer as Timer } from '../utils/timers';\nimport Strategy from './strategy';\nimport StrategyOptions from './strategy_options';\n\n/** Runs substrategy after specified delay.\n *\n * Options:\n * - delay - time in miliseconds to delay the substrategy attempt\n *\n * @param {Strategy} strategy\n * @param {Object} options\n */\nexport default class DelayedStrategy implements Strategy {\n  strategy: Strategy;\n  options: { delay: number };\n\n  constructor(strategy: Strategy, { delay: number }) {\n    this.strategy = strategy;\n    this.options = { delay: number };\n  }\n\n  isSupported(): boolean {\n    return this.strategy.isSupported();\n  }\n\n  connect(minPriority: number, callback: Function) {\n    var strategy = this.strategy;\n    var runner;\n    var timer = new Timer(this.options.delay, function () {\n      runner = strategy.connect(minPriority, callback);\n    });\n\n    return {\n      abort: function () {\n        timer.ensureAborted();\n        if (runner) {\n          runner.abort();\n        }\n      },\n      forceMinPriority: function (p) {\n        minPriority = p;\n        if (runner) {\n          runner.forceMinPriority(p);\n        }\n      },\n    };\n  }\n}\n", "import Strategy from './strategy';\nimport StrategyRunner from './strategy_runner';\n\n/** Proxies method calls to one of substrategies basing on the test function.\n *\n * @param {Function} test\n * @param {Strategy} trueBranch strategy used when test returns true\n * @param {Strategy} falseBranch strategy used when test returns false\n */\nexport default class IfStrategy implements Strategy {\n  test: () => boolean;\n  trueBranch: Strategy;\n  falseBranch: Strategy;\n\n  constructor(\n    test: () => boolean,\n    trueBranch: Strategy,\n    falseBranch: Strategy,\n  ) {\n    this.test = test;\n    this.trueBranch = trueBranch;\n    this.falseBranch = falseBranch;\n  }\n\n  isSupported(): boolean {\n    var branch = this.test() ? this.trueBranch : this.falseBranch;\n    return branch.isSupported();\n  }\n\n  connect(minPriority: number, callback: Function): StrategyRunner {\n    var branch = this.test() ? this.trueBranch : this.falseBranch;\n    return branch.connect(minPriority, callback);\n  }\n}\n", "import Strategy from './strategy';\nimport StrategyRunner from './strategy_runner';\n\n/** Launches the substrategy and terminates on the first open connection.\n *\n * @param {Strategy} strategy\n */\nexport default class FirstConnectedStrategy implements Strategy {\n  strategy: Strategy;\n\n  constructor(strategy: Strategy) {\n    this.strategy = strategy;\n  }\n\n  isSupported(): boolean {\n    return this.strategy.isSupported();\n  }\n\n  connect(minPriority: number, callback: Function): StrategyRunner {\n    var runner = this.strategy.connect(\n      minPriority,\n      function (error, handshake) {\n        if (handshake) {\n          runner.abort();\n        }\n        callback(error, handshake);\n      },\n    );\n    return runner;\n  }\n}\n", "import * as Collections from 'core/utils/collections';\nimport TransportManager from 'core/transports/transport_manager';\nimport Strategy from 'core/strategies/strategy';\nimport SequentialStrategy from 'core/strategies/sequential_strategy';\nimport BestConnectedEverStrategy from 'core/strategies/best_connected_ever_strategy';\nimport WebSocketPrioritizedCachedStrategy, {\n  TransportStrategyDictionary,\n} from 'core/strategies/websocket_prioritized_cached_strategy';\nimport DelayedStrategy from 'core/strategies/delayed_strategy';\nimport IfStrategy from 'core/strategies/if_strategy';\nimport FirstConnectedStrategy from 'core/strategies/first_connected_strategy';\nimport { Config } from 'core/config';\nimport StrategyOptions from 'core/strategies/strategy_options';\n\nfunction testSupportsStrategy(strategy: Strategy) {\n  return function () {\n    return strategy.isSupported();\n  };\n}\n\nvar getDefaultStrategy = function (\n  config: Config,\n  baseOptions: StrategyOptions,\n  defineTransport: Function,\n): Strategy {\n  var definedTransports = <TransportStrategyDictionary>{};\n\n  function defineTransportStrategy(\n    name: string,\n    type: string,\n    priority: number,\n    options: StrategyOptions,\n    manager?: TransportManager,\n  ) {\n    var transport = defineTransport(\n      config,\n      name,\n      type,\n      priority,\n      options,\n      manager,\n    );\n\n    definedTransports[name] = transport;\n\n    return transport;\n  }\n\n  var ws_options: StrategyOptions = Object.assign({}, baseOptions, {\n    hostNonTLS: config.wsHost + ':' + config.wsPort,\n    hostTLS: config.wsHost + ':' + config.wssPort,\n    httpPath: config.wsPath,\n  });\n  var wss_options: StrategyOptions = Collections.extend({}, ws_options, {\n    useTLS: true,\n  });\n  var http_options: StrategyOptions = Object.assign({}, baseOptions, {\n    hostNonTLS: config.httpHost + ':' + config.httpPort,\n    hostTLS: config.httpHost + ':' + config.httpsPort,\n    httpPath: config.httpPath,\n  });\n  var timeouts = {\n    loop: true,\n    timeout: 15000,\n    timeoutLimit: 60000,\n  };\n\n  var ws_manager = new TransportManager({\n    minPingDelay: 10000,\n    maxPingDelay: config.activityTimeout,\n  });\n  var streaming_manager = new TransportManager({\n    lives: 2,\n    minPingDelay: 10000,\n    maxPingDelay: config.activityTimeout,\n  });\n\n  var ws_transport = defineTransportStrategy(\n    'ws',\n    'ws',\n    3,\n    ws_options,\n    ws_manager,\n  );\n  var wss_transport = defineTransportStrategy(\n    'wss',\n    'ws',\n    3,\n    wss_options,\n    ws_manager,\n  );\n  var xhr_streaming_transport = defineTransportStrategy(\n    'xhr_streaming',\n    'xhr_streaming',\n    1,\n    http_options,\n    streaming_manager,\n  );\n  var xhr_polling_transport = defineTransportStrategy(\n    'xhr_polling',\n    'xhr_polling',\n    1,\n    http_options,\n  );\n\n  var ws_loop = new SequentialStrategy([ws_transport], timeouts);\n  var wss_loop = new SequentialStrategy([wss_transport], timeouts);\n  var streaming_loop = new SequentialStrategy(\n    [xhr_streaming_transport],\n    timeouts,\n  );\n  var polling_loop = new SequentialStrategy([xhr_polling_transport], timeouts);\n\n  var http_loop = new SequentialStrategy(\n    [\n      new IfStrategy(\n        testSupportsStrategy(streaming_loop),\n        new BestConnectedEverStrategy([\n          streaming_loop,\n          new DelayedStrategy(polling_loop, { delay: 4000 }),\n        ]),\n        polling_loop,\n      ),\n    ],\n    timeouts,\n  );\n\n  var wsStrategy;\n  if (baseOptions.useTLS) {\n    wsStrategy = new BestConnectedEverStrategy([\n      ws_loop,\n      new DelayedStrategy(http_loop, { delay: 2000 }),\n    ]);\n  } else {\n    wsStrategy = new BestConnectedEverStrategy([\n      ws_loop,\n      new DelayedStrategy(wss_loop, { delay: 2000 }),\n      new DelayedStrategy(http_loop, { delay: 5000 }),\n    ]);\n  }\n\n  return new WebSocketPrioritizedCachedStrategy(\n    new FirstConnectedStrategy(\n      new IfStrategy(testSupportsStrategy(ws_transport), wsStrategy, http_loop),\n    ),\n    definedTransports,\n    {\n      ttl: 1800000,\n      timeline: baseOptions.timeline,\n      useTLS: baseOptions.useTLS,\n    },\n  );\n};\n\nexport default getDefaultStrategy;\n", "import Runtime from 'runtime';\nimport RequestHooks from './request_hooks';\nimport <PERSON> from './ajax';\nimport { default as EventsDispatcher } from '../events/dispatcher';\n\nconst MAX_BUFFER_LENGTH = 256 * 1024;\n\nexport default class HTTPRequest extends EventsDispatcher {\n  hooks: RequestHooks;\n  method: string;\n  url: string;\n  position: number;\n  xhr: Ajax;\n  unloader: Function;\n\n  constructor(hooks: RequestHooks, method: string, url: string) {\n    super();\n    this.hooks = hooks;\n    this.method = method;\n    this.url = url;\n  }\n\n  start(payload?: any) {\n    this.position = 0;\n    this.xhr = this.hooks.getRequest(this);\n\n    this.unloader = () => {\n      this.close();\n    };\n    Runtime.addUnloadListener(this.unloader);\n\n    this.xhr.open(this.method, this.url, true);\n\n    if (this.xhr.setRequestHeader) {\n      this.xhr.setRequestHeader('Content-Type', 'application/json'); // ReactNative doesn't set this header by default.\n    }\n    this.xhr.send(payload);\n  }\n\n  close() {\n    if (this.unloader) {\n      Runtime.removeUnloadListener(this.unloader);\n      this.unloader = null;\n    }\n    if (this.xhr) {\n      this.hooks.abortRequest(this.xhr);\n      this.xhr = null;\n    }\n  }\n\n  onChunk(status: number, data: any) {\n    while (true) {\n      var chunk = this.advanceBuffer(data);\n      if (chunk) {\n        this.emit('chunk', { status: status, data: chunk });\n      } else {\n        break;\n      }\n    }\n    if (this.isBufferTooLong(data)) {\n      this.emit('buffer_too_long');\n    }\n  }\n\n  private advanceBuffer(buffer: any[]): any {\n    var unreadData = buffer.slice(this.position);\n    var endOfLinePosition = unreadData.indexOf('\\n');\n\n    if (endOfLinePosition !== -1) {\n      this.position += endOfLinePosition + 1;\n      return unreadData.slice(0, endOfLinePosition);\n    } else {\n      // chunk is not finished yet, don't move the buffer pointer\n      return null;\n    }\n  }\n\n  private isBufferTooLong(buffer: any): boolean {\n    return this.position === buffer.length && buffer.length > MAX_BUFFER_LENGTH;\n  }\n}\n", "enum State {\n  CONNECTING = 0,\n  OPEN = 1,\n  CLOSED = 3,\n}\n\nexport default State;\n", "import URLLocation from './url_location';\nimport State from './state';\nimport Socket from '../socket';\nimport SocketHooks from './socket_hooks';\nimport Util from '../util';\nimport Ajax from './ajax';\nimport HTTPRequest from './http_request';\nimport Runtime from 'runtime';\n\nvar autoIncrement = 1;\n\nclass HTTPSocket implements Socket {\n  hooks: SocketHooks;\n  session: string;\n  location: URLLocation;\n  readyState: State;\n  stream: HTTPRequest;\n\n  onopen: () => void;\n  onerror: (error: any) => void;\n  onclose: (closeEvent: any) => void;\n  onmessage: (message: any) => void;\n  onactivity: () => void;\n\n  constructor(hooks: SocketHooks, url: string) {\n    this.hooks = hooks;\n    this.session = randomNumber(1000) + '/' + randomString(8);\n    this.location = getLocation(url);\n    this.readyState = State.CONNECTING;\n    this.openStream();\n  }\n\n  send(payload: any) {\n    return this.sendRaw(JSON.stringify([payload]));\n  }\n\n  ping() {\n    this.hooks.sendHeartbeat(this);\n  }\n\n  close(code: any, reason: any) {\n    this.onClose(code, reason, true);\n  }\n\n  /** For internal use only */\n  sendRaw(payload: any): boolean {\n    if (this.readyState === State.OPEN) {\n      try {\n        Runtime.createSocketRequest(\n          'POST',\n          getUniqueURL(getSendURL(this.location, this.session)),\n        ).start(payload);\n        return true;\n      } catch (e) {\n        return false;\n      }\n    } else {\n      return false;\n    }\n  }\n\n  /** For internal use only */\n  reconnect() {\n    this.closeStream();\n    this.openStream();\n  }\n\n  /** For internal use only */\n  onClose(code, reason, wasClean) {\n    this.closeStream();\n    this.readyState = State.CLOSED;\n    if (this.onclose) {\n      this.onclose({\n        code: code,\n        reason: reason,\n        wasClean: wasClean,\n      });\n    }\n  }\n\n  private onChunk(chunk) {\n    if (chunk.status !== 200) {\n      return;\n    }\n    if (this.readyState === State.OPEN) {\n      this.onActivity();\n    }\n\n    var payload;\n    var type = chunk.data.slice(0, 1);\n    switch (type) {\n      case 'o':\n        payload = JSON.parse(chunk.data.slice(1) || '{}');\n        this.onOpen(payload);\n        break;\n      case 'a':\n        payload = JSON.parse(chunk.data.slice(1) || '[]');\n        for (var i = 0; i < payload.length; i++) {\n          this.onEvent(payload[i]);\n        }\n        break;\n      case 'm':\n        payload = JSON.parse(chunk.data.slice(1) || 'null');\n        this.onEvent(payload);\n        break;\n      case 'h':\n        this.hooks.onHeartbeat(this);\n        break;\n      case 'c':\n        payload = JSON.parse(chunk.data.slice(1) || '[]');\n        this.onClose(payload[0], payload[1], true);\n        break;\n    }\n  }\n\n  private onOpen(options) {\n    if (this.readyState === State.CONNECTING) {\n      if (options && options.hostname) {\n        this.location.base = replaceHost(this.location.base, options.hostname);\n      }\n      this.readyState = State.OPEN;\n\n      if (this.onopen) {\n        this.onopen();\n      }\n    } else {\n      this.onClose(1006, 'Server lost session', true);\n    }\n  }\n\n  private onEvent(event) {\n    if (this.readyState === State.OPEN && this.onmessage) {\n      this.onmessage({ data: event });\n    }\n  }\n\n  private onActivity() {\n    if (this.onactivity) {\n      this.onactivity();\n    }\n  }\n\n  private onError(error) {\n    if (this.onerror) {\n      this.onerror(error);\n    }\n  }\n\n  private openStream() {\n    this.stream = Runtime.createSocketRequest(\n      'POST',\n      getUniqueURL(this.hooks.getReceiveURL(this.location, this.session)),\n    );\n\n    this.stream.bind('chunk', (chunk) => {\n      this.onChunk(chunk);\n    });\n    this.stream.bind('finished', (status) => {\n      this.hooks.onFinished(this, status);\n    });\n    this.stream.bind('buffer_too_long', () => {\n      this.reconnect();\n    });\n\n    try {\n      this.stream.start();\n    } catch (error) {\n      Util.defer(() => {\n        this.onError(error);\n        this.onClose(1006, 'Could not start streaming', false);\n      });\n    }\n  }\n\n  private closeStream() {\n    if (this.stream) {\n      this.stream.unbind_all();\n      this.stream.close();\n      this.stream = null;\n    }\n  }\n}\n\nfunction getLocation(url): URLLocation {\n  var parts = /([^\\?]*)\\/*(\\??.*)/.exec(url);\n  return {\n    base: parts[1],\n    queryString: parts[2],\n  };\n}\n\nfunction getSendURL(url: URLLocation, session: string): string {\n  return url.base + '/' + session + '/xhr_send';\n}\n\nfunction getUniqueURL(url: string): string {\n  var separator = url.indexOf('?') === -1 ? '?' : '&';\n  return url + separator + 't=' + +new Date() + '&n=' + autoIncrement++;\n}\n\nfunction replaceHost(url: string, hostname: string): string {\n  var urlParts = /(https?:\\/\\/)([^\\/:]+)((\\/|:)?.*)/.exec(url);\n  return urlParts[1] + hostname + urlParts[3];\n}\n\nfunction randomNumber(max: number): number {\n  return Runtime.randomInt(max);\n}\n\nfunction randomString(length: number): string {\n  var result = [];\n\n  for (var i = 0; i < length; i++) {\n    result.push(randomNumber(32).toString(32));\n  }\n\n  return result.join('');\n}\n\nexport default HTTPSocket;\n", "import SocketHooks from './socket_hooks';\nimport HTTPSocket from './http_socket';\n\nvar hooks: SocketHooks = {\n  getReceiveURL: function (url, session) {\n    return url.base + '/' + session + '/xhr_streaming' + url.queryString;\n  },\n  onHeartbeat: function (socket) {\n    socket.sendRaw('[]');\n  },\n  sendHeartbeat: function (socket) {\n    socket.sendRaw('[]');\n  },\n  onFinished: function (socket, status) {\n    socket.onClose(1006, 'Connection interrupted (' + status + ')', false);\n  },\n};\n\nexport default hooks;\n", "import SocketHooks from './socket_hooks';\nimport URLLocation from './url_location';\nimport HTTPSocket from './http_socket';\n\nvar hooks: SocketHooks = {\n  getReceiveURL: function (url: URLLocation, session: string): string {\n    return url.base + '/' + session + '/xhr' + url.queryString;\n  },\n  onHeartbeat: function () {\n    // next HTTP request will reset server's activity timer\n  },\n  sendHeartbeat: function (socket) {\n    socket.sendRaw('[]');\n  },\n  onFinished: function (socket, status) {\n    if (status === 200) {\n      socket.reconnect();\n    } else {\n      socket.onClose(1006, 'Connection interrupted (' + status + ')', false);\n    }\n  },\n};\n\nexport default hooks;\n", "import HTTPRequest from 'core/http/http_request';\nimport <PERSON>questHooks from 'core/http/request_hooks';\nimport Ajax from 'core/http/ajax';\nimport Runtime from 'runtime';\n\nvar hooks: RequestHooks = {\n  getRequest: function (socket: HTTPRequest): Ajax {\n    var Constructor = Runtime.getXHRAPI();\n    var xhr = new Constructor();\n    xhr.onreadystatechange = xhr.onprogress = function () {\n      switch (xhr.readyState) {\n        case 3:\n          if (xhr.responseText && xhr.responseText.length > 0) {\n            socket.onChunk(xhr.status, xhr.responseText);\n          }\n          break;\n        case 4:\n          // this happens only on errors, never after calling close\n          if (xhr.responseText && xhr.responseText.length > 0) {\n            socket.onChunk(xhr.status, xhr.responseText);\n          }\n          socket.emit('finished', xhr.status);\n          socket.close();\n          break;\n      }\n    };\n    return xhr;\n  },\n  abortRequest: function (xhr: Ajax) {\n    xhr.onreadystatechange = null;\n    xhr.abort();\n  },\n};\n\nexport default hooks;\n", "import * as Collections from 'core/utils/collections';\nimport Transports from 'isomorphic/transports/transports';\nimport TimelineSender from 'core/timeline/timeline_sender';\nimport Ajax from 'core/http/ajax';\nimport getDefaultStrategy from './default_strategy';\nimport TransportsTable from 'core/transports/transports_table';\nimport transportConnectionInitializer from './transports/transport_connection_initializer';\nimport HTTPFactory from './http/http';\n\nvar Isomorphic: any = {\n  getDefaultStrategy,\n  Transports: <TransportsTable>Transports,\n  transportConnectionInitializer,\n  HTTPFactory,\n\n  setup(PusherClass): void {\n    PusherClass.ready();\n  },\n\n  getLocalStorage(): any {\n    return undefined;\n  },\n\n  getClientFeatures(): any[] {\n    return Collections.keys(\n      Collections.filterObject({ ws: Transports.ws }, function (t) {\n        return t.isSupported({});\n      }),\n    );\n  },\n\n  getProtocol(): string {\n    return 'http:';\n  },\n\n  isXHRSupported(): boolean {\n    return true;\n  },\n\n  createSocketRequest(method: string, url: string) {\n    if (this.isXHRSupported()) {\n      return this.HTTPFactory.createXHR(method, url);\n    } else {\n      throw 'Cross-origin HTTP requests are not supported';\n    }\n  },\n\n  createXHR(): Ajax {\n    var Constructor = this.getXHRAPI();\n    return new Constructor();\n  },\n\n  createWebSocket(url: string): any {\n    var Constructor = this.getWebSocketAPI();\n    return new Constructor(url);\n  },\n\n  addUnloadListener(listener: any) {},\n  removeUnloadListener(listener: any) {},\n};\n\nexport default Isomorphic;\n", "/** Initializes the transport.\n *\n * Fetches resources if needed and then transitions to initialized.\n */\nexport default function () {\n  var self = this;\n\n  self.timeline.info(\n    self.buildTimelineMessage({\n      transport: self.name + (self.options.useTLS ? 's' : ''),\n    }),\n  );\n\n  if (self.hooks.isInitialized()) {\n    self.changeState('initialized');\n  } else {\n    self.onClose();\n  }\n}\n", "import HTTPRequest from 'core/http/http_request';\nimport HTTPSocket from 'core/http/http_socket';\nimport SocketHooks from 'core/http/socket_hooks';\nimport RequestHooks from 'core/http/request_hooks';\nimport streamingHooks from 'core/http/http_streaming_socket';\nimport pollingHooks from 'core/http/http_polling_socket';\nimport xhrHooks from './http_xhr_request';\nimport HTTPFactory from 'core/http/http_factory';\n\nvar HTTP: HTTPFactory = {\n  createStreamingSocket(url: string): HTTPSocket {\n    return this.createSocket(streamingHooks, url);\n  },\n\n  createPollingSocket(url: string): HTTPSocket {\n    return this.createSocket(pollingHooks, url);\n  },\n\n  createSocket(hooks: SocketHooks, url: string): HTTPSocket {\n    return new HTTPSocket(hooks, url);\n  },\n\n  createXHR(method: string, url: string): HTTPRequest {\n    return this.createRequest(xhrHooks, method, url);\n  },\n\n  createRequest(hooks: RequestHooks, method: string, url: string): HTTPRequest {\n    return new HTTPRequest(hooks, method, url);\n  },\n};\n\nexport default HTTP;\n", "import { default as EventsDispatcher } from 'core/events/dispatcher';\nimport Reachability from 'core/reachability';\n\nexport class NetInfo extends EventsDispatcher implements Reachability {\n  isOnline(): boolean {\n    return true;\n  }\n}\n\nexport var Network = new NetInfo();\n", "import AbstractRuntime from 'runtimes/interface';\nimport { AuthTransport } from 'core/auth/auth_transports';\nimport {\n  AuthRequestType,\n  AuthTransportCallback,\n  InternalAuthOptions,\n} from 'core/auth/options';\nimport { HTTPAuthError } from 'core/errors';\n\nvar fetchAuth: AuthTransport = function (\n  context: AbstractRuntime,\n  query: string,\n  authOptions: InternalAuthOptions,\n  authRequestType: AuthRequestType,\n  callback: AuthTransportCallback,\n) {\n  var headers = new Headers();\n  headers.set('Content-Type', 'application/x-www-form-urlencoded');\n\n  for (var headerName in authOptions.headers) {\n    headers.set(headerName, authOptions.headers[headerName]);\n  }\n\n  if (authOptions.headersProvider != null) {\n    const dynamicHeaders = authOptions.headersProvider();\n    for (var headerName in dynamicHeaders) {\n      headers.set(headerName, dynamicHeaders[headerName]);\n    }\n  }\n\n  var body = query;\n  var request = new Request(authOptions.endpoint, {\n    headers,\n    body,\n    credentials: 'same-origin',\n    method: 'POST',\n  });\n\n  return fetch(request)\n    .then((response) => {\n      let { status } = response;\n      if (status === 200) {\n        // manually parse the json so we can provide a more helpful error in\n        // failure case\n        return response.text();\n      }\n      throw new HTTPAuthError(\n        status,\n        `Could not get ${authRequestType.toString()} info from your auth endpoint, status: ${status}`,\n      );\n    })\n    .then((data) => {\n      let parsedData;\n      try {\n        parsedData = JSON.parse(data);\n      } catch (e) {\n        throw new HTTPAuthError(\n          200,\n          `JSON returned from ${authRequestType.toString()} endpoint was invalid, yet status code was 200. Data was: ${data}`,\n        );\n      }\n      callback(null, parsedData);\n    })\n    .catch((err) => {\n      callback(err, null);\n    });\n};\n\nexport default fetchAuth;\n", "import Logger from 'core/logger';\nimport TimelineSender from 'core/timeline/timeline_sender';\nimport * as Collections from 'core/utils/collections';\nimport Util from 'core/util';\nimport Runtime from 'runtime';\nimport TimelineTransport from 'core/timeline/timeline_transport';\n\nvar getAgent = function (sender: TimelineSender, useTLS: boolean) {\n  return function (data: any, callback: Function) {\n    var scheme = 'http' + (useTLS ? 's' : '') + '://';\n    var url =\n      scheme + (sender.host || sender.options.host) + sender.options.path;\n    var query = Collections.buildQueryString(data);\n    url += '/' + 2 + '?' + query;\n\n    fetch(url)\n      .then((response) => {\n        if (response.status !== 200) {\n          throw `received ${response.status} from stats.pusher.com`;\n        }\n        return response.json();\n      })\n      .then(({ host }) => {\n        if (host) {\n          sender.host = host;\n        }\n      })\n      .catch((err) => {\n        Logger.debug('TimelineSender Error: ', err);\n      });\n  };\n};\n\nvar fetchTimeline = {\n  name: 'xhr',\n  getAgent,\n};\n\nexport default fetchTimeline;\n", "import Isomorphic from 'isomorphic/runtime';\nimport Runtime from '../interface';\nimport { Network } from './net_info';\nimport fetchAuth from './auth/fetch_auth';\nimport { AuthTransports } from 'core/auth/auth_transports';\nimport fetchTimeline from './timeline/fetch_timeline';\n\n// Very verbose but until unavoidable until\n// TypeScript 2.1, when spread attributes will be\n// supported\nconst {\n  getDefaultStrategy,\n  Transports,\n  setup,\n  getProtocol,\n  isXHRSupported,\n  getLocalStorage,\n  createXHR,\n  createWebSocket,\n  addUnloadListener,\n  removeUnloadListener,\n  transportConnectionInitializer,\n  createSocketRequest,\n  HTTPFactory,\n} = Isomorphic;\n\nconst Worker: Runtime = {\n  getDefaultStrategy,\n  Transports,\n  setup,\n  getProtocol,\n  isXHRSupported,\n  getLocalStorage,\n  createXHR,\n  createWebSocket,\n  addUnloadListener,\n  removeUnloadListener,\n  transportConnectionInitializer,\n  createSocketRequest,\n  HTTPFactory,\n\n  TimelineTransport: fetchTimeline,\n\n  getAuthorizers(): AuthTransports {\n    return { ajax: fetchAuth };\n  },\n\n  getWebSocketAPI() {\n    return WebSocket;\n  },\n\n  getXHRAPI() {\n    return XMLHttpRequest;\n  },\n\n  getNetwork() {\n    return Network;\n  },\n\n  randomInt(max: number): number {\n    /**\n     * Return values in the range of [0, 1[\n     */\n    const random = function () {\n      const crypto = globalThis.crypto || globalThis['msCrypto'];\n      const random = crypto.getRandomValues(new Uint32Array(1))[0];\n\n      return random / 2 ** 32;\n    };\n\n    return Math.floor(random() * max);\n  },\n};\n\nexport default Worker;\n", "enum TimelineLevel {\n  ERROR = 3,\n  INFO = 6,\n  DEBUG = 7,\n}\n\nexport default TimelineLevel;\n", "import * as Collections from '../utils/collections';\nimport Util from '../util';\nimport { default as Level } from './level';\n\nexport interface TimelineOptions {\n  level?: Level;\n  limit?: number;\n  version?: string;\n  cluster?: string;\n  features?: string[];\n  params?: any;\n}\n\nexport default class Timeline {\n  key: string;\n  session: number;\n  events: any[];\n  options: TimelineOptions;\n  sent: number;\n  uniqueID: number;\n\n  constructor(key: string, session: number, options: TimelineOptions) {\n    this.key = key;\n    this.session = session;\n    this.events = [];\n    this.options = options || {};\n    this.sent = 0;\n    this.uniqueID = 0;\n  }\n\n  log(level, event) {\n    if (level <= this.options.level) {\n      this.events.push(\n        Collections.extend({}, event, { timestamp: Util.now() }),\n      );\n      if (this.options.limit && this.events.length > this.options.limit) {\n        this.events.shift();\n      }\n    }\n  }\n\n  error(event) {\n    this.log(Level.ERROR, event);\n  }\n\n  info(event) {\n    this.log(Level.INFO, event);\n  }\n\n  debug(event) {\n    this.log(Level.DEBUG, event);\n  }\n\n  isEmpty() {\n    return this.events.length === 0;\n  }\n\n  send(sendfn, callback) {\n    var data = Collections.extend(\n      {\n        session: this.session,\n        bundle: this.sent + 1,\n        key: this.key,\n        lib: 'js',\n        version: this.options.version,\n        cluster: this.options.cluster,\n        features: this.options.features,\n        timeline: this.events,\n      },\n      this.options.params,\n    );\n\n    this.events = [];\n    sendfn(data, (error, result) => {\n      if (!error) {\n        this.sent++;\n      }\n      if (callback) {\n        callback(error, result);\n      }\n    });\n\n    return true;\n  }\n\n  generateUniqueID(): number {\n    this.uniqueID++;\n    return this.uniqueID;\n  }\n}\n", "import Factory from '../utils/factory';\nimport Util from '../util';\nimport * as Errors from '../errors';\nimport * as Collections from '../utils/collections';\nimport Strategy from './strategy';\nimport Transport from '../transports/transport';\nimport StrategyOptions from './strategy_options';\nimport Handshake from '../connection/handshake';\n\n/** Provides a strategy interface for transports.\n *\n * @param {String} name\n * @param {Number} priority\n * @param {Class} transport\n * @param {Object} options\n */\nexport default class TransportStrategy implements Strategy {\n  name: string;\n  priority: number;\n  transport: Transport;\n  options: StrategyOptions;\n\n  constructor(\n    name: string,\n    priority: number,\n    transport: Transport,\n    options: StrategyOptions,\n  ) {\n    this.name = name;\n    this.priority = priority;\n    this.transport = transport;\n    this.options = options || {};\n  }\n\n  /** Returns whether the transport is supported in the browser.\n   *\n   * @returns {Boolean}\n   */\n  isSupported(): boolean {\n    return this.transport.isSupported({\n      useTLS: this.options.useTLS,\n    });\n  }\n\n  /** Launches a connection attempt and returns a strategy runner.\n   *\n   * @param  {Function} callback\n   * @return {Object} strategy runner\n   */\n  connect(minPriority: number, callback: Function) {\n    if (!this.isSupported()) {\n      return failAttempt(new Errors.UnsupportedStrategy(), callback);\n    } else if (this.priority < minPriority) {\n      return failAttempt(new Errors.TransportPriorityTooLow(), callback);\n    }\n\n    var connected = false;\n    var transport = this.transport.createConnection(\n      this.name,\n      this.priority,\n      this.options.key,\n      this.options,\n    );\n    var handshake = null;\n\n    var onInitialized = function () {\n      transport.unbind('initialized', onInitialized);\n      transport.connect();\n    };\n    var onOpen = function () {\n      handshake = Factory.createHandshake(transport, function (result) {\n        connected = true;\n        unbindListeners();\n        callback(null, result);\n      });\n    };\n    var onError = function (error) {\n      unbindListeners();\n      callback(error);\n    };\n    var onClosed = function () {\n      unbindListeners();\n      var serializedTransport;\n\n      // The reason for this try/catch block is that on React Native\n      // the WebSocket object is circular. Therefore transport.socket will\n      // throw errors upon stringification. Collections.safeJSONStringify\n      // discards circular references when serializing.\n      serializedTransport = Collections.safeJSONStringify(transport);\n      callback(new Errors.TransportClosed(serializedTransport));\n    };\n\n    var unbindListeners = function () {\n      transport.unbind('initialized', onInitialized);\n      transport.unbind('open', onOpen);\n      transport.unbind('error', onError);\n      transport.unbind('closed', onClosed);\n    };\n\n    transport.bind('initialized', onInitialized);\n    transport.bind('open', onOpen);\n    transport.bind('error', onError);\n    transport.bind('closed', onClosed);\n\n    // connect will be called automatically after initialization\n    transport.initialize();\n\n    return {\n      abort: () => {\n        if (connected) {\n          return;\n        }\n        unbindListeners();\n        if (handshake) {\n          handshake.close();\n        } else {\n          transport.close();\n        }\n      },\n      forceMinPriority: (p) => {\n        if (connected) {\n          return;\n        }\n        if (this.priority < p) {\n          if (handshake) {\n            handshake.close();\n          } else {\n            transport.close();\n          }\n        }\n      },\n    };\n  }\n}\n\nfunction failAttempt(error: Error, callback: Function) {\n  Util.defer(function () {\n    callback(error);\n  });\n  return {\n    abort: function () {},\n    forceMinPriority: function () {},\n  };\n}\n", "import * as Collections from '../utils/collections';\nimport Util from '../util';\nimport TransportManager from '../transports/transport_manager';\nimport * as Errors from '../errors';\nimport Strategy from './strategy';\nimport TransportStrategy from './transport_strategy';\nimport StrategyOptions from '../strategies/strategy_options';\nimport { Config } from '../config';\nimport Runtime from 'runtime';\n\nconst { Transports } = Runtime;\n\nexport var defineTransport = function (\n  config: Config,\n  name: string,\n  type: string,\n  priority: number,\n  options: StrategyOptions,\n  manager?: TransportManager,\n): Strategy {\n  var transportClass = Transports[type];\n  if (!transportClass) {\n    throw new Errors.UnsupportedTransport(type);\n  }\n\n  var enabled =\n    (!config.enabledTransports ||\n      Collections.arrayIndexOf(config.enabledTransports, name) !== -1) &&\n    (!config.disabledTransports ||\n      Collections.arrayIndexOf(config.disabledTransports, name) === -1);\n\n  var transport;\n  if (enabled) {\n    options = Object.assign(\n      { ignoreNullOrigin: config.ignoreNullOrigin },\n      options,\n    );\n\n    transport = new TransportStrategy(\n      name,\n      priority,\n      manager ? manager.getAssistant(transportClass) : transportClass,\n      options,\n    );\n  } else {\n    transport = UnsupportedStrategy;\n  }\n\n  return transport;\n};\n\nvar UnsupportedStrategy: Strategy = {\n  isSupported: function () {\n    return false;\n  },\n  connect: function (_, callback) {\n    var deferred = Util.defer(function () {\n      callback(new Errors.UnsupportedStrategy());\n    });\n    return {\n      abort: function () {\n        deferred.ensureAborted();\n      },\n      forceMinPriority: function () {},\n    };\n  },\n};\n", "export enum AuthRequestType {\n  UserAuthentication = 'user-authentication',\n  ChannelAuthorization = 'channel-authorization',\n}\n\nexport interface ChannelAuthorizationData {\n  auth: string;\n  channel_data?: string;\n  shared_secret?: string;\n}\n\nexport type ChannelAuthorizationCallback = (\n  error: Error | null,\n  authData: ChannelAuthorizationData | null,\n) => void;\n\nexport interface ChannelAuthorizationRequestParams {\n  socketId: string;\n  channelName: string;\n}\n\nexport interface ChannelAuthorizationHandler {\n  (\n    params: ChannelAuthorizationRequestParams,\n    callback: ChannelAuthorizationCallback,\n  ): void;\n}\n\nexport interface UserAuthenticationData {\n  auth: string;\n  user_data: string;\n}\n\nexport type UserAuthenticationCallback = (\n  error: Error | null,\n  authData: UserAuthenticationData | null,\n) => void;\n\nexport interface UserAuthenticationRequestParams {\n  socketId: string;\n}\n\nexport interface UserAuthenticationHandler {\n  (\n    params: UserAuthenticationRequestParams,\n    callback: UserAuthenticationCallback,\n  ): void;\n}\n\nexport type AuthTransportCallback =\n  | ChannelAuthorizationCallback\n  | UserAuthenticationCallback;\n\nexport interface AuthOptionsT<AuthHandler> {\n  transport: 'ajax' | 'jsonp';\n  endpoint: string;\n  params?: any;\n  headers?: any;\n  paramsProvider?: () => any;\n  headersProvider?: () => any;\n  customHandler?: AuthHandler;\n}\n\nexport declare type UserAuthenticationOptions =\n  AuthOptionsT<UserAuthenticationHandler>;\nexport declare type ChannelAuthorizationOptions =\n  AuthOptionsT<ChannelAuthorizationHandler>;\n\nexport interface InternalAuthOptions {\n  transport: 'ajax' | 'jsonp';\n  endpoint: string;\n  params?: any;\n  headers?: any;\n  paramsProvider?: () => any;\n  headersProvider?: () => any;\n}\n", "import ConnectionManager from './connection/connection_manager';\nimport {\n  ChannelAuthorizationOptions,\n  UserAuthenticationOptions,\n} from './auth/options';\nimport {\n  ChannelAuthorizerGenerator,\n  DeprecatedAuthOptions,\n} from './auth/deprecated_channel_authorizer';\nimport { AuthTransport, Transport } from './config';\nimport * as nacl from 'tweetnacl';\nimport Logger from './logger';\n\nexport interface Options {\n  activityTimeout?: number;\n\n  auth?: DeprecatedAuthOptions; // DEPRECATED use channelAuthorization instead\n  authEndpoint?: string; // DEPRECATED use channelAuthorization instead\n  authTransport?: AuthTransport; // DEPRECATED use channelAuthorization instead\n  authorizer?: ChannelAuthorizerGenerator; // DEPRECATED use channelAuthorization instead\n\n  channelAuthorization?: ChannelAuthorizationOptions;\n  userAuthentication?: UserAuthenticationOptions;\n\n  cluster: string;\n  enableStats?: boolean;\n  disableStats?: boolean;\n  disabledTransports?: Transport[];\n  enabledTransports?: Transport[];\n  forceTLS?: boolean;\n  httpHost?: string;\n  httpPath?: string;\n  httpPort?: number;\n  httpsPort?: number;\n  ignoreNullOrigin?: boolean;\n  nacl?: nacl;\n  pongTimeout?: number;\n  statsHost?: string;\n  timelineParams?: any;\n  unavailableTimeout?: number;\n  wsHost?: string;\n  wsPath?: string;\n  wsPort?: number;\n  wssPort?: number;\n}\n\nexport function validateOptions(options) {\n  if (options == null) {\n    throw 'You must pass an options object';\n  }\n  if (options.cluster == null) {\n    throw 'Options object must provide a cluster';\n  }\n  if ('disableStats' in options) {\n    Logger.warn(\n      'The disableStats option is deprecated in favor of enableStats',\n    );\n  }\n}\n", "import {\n  UserAuthenticationCallback,\n  InternalAuthOptions,\n  UserAuthenticationHandler,\n  UserAuthenticationRequestParams,\n  AuthRequestType,\n} from './options';\n\nimport Runtime from 'runtime';\n\nconst composeChannelQuery = (\n  params: UserAuthenticationRequestParams,\n  authOptions: InternalAuthOptions,\n) => {\n  var query = 'socket_id=' + encodeURIComponent(params.socketId);\n\n  for (var key in authOptions.params) {\n    query +=\n      '&' +\n      encodeURIComponent(key) +\n      '=' +\n      encodeURIComponent(authOptions.params[key]);\n  }\n\n  if (authOptions.paramsProvider != null) {\n    let dynamicParams = authOptions.paramsProvider();\n    for (var key in dynamicParams) {\n      query +=\n        '&' +\n        encodeURIComponent(key) +\n        '=' +\n        encodeURIComponent(dynamicParams[key]);\n    }\n  }\n\n  return query;\n};\n\nconst UserAuthenticator = (\n  authOptions: InternalAuthOptions,\n): UserAuthenticationHandler => {\n  if (typeof Runtime.getAuthorizers()[authOptions.transport] === 'undefined') {\n    throw `'${authOptions.transport}' is not a recognized auth transport`;\n  }\n\n  return (\n    params: UserAuthenticationRequestParams,\n    callback: UserAuthenticationCallback,\n  ) => {\n    const query = composeChannelQuery(params, authOptions);\n\n    Runtime.getAuthorizers()[authOptions.transport](\n      Runtime,\n      query,\n      authOptions,\n      AuthRequestType.UserAuthentication,\n      callback,\n    );\n  };\n};\n\nexport default UserAuthenticator;\n", "import {\n  AuthRequestType,\n  InternalAuthOptions,\n  ChannelAuthorizationHandler,\n  ChannelAuthorizationRequestParams,\n  ChannelAuthorizationCallback,\n} from './options';\n\nimport Runtime from 'runtime';\n\nconst composeChannelQuery = (\n  params: ChannelAuthorizationRequestParams,\n  authOptions: InternalAuthOptions,\n) => {\n  var query = 'socket_id=' + encodeURIComponent(params.socketId);\n\n  query += '&channel_name=' + encodeURIComponent(params.channelName);\n\n  for (var key in authOptions.params) {\n    query +=\n      '&' +\n      encodeURIComponent(key) +\n      '=' +\n      encodeURIComponent(authOptions.params[key]);\n  }\n\n  if (authOptions.paramsProvider != null) {\n    let dynamicParams = authOptions.paramsProvider();\n    for (var key in dynamicParams) {\n      query +=\n        '&' +\n        encodeURIComponent(key) +\n        '=' +\n        encodeURIComponent(dynamicParams[key]);\n    }\n  }\n\n  return query;\n};\n\nconst ChannelAuthorizer = (\n  authOptions: InternalAuthOptions,\n): ChannelAuthorizationHandler => {\n  if (typeof Runtime.getAuthorizers()[authOptions.transport] === 'undefined') {\n    throw `'${authOptions.transport}' is not a recognized auth transport`;\n  }\n\n  return (\n    params: ChannelAuthorizationRequestParams,\n    callback: ChannelAuthorizationCallback,\n  ) => {\n    const query = composeChannelQuery(params, authOptions);\n\n    Runtime.getAuthorizers()[authOptions.transport](\n      Runtime,\n      query,\n      authOptions,\n      AuthRequestType.ChannelAuthorization,\n      callback,\n    );\n  };\n};\n\nexport default ChannelAuthorizer;\n", "import { Options } from './options';\nimport Defaults from './defaults';\nimport {\n  Channel<PERSON>uth<PERSON>zation<PERSON><PERSON><PERSON>,\n  UserAuthenticationHandler,\n  ChannelAuthorizationOptions,\n} from './auth/options';\nimport UserAuthenticator from './auth/user_authenticator';\nimport ChannelAuthorizer from './auth/channel_authorizer';\nimport { ChannelAuthorizerProxy } from './auth/deprecated_channel_authorizer';\nimport Runtime from 'runtime';\nimport * as nacl from 'tweetnacl';\n\nexport type AuthTransport = 'ajax' | 'jsonp';\nexport type Transport =\n  | 'ws'\n  | 'wss'\n  | 'xhr_streaming'\n  | 'xhr_polling'\n  | 'sockjs';\n\nexport interface Config {\n  // these are all 'required' config parameters, it's not necessary for the user\n  // to set them, but they have configured defaults.\n  activityTimeout: number;\n  enableStats: boolean;\n  httpHost: string;\n  httpPath: string;\n  httpPort: number;\n  httpsPort: number;\n  pongTimeout: number;\n  statsHost: string;\n  unavailableTimeout: number;\n  useTLS: boolean;\n  wsHost: string;\n  wsPath: string;\n  wsPort: number;\n  wssPort: number;\n  userAuthenticator: UserAuthenticationHandler;\n  channelAuthorizer: ChannelAuthorizationHandler;\n\n  // these are all optional parameters or overrrides. The customer can set these\n  // but it's not strictly necessary\n  forceTLS?: boolean;\n  cluster?: string;\n  disabledTransports?: Transport[];\n  enabledTransports?: Transport[];\n  ignoreNullOrigin?: boolean;\n  nacl?: nacl;\n  timelineParams?: any;\n}\n\n// getConfig mainly sets the defaults for the options that are not provided\nexport function getConfig(opts: Options, pusher): Config {\n  let config: Config = {\n    activityTimeout: opts.activityTimeout || Defaults.activityTimeout,\n    cluster: opts.cluster,\n    httpPath: opts.httpPath || Defaults.httpPath,\n    httpPort: opts.httpPort || Defaults.httpPort,\n    httpsPort: opts.httpsPort || Defaults.httpsPort,\n    pongTimeout: opts.pongTimeout || Defaults.pongTimeout,\n    statsHost: opts.statsHost || Defaults.stats_host,\n    unavailableTimeout: opts.unavailableTimeout || Defaults.unavailableTimeout,\n    wsPath: opts.wsPath || Defaults.wsPath,\n    wsPort: opts.wsPort || Defaults.wsPort,\n    wssPort: opts.wssPort || Defaults.wssPort,\n\n    enableStats: getEnableStatsConfig(opts),\n    httpHost: getHttpHost(opts),\n    useTLS: shouldUseTLS(opts),\n    wsHost: getWebsocketHost(opts),\n\n    userAuthenticator: buildUserAuthenticator(opts),\n    channelAuthorizer: buildChannelAuthorizer(opts, pusher),\n  };\n\n  if ('disabledTransports' in opts)\n    config.disabledTransports = opts.disabledTransports;\n  if ('enabledTransports' in opts)\n    config.enabledTransports = opts.enabledTransports;\n  if ('ignoreNullOrigin' in opts)\n    config.ignoreNullOrigin = opts.ignoreNullOrigin;\n  if ('timelineParams' in opts) config.timelineParams = opts.timelineParams;\n  if ('nacl' in opts) {\n    config.nacl = opts.nacl;\n  }\n\n  return config;\n}\n\nfunction getHttpHost(opts: Options): string {\n  if (opts.httpHost) {\n    return opts.httpHost;\n  }\n  if (opts.cluster) {\n    return `sockjs-${opts.cluster}.pusher.com`;\n  }\n  return Defaults.httpHost;\n}\n\nfunction getWebsocketHost(opts: Options): string {\n  if (opts.wsHost) {\n    return opts.wsHost;\n  }\n  return getWebsocketHostFromCluster(opts.cluster);\n}\n\nfunction getWebsocketHostFromCluster(cluster: string): string {\n  return `ws-${cluster}.pusher.com`;\n}\n\nfunction shouldUseTLS(opts: Options): boolean {\n  if (Runtime.getProtocol() === 'https:') {\n    return true;\n  } else if (opts.forceTLS === false) {\n    return false;\n  }\n  return true;\n}\n\n// if enableStats is set take the value\n// if disableStats is set take the inverse\n// otherwise default to false\nfunction getEnableStatsConfig(opts: Options): boolean {\n  if ('enableStats' in opts) {\n    return opts.enableStats;\n  }\n  if ('disableStats' in opts) {\n    return !opts.disableStats;\n  }\n  return false;\n}\n\nfunction buildUserAuthenticator(opts: Options): UserAuthenticationHandler {\n  const userAuthentication = {\n    ...Defaults.userAuthentication,\n    ...opts.userAuthentication,\n  };\n  if (\n    'customHandler' in userAuthentication &&\n    userAuthentication['customHandler'] != null\n  ) {\n    return userAuthentication['customHandler'];\n  }\n\n  return UserAuthenticator(userAuthentication);\n}\n\nfunction buildChannelAuth(opts: Options, pusher): ChannelAuthorizationOptions {\n  let channelAuthorization: ChannelAuthorizationOptions;\n  if ('channelAuthorization' in opts) {\n    channelAuthorization = {\n      ...Defaults.channelAuthorization,\n      ...opts.channelAuthorization,\n    };\n  } else {\n    channelAuthorization = {\n      transport: opts.authTransport || Defaults.authTransport,\n      endpoint: opts.authEndpoint || Defaults.authEndpoint,\n    };\n    if ('auth' in opts) {\n      if ('params' in opts.auth) channelAuthorization.params = opts.auth.params;\n      if ('headers' in opts.auth)\n        channelAuthorization.headers = opts.auth.headers;\n    }\n    if ('authorizer' in opts)\n      channelAuthorization.customHandler = ChannelAuthorizerProxy(\n        pusher,\n        channelAuthorization,\n        opts.authorizer,\n      );\n  }\n  return channelAuthorization;\n}\n\nfunction buildChannelAuthorizer(\n  opts: Options,\n  pusher,\n): ChannelAuthorizationHandler {\n  const channelAuthorization = buildChannelAuth(opts, pusher);\n  if (\n    'customHandler' in channelAuthorization &&\n    channelAuthorization['customHandler'] != null\n  ) {\n    return channelAuthorization['customHandler'];\n  }\n\n  return ChannelAuthorizer(channelAuthorization);\n}\n", "import Channel from '../channels/channel';\nimport {\n  ChannelAuthorizationCallback,\n  ChannelAuthorizationHandler,\n  ChannelAuthorizationRequestParams,\n  InternalAuthOptions,\n} from './options';\n\nexport interface DeprecatedChannelAuthorizer {\n  authorize(socketId: string, callback: ChannelAuthorizationCallback): void;\n}\n\nexport interface ChannelAuthorizerGenerator {\n  (\n    channel: Channel,\n    options: DeprecatedAuthorizerOptions,\n  ): DeprecatedChannelAuthorizer;\n}\n\nexport interface DeprecatedAuthOptions {\n  params?: any;\n  headers?: any;\n}\n\nexport interface DeprecatedAuthorizerOptions {\n  authTransport: 'ajax' | 'jsonp';\n  authEndpoint: string;\n  auth?: DeprecatedAuthOptions;\n}\n\nexport const ChannelAuthorizerProxy = (\n  pusher,\n  authOptions: InternalAuthOptions,\n  channelAuthorizerGenerator: ChannelAuthorizerGenerator,\n): ChannelAuthorizationHandler => {\n  const deprecatedAuthorizerOptions: DeprecatedAuthorizerOptions = {\n    authTransport: authOptions.transport,\n    authEndpoint: authOptions.endpoint,\n    auth: {\n      params: authOptions.params,\n      headers: authOptions.headers,\n    },\n  };\n  return (\n    params: ChannelAuthorizationRequestParams,\n    callback: ChannelAuthorizationCallback,\n  ) => {\n    const channel = pusher.channel(params.channelName);\n    // This line creates a new channel authorizer every time.\n    // In the past, this was only done once per channel and reused.\n    // We can do that again if we want to keep this behavior intact.\n    const channelAuthorizer: DeprecatedChannelAuthorizer =\n      channelAuthorizerGenerator(channel, deprecatedAuthorizerOptions);\n    channelAuthorizer.authorize(params.socketId, callback);\n  };\n};\n", "import Logger from './logger';\nimport Pusher from './pusher';\nimport EventsDispatcher from './events/dispatcher';\n\nexport default class WatchlistFacade extends EventsDispatcher {\n  private pusher: Pusher;\n\n  public constructor(pusher: Pusher) {\n    super(function (eventName, data) {\n      Logger.debug(`No callbacks on watchlist events for ${eventName}`);\n    });\n\n    this.pusher = pusher;\n    this.bindWatchlistInternalEvent();\n  }\n\n  handleEvent(pusherEvent) {\n    pusherEvent.data.events.forEach((watchlistEvent) => {\n      this.emit(watchlistEvent.name, watchlistEvent);\n    });\n  }\n\n  private bindWatchlistInternalEvent() {\n    this.pusher.connection.bind('message', (pusherEvent) => {\n      var eventName = pusherEvent.event;\n      if (eventName === 'pusher_internal:watchlist_events') {\n        this.handleEvent(pusherEvent);\n      }\n    });\n  }\n}\n", "function flatPromise() {\n  let resolve, reject;\n  const promise = new Promise((res, rej) => {\n    resolve = res;\n    reject = rej;\n  });\n  return { promise, resolve, reject };\n}\n\nexport default flatPromise;\n", "import Pusher from './pusher';\nimport Logger from './logger';\nimport {\n  UserAuthenticationData,\n  UserAuthenticationCallback,\n} from './auth/options';\nimport Channel from './channels/channel';\nimport WatchlistFacade from './watchlist';\nimport EventsDispatcher from './events/dispatcher';\nimport flatPromise from './utils/flat_promise';\n\nexport default class UserFacade extends EventsDispatcher {\n  pusher: Pusher;\n  signin_requested: boolean = false;\n  user_data: any = null;\n  serverToUserChannel: Channel = null;\n  signinDonePromise: Promise<any> = null;\n  watchlist: WatchlistFacade;\n  private _signinDoneResolve: Function = null;\n\n  public constructor(pusher: Pusher) {\n    super(function (eventName, data) {\n      Logger.debug('No callbacks on user for ' + eventName);\n    });\n    this.pusher = pusher;\n    this.pusher.connection.bind('state_change', ({ previous, current }) => {\n      if (previous !== 'connected' && current === 'connected') {\n        this._signin();\n      }\n      if (previous === 'connected' && current !== 'connected') {\n        this._cleanup();\n        this._newSigninPromiseIfNeeded();\n      }\n    });\n\n    this.watchlist = new WatchlistFacade(pusher);\n\n    this.pusher.connection.bind('message', (event) => {\n      var eventName = event.event;\n      if (eventName === 'pusher:signin_success') {\n        this._onSigninSuccess(event.data);\n      }\n      if (\n        this.serverToUserChannel &&\n        this.serverToUserChannel.name === event.channel\n      ) {\n        this.serverToUserChannel.handleEvent(event);\n      }\n    });\n  }\n\n  public signin() {\n    if (this.signin_requested) {\n      return;\n    }\n\n    this.signin_requested = true;\n    this._signin();\n  }\n\n  private _signin() {\n    if (!this.signin_requested) {\n      return;\n    }\n\n    this._newSigninPromiseIfNeeded();\n\n    if (this.pusher.connection.state !== 'connected') {\n      // Signin will be attempted when the connection is connected\n      return;\n    }\n\n    this.pusher.config.userAuthenticator(\n      {\n        socketId: this.pusher.connection.socket_id,\n      },\n      this._onAuthorize,\n    );\n  }\n\n  private _onAuthorize: UserAuthenticationCallback = (\n    err,\n    authData: UserAuthenticationData,\n  ) => {\n    if (err) {\n      Logger.warn(`Error during signin: ${err}`);\n      this._cleanup();\n      return;\n    }\n\n    this.pusher.send_event('pusher:signin', {\n      auth: authData.auth,\n      user_data: authData.user_data,\n    });\n\n    // Later when we get pusher:singin_success event, the user will be marked as signed in\n  };\n\n  private _onSigninSuccess(data: any) {\n    try {\n      this.user_data = JSON.parse(data.user_data);\n    } catch (e) {\n      Logger.error(`Failed parsing user data after signin: ${data.user_data}`);\n      this._cleanup();\n      return;\n    }\n\n    if (typeof this.user_data.id !== 'string' || this.user_data.id === '') {\n      Logger.error(\n        `user_data doesn't contain an id. user_data: ${this.user_data}`,\n      );\n      this._cleanup();\n      return;\n    }\n\n    // Signin succeeded\n    this._signinDoneResolve();\n    this._subscribeChannels();\n  }\n\n  private _subscribeChannels() {\n    const ensure_subscribed = (channel) => {\n      if (channel.subscriptionPending && channel.subscriptionCancelled) {\n        channel.reinstateSubscription();\n      } else if (\n        !channel.subscriptionPending &&\n        this.pusher.connection.state === 'connected'\n      ) {\n        channel.subscribe();\n      }\n    };\n\n    this.serverToUserChannel = new Channel(\n      `#server-to-user-${this.user_data.id}`,\n      this.pusher,\n    );\n    this.serverToUserChannel.bind_global((eventName, data) => {\n      if (\n        eventName.indexOf('pusher_internal:') === 0 ||\n        eventName.indexOf('pusher:') === 0\n      ) {\n        // ignore internal events\n        return;\n      }\n      this.emit(eventName, data);\n    });\n    ensure_subscribed(this.serverToUserChannel);\n  }\n\n  private _cleanup() {\n    this.user_data = null;\n    if (this.serverToUserChannel) {\n      this.serverToUserChannel.unbind_all();\n      this.serverToUserChannel.disconnect();\n      this.serverToUserChannel = null;\n    }\n\n    if (this.signin_requested) {\n      // If signin is in progress and cleanup is called,\n      // Mark the current signin process as done.\n      this._signinDoneResolve();\n    }\n  }\n\n  private _newSigninPromiseIfNeeded() {\n    if (!this.signin_requested) {\n      return;\n    }\n\n    // If there is a promise and it is not resolved, return without creating a new one.\n    if (this.signinDonePromise && !(this.signinDonePromise as any).done) {\n      return;\n    }\n\n    // This promise is never rejected.\n    // It gets resolved when the signin process is done whether it failed or succeeded\n    const { promise, resolve, reject: _ } = flatPromise();\n    (promise as any).done = false;\n    const setDone = () => {\n      (promise as any).done = true;\n    };\n    promise.then(setDone).catch(setDone);\n    this.signinDonePromise = promise;\n    this._signinDoneResolve = resolve;\n  }\n}\n", "import AbstractRuntime from '../runtimes/interface';\nimport Runtime from 'runtime';\nimport Util from './util';\nimport * as Collections from './utils/collections';\nimport Channels from './channels/channels';\nimport Channel from './channels/channel';\nimport { default as EventsDispatcher } from './events/dispatcher';\nimport Timeline from './timeline/timeline';\nimport TimelineSender from './timeline/timeline_sender';\nimport TimelineLevel from './timeline/level';\nimport { defineTransport } from './strategies/strategy_builder';\nimport ConnectionManager from './connection/connection_manager';\nimport ConnectionManagerOptions from './connection/connection_manager_options';\nimport { PeriodicTimer } from './utils/timers';\nimport Defaults from './defaults';\nimport * as DefaultConfig from './config';\nimport Logger from './logger';\nimport Factory from './utils/factory';\nimport UrlStore from 'core/utils/url_store';\nimport { Options, validateOptions } from './options';\nimport { Config, getConfig } from './config';\nimport StrategyOptions from './strategies/strategy_options';\nimport UserFacade from './user';\n\nexport default class Pusher {\n  /*  STATIC PROPERTIES */\n  static instances: Pusher[] = [];\n  static isReady: boolean = false;\n  static logToConsole: boolean = false;\n\n  // for jsonp\n  static Runtime: AbstractRuntime = Runtime;\n  static ScriptReceivers: any = (<any>Runtime).ScriptReceivers;\n  static DependenciesReceivers: any = (<any>Runtime).DependenciesReceivers;\n  static auth_callbacks: any = (<any>Runtime).auth_callbacks;\n\n  static ready() {\n    Pusher.isReady = true;\n    for (var i = 0, l = Pusher.instances.length; i < l; i++) {\n      Pusher.instances[i].connect();\n    }\n  }\n\n  static log: (message: any) => void;\n\n  private static getClientFeatures(): string[] {\n    return Collections.keys(\n      Collections.filterObject({ ws: Runtime.Transports.ws }, function (t) {\n        return t.isSupported({});\n      }),\n    );\n  }\n\n  /* INSTANCE PROPERTIES */\n  key: string;\n  config: Config;\n  channels: Channels;\n  global_emitter: EventsDispatcher;\n  sessionID: number;\n  timeline: Timeline;\n  timelineSender: TimelineSender;\n  connection: ConnectionManager;\n  timelineSenderTimer: PeriodicTimer;\n  user: UserFacade;\n  constructor(app_key: string, options: Options) {\n    checkAppKey(app_key);\n    validateOptions(options);\n    this.key = app_key;\n    this.config = getConfig(options, this);\n\n    this.channels = Factory.createChannels();\n    this.global_emitter = new EventsDispatcher();\n    this.sessionID = Runtime.randomInt(1000000000);\n\n    this.timeline = new Timeline(this.key, this.sessionID, {\n      cluster: this.config.cluster,\n      features: Pusher.getClientFeatures(),\n      params: this.config.timelineParams || {},\n      limit: 50,\n      level: TimelineLevel.INFO,\n      version: Defaults.VERSION,\n    });\n    if (this.config.enableStats) {\n      this.timelineSender = Factory.createTimelineSender(this.timeline, {\n        host: this.config.statsHost,\n        path: '/timeline/v2/' + Runtime.TimelineTransport.name,\n      });\n    }\n\n    var getStrategy = (options: StrategyOptions) => {\n      return Runtime.getDefaultStrategy(this.config, options, defineTransport);\n    };\n\n    this.connection = Factory.createConnectionManager(this.key, {\n      getStrategy: getStrategy,\n      timeline: this.timeline,\n      activityTimeout: this.config.activityTimeout,\n      pongTimeout: this.config.pongTimeout,\n      unavailableTimeout: this.config.unavailableTimeout,\n      useTLS: Boolean(this.config.useTLS),\n    });\n\n    this.connection.bind('connected', () => {\n      this.subscribeAll();\n      if (this.timelineSender) {\n        this.timelineSender.send(this.connection.isUsingTLS());\n      }\n    });\n\n    this.connection.bind('message', (event) => {\n      var eventName = event.event;\n      var internal = eventName.indexOf('pusher_internal:') === 0;\n      if (event.channel) {\n        var channel = this.channel(event.channel);\n        if (channel) {\n          channel.handleEvent(event);\n        }\n      }\n      // Emit globally [deprecated]\n      if (!internal) {\n        this.global_emitter.emit(event.event, event.data);\n      }\n    });\n    this.connection.bind('connecting', () => {\n      this.channels.disconnect();\n    });\n    this.connection.bind('disconnected', () => {\n      this.channels.disconnect();\n    });\n    this.connection.bind('error', (err) => {\n      Logger.warn(err);\n    });\n\n    Pusher.instances.push(this);\n    this.timeline.info({ instances: Pusher.instances.length });\n\n    this.user = new UserFacade(this);\n\n    if (Pusher.isReady) {\n      this.connect();\n    }\n  }\n\n  channel(name: string): Channel {\n    return this.channels.find(name);\n  }\n\n  allChannels(): Channel[] {\n    return this.channels.all();\n  }\n\n  connect() {\n    this.connection.connect();\n\n    if (this.timelineSender) {\n      if (!this.timelineSenderTimer) {\n        var usingTLS = this.connection.isUsingTLS();\n        var timelineSender = this.timelineSender;\n        this.timelineSenderTimer = new PeriodicTimer(60000, function () {\n          timelineSender.send(usingTLS);\n        });\n      }\n    }\n  }\n\n  disconnect() {\n    this.connection.disconnect();\n\n    if (this.timelineSenderTimer) {\n      this.timelineSenderTimer.ensureAborted();\n      this.timelineSenderTimer = null;\n    }\n  }\n\n  bind(event_name: string, callback: Function, context?: any): Pusher {\n    this.global_emitter.bind(event_name, callback, context);\n    return this;\n  }\n\n  unbind(event_name?: string, callback?: Function, context?: any): Pusher {\n    this.global_emitter.unbind(event_name, callback, context);\n    return this;\n  }\n\n  bind_global(callback: Function): Pusher {\n    this.global_emitter.bind_global(callback);\n    return this;\n  }\n\n  unbind_global(callback?: Function): Pusher {\n    this.global_emitter.unbind_global(callback);\n    return this;\n  }\n\n  unbind_all(callback?: Function): Pusher {\n    this.global_emitter.unbind_all();\n    return this;\n  }\n\n  subscribeAll() {\n    var channelName;\n    for (channelName in this.channels.channels) {\n      if (this.channels.channels.hasOwnProperty(channelName)) {\n        this.subscribe(channelName);\n      }\n    }\n  }\n\n  subscribe(channel_name: string) {\n    var channel = this.channels.add(channel_name, this);\n    if (channel.subscriptionPending && channel.subscriptionCancelled) {\n      channel.reinstateSubscription();\n    } else if (\n      !channel.subscriptionPending &&\n      this.connection.state === 'connected'\n    ) {\n      channel.subscribe();\n    }\n    return channel;\n  }\n\n  unsubscribe(channel_name: string) {\n    var channel = this.channels.find(channel_name);\n    if (channel && channel.subscriptionPending) {\n      channel.cancelSubscription();\n    } else {\n      channel = this.channels.remove(channel_name);\n      if (channel && channel.subscribed) {\n        channel.unsubscribe();\n      }\n    }\n  }\n\n  send_event(event_name: string, data: any, channel?: string) {\n    return this.connection.send_event(event_name, data, channel);\n  }\n\n  shouldUseTLS(): boolean {\n    return this.config.useTLS;\n  }\n\n  signin() {\n    this.user.signin();\n  }\n}\n\nfunction checkAppKey(key) {\n  if (key === null || key === undefined) {\n    throw 'You must pass your app key when you instantiate Pusher.';\n  }\n}\n\nRuntime.setup(Pusher);\n", "import Pusher from './pusher';\nimport { Options, validateOptions } from './options';\nimport * as nacl from 'tweetnacl';\n\nexport default class PusherWithEncryption extends Pusher {\n  constructor(app_key: string, options: Options) {\n    Pusher.logToConsole = PusherWithEncryption.logToConsole;\n    Pusher.log = PusherWithEncryption.log;\n\n    validateOptions(options);\n    options.nacl = nacl;\n    super(app_key, options);\n  }\n}\n"], "sourceRoot": ""}