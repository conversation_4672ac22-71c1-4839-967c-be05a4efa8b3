<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Fibi Telecom</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
        }
    </style>
</head>
<body class="gradient-bg min-h-screen">
    <!-- Header -->
    <header class="bg-white shadow">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-6">
                <div class="flex items-center">
                    <i data-lucide="phone" class="h-8 w-8 text-blue-600 mr-2"></i>
                    <span class="text-2xl font-bold text-gray-900">Fibi Telecom</span>
                </div>
                <div class="flex items-center space-x-4">
                    <!-- Notification Bell -->
                    <div class="relative">
                        <button class="relative p-2 text-gray-600 hover:text-gray-800">
                            <i data-lucide="bell" class="h-6 w-6"></i>
                            <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">3</span>
                        </button>
                    </div>
                    <span class="text-gray-700">Иван Иванов</span>
                    <button class="flex items-center text-gray-600 hover:text-gray-800">
                        <i data-lucide="log-out" class="h-5 w-5 mr-1"></i>
                        Выйти
                    </button>
                </div>
            </div>
        </div>
    </header>

    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div class="px-4 py-6 sm:px-0">
            <div class="mb-8">
                <h1 class="text-3xl font-bold text-gray-900">Личный кабинет</h1>
                <p class="mt-2 text-gray-600">Управляйте вашими услугами и настройками</p>
            </div>

            <!-- Quick Stats -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <i data-lucide="wifi" class="h-8 w-8 text-blue-600"></i>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Интернет</p>
                            <p class="text-2xl font-bold text-gray-900">100 Мбит/с</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <i data-lucide="phone" class="h-8 w-8 text-green-600"></i>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Телефония</p>
                            <p class="text-2xl font-bold text-gray-900">Активна</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <i data-lucide="credit-card" class="h-8 w-8 text-purple-600"></i>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Баланс</p>
                            <p class="text-2xl font-bold text-gray-900">1,250 ₽</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <i data-lucide="calendar" class="h-8 w-8 text-orange-600"></i>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">До продления</p>
                            <p class="text-2xl font-bold text-gray-900">15 дней</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Profile Card -->
                <div class="lg:col-span-1">
                    <div class="bg-white overflow-hidden shadow rounded-lg">
                        <div class="px-4 py-5 sm:p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <i data-lucide="user" class="h-10 w-10 text-gray-400"></i>
                                </div>
                                <div class="ml-4">
                                    <h3 class="text-lg font-medium text-gray-900">Иван Иванов</h3>
                                    <p class="text-sm text-gray-500"><EMAIL></p>
                                    <p class="text-sm text-gray-500">Роль: Пользователь</p>
                                </div>
                            </div>
                            <div class="mt-6">
                                <button class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                    <i data-lucide="settings" class="h-4 w-4 mr-2"></i>
                                    Настройки профиля
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Services -->
                <div class="lg:col-span-2">
                    <div class="bg-white shadow rounded-lg">
                        <div class="px-4 py-5 sm:p-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-6">Ваши услуги</h3>
                            <div class="space-y-4">
                                <!-- Internet Service -->
                                <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                                    <div class="flex items-center">
                                        <i data-lucide="wifi" class="h-8 w-8 text-blue-600 mr-4"></i>
                                        <div>
                                            <h4 class="text-lg font-medium text-gray-900">Интернет</h4>
                                            <p class="text-sm text-gray-500">Домашний 100 Мбит/с</p>
                                            <p class="text-sm text-green-600">Активен</p>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <p class="text-lg font-semibold text-gray-900">990 ₽/мес</p>
                                        <button class="mt-2 text-sm text-blue-600 hover:text-blue-800">Управлять</button>
                                    </div>
                                </div>

                                <!-- Phone Service -->
                                <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                                    <div class="flex items-center">
                                        <i data-lucide="phone" class="h-8 w-8 text-blue-600 mr-4"></i>
                                        <div>
                                            <h4 class="text-lg font-medium text-gray-900">Телефония</h4>
                                            <p class="text-sm text-gray-500">Безлимитные звонки</p>
                                            <p class="text-sm text-green-600">Активен</p>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <p class="text-lg font-semibold text-gray-900">490 ₽/мес</p>
                                        <button class="mt-2 text-sm text-blue-600 hover:text-blue-800">Управлять</button>
                                    </div>
                                </div>

                                <!-- Security Service -->
                                <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg opacity-60">
                                    <div class="flex items-center">
                                        <i data-lucide="shield" class="h-8 w-8 text-gray-400 mr-4"></i>
                                        <div>
                                            <h4 class="text-lg font-medium text-gray-900">Безопасность</h4>
                                            <p class="text-sm text-gray-500">Базовая защита</p>
                                            <p class="text-sm text-gray-500">Не подключен</p>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <p class="text-lg font-semibold text-gray-900">290 ₽/мес</p>
                                        <button class="mt-2 text-sm text-blue-600 hover:text-blue-800">Подключить</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="mt-8">
                <h2 class="text-xl font-bold text-gray-900 mb-4">Быстрые действия</h2>
                <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
                    <div class="bg-white p-6 rounded-lg shadow hover:shadow-md transition-shadow cursor-pointer">
                        <i data-lucide="credit-card" class="h-8 w-8 text-blue-600 mb-2"></i>
                        <h3 class="text-lg font-medium text-gray-900">Счета</h3>
                        <p class="text-sm text-gray-500">Просмотр и оплата счетов</p>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow hover:shadow-md transition-shadow cursor-pointer">
                        <i data-lucide="phone" class="h-8 w-8 text-blue-600 mb-2"></i>
                        <h3 class="text-lg font-medium text-gray-900">Поддержка</h3>
                        <p class="text-sm text-gray-500">Связаться с поддержкой</p>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow hover:shadow-md transition-shadow cursor-pointer">
                        <i data-lucide="settings" class="h-8 w-8 text-blue-600 mb-2"></i>
                        <h3 class="text-lg font-medium text-gray-900">Услуги</h3>
                        <p class="text-sm text-gray-500">Управление услугами</p>
                    </div>

                    <div class="bg-white p-6 rounded-lg shadow hover:shadow-md transition-shadow cursor-pointer">
                        <i data-lucide="bar-chart-3" class="h-8 w-8 text-blue-600 mb-2"></i>
                        <h3 class="text-lg font-medium text-gray-900">Статистика</h3>
                        <p class="text-sm text-gray-500">Использование услуг</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Chat Widget -->
    <div class="fixed bottom-6 right-6">
        <button class="bg-blue-600 text-white p-4 rounded-full shadow-lg hover:bg-blue-700 transition-colors">
            <i data-lucide="message-circle" class="h-6 w-6"></i>
        </button>
    </div>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();

        // Add click handlers for demo
        document.querySelectorAll('.cursor-pointer').forEach(element => {
            element.addEventListener('click', function() {
                alert('Функция будет доступна после полной настройки проекта!');
            });
        });

        // Chat widget click
        document.querySelector('.fixed button').addEventListener('click', function() {
            alert('Чат поддержки будет доступен после настройки Pusher!');
        });
    </script>
</body>
</html>
