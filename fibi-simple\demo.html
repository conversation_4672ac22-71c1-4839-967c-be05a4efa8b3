<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fibi Telecom - Демонстрация функциональности</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #dbeafe 0%, #e0e7ff 100%);
        }
        .demo-section {
            border: 2px dashed #e5e7eb;
            margin: 2rem 0;
            padding: 2rem;
            border-radius: 0.5rem;
        }
        .demo-title {
            background: #f3f4f6;
            color: #374151;
            padding: 0.5rem 1rem;
            border-radius: 0.25rem;
            font-weight: bold;
            margin-bottom: 1rem;
            display: inline-block;
        }
    </style>
</head>
<body class="gradient-bg min-h-screen">
    <div class="max-w-7xl mx-auto px-4 py-8">
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold text-gray-900 mb-4">
                🎉 Fibi Telecom - Демонстрация функциональности
            </h1>
            <p class="text-xl text-gray-600">
                Полная интеграция с Next.js, формы обратной связи и система авторизации
            </p>
        </div>

        <!-- Реализованные компоненты -->
        <div class="demo-section bg-white rounded-lg">
            <div class="demo-title">✅ Реализованные React компоненты</div>
            <div class="grid md:grid-cols-2 gap-6">
                <div>
                    <h3 class="text-lg font-semibold mb-3">Основные компоненты:</h3>
                    <ul class="space-y-2 text-gray-700">
                        <li>📱 <strong>Header</strong> - навигация с авторизацией</li>
                        <li>🎯 <strong>HeroSection</strong> - главная секция</li>
                        <li>⚡ <strong>ServicesSection</strong> - услуги с иконками</li>
                        <li>📞 <strong>ContactSection</strong> - контакты с формой</li>
                        <li>🦶 <strong>Footer</strong> - подвал сайта</li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-3">Формы и авторизация:</h3>
                    <ul class="space-y-2 text-gray-700">
                        <li>📝 <strong>ContactForm</strong> - форма обратной связи</li>
                        <li>🔐 <strong>LoginPage</strong> - страница входа</li>
                        <li>📋 <strong>SignupPage</strong> - регистрация</li>
                        <li>👤 <strong>Dashboard</strong> - личный кабинет</li>
                        <li>⚙️ <strong>AdminPanel</strong> - админ-панель</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Технологии -->
        <div class="demo-section bg-blue-50 rounded-lg">
            <div class="demo-title">🛠️ Использованные технологии</div>
            <div class="grid md:grid-cols-3 gap-6">
                <div>
                    <h3 class="text-lg font-semibold mb-3 text-blue-800">Frontend:</h3>
                    <ul class="space-y-1 text-gray-700">
                        <li>• Next.js 15.3.2</li>
                        <li>• React 19</li>
                        <li>• TypeScript</li>
                        <li>• Tailwind CSS</li>
                        <li>• Lucide React (иконки)</li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-3 text-blue-800">Формы и валидация:</h3>
                    <ul class="space-y-1 text-gray-700">
                        <li>• React Hook Form</li>
                        <li>• Zod (валидация)</li>
                        <li>• @hookform/resolvers</li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-3 text-blue-800">Backend и Auth:</h3>
                    <ul class="space-y-1 text-gray-700">
                        <li>• Supabase</li>
                        <li>• @supabase/ssr</li>
                        <li>• Аутентификация</li>
                        <li>• База данных</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Функциональность форм -->
        <div class="demo-section bg-green-50 rounded-lg">
            <div class="demo-title">📝 Функциональность форм обратной связи</div>
            <div class="grid md:grid-cols-2 gap-6">
                <div>
                    <h3 class="text-lg font-semibold mb-3 text-green-800">Возможности:</h3>
                    <ul class="space-y-2 text-gray-700">
                        <li>✅ Валидация полей в реальном времени</li>
                        <li>✅ Красивые сообщения об ошибках</li>
                        <li>✅ Индикатор загрузки при отправке</li>
                        <li>✅ Уведомление об успешной отправке</li>
                        <li>✅ Адаптивный дизайн</li>
                        <li>✅ Иконки для полей ввода</li>
                    </ul>
                </div>
                <div class="bg-white p-4 rounded-lg border">
                    <h4 class="font-semibold mb-2">Пример формы:</h4>
                    <div class="space-y-3">
                        <div class="flex items-center space-x-2">
                            <div class="w-4 h-4 bg-blue-500 rounded"></div>
                            <span class="text-sm">Поле "Имя" с валидацией</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="w-4 h-4 bg-blue-500 rounded"></div>
                            <span class="text-sm">Email с проверкой формата</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="w-4 h-4 bg-blue-500 rounded"></div>
                            <span class="text-sm">Телефон с маской ввода</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="w-4 h-4 bg-blue-500 rounded"></div>
                            <span class="text-sm">Текстовое сообщение</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Система авторизации -->
        <div class="demo-section bg-purple-50 rounded-lg">
            <div class="demo-title">🔐 Система авторизации</div>
            <div class="grid md:grid-cols-2 gap-6">
                <div>
                    <h3 class="text-lg font-semibold mb-3 text-purple-800">Возможности:</h3>
                    <ul class="space-y-2 text-gray-700">
                        <li>🔑 Регистрация с подтверждением email</li>
                        <li>🔓 Вход в систему</li>
                        <li>👁️ Показать/скрыть пароль</li>
                        <li>🔄 Восстановление пароля</li>
                        <li>👤 Профили пользователей</li>
                        <li>🛡️ Роли (пользователь/админ)</li>
                        <li>📊 Личный кабинет</li>
                        <li>⚙️ Админ-панель</li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-3 text-purple-800">Защищенные страницы:</h3>
                    <div class="space-y-3">
                        <div class="bg-white p-3 rounded border">
                            <strong>/dashboard</strong> - Личный кабинет пользователя
                        </div>
                        <div class="bg-white p-3 rounded border">
                            <strong>/admin</strong> - Панель администратора
                        </div>
                        <div class="bg-white p-3 rounded border">
                            <strong>/auth/login</strong> - Страница входа
                        </div>
                        <div class="bg-white p-3 rounded border">
                            <strong>/auth/signup</strong> - Страница регистрации
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Структура проекта -->
        <div class="demo-section bg-gray-50 rounded-lg">
            <div class="demo-title">📁 Структура проекта</div>
            <div class="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm overflow-x-auto">
<pre>fibi-simple/
├── components/           # React компоненты
│   ├── Header.tsx       # Навигация
│   ├── HeroSection.tsx  # Главная секция
│   ├── ServicesSection.tsx
│   ├── ContactForm.tsx  # Форма обратной связи
│   ├── ContactSection.tsx
│   └── Footer.tsx
├── src/app/             # Next.js App Router
│   ├── page.tsx         # Главная страница
│   ├── layout.tsx       # Общий layout
│   ├── auth/
│   │   ├── login/page.tsx
│   │   └── signup/page.tsx
│   ├── dashboard/page.tsx
│   └── admin/page.tsx
├── hooks/
│   └── useAuth.ts       # Хук авторизации
├── lib/
│   ├── supabase.ts      # Клиент Supabase
│   └── supabase-server.ts
└── .env.local           # Переменные окружения</pre>
            </div>
        </div>

        <!-- Следующие шаги -->
        <div class="demo-section bg-yellow-50 rounded-lg">
            <div class="demo-title">🚀 Следующие шаги для запуска</div>
            <div class="space-y-4">
                <div class="bg-white p-4 rounded-lg border">
                    <h3 class="font-semibold mb-2">1. Настройка Supabase:</h3>
                    <p class="text-gray-700">Создайте проект в Supabase и обновите переменные в .env.local</p>
                </div>
                <div class="bg-white p-4 rounded-lg border">
                    <h3 class="font-semibold mb-2">2. Установка зависимостей:</h3>
                    <code class="bg-gray-100 px-2 py-1 rounded">npm install</code>
                </div>
                <div class="bg-white p-4 rounded-lg border">
                    <h3 class="font-semibold mb-2">3. Запуск сервера:</h3>
                    <code class="bg-gray-100 px-2 py-1 rounded">npm run dev</code>
                </div>
                <div class="bg-white p-4 rounded-lg border">
                    <h3 class="font-semibold mb-2">4. Создание таблиц в Supabase:</h3>
                    <p class="text-gray-700">Создайте таблицу profiles для хранения данных пользователей</p>
                </div>
            </div>
        </div>

        <!-- API и Backend -->
        <div class="demo-section bg-indigo-50 rounded-lg">
            <div class="demo-title">🔌 API и Backend</div>
            <div class="grid md:grid-cols-2 gap-6">
                <div>
                    <h3 class="text-lg font-semibold mb-3 text-indigo-800">API Endpoints:</h3>
                    <ul class="space-y-2 text-gray-700">
                        <li>📝 <strong>POST /api/contact</strong> - отправка формы</li>
                        <li>🔐 <strong>Supabase Auth API</strong> - авторизация</li>
                        <li>📊 <strong>Supabase Database API</strong> - данные</li>
                        <li>🛡️ <strong>Row Level Security</strong> - безопасность</li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-semibold mb-3 text-indigo-800">База данных:</h3>
                    <ul class="space-y-2 text-gray-700">
                        <li>👤 profiles - профили пользователей</li>
                        <li>📞 contact_messages - обратная связь</li>
                        <li>⚡ services - услуги компании</li>
                        <li>🔗 user_services - подписки</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Заключение -->
        <div class="text-center mt-12 p-8 bg-white rounded-lg shadow-lg">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">
                🎯 Проект полностью готов!
            </h2>
            <p class="text-lg text-gray-600 mb-6">
                Полнофункциональный сайт телекоммуникационной компании с современным стеком технологий
            </p>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                <div class="bg-blue-100 text-blue-800 px-4 py-2 rounded-lg">
                    ✅ React компоненты
                </div>
                <div class="bg-green-100 text-green-800 px-4 py-2 rounded-lg">
                    ✅ Формы + API
                </div>
                <div class="bg-purple-100 text-purple-800 px-4 py-2 rounded-lg">
                    ✅ Авторизация
                </div>
                <div class="bg-red-100 text-red-800 px-4 py-2 rounded-lg">
                    ✅ Админ-панель
                </div>
                <div class="bg-yellow-100 text-yellow-800 px-4 py-2 rounded-lg">
                    ✅ База данных
                </div>
                <div class="bg-indigo-100 text-indigo-800 px-4 py-2 rounded-lg">
                    ✅ TypeScript
                </div>
                <div class="bg-pink-100 text-pink-800 px-4 py-2 rounded-lg">
                    ✅ Валидация
                </div>
                <div class="bg-teal-100 text-teal-800 px-4 py-2 rounded-lg">
                    ✅ Безопасность
                </div>
            </div>
            <div class="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-4 rounded-lg">
                <p class="text-lg font-semibold">
                    🚀 Готов к развертыванию на Vercel, Netlify или любой другой платформе!
                </p>
            </div>
        </div>
    </div>
</body>
</html>
