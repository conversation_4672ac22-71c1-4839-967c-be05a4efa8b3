import Pusher from 'pusher'
import PusherClient from 'pusher-js'

// Серверная конфигурация Pusher
export const pusherServer = new Pusher({
  appId: process.env.NEXT_PUBLIC_PUSHER_APP_ID!,
  key: process.env.NEXT_PUBLIC_PUSHER_KEY!,
  secret: process.env.NEXT_PUBLIC_PUSHER_SECRET!,
  cluster: process.env.NEXT_PUBLIC_PUSHER_CLUSTER!,
  useTLS: true,
})

// Клиентская конфигурация Pusher
export const pusherClient = new PusherClient(
  process.env.NEXT_PUBLIC_PUSHER_KEY!,
  {
    cluster: process.env.NEXT_PUBLIC_PUSHER_CLUSTER!,
    forceTLS: true,
  }
)

// Типы для чата
export interface ChatMessage {
  id: string
  content: string
  user_id: string
  user_name: string
  user_role: 'user' | 'admin'
  created_at: string
  chat_id: string
}

export interface ChatRoom {
  id: string
  user_id: string
  admin_id?: string
  status: 'open' | 'closed'
  created_at: string
  updated_at: string
}

// Утилиты для работы с каналами
export const getChatChannelName = (chatId: string) => `chat-${chatId}`
export const getAdminChannelName = () => 'admin-notifications'
export const getUserChannelName = (userId: string) => `user-${userId}`
