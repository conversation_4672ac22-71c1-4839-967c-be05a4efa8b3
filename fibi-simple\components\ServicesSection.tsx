import { Wifi, Phone, Shield } from 'lucide-react'

const services = [
  {
    icon: Wifi,
    title: 'Интернет',
    description: 'Высокоскоростной интернет до 1 Гбит/с для дома и офиса'
  },
  {
    icon: Phone,
    title: 'Телефония',
    description: 'IP-телефония и мобильная связь с выгодными тарифами'
  },
  {
    icon: Shield,
    title: 'Безопасность',
    description: 'Защищенные каналы связи и системы безопасности'
  }
]

export default function ServicesSection() {
  return (
    <section id="services" className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Наши услуги
          </h2>
          <p className="text-xl text-gray-600">
            Полный спектр телекоммуникационных решений
          </p>
        </div>
        
        <div className="grid md:grid-cols-3 gap-8">
          {services.map((service, index) => {
            const IconComponent = service.icon
            return (
              <div
                key={index}
                className="text-center p-6 rounded-lg border border-gray-200 hover:shadow-lg transition-shadow"
              >
                <IconComponent className="h-12 w-12 text-blue-600 mx-auto mb-4" />
                <h3 className="text-xl font-semibold mb-2">{service.title}</h3>
                <p className="text-gray-600">{service.description}</p>
              </div>
            )
          })}
        </div>
      </div>
    </section>
  )
}
