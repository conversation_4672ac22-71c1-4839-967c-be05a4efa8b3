{"name": "pusher-js", "location": "git://github.com/pusher/pusher-js.git", "description": "Pusher JavaScript library for browsers, React Native, NodeJS and web workers", "keywords": ["pusher", "client", "websocket", "http", "fallback", "isomorphic", "events", "pubsub"], "licence": "MIT", "authors": ["Pusher <<EMAIL>>"], "main": "./dist/web/pusher.js", "ignore": ["**/*", "!/dist/**/*", "!LICENCE", "!README.markdown", "!bower.json", "!CHANGELOG.markdown"]}