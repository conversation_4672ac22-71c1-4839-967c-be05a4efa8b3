language: node_js
node_js:
  - "10"
sudo: required
env: DISPLAY=':99.0'
dist: bionic

jobs:
  include:
    - name: "Linting"
      script: npm run lint

    - name: "Typescript type checks"
      script: npm run test:ts

    - name: "Unit tests"
      script: npm run test:unit

    - name: "End to end tests"
      script:
        - npm run build:cdn
        - npm run test:e2e
      before_script:
        - CHROME_VERSION=`google-chrome --version | cut -d ' ' -f 3 | rev | cut -d '.' -f2- | rev`
        - echo $CHROME_VERSION
        - LATEST_CHROMEDRIVER_VERSION=`curl -s "https://chromedriver.storage.googleapis.com/LATEST_RELEASE_$CHROME_VERSION"`
        - echo $LATEST_CHROMEDRIVER_VERSION
        - curl "https://chromedriver.storage.googleapis.com/${LATEST_CHROMEDRIVER_VERSION}/chromedriver_linux64.zip" -O
        - unzip chromedriver_linux64.zip
        - sudo mv chromedriver /usr/local/bin
        - sudo service xvfb start
      addons:
        chrome: stable
        apt:
          packages:
            - curl
            - unzip
