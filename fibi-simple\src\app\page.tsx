import Header from '@/components/Header'
import HeroSection from '@/components/HeroSection'
import ServicesSection from '@/components/ServicesSection'
import ContactSection from '@/components/ContactSection'
import Footer from '@/components/Footer'
import { Zap, Shield, Phone } from 'lucide-react'
import Link from 'next/link'

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <Header />
      <HeroSection />
      <ServicesSection />

      {/* Features Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Почему выбирают нас?
              </h2>
              <div className="space-y-4">
                <div className="flex items-start">
                  <Zap className="h-6 w-6 text-blue-600 mt-1 mr-3" />
                  <div>
                    <h3 className="font-semibold">Высокая скорость</h3>
                    <p className="text-gray-600">Стабильное соединение без перебоев</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <Shield className="h-6 w-6 text-blue-600 mt-1 mr-3" />
                  <div>
                    <h3 className="font-semibold">Надежность</h3>
                    <p className="text-gray-600">99.9% времени работы сети</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <Phone className="h-6 w-6 text-blue-600 mt-1 mr-3" />
                  <div>
                    <h3 className="font-semibold">Поддержка 24/7</h3>
                    <p className="text-gray-600">Круглосуточная техническая поддержка</p>
                  </div>
                </div>
              </div>
            </div>
            <div className="bg-blue-600 rounded-lg p-8 text-white">
              <h3 className="text-2xl font-bold mb-4">Готовы начать?</h3>
              <p className="mb-6">
                Подключитесь к нашим услугам уже сегодня и получите первый месяц бесплатно!
              </p>
              <Link
                href="/auth/signup"
                className="bg-white text-blue-600 px-6 py-3 rounded-lg font-medium hover:bg-gray-100 transition-colors inline-block"
              >
                Подключиться
              </Link>
            </div>
          </div>
        </div>
      </section>

      <ContactSection />
      <Footer />
    </div>
  )
}
