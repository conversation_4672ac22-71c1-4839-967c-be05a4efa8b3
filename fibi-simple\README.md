# Fibi Telecom - Телекоммуникационная компания

Современный веб-сайт телекоммуникационной компании с полной функциональностью авторизации, формами обратной связи и админ-панелью.

## 🚀 Возможности

### Базовая функциональность
- ✅ **Современный дизайн** с Tailwind CSS
- ✅ **Система авторизации** с Supabase
- ✅ **Формы обратной связи** с валидацией
- ✅ **Личный кабинет** пользователя
- ✅ **Админ-панель** для управления
- ✅ **Адаптивный дизайн** для всех устройств
- ✅ **TypeScript** для типобезопасности

### Расширенная функциональность
- 💳 **Система оплаты Stripe** - безопасные платежи и подписки
- 📊 **Аналитика и отчеты** - интерактивные графики и статистика
- 💬 **Чат поддержки** - общение в реальном времени
- 🔔 **Push уведомления** - браузерные уведомления
- 📧 **Email рассылки** - автоматические письма и новостные рассылки

## 🛠️ Технологии

### Основной стек
- **Frontend**: Next.js 15, React 19, TypeScript
- **Стили**: Tailwind CSS
- **Иконки**: Lucide React
- **Формы**: React Hook Form + Zod
- **Backend**: Supabase (Auth + Database)

### Интеграции и сервисы
- **Платежи**: Stripe (платежи, подписки, webhooks)
- **Аналитика**: Recharts (графики), Date-fns (даты)
- **Чат**: Pusher (WebSockets, реальное время)
- **Email**: Nodemailer (SMTP, HTML шаблоны)
- **Уведомления**: Browser Notifications API

### Развертывание
- **Рекомендуется**: Vercel, Netlify
- **Альтернативы**: Railway, Heroku, DigitalOcean

## 📋 Быстрый старт

### 1. Клонирование и установка

```bash
# Установка зависимостей
npm install
```

### 2. Настройка Supabase

1. Создайте проект в [Supabase](https://supabase.com)
2. Скопируйте URL и Anon Key из Settings > API
3. Создайте файл `.env.local`:

```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url_here
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key_here
```

### 3. Настройка базы данных

1. Откройте SQL Editor в Supabase Dashboard
2. Выполните скрипт из файла `supabase-setup.sql`
3. Это создаст все необходимые таблицы и настройки

### 4. Запуск проекта

```bash
npm run dev
```

Откройте [http://localhost:3000](http://localhost:3000) в браузере.

## 📁 Структура проекта

```
fibi-simple/
├── components/              # React компоненты
│   ├── Header.tsx          # Навигация с авторизацией
│   ├── HeroSection.tsx     # Главная секция
│   ├── ServicesSection.tsx # Секция услуг
│   ├── ContactForm.tsx     # Форма обратной связи
│   ├── ContactSection.tsx  # Секция контактов
│   └── Footer.tsx          # Подвал сайта
├── src/app/                # Next.js App Router
│   ├── page.tsx           # Главная страница
│   ├── layout.tsx         # Общий layout
│   ├── auth/              # Страницы авторизации
│   │   ├── login/page.tsx
│   │   └── signup/page.tsx
│   ├── dashboard/page.tsx # Личный кабинет
│   └── admin/page.tsx     # Админ-панель
├── hooks/
│   └── useAuth.ts         # Хук авторизации
├── lib/
│   ├── supabase.ts        # Клиент Supabase
│   └── supabase-server.ts # Серверный клиент
├── supabase-setup.sql     # SQL для настройки БД
└── demo.html              # Демонстрация функций
```

## 🔐 Система авторизации

### Возможности
- Регистрация с подтверждением email
- Вход в систему
- Восстановление пароля
- Профили пользователей
- Роли (пользователь/админ)
- Защищенные маршруты

### Страницы
- `/auth/login` - Вход в систему
- `/auth/signup` - Регистрация
- `/dashboard` - Личный кабинет (требует авторизации)
- `/admin` - Админ-панель (только для администраторов)

## 📝 Формы обратной связи

### Функциональность
- Валидация полей в реальном времени
- Красивые сообщения об ошибках
- Индикатор загрузки
- Уведомления об успешной отправке
- Адаптивный дизайн

### Поля формы
- Имя (обязательное, минимум 2 символа)
- Email (обязательное, валидация формата)
- Телефон (обязательное, минимум 10 символов)
- Сообщение (обязательное, минимум 10 символов)

## 👤 Роли пользователей

### Пользователь
- Просмотр и редактирование своего профиля
- Управление своими услугами
- Просмотр счетов
- Обращение в поддержку

### Администратор
- Все возможности пользователя
- Управление пользователями
- Просмотр всех обращений
- Управление услугами
- Системная аналитика

## 🗄️ База данных

### Таблицы
- `profiles` - Профили пользователей
- `contact_messages` - Сообщения обратной связи
- `services` - Услуги компании
- `user_services` - Подписки пользователей

### Безопасность
- Row Level Security (RLS)
- Политики доступа
- Автоматическое создание профилей
- Защищенные API

## 🚀 Развертывание

### Vercel (рекомендуется)
1. Подключите репозиторий к Vercel
2. Добавьте переменные окружения
3. Деплой произойдет автоматически

### Другие платформы
- Netlify
- Railway
- Heroku
- DigitalOcean App Platform

## 🔧 Настройка администратора

1. Зарегистрируйтесь через интерфейс сайта
2. В Supabase Dashboard выполните:
```sql
UPDATE profiles SET role = 'admin' WHERE email = '<EMAIL>';
```

## 📞 Поддержка

Если у вас возникли вопросы:
1. Проверьте документацию
2. Посмотрите demo.html для примеров
3. Создайте issue в репозитории

## 📄 Лицензия

MIT License - используйте свободно для коммерческих и некоммерческих проектов.
