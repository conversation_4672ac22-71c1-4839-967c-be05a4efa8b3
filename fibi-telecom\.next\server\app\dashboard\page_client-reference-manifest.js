globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/dashboard/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"894":{"*":{"id":"6346","name":"*","chunks":[],"async":false}},"1295":{"*":{"id":"7173","name":"*","chunks":[],"async":false}},"2614":{"*":{"id":"4811","name":"*","chunks":[],"async":false}},"4177":{"*":{"id":"9457","name":"*","chunks":[],"async":false}},"4820":{"*":{"id":"58","name":"*","chunks":[],"async":false}},"4911":{"*":{"id":"8827","name":"*","chunks":[],"async":false}},"4970":{"*":{"id":"7924","name":"*","chunks":[],"async":false}},"6614":{"*":{"id":"5656","name":"*","chunks":[],"async":false}},"6874":{"*":{"id":"5814","name":"*","chunks":[],"async":false}},"6975":{"*":{"id":"99","name":"*","chunks":[],"async":false}},"7555":{"*":{"id":"8243","name":"*","chunks":[],"async":false}},"8689":{"*":{"id":"3150","name":"*","chunks":[],"async":false}},"9665":{"*":{"id":"2763","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Fibi-telesominication\\fibi-telecom\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":894,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Fibi-telesominication\\fibi-telecom\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":894,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Fibi-telesominication\\fibi-telecom\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":4970,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Fibi-telesominication\\fibi-telecom\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":4970,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Fibi-telesominication\\fibi-telecom\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":6614,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Fibi-telesominication\\fibi-telecom\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":6614,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Fibi-telesominication\\fibi-telecom\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":6975,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Fibi-telesominication\\fibi-telecom\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":6975,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Fibi-telesominication\\fibi-telecom\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":7555,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Fibi-telesominication\\fibi-telecom\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":7555,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Fibi-telesominication\\fibi-telecom\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":4911,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Fibi-telesominication\\fibi-telecom\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":4911,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Fibi-telesominication\\fibi-telecom\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":9665,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Fibi-telesominication\\fibi-telecom\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":9665,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Fibi-telesominication\\fibi-telecom\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":1295,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Fibi-telesominication\\fibi-telecom\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":1295,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Fibi-telesominication\\fibi-telecom\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"}":{"id":2093,"name":"*","chunks":["177","static/chunks/app/layout-1d03b2d49d2d8669.js"],"async":false},"C:\\Users\\<USER>\\Fibi-telesominication\\fibi-telecom\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"}":{"id":7735,"name":"*","chunks":["177","static/chunks/app/layout-1d03b2d49d2d8669.js"],"async":false},"C:\\Users\\<USER>\\Fibi-telesominication\\fibi-telecom\\src\\app\\globals.css":{"id":347,"name":"*","chunks":["177","static/chunks/app/layout-1d03b2d49d2d8669.js"],"async":false},"C:\\Users\\<USER>\\Fibi-telesominication\\fibi-telecom\\src\\app\\auth\\login\\page.tsx":{"id":4177,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Fibi-telesominication\\fibi-telecom\\src\\app\\admin\\page.tsx":{"id":8689,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Fibi-telesominication\\fibi-telecom\\src\\app\\auth\\signup\\page.tsx":{"id":4820,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Fibi-telesominication\\fibi-telecom\\src\\app\\dashboard\\page.tsx":{"id":2614,"name":"*","chunks":["664","static/chunks/664-f1704c44141b2326.js","105","static/chunks/app/dashboard/page-058db47073a87a6e.js"],"async":false},"C:\\Users\\<USER>\\Fibi-telesominication\\fibi-telecom\\node_modules\\next\\dist\\client\\app-dir\\link.js":{"id":6874,"name":"*","chunks":["874","static/chunks/874-e0b9799315dd260f.js","974","static/chunks/app/page-c220c7546b8c10c5.js"],"async":false},"C:\\Users\\<USER>\\Fibi-telesominication\\fibi-telecom\\node_modules\\next\\dist\\esm\\client\\app-dir\\link.js":{"id":6874,"name":"*","chunks":["874","static/chunks/874-e0b9799315dd260f.js","974","static/chunks/app/page-c220c7546b8c10c5.js"],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\Fibi-telesominication\\fibi-telecom\\src\\":[],"C:\\Users\\<USER>\\Fibi-telesominication\\fibi-telecom\\src\\app\\layout":[{"inlined":false,"path":"static/css/b0d7cf5025bc2e02.css"}],"C:\\Users\\<USER>\\Fibi-telesominication\\fibi-telecom\\src\\app\\page":[],"C:\\Users\\<USER>\\Fibi-telesominication\\fibi-telecom\\src\\app\\dashboard\\page":[]},"rscModuleMapping":{"347":{"*":{"id":"1135","name":"*","chunks":[],"async":false}},"894":{"*":{"id":"6444","name":"*","chunks":[],"async":false}},"1295":{"*":{"id":"1307","name":"*","chunks":[],"async":false}},"2614":{"*":{"id":"559","name":"*","chunks":[],"async":false}},"4177":{"*":{"id":"1351","name":"*","chunks":[],"async":false}},"4820":{"*":{"id":"4796","name":"*","chunks":[],"async":false}},"4911":{"*":{"id":"2089","name":"*","chunks":[],"async":false}},"4970":{"*":{"id":"6042","name":"*","chunks":[],"async":false}},"6614":{"*":{"id":"8170","name":"*","chunks":[],"async":false}},"6874":{"*":{"id":"4536","name":"*","chunks":[],"async":false}},"6975":{"*":{"id":"9477","name":"*","chunks":[],"async":false}},"7555":{"*":{"id":"9345","name":"*","chunks":[],"async":false}},"8689":{"*":{"id":"1132","name":"*","chunks":[],"async":false}},"9665":{"*":{"id":"6577","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}