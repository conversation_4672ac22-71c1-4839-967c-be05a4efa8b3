<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Админ-панель - Fibi Telecom</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
        }
    </style>
</head>
<body class="gradient-bg min-h-screen">
    <!-- Header -->
    <header class="bg-white shadow">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-6">
                <div class="flex items-center">
                    <i data-lucide="phone" class="h-8 w-8 text-red-600 mr-2"></i>
                    <span class="text-2xl font-bold text-gray-900">Fibi Telecom</span>
                    <span class="ml-2 px-2 py-1 text-xs font-semibold text-red-600 bg-red-100 rounded-full">ADMIN</span>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="#" class="text-blue-600 hover:text-blue-800 font-medium">Личный кабинет</a>
                    <span class="text-gray-700">Администратор</span>
                    <button class="flex items-center text-gray-600 hover:text-gray-800">
                        <i data-lucide="log-out" class="h-5 w-5 mr-1"></i>
                        Выйти
                    </button>
                </div>
            </div>
        </div>
    </header>

    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div class="px-4 py-6 sm:px-0">
            <div class="mb-8">
                <h1 class="text-3xl font-bold text-gray-900">Панель администратора</h1>
                <p class="mt-2 text-gray-600">Управление системой и мониторинг показателей</p>
            </div>

            <!-- Stats Cards -->
            <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8">
                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <i data-lucide="users" class="h-6 w-6 text-gray-400"></i>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">Всего пользователей</dt>
                                    <dd class="text-lg font-medium text-gray-900">1,247</dd>
                                </dl>
                            </div>
                        </div>
                        <div class="mt-4 flex items-center">
                            <i data-lucide="trending-up" class="h-4 w-4 text-green-500 mr-1"></i>
                            <span class="text-sm font-medium text-green-600">12.5%</span>
                            <span class="text-sm text-gray-500 ml-1">за месяц</span>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <i data-lucide="settings" class="h-6 w-6 text-gray-400"></i>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">Активные услуги</dt>
                                    <dd class="text-lg font-medium text-gray-900">3,891</dd>
                                </dl>
                            </div>
                        </div>
                        <div class="mt-4 flex items-center">
                            <i data-lucide="trending-up" class="h-4 w-4 text-green-500 mr-1"></i>
                            <span class="text-sm font-medium text-green-600">8.2%</span>
                            <span class="text-sm text-gray-500 ml-1">за месяц</span>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <i data-lucide="message-square" class="h-6 w-6 text-gray-400"></i>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">Заявки в обработке</dt>
                                    <dd class="text-lg font-medium text-gray-900">23</dd>
                                </dl>
                            </div>
                        </div>
                        <div class="mt-4 flex items-center">
                            <i data-lucide="trending-down" class="h-4 w-4 text-red-500 mr-1"></i>
                            <span class="text-sm font-medium text-red-600">2.1%</span>
                            <span class="text-sm text-gray-500 ml-1">за месяц</span>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow rounded-lg">
                    <div class="p-5">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <i data-lucide="bar-chart-3" class="h-6 w-6 text-gray-400"></i>
                            </div>
                            <div class="ml-5 w-0 flex-1">
                                <dl>
                                    <dt class="text-sm font-medium text-gray-500 truncate">Выручка за месяц</dt>
                                    <dd class="text-lg font-medium text-gray-900">2,450,000 ₽</dd>
                                </dl>
                            </div>
                        </div>
                        <div class="mt-4 flex items-center">
                            <i data-lucide="trending-up" class="h-4 w-4 text-green-500 mr-1"></i>
                            <span class="text-sm font-medium text-green-600">15.3%</span>
                            <span class="text-sm text-gray-500 ml-1">за месяц</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                <!-- Revenue Chart -->
                <div class="bg-white shadow rounded-lg p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Доходы за последние 30 дней</h3>
                    <canvas id="revenueChart" width="400" height="200"></canvas>
                </div>

                <!-- Users Chart -->
                <div class="bg-white shadow rounded-lg p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Новые пользователи</h3>
                    <canvas id="usersChart" width="400" height="200"></canvas>
                </div>
            </div>

            <!-- Management Sections -->
            <div class="grid grid-cols-1 gap-6 lg:grid-cols-2">
                <!-- User Management -->
                <div class="bg-white shadow rounded-lg">
                    <div class="px-4 py-5 sm:p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Управление пользователями</h3>
                        <div class="space-y-3">
                            <div class="block p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                                <div class="flex items-center">
                                    <i data-lucide="users" class="h-5 w-5 text-blue-600 mr-3"></i>
                                    <div>
                                        <p class="text-sm font-medium text-gray-900">Список пользователей</p>
                                        <p class="text-sm text-gray-500">Просмотр и управление аккаунтами</p>
                                    </div>
                                </div>
                            </div>
                            <div class="block p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                                <div class="flex items-center">
                                    <i data-lucide="shield" class="h-5 w-5 text-blue-600 mr-3"></i>
                                    <div>
                                        <p class="text-sm font-medium text-gray-900">Роли и права</p>
                                        <p class="text-sm text-gray-500">Настройка прав доступа</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Service Management -->
                <div class="bg-white shadow rounded-lg">
                    <div class="px-4 py-5 sm:p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Управление услугами</h3>
                        <div class="space-y-3">
                            <div class="block p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                                <div class="flex items-center">
                                    <i data-lucide="settings" class="h-5 w-5 text-blue-600 mr-3"></i>
                                    <div>
                                        <p class="text-sm font-medium text-gray-900">Конфигурация услуг</p>
                                        <p class="text-sm text-gray-500">Настройка тарифов и планов</p>
                                    </div>
                                </div>
                            </div>
                            <div class="block p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                                <div class="flex items-center">
                                    <i data-lucide="bar-chart-3" class="h-5 w-5 text-blue-600 mr-3"></i>
                                    <div>
                                        <p class="text-sm font-medium text-gray-900">Заказы и подключения</p>
                                        <p class="text-sm text-gray-500">Обработка новых заявок</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Support Management -->
                <div class="bg-white shadow rounded-lg">
                    <div class="px-4 py-5 sm:p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Поддержка клиентов</h3>
                        <div class="space-y-3">
                            <div class="block p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                                <div class="flex items-center">
                                    <i data-lucide="message-square" class="h-5 w-5 text-blue-600 mr-3"></i>
                                    <div>
                                        <p class="text-sm font-medium text-gray-900">Тикеты поддержки</p>
                                        <p class="text-sm text-gray-500">Обработка обращений клиентов</p>
                                    </div>
                                </div>
                            </div>
                            <div class="block p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                                <div class="flex items-center">
                                    <i data-lucide="message-square" class="h-5 w-5 text-blue-600 mr-3"></i>
                                    <div>
                                        <p class="text-sm font-medium text-gray-900">Обратная связь</p>
                                        <p class="text-sm text-gray-500">Отзывы и предложения</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- System Settings -->
                <div class="bg-white shadow rounded-lg">
                    <div class="px-4 py-5 sm:p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Системные настройки</h3>
                        <div class="space-y-3">
                            <div class="block p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                                <div class="flex items-center">
                                    <i data-lucide="settings" class="h-5 w-5 text-blue-600 mr-3"></i>
                                    <div>
                                        <p class="text-sm font-medium text-gray-900">Общие настройки</p>
                                        <p class="text-sm text-gray-500">Конфигурация системы</p>
                                    </div>
                                </div>
                            </div>
                            <div class="block p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                                <div class="flex items-center">
                                    <i data-lucide="bar-chart-3" class="h-5 w-5 text-blue-600 mr-3"></i>
                                    <div>
                                        <p class="text-sm font-medium text-gray-900">Аналитика</p>
                                        <p class="text-sm text-gray-500">Отчеты и статистика</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();

        // Revenue Chart
        const revenueCtx = document.getElementById('revenueChart').getContext('2d');
        new Chart(revenueCtx, {
            type: 'line',
            data: {
                labels: ['1 нед', '2 нед', '3 нед', '4 нед'],
                datasets: [{
                    label: 'Доходы (₽)',
                    data: [580000, 620000, 590000, 660000],
                    borderColor: 'rgb(59, 130, 246)',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return value.toLocaleString('ru-RU') + ' ₽';
                            }
                        }
                    }
                }
            }
        });

        // Users Chart
        const usersCtx = document.getElementById('usersChart').getContext('2d');
        new Chart(usersCtx, {
            type: 'bar',
            data: {
                labels: ['1 нед', '2 нед', '3 нед', '4 нед'],
                datasets: [{
                    label: 'Новые пользователи',
                    data: [45, 52, 38, 61],
                    backgroundColor: 'rgba(16, 185, 129, 0.8)',
                    borderColor: 'rgb(16, 185, 129)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        // Add click handlers for demo
        document.querySelectorAll('.cursor-pointer').forEach(element => {
            element.addEventListener('click', function() {
                alert('Функция будет доступна после полной настройки проекта!');
            });
        });
    </script>
</body>
</html>
