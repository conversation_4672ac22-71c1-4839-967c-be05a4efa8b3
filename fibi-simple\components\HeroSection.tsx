import Link from 'next/link'

export default function HeroSection() {
  return (
    <section className="py-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
            Современные телекоммуникационные решения
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            Fibi Telecom предоставляет высококачественные услуги связи для бизнеса и частных клиентов. 
            Надежность, скорость и инновации в каждом решении.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/auth/signup"
              className="bg-blue-600 text-white px-8 py-3 rounded-lg text-lg font-medium hover:bg-blue-700 transition-colors"
            >
              Начать сейчас
            </Link>
            <a
              href="#services"
              className="border border-blue-600 text-blue-600 px-8 py-3 rounded-lg text-lg font-medium hover:bg-blue-50 transition-colors"
            >
              Узнать больше
            </a>
          </div>
        </div>
      </div>
    </section>
  )
}
