# Push Notifications Web
[![Build Status](https://travis-ci.org/pusher/push-notifications-web.svg?branch=master)](https://travis-ci.org/pusher/push-notifications-web)
[![Twitter](https://img.shields.io/badge/<EMAIL>?style=flat)](http://twitter.com/Pusher)

This is the web SDK for the [Pusher Beams](https://pusher.com/beams) service.

## Getting started

You can find the getting started guide [here](https://pusher.com/docs/beams/getting-started/web/sdk-integration).

## Documentation

You can find our up-to-date documentation in [here](https://pusher.com/docs/beams/).

## Communication

- Found a bug? Please open an [issue](https://github.com/pusher/push-notifications-web/issues).
- Have a feature request. Please open an [issue](https://github.com/pusher/push-notifications-web/issues).
- If you want to contribute, please submit a [pull request](https://github.com/pusher/push-notifications-web/pulls) (preferably with some tests).

## Credits

Pusher Beams is owned and maintained by [<PERSON><PERSON><PERSON>](https://pusher.com).

## License

This library is released under the MIT license. See [LICENSE](https://github.com/pusher/push-notifications-web/blob/master/LICENSE) for details.
