name: test
on:
  push:
    branches:
      - '*'
  pull_request:
    branches:
      - master
jobs:
  run-web-tests:
    runs-on: ubuntu-latest
    env:
      CI: ${{ github.event_name == 'push' && 'basic' || 'full' }}
      MINIMAL_INTEGRATION_TESTS: ${{ github.event_name == 'push' && 'true' || '' }}
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          node-version: '16'
      - run: npm install
      - run: npm run check-format
      - run: make web_unit
      - name: make web_integration
        run: |
          cd integration_tests_server
          npm install
          node index.js&
          cd ..
          make web_integration
        env:
          INTEGRATION_TESTS_APP_MT1_APP_ID: ${{ secrets.INTEGRATION_TESTS_APP_MT1_APP_ID }}
          INTEGRATION_TESTS_APP_MT1_KEY: ${{ secrets.INTEGRATION_TESTS_APP_MT1_KEY }}
          INTEGRATION_TESTS_APP_MT1_SECRET: ${{ secrets.INTEGRATION_TESTS_APP_MT1_SECRET }}
          INTEGRATION_TESTS_APP_EU_APP_ID: ${{ secrets.INTEGRATION_TESTS_APP_EU_APP_ID }}
          INTEGRATION_TESTS_APP_EU_KEY: ${{ secrets.INTEGRATION_TESTS_APP_EU_KEY }}
          INTEGRATION_TESTS_APP_EU_SECRET: ${{ secrets.INTEGRATION_TESTS_APP_EU_SECRET }}
          BROWSER_STACK_ACCESS_KEY: ${{ secrets.BROWSER_STACK_ACCESS_KEY }}
          BROWSER_STACK_USERNAME: ${{ secrets.BROWSER_STACK_USERNAME }}
  run-node-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          node-version: '16'
      - run: npm install
      - run: npm run check-format
      - run: make node_unit
      - name: make node_integration
        run: |
          cd integration_tests_server
          npm install
          node index.js&
          cd ..
          make node_integration
        env:
          INTEGRATION_TESTS_APP_MT1_APP_ID: ${{ secrets.INTEGRATION_TESTS_APP_MT1_APP_ID }}
          INTEGRATION_TESTS_APP_MT1_KEY: ${{ secrets.INTEGRATION_TESTS_APP_MT1_KEY }}
          INTEGRATION_TESTS_APP_MT1_SECRET: ${{ secrets.INTEGRATION_TESTS_APP_MT1_SECRET }}
          INTEGRATION_TESTS_APP_EU_APP_ID: ${{ secrets.INTEGRATION_TESTS_APP_EU_APP_ID }}
          INTEGRATION_TESTS_APP_EU_KEY: ${{ secrets.INTEGRATION_TESTS_APP_EU_KEY }}
          INTEGRATION_TESTS_APP_EU_SECRET: ${{ secrets.INTEGRATION_TESTS_APP_EU_SECRET }}
