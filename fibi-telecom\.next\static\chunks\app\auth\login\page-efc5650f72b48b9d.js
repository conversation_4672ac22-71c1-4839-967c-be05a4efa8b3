(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[859],{232:(e,s,t)=>{Promise.resolve().then(t.bind(t,4177))},2099:(e,s,t)=>{"use strict";t.d(s,{N:()=>a});let a=(0,t(851).UU)("your_supabase_url_here","your_supabase_anon_key_here")},2657:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},2919:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},4177:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>h});var a=t(5155),r=t(2115),l=t(6874),i=t.n(l),d=t(5695),n=t(2099),c=t(9420),o=t(8883),u=t(2919),m=t(8749),x=t(2657);function h(){let[e,s]=(0,r.useState)(""),[t,l]=(0,r.useState)(""),[h,p]=(0,r.useState)(!1),[y,b]=(0,r.useState)(!1),[f,v]=(0,r.useState)(""),j=(0,d.useRouter)(),N=async s=>{s.preventDefault(),b(!0),v("");try{let{data:s,error:a}=await n.N.auth.signInWithPassword({email:e,password:t});if(a)return void v(a.message);if(s.user){let{data:e}=await n.N.from("profiles").select("role").eq("id",s.user.id).single();(null==e?void 0:e.role)==="admin"?j.push("/admin"):j.push("/dashboard")}}catch(e){v("Произошла ошибка при входе")}finally{b(!1)}};return(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"flex justify-center",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(c.A,{className:"h-12 w-12 text-blue-600 mr-2"}),(0,a.jsx)("span",{className:"text-3xl font-bold text-gray-900",children:"Fibi Telecom"})]})}),(0,a.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Войти в аккаунт"}),(0,a.jsxs)("p",{className:"mt-2 text-center text-sm text-gray-600",children:["Или"," ",(0,a.jsx)(i(),{href:"/auth/signup",className:"font-medium text-blue-600 hover:text-blue-500",children:"создать новый аккаунт"})]})]}),(0,a.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:N,children:[(0,a.jsxs)("div",{className:"rounded-md shadow-sm -space-y-px",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"email",className:"sr-only",children:"Email адрес"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,a.jsx)(o.A,{className:"h-5 w-5 text-gray-400"})}),(0,a.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,className:"appearance-none rounded-none relative block w-full px-3 py-2 pl-10 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm",placeholder:"Email адрес",value:e,onChange:e=>s(e.target.value)})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"password",className:"sr-only",children:"Пароль"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,a.jsx)(u.A,{className:"h-5 w-5 text-gray-400"})}),(0,a.jsx)("input",{id:"password",name:"password",type:h?"text":"password",autoComplete:"current-password",required:!0,className:"appearance-none rounded-none relative block w-full px-3 py-2 pl-10 pr-10 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm",placeholder:"Пароль",value:t,onChange:e=>l(e.target.value)}),(0,a.jsx)("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center",children:(0,a.jsx)("button",{type:"button",className:"text-gray-400 hover:text-gray-600",onClick:()=>p(!h),children:h?(0,a.jsx)(m.A,{className:"h-5 w-5"}):(0,a.jsx)(x.A,{className:"h-5 w-5"})})})]})]})]}),f&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded",children:f}),(0,a.jsx)("div",{className:"flex items-center justify-between",children:(0,a.jsx)("div",{className:"text-sm",children:(0,a.jsx)("a",{href:"#",className:"font-medium text-blue-600 hover:text-blue-500",children:"Забыли пароль?"})})}),(0,a.jsx)("div",{children:(0,a.jsx)("button",{type:"submit",disabled:y,className:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",children:y?"Вход...":"Войти"})}),(0,a.jsx)("div",{className:"text-center",children:(0,a.jsx)(i(),{href:"/",className:"font-medium text-blue-600 hover:text-blue-500",children:"← Вернуться на главную"})})]})]})})}},8749:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},8883:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(9946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])}},e=>{var s=s=>e(e.s=s);e.O(0,[664,874,441,684,358],()=>s(232)),_N_E=e.O()}]);