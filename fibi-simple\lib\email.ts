import nodemailer from 'nodemailer'

// Конфигурация транспорта email
const createTransporter = () => {
  return nodemailer.createTransporter({
    host: process.env.SMTP_HOST,
    port: parseInt(process.env.SMTP_PORT || '587'),
    secure: false, // true для 465, false для других портов
    auth: {
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASS,
    },
  })
}

// Типы для email
export interface EmailData {
  to: string | string[]
  subject: string
  html?: string
  text?: string
  from?: string
}

// Шаблоны email
export const emailTemplates = {
  welcome: (userName: string) => ({
    subject: 'Добро пожаловать в Fibi Telecom!',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #3B82F6, #1E40AF); padding: 40px; text-align: center;">
          <h1 style="color: white; margin: 0; font-size: 28px;">Добро пожаловать в Fibi Telecom!</h1>
        </div>
        <div style="padding: 40px; background: #f8fafc;">
          <h2 style="color: #1f2937;">Здравствуйте, ${userName}!</h2>
          <p style="color: #4b5563; line-height: 1.6;">
            Спасибо за регистрацию в Fibi Telecom. Мы рады приветствовать вас в нашей семье!
          </p>
          <p style="color: #4b5563; line-height: 1.6;">
            Теперь вы можете:
          </p>
          <ul style="color: #4b5563; line-height: 1.6;">
            <li>Управлять своими услугами в личном кабинете</li>
            <li>Оплачивать счета онлайн</li>
            <li>Получать поддержку через чат</li>
            <li>Следить за статистикой использования</li>
          </ul>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${process.env.NEXT_PUBLIC_APP_URL}/dashboard" 
               style="background: #3B82F6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
              Перейти в личный кабинет
            </a>
          </div>
        </div>
        <div style="padding: 20px; text-align: center; color: #6b7280; font-size: 14px;">
          <p>С уважением, команда Fibi Telecom</p>
        </div>
      </div>
    `
  }),

  paymentSuccess: (userName: string, serviceName: string, amount: number) => ({
    subject: 'Платеж успешно обработан',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #10B981, #059669); padding: 40px; text-align: center;">
          <h1 style="color: white; margin: 0; font-size: 28px;">Платеж обработан!</h1>
        </div>
        <div style="padding: 40px; background: #f8fafc;">
          <h2 style="color: #1f2937;">Здравствуйте, ${userName}!</h2>
          <p style="color: #4b5563; line-height: 1.6;">
            Ваш платеж успешно обработан. Услуга активирована.
          </p>
          <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="color: #1f2937; margin-top: 0;">Детали платежа:</h3>
            <p style="color: #4b5563; margin: 5px 0;"><strong>Услуга:</strong> ${serviceName}</p>
            <p style="color: #4b5563; margin: 5px 0;"><strong>Сумма:</strong> ${amount.toLocaleString('ru-RU')} ₽</p>
            <p style="color: #4b5563; margin: 5px 0;"><strong>Дата:</strong> ${new Date().toLocaleDateString('ru-RU')}</p>
          </div>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${process.env.NEXT_PUBLIC_APP_URL}/dashboard/billing" 
               style="background: #10B981; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
              Посмотреть историю платежей
            </a>
          </div>
        </div>
        <div style="padding: 20px; text-align: center; color: #6b7280; font-size: 14px;">
          <p>С уважением, команда Fibi Telecom</p>
        </div>
      </div>
    `
  }),

  supportTicket: (userName: string, ticketId: string, message: string) => ({
    subject: `Новое обращение в поддержку #${ticketId}`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #F59E0B, #D97706); padding: 40px; text-align: center;">
          <h1 style="color: white; margin: 0; font-size: 28px;">Обращение получено</h1>
        </div>
        <div style="padding: 40px; background: #f8fafc;">
          <h2 style="color: #1f2937;">Здравствуйте, ${userName}!</h2>
          <p style="color: #4b5563; line-height: 1.6;">
            Мы получили ваше обращение в службу поддержки. Наши специалисты рассмотрят его в ближайшее время.
          </p>
          <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="color: #1f2937; margin-top: 0;">Номер обращения: #${ticketId}</h3>
            <p style="color: #4b5563; line-height: 1.6;"><strong>Ваше сообщение:</strong></p>
            <p style="color: #4b5563; line-height: 1.6; font-style: italic;">"${message}"</p>
          </div>
          <p style="color: #4b5563; line-height: 1.6;">
            Среднее время ответа: 2-4 часа в рабочее время.
          </p>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${process.env.NEXT_PUBLIC_APP_URL}/dashboard/support" 
               style="background: #F59E0B; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
              Отследить обращение
            </a>
          </div>
        </div>
        <div style="padding: 20px; text-align: center; color: #6b7280; font-size: 14px;">
          <p>С уважением, команда поддержки Fibi Telecom</p>
        </div>
      </div>
    `
  }),

  newsletter: (userName: string, title: string, content: string) => ({
    subject: title,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #8B5CF6, #7C3AED); padding: 40px; text-align: center;">
          <h1 style="color: white; margin: 0; font-size: 28px;">${title}</h1>
        </div>
        <div style="padding: 40px; background: #f8fafc;">
          <h2 style="color: #1f2937;">Здравствуйте, ${userName}!</h2>
          <div style="color: #4b5563; line-height: 1.6;">
            ${content}
          </div>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${process.env.NEXT_PUBLIC_APP_URL}" 
               style="background: #8B5CF6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
              Посетить сайт
            </a>
          </div>
        </div>
        <div style="padding: 20px; text-align: center; color: #6b7280; font-size: 14px;">
          <p>С уважением, команда Fibi Telecom</p>
          <p>
            <a href="${process.env.NEXT_PUBLIC_APP_URL}/unsubscribe" style="color: #6b7280;">
              Отписаться от рассылки
            </a>
          </p>
        </div>
      </div>
    `
  })
}

// Отправка email
export const sendEmail = async (emailData: EmailData): Promise<boolean> => {
  try {
    const transporter = createTransporter()
    
    const mailOptions = {
      from: emailData.from || `"Fibi Telecom" <${process.env.SMTP_USER}>`,
      to: emailData.to,
      subject: emailData.subject,
      html: emailData.html,
      text: emailData.text,
    }

    const result = await transporter.sendMail(mailOptions)
    console.log('Email sent successfully:', result.messageId)
    return true
  } catch (error) {
    console.error('Error sending email:', error)
    return false
  }
}

// Массовая рассылка
export const sendBulkEmail = async (
  recipients: string[], 
  template: { subject: string; html: string }
): Promise<{ success: number; failed: number }> => {
  let success = 0
  let failed = 0

  for (const recipient of recipients) {
    const sent = await sendEmail({
      to: recipient,
      subject: template.subject,
      html: template.html
    })

    if (sent) {
      success++
    } else {
      failed++
    }

    // Небольшая задержка между отправками
    await new Promise(resolve => setTimeout(resolve, 100))
  }

  return { success, failed }
}
