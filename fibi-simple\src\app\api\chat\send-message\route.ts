import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase-server'
import { pusherServer, getChatChannelName, getAdminChannelName } from '@/lib/pusher'
import { z } from 'zod'

const messageSchema = z.object({
  content: z.string().min(1, 'Сообщение не может быть пустым').max(1000, 'Сообщение слишком длинное'),
  chatId: z.string().uuid()
})

export async function POST(request: NextRequest) {
  try {
    const supabase = createClient()
    
    // Проверяем авторизацию
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Необходима авторизация' },
        { status: 401 }
      )
    }

    // Получаем профиль пользователя
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('full_name, role')
      .eq('id', user.id)
      .single()

    if (profileError) {
      return NextResponse.json(
        { error: 'Профиль не найден' },
        { status: 404 }
      )
    }

    const body = await request.json()
    const { content, chatId } = messageSchema.parse(body)

    // Проверяем существование чата и права доступа
    const { data: chat, error: chatError } = await supabase
      .from('chat_rooms')
      .select('*')
      .eq('id', chatId)
      .single()

    if (chatError || !chat) {
      return NextResponse.json(
        { error: 'Чат не найден' },
        { status: 404 }
      )
    }

    // Проверяем права доступа к чату
    if (chat.user_id !== user.id && profile.role !== 'admin') {
      return NextResponse.json(
        { error: 'Нет доступа к этому чату' },
        { status: 403 }
      )
    }

    // Создаем сообщение
    const { data: message, error: messageError } = await supabase
      .from('chat_messages')
      .insert({
        content,
        user_id: user.id,
        user_name: profile.full_name || user.email || 'Пользователь',
        user_role: profile.role,
        chat_id: chatId
      })
      .select()
      .single()

    if (messageError) {
      console.error('Error creating message:', messageError)
      return NextResponse.json(
        { error: 'Ошибка создания сообщения' },
        { status: 500 }
      )
    }

    // Обновляем время последней активности чата
    await supabase
      .from('chat_rooms')
      .update({ updated_at: new Date().toISOString() })
      .eq('id', chatId)

    // Отправляем сообщение через Pusher
    const channelName = getChatChannelName(chatId)
    await pusherServer.trigger(channelName, 'new-message', message)

    // Если сообщение от пользователя, уведомляем админов
    if (profile.role === 'user') {
      await pusherServer.trigger(getAdminChannelName(), 'new-user-message', {
        chatId,
        message,
        userName: profile.full_name || user.email
      })
    }

    return NextResponse.json({ message })

  } catch (error) {
    console.error('Chat message error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Неверные данные', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Ошибка отправки сообщения' },
      { status: 500 }
    )
  }
}
