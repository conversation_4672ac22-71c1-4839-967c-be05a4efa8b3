# Fibi Telecom - Лендинг с авторизацией и админ-панелью

Современный веб-сайт телекоммуникационной компании с системой авторизации и административной панелью.

## Технологии

- **Next.js 15** - React фреймворк с App Router
- **TypeScript** - Типизированный JavaScript
- **Tailwind CSS** - Utility-first CSS фреймворк
- **Supabase** - Backend-as-a-Service для аутентификации и базы данных
- **Lucide React** - Иконки

## Функциональность

### Лендинг страница
- Современный дизайн с градиентами
- Адаптивная верстка
- Секции: Hero, Услуги, Преимущества, Контакты
- Навигация к страницам авторизации

### Система авторизации
- Регистрация новых пользователей
- Вход в систему
- Защищенные роуты
- Ролевая модель (user/admin)

### Пользовательский дашборд
- Личный кабинет пользователя
- Просмотр подключенных услуг
- Управление профилем
- Статистика использования

### Админ-панель
- Управление пользователями
- Изменение ролей пользователей
- Статистика системы
- Защищенный доступ только для администраторов

## Установка и настройка

### 1. Клонирование и установка зависимостей

```bash
# Установка зависимостей
npm install
```

### 2. Настройка Supabase

1. Создайте проект на [supabase.com](https://supabase.com)
2. Перейдите в Settings > API
3. Скопируйте URL и anon key

### 3. Настройка переменных окружения

Отредактируйте файл `.env.local`:

```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

### 4. Настройка базы данных

1. Откройте SQL Editor в Supabase Dashboard
2. Выполните SQL скрипт из файла `supabase-setup.sql`
3. Это создаст необходимые таблицы и настройки безопасности

### 5. Создание первого администратора

1. Зарегистрируйтесь через интерфейс приложения
2. В Supabase Dashboard откройте Table Editor > profiles
3. Найдите свою запись и измените поле `role` на `admin`

### 6. Запуск приложения

```bash
npm run dev
```

Приложение будет доступно по адресу [http://localhost:3000](http://localhost:3000)

## Структура проекта

```
fibi-telecom/
├── app/
│   ├── auth/
│   │   ├── login/page.tsx      # Страница входа
│   │   └── signup/page.tsx     # Страница регистрации
│   ├── dashboard/page.tsx      # Пользовательский дашборд
│   ├── admin/page.tsx          # Админ-панель
│   ├── layout.tsx              # Основной layout
│   ├── page.tsx                # Лендинг страница
│   └── globals.css             # Глобальные стили
├── lib/
│   └── supabase.ts             # Конфигурация Supabase
├── hooks/
│   └── useAuth.ts              # Хук для аутентификации
├── middleware.ts               # Middleware для защиты роутов
├── supabase-setup.sql          # SQL скрипт для настройки БД
└── README.md                   # Документация
```

## Основные компоненты

### useAuth Hook
Предоставляет:
- `user` - текущий пользователь
- `profile` - профиль пользователя с ролью
- `loading` - состояние загрузки
- `signOut` - функция выхода
- `isAdmin` - проверка роли администратора

### Middleware
Защищает роуты:
- `/dashboard/*` - только для авторизованных пользователей
- `/admin/*` - только для администраторов
- `/auth/*` - перенаправляет авторизованных пользователей

### Безопасность
- Row Level Security (RLS) в Supabase
- Политики доступа к данным
- Защищенные API роуты
- Валидация ролей на уровне базы данных

## Развертывание

### Vercel (рекомендуется)
1. Подключите репозиторий к Vercel
2. Добавьте переменные окружения в настройках проекта
3. Разверните приложение

### Другие платформы
Приложение совместимо с любыми платформами, поддерживающими Next.js:
- Netlify
- Railway
- DigitalOcean App Platform

## Дальнейшее развитие

Возможные улучшения:
- Система платежей
- Email уведомления
- Детальная аналитика
- API для мобильного приложения
- Система тикетов поддержки
- Многоязычность

## Поддержка

При возникновении вопросов:
1. Проверьте настройки Supabase
2. Убедитесь, что все переменные окружения настроены
3. Проверьте консоль браузера на наличие ошибок

## Лицензия

MIT License
