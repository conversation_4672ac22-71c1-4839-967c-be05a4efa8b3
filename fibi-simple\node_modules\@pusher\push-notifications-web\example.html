<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <title>Example</title>
  </head>
  <body>
    <script src="./dist/push-notifications-cdn.js"></script>
    <script>
      let tokenProvider = new PusherPushNotifications.TokenProvider({
        url: 'https://deadc0de-beams-test-server.herokuapp.com/auth',
      });

      let beamsClient;
      PusherPushNotifications.init({
        instanceId: 'deadc0de-2ce6-46e3-ad9a-5c02d0ab119b',
      })
        .then(c => {
          beamsClient = c;
        })
        .catch(console.error);
    </script>
  </body>
</html>
