/*!
 * Pusher JavaScript Library v8.3.0
 * https://pusher.com/
 *
 * Copyright 2020, <PERSON><PERSON><PERSON>
 * Released under the MIT licence.
 */
module.exports=function(t){var e={};function n(r){if(e[r])return e[r].exports;var i=e[r]={i:r,l:!1,exports:{}};return t[r].call(i.exports,i,i.exports,n),i.l=!0,i.exports}return n.m=t,n.c=e,n.d=function(t,e,r){n.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:r})},n.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},n.t=function(t,e){if(1&e&&(t=n(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var i in t)n.d(r,i,function(e){return t[e]}.bind(null,i));return r},n.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return n.d(e,"a",e),e},n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},n.p="",n(n.s=15)}([function(t,e,n){"use strict";(function(t){n.d(e,"f",(function(){return s})),n.d(e,"m",(function(){return o})),n.d(e,"d",(function(){return a})),n.d(e,"k",(function(){return h})),n.d(e,"i",(function(){return c})),n.d(e,"n",(function(){return u})),n.d(e,"c",(function(){return l})),n.d(e,"j",(function(){return d})),n.d(e,"g",(function(){return f})),n.d(e,"h",(function(){return p})),n.d(e,"b",(function(){return g})),n.d(e,"a",(function(){return b})),n.d(e,"e",(function(){return y})),n.d(e,"l",(function(){return m}));var r=n(11),i=n(2);function s(t,...e){for(var n=0;n<e.length;n++){var r=e[n];for(var i in r)r[i]&&r[i].constructor&&r[i].constructor===Object?t[i]=s(t[i]||{},r[i]):t[i]=r[i]}return t}function o(){for(var t=["Pusher"],e=0;e<arguments.length;e++)"string"==typeof arguments[e]?t.push(arguments[e]):t.push(m(arguments[e]));return t.join(" : ")}function a(t,e){var n=Array.prototype.indexOf;if(null===t)return-1;if(n&&t.indexOf===n)return t.indexOf(e);for(var r=0,i=t.length;r<i;r++)if(t[r]===e)return r;return-1}function h(t,e){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&e(t[n],n,t)}function c(t){var e=[];return h(t,(function(t,n){e.push(n)})),e}function u(t){var e=[];return h(t,(function(t){e.push(t)})),e}function l(e,n,r){for(var i=0;i<e.length;i++)n.call(r||t,e[i],i,e)}function d(t,e){for(var n=[],r=0;r<t.length;r++)n.push(e(t[r],r,t,n));return n}function f(t,e){e=e||function(t){return!!t};for(var n=[],r=0;r<t.length;r++)e(t[r],r,t,n)&&n.push(t[r]);return n}function p(t,e){var n={};return h(t,(function(r,i){(e&&e(r,i,t,n)||Boolean(r))&&(n[i]=r)})),n}function g(t,e){for(var n=0;n<t.length;n++)if(e(t[n],n,t))return!0;return!1}function b(t,e){for(var n=0;n<t.length;n++)if(!e(t[n],n,t))return!1;return!0}function v(t){return e=function(t){return"object"==typeof t&&(t=m(t)),encodeURIComponent(Object(r.a)(t.toString()))},n={},h(t,(function(t,r){n[r]=e(t)})),n;var e,n}function y(t){var e,n,r=p(t,(function(t){return void 0!==t}));return d((e=v(r),n=[],h(e,(function(t,e){n.push([e,t])})),n),i.a.method("join","=")).join("&")}function m(t){try{return JSON.stringify(t)}catch(r){return JSON.stringify((e=[],n=[],function t(r,i){var s,o,a;switch(typeof r){case"object":if(!r)return null;for(s=0;s<e.length;s+=1)if(e[s]===r)return{$ref:n[s]};if(e.push(r),n.push(i),"[object Array]"===Object.prototype.toString.apply(r))for(a=[],s=0;s<r.length;s+=1)a[s]=t(r[s],i+"["+s+"]");else for(o in a={},r)Object.prototype.hasOwnProperty.call(r,o)&&(a[o]=t(r[o],i+"["+JSON.stringify(o)+"]"));return a;case"number":case"string":case"boolean":return r}}(t,"$")))}var e,n}}).call(this,n(6))},function(t,e,n){"use strict";(function(t){var r=n(0),i=n(5);e.a=new class{constructor(){this.globalLog=e=>{t.console&&t.console.log&&t.console.log(e)}}debug(...t){this.log(this.globalLog,t)}warn(...t){this.log(this.globalLogWarn,t)}error(...t){this.log(this.globalLogError,t)}globalLogWarn(e){t.console&&t.console.warn?t.console.warn(e):this.globalLog(e)}globalLogError(e){t.console&&t.console.error?t.console.error(e):this.globalLogWarn(e)}log(t,...e){var n=r.m.apply(this,arguments);if(i.a.log)i.a.log(n);else if(i.a.logToConsole){t.bind(this)(n)}}}}).call(this,n(6))},function(t,e,n){"use strict";var r=n(4),i={now:()=>Date.now?Date.now():(new Date).valueOf(),defer:t=>new r.a(0,t),method(t,...e){var n=Array.prototype.slice.call(arguments,1);return function(e){return e[t].apply(e,n.concat(arguments))}}};e.a=i},function(t,e,n){"use strict";(function(t){n.d(e,"a",(function(){return s}));var r=n(0),i=n(12);class s{constructor(t){this.callbacks=new i.a,this.global_callbacks=[],this.failThrough=t}bind(t,e,n){return this.callbacks.add(t,e,n),this}bind_global(t){return this.global_callbacks.push(t),this}unbind(t,e,n){return this.callbacks.remove(t,e,n),this}unbind_global(t){return t?(this.global_callbacks=r.g(this.global_callbacks||[],e=>e!==t),this):(this.global_callbacks=[],this)}unbind_all(){return this.unbind(),this.unbind_global(),this}emit(e,n,r){for(var i=0;i<this.global_callbacks.length;i++)this.global_callbacks[i](e,n);var s=this.callbacks.get(e),o=[];if(r?o.push(n,r):n&&o.push(n),s&&s.length>0)for(i=0;i<s.length;i++)s[i].fn.apply(s[i].context||t,o);else this.failThrough&&this.failThrough(e,n);return this}}}).call(this,n(6))},function(t,e,n){"use strict";(function(t){n.d(e,"a",(function(){return o})),n.d(e,"b",(function(){return a}));var r=n(9);function i(e){t.clearTimeout(e)}function s(e){t.clearInterval(e)}class o extends r.a{constructor(t,e){super(setTimeout,i,t,(function(t){return e(),null}))}}class a extends r.a{constructor(t,e){super(setInterval,s,t,(function(t){return e(),t}))}}}).call(this,n(6))},function(t,e,n){"use strict";var r=n(0),i={VERSION:"8.3.0",PROTOCOL:7,wsPort:80,wssPort:443,wsPath:"",httpHost:"sockjs.pusher.com",httpPort:80,httpsPort:443,httpPath:"/pusher",stats_host:"stats.pusher.com",authEndpoint:"/pusher/auth",authTransport:"ajax",activityTimeout:12e4,pongTimeout:3e4,unavailableTimeout:1e4,userAuthentication:{endpoint:"/pusher/user-auth",transport:"ajax"},channelAuthorization:{endpoint:"/pusher/auth",transport:"ajax"},cdn_http:"http://js.pusher.com",cdn_https:"https://js.pusher.com",dependency_suffix:""};function s(t,e,n){return t+(e.useTLS?"s":"")+"://"+(e.useTLS?e.hostTLS:e.hostNonTLS)+n}function o(t,e){return"/app/"+t+("?protocol="+i.PROTOCOL+"&client=js&version="+i.VERSION+(e?"&"+e:""))}var a={getInitial:function(t,e){return s("ws",e,(e.httpPath||"")+o(t,"flash=false"))}},h={getInitial:function(t,e){return s("http",e,(e.httpPath||"/pusher")+o(t))}},c=n(2),u=n(3),l=n(1);class d extends u.a{constructor(t,e,n,r,i){super(),this.initialize=zt.transportConnectionInitializer,this.hooks=t,this.name=e,this.priority=n,this.key=r,this.options=i,this.state="new",this.timeline=i.timeline,this.activityTimeout=i.activityTimeout,this.id=this.timeline.generateUniqueID()}handlesActivityChecks(){return Boolean(this.hooks.handlesActivityChecks)}supportsPing(){return Boolean(this.hooks.supportsPing)}connect(){if(this.socket||"initialized"!==this.state)return!1;var t=this.hooks.urls.getInitial(this.key,this.options);try{this.socket=this.hooks.getSocket(t,this.options)}catch(t){return c.a.defer(()=>{this.onError(t),this.changeState("closed")}),!1}return this.bindListeners(),l.a.debug("Connecting",{transport:this.name,url:t}),this.changeState("connecting"),!0}close(){return!!this.socket&&(this.socket.close(),!0)}send(t){return"open"===this.state&&(c.a.defer(()=>{this.socket&&this.socket.send(t)}),!0)}ping(){"open"===this.state&&this.supportsPing()&&this.socket.ping()}onOpen(){this.hooks.beforeOpen&&this.hooks.beforeOpen(this.socket,this.hooks.urls.getPath(this.key,this.options)),this.changeState("open"),this.socket.onopen=void 0}onError(t){this.emit("error",{type:"WebSocketError",error:t}),this.timeline.error(this.buildTimelineMessage({error:t.toString()}))}onClose(t){t?this.changeState("closed",{code:t.code,reason:t.reason,wasClean:t.wasClean}):this.changeState("closed"),this.unbindListeners(),this.socket=void 0}onMessage(t){this.emit("message",t)}onActivity(){this.emit("activity")}bindListeners(){this.socket.onopen=()=>{this.onOpen()},this.socket.onerror=t=>{this.onError(t)},this.socket.onclose=t=>{this.onClose(t)},this.socket.onmessage=t=>{this.onMessage(t)},this.supportsPing()&&(this.socket.onactivity=()=>{this.onActivity()})}unbindListeners(){this.socket&&(this.socket.onopen=void 0,this.socket.onerror=void 0,this.socket.onclose=void 0,this.socket.onmessage=void 0,this.supportsPing()&&(this.socket.onactivity=void 0))}changeState(t,e){this.state=t,this.timeline.info(this.buildTimelineMessage({state:t,params:e})),this.emit(t,e)}buildTimelineMessage(t){return r.f({cid:this.id},t)}}class f{constructor(t){this.hooks=t}isSupported(t){return this.hooks.isSupported(t)}createConnection(t,e,n,r){return new d(this.hooks,t,e,n,r)}}var p=new f({urls:a,handlesActivityChecks:!1,supportsPing:!1,isInitialized:function(){return Boolean(zt.getWebSocketAPI())},isSupported:function(){return Boolean(zt.getWebSocketAPI())},getSocket:function(t){return zt.createWebSocket(t)}}),g={urls:h,handlesActivityChecks:!1,supportsPing:!0,isInitialized:function(){return!0}},b=r.f({getSocket:function(t){return zt.HTTPFactory.createStreamingSocket(t)}},g),v=r.f({getSocket:function(t){return zt.HTTPFactory.createPollingSocket(t)}},g),y={isSupported:function(){return zt.isXHRSupported()}},m={ws:p,xhr_streaming:new f(r.f({},b,y)),xhr_polling:new f(r.f({},v,y))};class w{constructor(t,e,n){this.manager=t,this.transport=e,this.minPingDelay=n.minPingDelay,this.maxPingDelay=n.maxPingDelay,this.pingDelay=void 0}createConnection(t,e,n,i){i=r.f({},i,{activityTimeout:this.pingDelay});var s=this.transport.createConnection(t,e,n,i),o=null,a=function(){s.unbind("open",a),s.bind("closed",h),o=c.a.now()},h=t=>{if(s.unbind("closed",h),1002===t.code||1003===t.code)this.manager.reportDeath();else if(!t.wasClean&&o){var e=c.a.now()-o;e<2*this.maxPingDelay&&(this.manager.reportDeath(),this.pingDelay=Math.max(e/2,this.minPingDelay))}};return s.bind("open",a),s}isSupported(t){return this.manager.isAlive()&&this.transport.isSupported(t)}}const _={decodeMessage:function(t){try{var e=JSON.parse(t.data),n=e.data;if("string"==typeof n)try{n=JSON.parse(e.data)}catch(t){}var r={event:e.event,channel:e.channel,data:n};return e.user_id&&(r.user_id=e.user_id),r}catch(e){throw{type:"MessageParseError",error:e,data:t.data}}},encodeMessage:function(t){return JSON.stringify(t)},processHandshake:function(t){var e=_.decodeMessage(t);if("pusher:connection_established"===e.event){if(!e.data.activity_timeout)throw"No activity timeout specified in handshake";return{action:"connected",id:e.data.socket_id,activityTimeout:1e3*e.data.activity_timeout}}if("pusher:error"===e.event)return{action:this.getCloseAction(e.data),error:this.getCloseError(e.data)};throw"Invalid handshake"},getCloseAction:function(t){return t.code<4e3?t.code>=1002&&t.code<=1004?"backoff":null:4e3===t.code?"tls_only":t.code<4100?"refused":t.code<4200?"backoff":t.code<4300?"retry":"refused"},getCloseError:function(t){return 1e3!==t.code&&1001!==t.code?{type:"PusherError",data:{code:t.code,message:t.reason||t.message}}:null}};var S=_;class k extends u.a{constructor(t,e){super(),this.id=t,this.transport=e,this.activityTimeout=e.activityTimeout,this.bindListeners()}handlesActivityChecks(){return this.transport.handlesActivityChecks()}send(t){return this.transport.send(t)}send_event(t,e,n){var r={event:t,data:e};return n&&(r.channel=n),l.a.debug("Event sent",r),this.send(S.encodeMessage(r))}ping(){this.transport.supportsPing()?this.transport.ping():this.send_event("pusher:ping",{})}close(){this.transport.close()}bindListeners(){var t={message:t=>{var e;try{e=S.decodeMessage(t)}catch(e){this.emit("error",{type:"MessageParseError",error:e,data:t.data})}if(void 0!==e){switch(l.a.debug("Event recd",e),e.event){case"pusher:error":this.emit("error",{type:"PusherError",data:e.data});break;case"pusher:ping":this.emit("ping");break;case"pusher:pong":this.emit("pong")}this.emit("message",e)}},activity:()=>{this.emit("activity")},error:t=>{this.emit("error",t)},closed:t=>{e(),t&&t.code&&this.handleCloseEvent(t),this.transport=null,this.emit("closed")}},e=()=>{r.k(t,(t,e)=>{this.transport.unbind(e,t)})};r.k(t,(t,e)=>{this.transport.bind(e,t)})}handleCloseEvent(t){var e=S.getCloseAction(t),n=S.getCloseError(t);n&&this.emit("error",n),e&&this.emit(e,{action:e,error:n})}}class C{constructor(t,e){this.transport=t,this.callback=e,this.bindListeners()}close(){this.unbindListeners(),this.transport.close()}bindListeners(){this.onMessage=t=>{var e;this.unbindListeners();try{e=S.processHandshake(t)}catch(t){return this.finish("error",{error:t}),void this.transport.close()}"connected"===e.action?this.finish("connected",{connection:new k(e.id,this.transport),activityTimeout:e.activityTimeout}):(this.finish(e.action,{error:e.error}),this.transport.close())},this.onClosed=t=>{this.unbindListeners();var e=S.getCloseAction(t)||"backoff",n=S.getCloseError(t);this.finish(e,{error:n})},this.transport.bind("message",this.onMessage),this.transport.bind("closed",this.onClosed)}unbindListeners(){this.transport.unbind("message",this.onMessage),this.transport.unbind("closed",this.onClosed)}finish(t,e){this.callback(r.f({transport:this.transport,action:t},e))}}class T{constructor(t,e){this.timeline=t,this.options=e||{}}send(t,e){this.timeline.isEmpty()||this.timeline.send(zt.TimelineTransport.getAgent(this,t),e)}}class E extends Error{constructor(t){super(t),Object.setPrototypeOf(this,new.target.prototype)}}class A extends Error{constructor(t){super(t),Object.setPrototypeOf(this,new.target.prototype)}}Error;class P extends Error{constructor(t){super(t),Object.setPrototypeOf(this,new.target.prototype)}}class x extends Error{constructor(t){super(t),Object.setPrototypeOf(this,new.target.prototype)}}class O extends Error{constructor(t){super(t),Object.setPrototypeOf(this,new.target.prototype)}}class L extends Error{constructor(t){super(t),Object.setPrototypeOf(this,new.target.prototype)}}class U extends Error{constructor(t){super(t),Object.setPrototypeOf(this,new.target.prototype)}}class R extends Error{constructor(t,e){super(e),this.status=t,Object.setPrototypeOf(this,new.target.prototype)}}const M={baseUrl:"https://pusher.com",urls:{authenticationEndpoint:{path:"/docs/channels/server_api/authenticating_users"},authorizationEndpoint:{path:"/docs/channels/server_api/authorizing-users/"},javascriptQuickStart:{path:"/docs/javascript_quick_start"},triggeringClientEvents:{path:"/docs/client_api_guide/client_events#trigger-events"},encryptedChannelSupport:{fullUrl:"https://github.com/pusher/pusher-js/tree/cc491015371a4bde5743d1c87a0fbac0feb53195#encrypted-channel-support"}}};var I=function(t){const e=M.urls[t];if(!e)return"";let n;return e.fullUrl?n=e.fullUrl:e.path&&(n=M.baseUrl+e.path),n?"See: "+n:""};class N extends u.a{constructor(t,e){super((function(e,n){l.a.debug("No callbacks on "+t+" for "+e)})),this.name=t,this.pusher=e,this.subscribed=!1,this.subscriptionPending=!1,this.subscriptionCancelled=!1}authorize(t,e){return e(null,{auth:""})}trigger(t,e){if(0!==t.indexOf("client-"))throw new E("Event '"+t+"' does not start with 'client-'");if(!this.subscribed){var n=I("triggeringClientEvents");l.a.warn("Client event triggered before channel 'subscription_succeeded' event . "+n)}return this.pusher.send_event(t,e,this.name)}disconnect(){this.subscribed=!1,this.subscriptionPending=!1}handleEvent(t){var e=t.event,n=t.data;if("pusher_internal:subscription_succeeded"===e)this.handleSubscriptionSucceededEvent(t);else if("pusher_internal:subscription_count"===e)this.handleSubscriptionCountEvent(t);else if(0!==e.indexOf("pusher_internal:")){this.emit(e,n,{})}}handleSubscriptionSucceededEvent(t){this.subscriptionPending=!1,this.subscribed=!0,this.subscriptionCancelled?this.pusher.unsubscribe(this.name):this.emit("pusher:subscription_succeeded",t.data)}handleSubscriptionCountEvent(t){t.data.subscription_count&&(this.subscriptionCount=t.data.subscription_count),this.emit("pusher:subscription_count",t.data)}subscribe(){this.subscribed||(this.subscriptionPending=!0,this.subscriptionCancelled=!1,this.authorize(this.pusher.connection.socket_id,(t,e)=>{t?(this.subscriptionPending=!1,l.a.error(t.toString()),this.emit("pusher:subscription_error",Object.assign({},{type:"AuthError",error:t.message},t instanceof R?{status:t.status}:{}))):this.pusher.send_event("pusher:subscribe",{auth:e.auth,channel_data:e.channel_data,channel:this.name})}))}unsubscribe(){this.subscribed=!1,this.pusher.send_event("pusher:unsubscribe",{channel:this.name})}cancelSubscription(){this.subscriptionCancelled=!0}reinstateSubscription(){this.subscriptionCancelled=!1}}class j extends N{authorize(t,e){return this.pusher.config.channelAuthorizer({channelName:this.name,socketId:t},e)}}class D{constructor(){this.reset()}get(t){return Object.prototype.hasOwnProperty.call(this.members,t)?{id:t,info:this.members[t]}:null}each(t){r.k(this.members,(e,n)=>{t(this.get(n))})}setMyID(t){this.myID=t}onSubscription(t){this.members=t.presence.hash,this.count=t.presence.count,this.me=this.get(this.myID)}addMember(t){return null===this.get(t.user_id)&&this.count++,this.members[t.user_id]=t.user_info,this.get(t.user_id)}removeMember(t){var e=this.get(t.user_id);return e&&(delete this.members[t.user_id],this.count--),e}reset(){this.members={},this.count=0,this.myID=null,this.me=null}}var z=function(t,e,n,r){return new(n||(n=Promise))((function(i,s){function o(t){try{h(r.next(t))}catch(t){s(t)}}function a(t){try{h(r.throw(t))}catch(t){s(t)}}function h(t){var e;t.done?i(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(o,a)}h((r=r.apply(t,e||[])).next())}))};class H extends j{constructor(t,e){super(t,e),this.members=new D}authorize(t,e){super.authorize(t,(t,n)=>z(this,void 0,void 0,(function*(){if(!t)if(null!=(n=n).channel_data){var r=JSON.parse(n.channel_data);this.members.setMyID(r.user_id)}else{if(yield this.pusher.user.signinDonePromise,null==this.pusher.user.user_data){let t=I("authorizationEndpoint");return l.a.error(`Invalid auth response for channel '${this.name}', expected 'channel_data' field. ${t}, or the user should be signed in.`),void e("Invalid auth response")}this.members.setMyID(this.pusher.user.user_data.id)}e(t,n)})))}handleEvent(t){var e=t.event;if(0===e.indexOf("pusher_internal:"))this.handleInternalEvent(t);else{var n=t.data,r={};t.user_id&&(r.user_id=t.user_id),this.emit(e,n,r)}}handleInternalEvent(t){var e=t.event,n=t.data;switch(e){case"pusher_internal:subscription_succeeded":this.handleSubscriptionSucceededEvent(t);break;case"pusher_internal:subscription_count":this.handleSubscriptionCountEvent(t);break;case"pusher_internal:member_added":var r=this.members.addMember(n);this.emit("pusher:member_added",r);break;case"pusher_internal:member_removed":var i=this.members.removeMember(n);i&&this.emit("pusher:member_removed",i)}}handleSubscriptionSucceededEvent(t){this.subscriptionPending=!1,this.subscribed=!0,this.subscriptionCancelled?this.pusher.unsubscribe(this.name):(this.members.onSubscription(t.data),this.emit("pusher:subscription_succeeded",this.members))}disconnect(){this.members.reset(),super.disconnect()}}var B=n(13),F=n(8);class q extends j{constructor(t,e,n){super(t,e),this.key=null,this.nacl=n}authorize(t,e){super.authorize(t,(t,n)=>{if(t)return void e(t,n);let r=n.shared_secret;r?(this.key=Object(F.decode)(r),delete n.shared_secret,e(null,n)):e(new Error("No shared_secret key in auth payload for encrypted channel: "+this.name),null)})}trigger(t,e){throw new O("Client events are not currently supported for encrypted channels")}handleEvent(t){var e=t.event,n=t.data;0!==e.indexOf("pusher_internal:")&&0!==e.indexOf("pusher:")?this.handleEncryptedEvent(e,n):super.handleEvent(t)}handleEncryptedEvent(t,e){if(!this.key)return void l.a.debug("Received encrypted event before key has been retrieved from the authEndpoint");if(!e.ciphertext||!e.nonce)return void l.a.error("Unexpected format for encrypted event, expected object with `ciphertext` and `nonce` fields, got: "+e);let n=Object(F.decode)(e.ciphertext);if(n.length<this.nacl.secretbox.overheadLength)return void l.a.error(`Expected encrypted event ciphertext length to be ${this.nacl.secretbox.overheadLength}, got: ${n.length}`);let r=Object(F.decode)(e.nonce);if(r.length<this.nacl.secretbox.nonceLength)return void l.a.error(`Expected encrypted event nonce length to be ${this.nacl.secretbox.nonceLength}, got: ${r.length}`);let i=this.nacl.secretbox.open(n,r,this.key);if(null===i)return l.a.debug("Failed to decrypt an event, probably because it was encrypted with a different key. Fetching a new key from the authEndpoint..."),void this.authorize(this.pusher.connection.socket_id,(e,s)=>{e?l.a.error(`Failed to make a request to the authEndpoint: ${s}. Unable to fetch new key, so dropping encrypted event`):(i=this.nacl.secretbox.open(n,r,this.key),null!==i?this.emit(t,this.getDataToEmit(i)):l.a.error("Failed to decrypt event with new key. Dropping encrypted event"))});this.emit(t,this.getDataToEmit(i))}getDataToEmit(t){let e=Object(B.decode)(t);try{return JSON.parse(e)}catch(t){return e}}}var Y=n(4);class K extends u.a{constructor(t,e){super(),this.state="initialized",this.connection=null,this.key=t,this.options=e,this.timeline=this.options.timeline,this.usingTLS=this.options.useTLS,this.errorCallbacks=this.buildErrorCallbacks(),this.connectionCallbacks=this.buildConnectionCallbacks(this.errorCallbacks),this.handshakeCallbacks=this.buildHandshakeCallbacks(this.errorCallbacks);var n=zt.getNetwork();n.bind("online",()=>{this.timeline.info({netinfo:"online"}),"connecting"!==this.state&&"unavailable"!==this.state||this.retryIn(0)}),n.bind("offline",()=>{this.timeline.info({netinfo:"offline"}),this.connection&&this.sendActivityCheck()}),this.updateStrategy()}connect(){this.connection||this.runner||(this.strategy.isSupported()?(this.updateState("connecting"),this.startConnecting(),this.setUnavailableTimer()):this.updateState("failed"))}send(t){return!!this.connection&&this.connection.send(t)}send_event(t,e,n){return!!this.connection&&this.connection.send_event(t,e,n)}disconnect(){this.disconnectInternally(),this.updateState("disconnected")}isUsingTLS(){return this.usingTLS}startConnecting(){var t=(e,n)=>{e?this.runner=this.strategy.connect(0,t):"error"===n.action?(this.emit("error",{type:"HandshakeError",error:n.error}),this.timeline.error({handshakeError:n.error})):(this.abortConnecting(),this.handshakeCallbacks[n.action](n))};this.runner=this.strategy.connect(0,t)}abortConnecting(){this.runner&&(this.runner.abort(),this.runner=null)}disconnectInternally(){(this.abortConnecting(),this.clearRetryTimer(),this.clearUnavailableTimer(),this.connection)&&this.abandonConnection().close()}updateStrategy(){this.strategy=this.options.getStrategy({key:this.key,timeline:this.timeline,useTLS:this.usingTLS})}retryIn(t){this.timeline.info({action:"retry",delay:t}),t>0&&this.emit("connecting_in",Math.round(t/1e3)),this.retryTimer=new Y.a(t||0,()=>{this.disconnectInternally(),this.connect()})}clearRetryTimer(){this.retryTimer&&(this.retryTimer.ensureAborted(),this.retryTimer=null)}setUnavailableTimer(){this.unavailableTimer=new Y.a(this.options.unavailableTimeout,()=>{this.updateState("unavailable")})}clearUnavailableTimer(){this.unavailableTimer&&this.unavailableTimer.ensureAborted()}sendActivityCheck(){this.stopActivityCheck(),this.connection.ping(),this.activityTimer=new Y.a(this.options.pongTimeout,()=>{this.timeline.error({pong_timed_out:this.options.pongTimeout}),this.retryIn(0)})}resetActivityCheck(){this.stopActivityCheck(),this.connection&&!this.connection.handlesActivityChecks()&&(this.activityTimer=new Y.a(this.activityTimeout,()=>{this.sendActivityCheck()}))}stopActivityCheck(){this.activityTimer&&this.activityTimer.ensureAborted()}buildConnectionCallbacks(t){return r.f({},t,{message:t=>{this.resetActivityCheck(),this.emit("message",t)},ping:()=>{this.send_event("pusher:pong",{})},activity:()=>{this.resetActivityCheck()},error:t=>{this.emit("error",t)},closed:()=>{this.abandonConnection(),this.shouldRetry()&&this.retryIn(1e3)}})}buildHandshakeCallbacks(t){return r.f({},t,{connected:t=>{this.activityTimeout=Math.min(this.options.activityTimeout,t.activityTimeout,t.connection.activityTimeout||1/0),this.clearUnavailableTimer(),this.setConnection(t.connection),this.socket_id=this.connection.id,this.updateState("connected",{socket_id:this.socket_id})}})}buildErrorCallbacks(){let t=t=>e=>{e.error&&this.emit("error",{type:"WebSocketError",error:e.error}),t(e)};return{tls_only:t(()=>{this.usingTLS=!0,this.updateStrategy(),this.retryIn(0)}),refused:t(()=>{this.disconnect()}),backoff:t(()=>{this.retryIn(1e3)}),retry:t(()=>{this.retryIn(0)})}}setConnection(t){for(var e in this.connection=t,this.connectionCallbacks)this.connection.bind(e,this.connectionCallbacks[e]);this.resetActivityCheck()}abandonConnection(){if(this.connection){for(var t in this.stopActivityCheck(),this.connectionCallbacks)this.connection.unbind(t,this.connectionCallbacks[t]);var e=this.connection;return this.connection=null,e}}updateState(t,e){var n=this.state;if(this.state=t,n!==t){var r=t;"connected"===r&&(r+=" with new socket ID "+e.socket_id),l.a.debug("State changed",n+" -> "+r),this.timeline.info({state:t,params:e}),this.emit("state_change",{previous:n,current:t}),this.emit(t,e)}}shouldRetry(){return"connecting"===this.state||"connected"===this.state}}class ${constructor(){this.channels={}}add(t,e){return this.channels[t]||(this.channels[t]=function(t,e){if(0===t.indexOf("private-encrypted-")){if(e.config.nacl)return J.createEncryptedChannel(t,e,e.config.nacl);let n="Tried to subscribe to a private-encrypted- channel but no nacl implementation available",r=I("encryptedChannelSupport");throw new O(`${n}. ${r}`)}if(0===t.indexOf("private-"))return J.createPrivateChannel(t,e);if(0===t.indexOf("presence-"))return J.createPresenceChannel(t,e);if(0===t.indexOf("#"))throw new A('Cannot create a channel with name "'+t+'".');return J.createChannel(t,e)}(t,e)),this.channels[t]}all(){return r.n(this.channels)}find(t){return this.channels[t]}remove(t){var e=this.channels[t];return delete this.channels[t],e}disconnect(){r.k(this.channels,(function(t){t.disconnect()}))}}var J={createChannels:()=>new $,createConnectionManager:(t,e)=>new K(t,e),createChannel:(t,e)=>new N(t,e),createPrivateChannel:(t,e)=>new j(t,e),createPresenceChannel:(t,e)=>new H(t,e),createEncryptedChannel:(t,e,n)=>new q(t,e,n),createTimelineSender:(t,e)=>new T(t,e),createHandshake:(t,e)=>new C(t,e),createAssistantToTheTransportManager:(t,e,n)=>new w(t,e,n)};class X{constructor(t){this.options=t||{},this.livesLeft=this.options.lives||1/0}getAssistant(t){return J.createAssistantToTheTransportManager(this,t,{minPingDelay:this.options.minPingDelay,maxPingDelay:this.options.maxPingDelay})}isAlive(){return this.livesLeft>0}reportDeath(){this.livesLeft-=1}}class W{constructor(t,e){this.strategies=t,this.loop=Boolean(e.loop),this.failFast=Boolean(e.failFast),this.timeout=e.timeout,this.timeoutLimit=e.timeoutLimit}isSupported(){return r.b(this.strategies,c.a.method("isSupported"))}connect(t,e){var n=this.strategies,r=0,i=this.timeout,s=null,o=(a,h)=>{h?e(null,h):(r+=1,this.loop&&(r%=n.length),r<n.length?(i&&(i*=2,this.timeoutLimit&&(i=Math.min(i,this.timeoutLimit))),s=this.tryStrategy(n[r],t,{timeout:i,failFast:this.failFast},o)):e(!0))};return s=this.tryStrategy(n[r],t,{timeout:i,failFast:this.failFast},o),{abort:function(){s.abort()},forceMinPriority:function(e){t=e,s&&s.forceMinPriority(e)}}}tryStrategy(t,e,n,r){var i=null,s=null;return n.timeout>0&&(i=new Y.a(n.timeout,(function(){s.abort(),r(!0)}))),s=t.connect(e,(function(t,e){t&&i&&i.isRunning()&&!n.failFast||(i&&i.ensureAborted(),r(t,e))})),{abort:function(){i&&i.ensureAborted(),s.abort()},forceMinPriority:function(t){s.forceMinPriority(t)}}}}class G{constructor(t){this.strategies=t}isSupported(){return r.b(this.strategies,c.a.method("isSupported"))}connect(t,e){return function(t,e,n){var i=r.j(t,(function(t,r,i,s){return t.connect(e,n(r,s))}));return{abort:function(){r.c(i,V)},forceMinPriority:function(t){r.c(i,(function(e){e.forceMinPriority(t)}))}}}(this.strategies,t,(function(t,n){return function(i,s){n[t].error=i,i?function(t){return r.a(t,(function(t){return Boolean(t.error)}))}(n)&&e(!0):(r.c(n,(function(t){t.forceMinPriority(s.transport.priority)})),e(null,s))}}))}}function V(t){t.error||t.aborted||(t.abort(),t.aborted=!0)}class Z{constructor(t,e,n){this.strategy=t,this.transports=e,this.ttl=n.ttl||18e5,this.usingTLS=n.useTLS,this.timeline=n.timeline}isSupported(){return this.strategy.isSupported()}connect(t,e){var n=this.usingTLS,i=function(t){var e=zt.getLocalStorage();if(e)try{var n=e[Q(t)];if(n)return JSON.parse(n)}catch(e){tt(t)}return null}(n),s=i&&i.cacheSkipCount?i.cacheSkipCount:0,o=[this.strategy];if(i&&i.timestamp+this.ttl>=c.a.now()){var a=this.transports[i.transport];a&&(["ws","wss"].includes(i.transport)||s>3?(this.timeline.info({cached:!0,transport:i.transport,latency:i.latency}),o.push(new W([a],{timeout:2*i.latency+1e3,failFast:!0}))):s++)}var h=c.a.now(),u=o.pop().connect(t,(function i(a,l){a?(tt(n),o.length>0?(h=c.a.now(),u=o.pop().connect(t,i)):e(a)):(!function(t,e,n,i){var s=zt.getLocalStorage();if(s)try{s[Q(t)]=r.l({timestamp:c.a.now(),transport:e,latency:n,cacheSkipCount:i})}catch(t){}}(n,l.transport.name,c.a.now()-h,s),e(null,l))}));return{abort:function(){u.abort()},forceMinPriority:function(e){t=e,u&&u.forceMinPriority(e)}}}}function Q(t){return"pusherTransport"+(t?"TLS":"NonTLS")}function tt(t){var e=zt.getLocalStorage();if(e)try{delete e[Q(t)]}catch(t){}}class et{constructor(t,{delay:e}){this.strategy=t,this.options={delay:e}}isSupported(){return this.strategy.isSupported()}connect(t,e){var n,r=this.strategy,i=new Y.a(this.options.delay,(function(){n=r.connect(t,e)}));return{abort:function(){i.ensureAborted(),n&&n.abort()},forceMinPriority:function(e){t=e,n&&n.forceMinPriority(e)}}}}class nt{constructor(t,e,n){this.test=t,this.trueBranch=e,this.falseBranch=n}isSupported(){return(this.test()?this.trueBranch:this.falseBranch).isSupported()}connect(t,e){return(this.test()?this.trueBranch:this.falseBranch).connect(t,e)}}class rt{constructor(t){this.strategy=t}isSupported(){return this.strategy.isSupported()}connect(t,e){var n=this.strategy.connect(t,(function(t,r){r&&n.abort(),e(t,r)}));return n}}function it(t){return function(){return t.isSupported()}}var st=function(t,e,n){var i={};function s(e,r,s,o,a){var h=n(t,e,r,s,o,a);return i[e]=h,h}var o,a=Object.assign({},e,{hostNonTLS:t.wsHost+":"+t.wsPort,hostTLS:t.wsHost+":"+t.wssPort,httpPath:t.wsPath}),h=r.f({},a,{useTLS:!0}),c=Object.assign({},e,{hostNonTLS:t.httpHost+":"+t.httpPort,hostTLS:t.httpHost+":"+t.httpsPort,httpPath:t.httpPath}),u={loop:!0,timeout:15e3,timeoutLimit:6e4},l=new X({minPingDelay:1e4,maxPingDelay:t.activityTimeout}),d=new X({lives:2,minPingDelay:1e4,maxPingDelay:t.activityTimeout}),f=s("ws","ws",3,a,l),p=s("wss","ws",3,h,l),g=s("xhr_streaming","xhr_streaming",1,c,d),b=s("xhr_polling","xhr_polling",1,c),v=new W([f],u),y=new W([p],u),m=new W([g],u),w=new W([b],u),_=new W([new nt(it(m),new G([m,new et(w,{delay:4e3})]),w)],u);return o=e.useTLS?new G([v,new et(_,{delay:2e3})]):new G([v,new et(y,{delay:2e3}),new et(_,{delay:5e3})]),new Z(new rt(new nt(it(f),o,_)),i,{ttl:18e5,timeline:e.timeline,useTLS:e.useTLS})};class ot extends u.a{constructor(t,e,n){super(),this.hooks=t,this.method=e,this.url=n}start(t){this.position=0,this.xhr=this.hooks.getRequest(this),this.unloader=()=>{this.close()},zt.addUnloadListener(this.unloader),this.xhr.open(this.method,this.url,!0),this.xhr.setRequestHeader&&this.xhr.setRequestHeader("Content-Type","application/json"),this.xhr.send(t)}close(){this.unloader&&(zt.removeUnloadListener(this.unloader),this.unloader=null),this.xhr&&(this.hooks.abortRequest(this.xhr),this.xhr=null)}onChunk(t,e){for(;;){var n=this.advanceBuffer(e);if(!n)break;this.emit("chunk",{status:t,data:n})}this.isBufferTooLong(e)&&this.emit("buffer_too_long")}advanceBuffer(t){var e=t.slice(this.position),n=e.indexOf("\n");return-1!==n?(this.position+=n+1,e.slice(0,n)):null}isBufferTooLong(t){return this.position===t.length&&t.length>262144}}var at;!function(t){t[t.CONNECTING=0]="CONNECTING",t[t.OPEN=1]="OPEN",t[t.CLOSED=3]="CLOSED"}(at||(at={}));var ht=at,ct=1;function ut(t){var e=-1===t.indexOf("?")?"?":"&";return t+e+"t="+ +new Date+"&n="+ct++}function lt(t){return zt.randomInt(t)}var dt=class{constructor(t,e){this.hooks=t,this.session=lt(1e3)+"/"+function(t){for(var e=[],n=0;n<t;n++)e.push(lt(32).toString(32));return e.join("")}(8),this.location=function(t){var e=/([^\?]*)\/*(\??.*)/.exec(t);return{base:e[1],queryString:e[2]}}(e),this.readyState=ht.CONNECTING,this.openStream()}send(t){return this.sendRaw(JSON.stringify([t]))}ping(){this.hooks.sendHeartbeat(this)}close(t,e){this.onClose(t,e,!0)}sendRaw(t){if(this.readyState!==ht.OPEN)return!1;try{return zt.createSocketRequest("POST",ut((e=this.location,n=this.session,e.base+"/"+n+"/xhr_send"))).start(t),!0}catch(t){return!1}var e,n}reconnect(){this.closeStream(),this.openStream()}onClose(t,e,n){this.closeStream(),this.readyState=ht.CLOSED,this.onclose&&this.onclose({code:t,reason:e,wasClean:n})}onChunk(t){var e;if(200===t.status)switch(this.readyState===ht.OPEN&&this.onActivity(),t.data.slice(0,1)){case"o":e=JSON.parse(t.data.slice(1)||"{}"),this.onOpen(e);break;case"a":e=JSON.parse(t.data.slice(1)||"[]");for(var n=0;n<e.length;n++)this.onEvent(e[n]);break;case"m":e=JSON.parse(t.data.slice(1)||"null"),this.onEvent(e);break;case"h":this.hooks.onHeartbeat(this);break;case"c":e=JSON.parse(t.data.slice(1)||"[]"),this.onClose(e[0],e[1],!0)}}onOpen(t){var e,n,r;this.readyState===ht.CONNECTING?(t&&t.hostname&&(this.location.base=(e=this.location.base,n=t.hostname,(r=/(https?:\/\/)([^\/:]+)((\/|:)?.*)/.exec(e))[1]+n+r[3])),this.readyState=ht.OPEN,this.onopen&&this.onopen()):this.onClose(1006,"Server lost session",!0)}onEvent(t){this.readyState===ht.OPEN&&this.onmessage&&this.onmessage({data:t})}onActivity(){this.onactivity&&this.onactivity()}onError(t){this.onerror&&this.onerror(t)}openStream(){this.stream=zt.createSocketRequest("POST",ut(this.hooks.getReceiveURL(this.location,this.session))),this.stream.bind("chunk",t=>{this.onChunk(t)}),this.stream.bind("finished",t=>{this.hooks.onFinished(this,t)}),this.stream.bind("buffer_too_long",()=>{this.reconnect()});try{this.stream.start()}catch(t){c.a.defer(()=>{this.onError(t),this.onClose(1006,"Could not start streaming",!1)})}}closeStream(){this.stream&&(this.stream.unbind_all(),this.stream.close(),this.stream=null)}},ft={getReceiveURL:function(t,e){return t.base+"/"+e+"/xhr_streaming"+t.queryString},onHeartbeat:function(t){t.sendRaw("[]")},sendHeartbeat:function(t){t.sendRaw("[]")},onFinished:function(t,e){t.onClose(1006,"Connection interrupted ("+e+")",!1)}},pt={getReceiveURL:function(t,e){return t.base+"/"+e+"/xhr"+t.queryString},onHeartbeat:function(){},sendHeartbeat:function(t){t.sendRaw("[]")},onFinished:function(t,e){200===e?t.reconnect():t.onClose(1006,"Connection interrupted ("+e+")",!1)}},gt={getRequest:function(t){var e=new(zt.getXHRAPI());return e.onreadystatechange=e.onprogress=function(){switch(e.readyState){case 3:e.responseText&&e.responseText.length>0&&t.onChunk(e.status,e.responseText);break;case 4:e.responseText&&e.responseText.length>0&&t.onChunk(e.status,e.responseText),t.emit("finished",e.status),t.close()}},e},abortRequest:function(t){t.onreadystatechange=null,t.abort()}},bt={getDefaultStrategy:st,Transports:m,transportConnectionInitializer:function(){this.timeline.info(this.buildTimelineMessage({transport:this.name+(this.options.useTLS?"s":"")})),this.hooks.isInitialized()?this.changeState("initialized"):this.onClose()},HTTPFactory:{createStreamingSocket(t){return this.createSocket(ft,t)},createPollingSocket(t){return this.createSocket(pt,t)},createSocket:(t,e)=>new dt(t,e),createXHR(t,e){return this.createRequest(gt,t,e)},createRequest:(t,e,n)=>new ot(t,e,n)},setup(t){t.ready()},getLocalStorage(){},getClientFeatures:()=>r.i(r.h({ws:m.ws},(function(t){return t.isSupported({})}))),getProtocol:()=>"http:",isXHRSupported:()=>!0,createSocketRequest(t,e){if(this.isXHRSupported())return this.HTTPFactory.createXHR(t,e);throw"Cross-origin HTTP requests are not supported"},createXHR(){return new(this.getXHRAPI())},createWebSocket(t){return new(this.getWebSocketAPI())(t)},addUnloadListener(t){},removeUnloadListener(t){}},vt=n(10),yt=n.n(vt);function mt(t){return"none"!==t.type.toLowerCase()}class wt extends u.a{constructor(){super(),this.online=!0,yt.a.fetch().then(t=>{this.online=mt(t)}),yt.a.addEventListener(t=>{var e=mt(t);this.online!==e&&(this.online=e,this.online?this.emit("online"):this.emit("offline"))})}isOnline(){return this.online}}var _t,St=new wt;!function(t){t.UserAuthentication="user-authentication",t.ChannelAuthorization="channel-authorization"}(_t||(_t={}));var kt=function(t,e,n,r,i){const s=zt.createXHR();for(var o in s.open("POST",n.endpoint,!0),s.setRequestHeader("Content-Type","application/x-www-form-urlencoded"),n.headers)s.setRequestHeader(o,n.headers[o]);if(null!=n.headersProvider){let t=n.headersProvider();for(var o in t)s.setRequestHeader(o,t[o])}return s.onreadystatechange=function(){if(4===s.readyState)if(200===s.status){let t,e=!1;try{t=JSON.parse(s.responseText),e=!0}catch(t){i(new R(200,`JSON returned from ${r.toString()} endpoint was invalid, yet status code was 200. Data was: ${s.responseText}`),null)}e&&i(null,t)}else{let t="";switch(r){case _t.UserAuthentication:t=I("authenticationEndpoint");break;case _t.ChannelAuthorization:t="Clients must be authorized to join private or presence channels. "+I("authorizationEndpoint")}i(new R(s.status,`Unable to retrieve auth string from ${r.toString()} endpoint - received status: ${s.status} from ${n.endpoint}. ${t}`),null)}},s.send(e),s},Ct={name:"xhr",getAgent:function(t,e){return function(n,i){var s="http"+(e?"s":"")+"://"+(t.host||t.options.host)+t.options.path;s+="/2?"+r.e(n);var o=zt.createXHR();o.open("GET",s,!0),o.onreadystatechange=function(){if(4===o.readyState){let{status:n,responseText:r}=o;if(200!==n)return void l.a.debug(`TimelineSender Error: received ${n} from stats.pusher.com`);try{var{host:e}=JSON.parse(r)}catch(t){l.a.debug("TimelineSenderError: invalid response "+r)}e&&(t.host=e)}},o.send()}}};const{getDefaultStrategy:Tt,Transports:Et,setup:At,getProtocol:Pt,isXHRSupported:xt,getLocalStorage:Ot,createXHR:Lt,createWebSocket:Ut,addUnloadListener:Rt,removeUnloadListener:Mt,transportConnectionInitializer:It,createSocketRequest:Nt,HTTPFactory:jt}=bt;var Dt,zt={getDefaultStrategy:Tt,Transports:Et,setup:At,getProtocol:Pt,isXHRSupported:xt,getLocalStorage:Ot,createXHR:Lt,createWebSocket:Ut,addUnloadListener:Rt,removeUnloadListener:Mt,transportConnectionInitializer:It,createSocketRequest:Nt,HTTPFactory:jt,TimelineTransport:Ct,getAuthorizers:()=>({ajax:kt}),getWebSocketAPI:()=>WebSocket,getXHRAPI:()=>XMLHttpRequest,getNetwork:()=>St,randomInt:t=>Math.floor(Math.random()*t)};!function(t){t[t.ERROR=3]="ERROR",t[t.INFO=6]="INFO",t[t.DEBUG=7]="DEBUG"}(Dt||(Dt={}));var Ht=Dt;class Bt{constructor(t,e,n){this.key=t,this.session=e,this.events=[],this.options=n||{},this.sent=0,this.uniqueID=0}log(t,e){t<=this.options.level&&(this.events.push(r.f({},e,{timestamp:c.a.now()})),this.options.limit&&this.events.length>this.options.limit&&this.events.shift())}error(t){this.log(Ht.ERROR,t)}info(t){this.log(Ht.INFO,t)}debug(t){this.log(Ht.DEBUG,t)}isEmpty(){return 0===this.events.length}send(t,e){var n=r.f({session:this.session,bundle:this.sent+1,key:this.key,lib:"js",version:this.options.version,cluster:this.options.cluster,features:this.options.features,timeline:this.events},this.options.params);return this.events=[],t(n,(t,n)=>{t||this.sent++,e&&e(t,n)}),!0}generateUniqueID(){return this.uniqueID++,this.uniqueID}}class Ft{constructor(t,e,n,r){this.name=t,this.priority=e,this.transport=n,this.options=r||{}}isSupported(){return this.transport.isSupported({useTLS:this.options.useTLS})}connect(t,e){if(!this.isSupported())return qt(new U,e);if(this.priority<t)return qt(new P,e);var n=!1,i=this.transport.createConnection(this.name,this.priority,this.options.key,this.options),s=null,o=function(){i.unbind("initialized",o),i.connect()},a=function(){s=J.createHandshake(i,(function(t){n=!0,u(),e(null,t)}))},h=function(t){u(),e(t)},c=function(){var t;u(),t=r.l(i),e(new x(t))},u=function(){i.unbind("initialized",o),i.unbind("open",a),i.unbind("error",h),i.unbind("closed",c)};return i.bind("initialized",o),i.bind("open",a),i.bind("error",h),i.bind("closed",c),i.initialize(),{abort:()=>{n||(u(),s?s.close():i.close())},forceMinPriority:t=>{n||this.priority<t&&(s?s.close():i.close())}}}}function qt(t,e){return c.a.defer((function(){e(t)})),{abort:function(){},forceMinPriority:function(){}}}const{Transports:Yt}=zt;var Kt=function(t,e,n,i,s,o){var a,h=Yt[n];if(!h)throw new L(n);return!(t.enabledTransports&&-1===r.d(t.enabledTransports,e)||t.disabledTransports&&-1!==r.d(t.disabledTransports,e))?(s=Object.assign({ignoreNullOrigin:t.ignoreNullOrigin},s),a=new Ft(e,i,o?o.getAssistant(h):h,s)):a=$t,a},$t={isSupported:function(){return!1},connect:function(t,e){var n=c.a.defer((function(){e(new U)}));return{abort:function(){n.ensureAborted()},forceMinPriority:function(){}}}},Jt=n(7);var Xt=t=>{if(void 0===zt.getAuthorizers()[t.transport])throw`'${t.transport}' is not a recognized auth transport`;return(e,n)=>{const r=((t,e)=>{var n="socket_id="+encodeURIComponent(t.socketId);for(var r in e.params)n+="&"+encodeURIComponent(r)+"="+encodeURIComponent(e.params[r]);if(null!=e.paramsProvider){let t=e.paramsProvider();for(var r in t)n+="&"+encodeURIComponent(r)+"="+encodeURIComponent(t[r])}return n})(e,t);zt.getAuthorizers()[t.transport](zt,r,t,_t.UserAuthentication,n)}};var Wt=t=>{if(void 0===zt.getAuthorizers()[t.transport])throw`'${t.transport}' is not a recognized auth transport`;return(e,n)=>{const r=((t,e)=>{var n="socket_id="+encodeURIComponent(t.socketId);for(var r in n+="&channel_name="+encodeURIComponent(t.channelName),e.params)n+="&"+encodeURIComponent(r)+"="+encodeURIComponent(e.params[r]);if(null!=e.paramsProvider){let t=e.paramsProvider();for(var r in t)n+="&"+encodeURIComponent(r)+"="+encodeURIComponent(t[r])}return n})(e,t);zt.getAuthorizers()[t.transport](zt,r,t,_t.ChannelAuthorization,n)}};function Gt(t){return t.httpHost?t.httpHost:t.cluster?`sockjs-${t.cluster}.pusher.com`:i.httpHost}function Vt(t){return t.wsHost?t.wsHost:`ws-${t.cluster}.pusher.com`}function Zt(t){return"https:"===zt.getProtocol()||!1!==t.forceTLS}function Qt(t){return"enableStats"in t?t.enableStats:"disableStats"in t&&!t.disableStats}function te(t){const e=Object.assign(Object.assign({},i.userAuthentication),t.userAuthentication);return"customHandler"in e&&null!=e.customHandler?e.customHandler:Xt(e)}function ee(t,e){const n=function(t,e){let n;return"channelAuthorization"in t?n=Object.assign(Object.assign({},i.channelAuthorization),t.channelAuthorization):(n={transport:t.authTransport||i.authTransport,endpoint:t.authEndpoint||i.authEndpoint},"auth"in t&&("params"in t.auth&&(n.params=t.auth.params),"headers"in t.auth&&(n.headers=t.auth.headers)),"authorizer"in t&&(n.customHandler=((t,e,n)=>{const r={authTransport:e.transport,authEndpoint:e.endpoint,auth:{params:e.params,headers:e.headers}};return(e,i)=>{const s=t.channel(e.channelName);n(s,r).authorize(e.socketId,i)}})(e,n,t.authorizer))),n}(t,e);return"customHandler"in n&&null!=n.customHandler?n.customHandler:Wt(n)}class ne extends u.a{constructor(t){super((function(t,e){l.a.debug("No callbacks on watchlist events for "+t)})),this.pusher=t,this.bindWatchlistInternalEvent()}handleEvent(t){t.data.events.forEach(t=>{this.emit(t.name,t)})}bindWatchlistInternalEvent(){this.pusher.connection.bind("message",t=>{"pusher_internal:watchlist_events"===t.event&&this.handleEvent(t)})}}var re=function(){let t,e;return{promise:new Promise((n,r)=>{t=n,e=r}),resolve:t,reject:e}};class ie extends u.a{constructor(t){super((function(t,e){l.a.debug("No callbacks on user for "+t)})),this.signin_requested=!1,this.user_data=null,this.serverToUserChannel=null,this.signinDonePromise=null,this._signinDoneResolve=null,this._onAuthorize=(t,e)=>{if(t)return l.a.warn("Error during signin: "+t),void this._cleanup();this.pusher.send_event("pusher:signin",{auth:e.auth,user_data:e.user_data})},this.pusher=t,this.pusher.connection.bind("state_change",({previous:t,current:e})=>{"connected"!==t&&"connected"===e&&this._signin(),"connected"===t&&"connected"!==e&&(this._cleanup(),this._newSigninPromiseIfNeeded())}),this.watchlist=new ne(t),this.pusher.connection.bind("message",t=>{"pusher:signin_success"===t.event&&this._onSigninSuccess(t.data),this.serverToUserChannel&&this.serverToUserChannel.name===t.channel&&this.serverToUserChannel.handleEvent(t)})}signin(){this.signin_requested||(this.signin_requested=!0,this._signin())}_signin(){this.signin_requested&&(this._newSigninPromiseIfNeeded(),"connected"===this.pusher.connection.state&&this.pusher.config.userAuthenticator({socketId:this.pusher.connection.socket_id},this._onAuthorize))}_onSigninSuccess(t){try{this.user_data=JSON.parse(t.user_data)}catch(e){return l.a.error("Failed parsing user data after signin: "+t.user_data),void this._cleanup()}if("string"!=typeof this.user_data.id||""===this.user_data.id)return l.a.error("user_data doesn't contain an id. user_data: "+this.user_data),void this._cleanup();this._signinDoneResolve(),this._subscribeChannels()}_subscribeChannels(){this.serverToUserChannel=new N("#server-to-user-"+this.user_data.id,this.pusher),this.serverToUserChannel.bind_global((t,e)=>{0!==t.indexOf("pusher_internal:")&&0!==t.indexOf("pusher:")&&this.emit(t,e)}),(t=>{t.subscriptionPending&&t.subscriptionCancelled?t.reinstateSubscription():t.subscriptionPending||"connected"!==this.pusher.connection.state||t.subscribe()})(this.serverToUserChannel)}_cleanup(){this.user_data=null,this.serverToUserChannel&&(this.serverToUserChannel.unbind_all(),this.serverToUserChannel.disconnect(),this.serverToUserChannel=null),this.signin_requested&&this._signinDoneResolve()}_newSigninPromiseIfNeeded(){if(!this.signin_requested)return;if(this.signinDonePromise&&!this.signinDonePromise.done)return;const{promise:t,resolve:e,reject:n}=re();t.done=!1;const r=()=>{t.done=!0};t.then(r).catch(r),this.signinDonePromise=t,this._signinDoneResolve=e}}class se{static ready(){se.isReady=!0;for(var t=0,e=se.instances.length;t<e;t++)se.instances[t].connect()}static getClientFeatures(){return r.i(r.h({ws:zt.Transports.ws},(function(t){return t.isSupported({})})))}constructor(t,e){!function(t){if(null==t)throw"You must pass your app key when you instantiate Pusher."}(t),Object(Jt.a)(e),this.key=t,this.config=function(t,e){let n={activityTimeout:t.activityTimeout||i.activityTimeout,cluster:t.cluster,httpPath:t.httpPath||i.httpPath,httpPort:t.httpPort||i.httpPort,httpsPort:t.httpsPort||i.httpsPort,pongTimeout:t.pongTimeout||i.pongTimeout,statsHost:t.statsHost||i.stats_host,unavailableTimeout:t.unavailableTimeout||i.unavailableTimeout,wsPath:t.wsPath||i.wsPath,wsPort:t.wsPort||i.wsPort,wssPort:t.wssPort||i.wssPort,enableStats:Qt(t),httpHost:Gt(t),useTLS:Zt(t),wsHost:Vt(t),userAuthenticator:te(t),channelAuthorizer:ee(t,e)};return"disabledTransports"in t&&(n.disabledTransports=t.disabledTransports),"enabledTransports"in t&&(n.enabledTransports=t.enabledTransports),"ignoreNullOrigin"in t&&(n.ignoreNullOrigin=t.ignoreNullOrigin),"timelineParams"in t&&(n.timelineParams=t.timelineParams),"nacl"in t&&(n.nacl=t.nacl),n}(e,this),this.channels=J.createChannels(),this.global_emitter=new u.a,this.sessionID=zt.randomInt(1e9),this.timeline=new Bt(this.key,this.sessionID,{cluster:this.config.cluster,features:se.getClientFeatures(),params:this.config.timelineParams||{},limit:50,level:Ht.INFO,version:i.VERSION}),this.config.enableStats&&(this.timelineSender=J.createTimelineSender(this.timeline,{host:this.config.statsHost,path:"/timeline/v2/"+zt.TimelineTransport.name}));this.connection=J.createConnectionManager(this.key,{getStrategy:t=>zt.getDefaultStrategy(this.config,t,Kt),timeline:this.timeline,activityTimeout:this.config.activityTimeout,pongTimeout:this.config.pongTimeout,unavailableTimeout:this.config.unavailableTimeout,useTLS:Boolean(this.config.useTLS)}),this.connection.bind("connected",()=>{this.subscribeAll(),this.timelineSender&&this.timelineSender.send(this.connection.isUsingTLS())}),this.connection.bind("message",t=>{var e=0===t.event.indexOf("pusher_internal:");if(t.channel){var n=this.channel(t.channel);n&&n.handleEvent(t)}e||this.global_emitter.emit(t.event,t.data)}),this.connection.bind("connecting",()=>{this.channels.disconnect()}),this.connection.bind("disconnected",()=>{this.channels.disconnect()}),this.connection.bind("error",t=>{l.a.warn(t)}),se.instances.push(this),this.timeline.info({instances:se.instances.length}),this.user=new ie(this),se.isReady&&this.connect()}channel(t){return this.channels.find(t)}allChannels(){return this.channels.all()}connect(){if(this.connection.connect(),this.timelineSender&&!this.timelineSenderTimer){var t=this.connection.isUsingTLS(),e=this.timelineSender;this.timelineSenderTimer=new Y.b(6e4,(function(){e.send(t)}))}}disconnect(){this.connection.disconnect(),this.timelineSenderTimer&&(this.timelineSenderTimer.ensureAborted(),this.timelineSenderTimer=null)}bind(t,e,n){return this.global_emitter.bind(t,e,n),this}unbind(t,e,n){return this.global_emitter.unbind(t,e,n),this}bind_global(t){return this.global_emitter.bind_global(t),this}unbind_global(t){return this.global_emitter.unbind_global(t),this}unbind_all(t){return this.global_emitter.unbind_all(),this}subscribeAll(){var t;for(t in this.channels.channels)this.channels.channels.hasOwnProperty(t)&&this.subscribe(t)}subscribe(t){var e=this.channels.add(t,this);return e.subscriptionPending&&e.subscriptionCancelled?e.reinstateSubscription():e.subscriptionPending||"connected"!==this.connection.state||e.subscribe(),e}unsubscribe(t){var e=this.channels.find(t);e&&e.subscriptionPending?e.cancelSubscription():(e=this.channels.remove(t))&&e.subscribed&&e.unsubscribe()}send_event(t,e,n){return this.connection.send_event(t,e,n)}shouldUseTLS(){return this.config.useTLS}signin(){this.user.signin()}}se.instances=[],se.isReady=!1,se.logToConsole=!1,se.Runtime=zt,se.ScriptReceivers=zt.ScriptReceivers,se.DependenciesReceivers=zt.DependenciesReceivers,se.auth_callbacks=zt.auth_callbacks;e.a=se;zt.setup(se)},function(t,e){var n;n=function(){return this}();try{n=n||new Function("return this")()}catch(t){"object"==typeof window&&(n=window)}t.exports=n},function(t,e,n){"use strict";n.d(e,"a",(function(){return i}));var r=n(1);function i(t){if(null==t)throw"You must pass an options object";if(null==t.cluster)throw"Options object must provide a cluster";"disableStats"in t&&r.a.warn("The disableStats option is deprecated in favor of enableStats")}},function(t,e,n){"use strict";var r,i=this&&this.__extends||(r=function(t,e){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)});Object.defineProperty(e,"__esModule",{value:!0});var s=function(){function t(t){void 0===t&&(t="="),this._paddingCharacter=t}return t.prototype.encodedLength=function(t){return this._paddingCharacter?(t+2)/3*4|0:(8*t+5)/6|0},t.prototype.encode=function(t){for(var e="",n=0;n<t.length-2;n+=3){var r=t[n]<<16|t[n+1]<<8|t[n+2];e+=this._encodeByte(r>>>18&63),e+=this._encodeByte(r>>>12&63),e+=this._encodeByte(r>>>6&63),e+=this._encodeByte(r>>>0&63)}var i=t.length-n;if(i>0){r=t[n]<<16|(2===i?t[n+1]<<8:0);e+=this._encodeByte(r>>>18&63),e+=this._encodeByte(r>>>12&63),e+=2===i?this._encodeByte(r>>>6&63):this._paddingCharacter||"",e+=this._paddingCharacter||""}return e},t.prototype.maxDecodedLength=function(t){return this._paddingCharacter?t/4*3|0:(6*t+7)/8|0},t.prototype.decodedLength=function(t){return this.maxDecodedLength(t.length-this._getPaddingLength(t))},t.prototype.decode=function(t){if(0===t.length)return new Uint8Array(0);for(var e=this._getPaddingLength(t),n=t.length-e,r=new Uint8Array(this.maxDecodedLength(n)),i=0,s=0,o=0,a=0,h=0,c=0,u=0;s<n-4;s+=4)a=this._decodeChar(t.charCodeAt(s+0)),h=this._decodeChar(t.charCodeAt(s+1)),c=this._decodeChar(t.charCodeAt(s+2)),u=this._decodeChar(t.charCodeAt(s+3)),r[i++]=a<<2|h>>>4,r[i++]=h<<4|c>>>2,r[i++]=c<<6|u,o|=256&a,o|=256&h,o|=256&c,o|=256&u;if(s<n-1&&(a=this._decodeChar(t.charCodeAt(s)),h=this._decodeChar(t.charCodeAt(s+1)),r[i++]=a<<2|h>>>4,o|=256&a,o|=256&h),s<n-2&&(c=this._decodeChar(t.charCodeAt(s+2)),r[i++]=h<<4|c>>>2,o|=256&c),s<n-3&&(u=this._decodeChar(t.charCodeAt(s+3)),r[i++]=c<<6|u,o|=256&u),0!==o)throw new Error("Base64Coder: incorrect characters for decoding");return r},t.prototype._encodeByte=function(t){var e=t;return e+=65,e+=25-t>>>8&6,e+=51-t>>>8&-75,e+=61-t>>>8&-15,e+=62-t>>>8&3,String.fromCharCode(e)},t.prototype._decodeChar=function(t){var e=256;return e+=(42-t&t-44)>>>8&-256+t-43+62,e+=(46-t&t-48)>>>8&-256+t-47+63,e+=(47-t&t-58)>>>8&-256+t-48+52,e+=(64-t&t-91)>>>8&-256+t-65+0,e+=(96-t&t-123)>>>8&-256+t-97+26},t.prototype._getPaddingLength=function(t){var e=0;if(this._paddingCharacter){for(var n=t.length-1;n>=0&&t[n]===this._paddingCharacter;n--)e++;if(t.length<4||e>2)throw new Error("Base64Coder: incorrect padding")}return e},t}();e.Coder=s;var o=new s;e.encode=function(t){return o.encode(t)},e.decode=function(t){return o.decode(t)};var a=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return i(e,t),e.prototype._encodeByte=function(t){var e=t;return e+=65,e+=25-t>>>8&6,e+=51-t>>>8&-75,e+=61-t>>>8&-13,e+=62-t>>>8&49,String.fromCharCode(e)},e.prototype._decodeChar=function(t){var e=256;return e+=(44-t&t-46)>>>8&-256+t-45+62,e+=(94-t&t-96)>>>8&-256+t-95+63,e+=(47-t&t-58)>>>8&-256+t-48+52,e+=(64-t&t-91)>>>8&-256+t-65+0,e+=(96-t&t-123)>>>8&-256+t-97+26},e}(s);e.URLSafeCoder=a;var h=new a;e.encodeURLSafe=function(t){return h.encode(t)},e.decodeURLSafe=function(t){return h.decode(t)},e.encodedLength=function(t){return o.encodedLength(t)},e.maxDecodedLength=function(t){return o.maxDecodedLength(t)},e.decodedLength=function(t){return o.decodedLength(t)}},function(t,e,n){"use strict";e.a=class{constructor(t,e,n,r){this.clear=e,this.timer=t(()=>{this.timer&&(this.timer=r(this.timer))},n)}isRunning(){return null!==this.timer}ensureAborted(){this.timer&&(this.clear(this.timer),this.timer=null)}}},function(t,e){t.exports=require("@react-native-community/netinfo")},function(t,e,n){"use strict";(function(t){function r(t){return d(u(t))}n.d(e,"a",(function(){return r}));for(var i=String.fromCharCode,s="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",o={},a=0,h=s.length;a<h;a++)o[s.charAt(a)]=a;var c=function(t){var e=t.charCodeAt(0);return e<128?t:e<2048?i(192|e>>>6)+i(128|63&e):i(224|e>>>12&15)+i(128|e>>>6&63)+i(128|63&e)},u=function(t){return t.replace(/[^\x00-\x7F]/g,c)},l=function(t){var e=[0,2,1][t.length%3],n=t.charCodeAt(0)<<16|(t.length>1?t.charCodeAt(1):0)<<8|(t.length>2?t.charCodeAt(2):0);return[s.charAt(n>>>18),s.charAt(n>>>12&63),e>=2?"=":s.charAt(n>>>6&63),e>=1?"=":s.charAt(63&n)].join("")},d=t.btoa||function(t){return t.replace(/[\s\S]{1,3}/g,l)}}).call(this,n(6))},function(t,e,n){"use strict";n.d(e,"a",(function(){return i}));var r=n(0);class i{constructor(){this._callbacks={}}get(t){return this._callbacks[s(t)]}add(t,e,n){var r=s(t);this._callbacks[r]=this._callbacks[r]||[],this._callbacks[r].push({fn:e,context:n})}remove(t,e,n){if(t||e||n){var i=t?[s(t)]:r.i(this._callbacks);e||n?this.removeCallback(i,e,n):this.removeAllCallbacks(i)}else this._callbacks={}}removeCallback(t,e,n){r.c(t,(function(t){this._callbacks[t]=r.g(this._callbacks[t]||[],(function(t){return e&&e!==t.fn||n&&n!==t.context})),0===this._callbacks[t].length&&delete this._callbacks[t]}),this)}removeAllCallbacks(t){r.c(t,(function(t){delete this._callbacks[t]}),this)}}function s(t){return"_"+t}},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r="utf8: invalid source encoding";function i(t){for(var e=0,n=0;n<t.length;n++){var r=t.charCodeAt(n);if(r<128)e+=1;else if(r<2048)e+=2;else if(r<55296)e+=3;else{if(!(r<=57343))throw new Error("utf8: invalid string");if(n>=t.length-1)throw new Error("utf8: invalid string");n++,e+=4}}return e}e.encode=function(t){for(var e=new Uint8Array(i(t)),n=0,r=0;r<t.length;r++){var s=t.charCodeAt(r);s<128?e[n++]=s:s<2048?(e[n++]=192|s>>6,e[n++]=128|63&s):s<55296?(e[n++]=224|s>>12,e[n++]=128|s>>6&63,e[n++]=128|63&s):(r++,s=(1023&s)<<10,s|=1023&t.charCodeAt(r),s+=65536,e[n++]=240|s>>18,e[n++]=128|s>>12&63,e[n++]=128|s>>6&63,e[n++]=128|63&s)}return e},e.encodedLength=i,e.decode=function(t){for(var e=[],n=0;n<t.length;n++){var i=t[n];if(128&i){var s=void 0;if(i<224){if(n>=t.length)throw new Error(r);if(128!=(192&(o=t[++n])))throw new Error(r);i=(31&i)<<6|63&o,s=128}else if(i<240){if(n>=t.length-1)throw new Error(r);var o=t[++n],a=t[++n];if(128!=(192&o)||128!=(192&a))throw new Error(r);i=(15&i)<<12|(63&o)<<6|63&a,s=2048}else{if(!(i<248))throw new Error(r);if(n>=t.length-2)throw new Error(r);o=t[++n],a=t[++n];var h=t[++n];if(128!=(192&o)||128!=(192&a)||128!=(192&h))throw new Error(r);i=(15&i)<<18|(63&o)<<12|(63&a)<<6|63&h,s=65536}if(i<s||i>=55296&&i<=57343)throw new Error(r);if(i>=65536){if(i>1114111)throw new Error(r);i-=65536,e.push(String.fromCharCode(55296|i>>10)),i=56320|1023&i}}e.push(String.fromCharCode(i))}return e.join("")}},function(t,e,n){!function(t){"use strict";var e=function(t){var e,n=new Float64Array(16);if(t)for(e=0;e<t.length;e++)n[e]=t[e];return n},r=function(){throw new Error("no PRNG")},i=new Uint8Array(16),s=new Uint8Array(32);s[0]=9;var o=e(),a=e([1]),h=e([56129,1]),c=e([30883,4953,19914,30187,55467,16705,2637,112,59544,30585,16505,36039,65139,11119,27886,20995]),u=e([61785,9906,39828,60374,45398,33411,5274,224,53552,61171,33010,6542,64743,22239,55772,9222]),l=e([54554,36645,11616,51542,42930,38181,51040,26924,56412,64982,57905,49316,21502,52590,14035,8553]),d=e([26200,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214]),f=e([41136,18958,6951,50414,58488,44335,6150,12099,55207,15867,153,11085,57099,20417,9344,11139]);function p(t,e,n,r){t[e]=n>>24&255,t[e+1]=n>>16&255,t[e+2]=n>>8&255,t[e+3]=255&n,t[e+4]=r>>24&255,t[e+5]=r>>16&255,t[e+6]=r>>8&255,t[e+7]=255&r}function g(t,e,n,r,i){var s,o=0;for(s=0;s<i;s++)o|=t[e+s]^n[r+s];return(1&o-1>>>8)-1}function b(t,e,n,r){return g(t,e,n,r,16)}function v(t,e,n,r){return g(t,e,n,r,32)}function y(t,e,n,r){!function(t,e,n,r){for(var i,s=255&r[0]|(255&r[1])<<8|(255&r[2])<<16|(255&r[3])<<24,o=255&n[0]|(255&n[1])<<8|(255&n[2])<<16|(255&n[3])<<24,a=255&n[4]|(255&n[5])<<8|(255&n[6])<<16|(255&n[7])<<24,h=255&n[8]|(255&n[9])<<8|(255&n[10])<<16|(255&n[11])<<24,c=255&n[12]|(255&n[13])<<8|(255&n[14])<<16|(255&n[15])<<24,u=255&r[4]|(255&r[5])<<8|(255&r[6])<<16|(255&r[7])<<24,l=255&e[0]|(255&e[1])<<8|(255&e[2])<<16|(255&e[3])<<24,d=255&e[4]|(255&e[5])<<8|(255&e[6])<<16|(255&e[7])<<24,f=255&e[8]|(255&e[9])<<8|(255&e[10])<<16|(255&e[11])<<24,p=255&e[12]|(255&e[13])<<8|(255&e[14])<<16|(255&e[15])<<24,g=255&r[8]|(255&r[9])<<8|(255&r[10])<<16|(255&r[11])<<24,b=255&n[16]|(255&n[17])<<8|(255&n[18])<<16|(255&n[19])<<24,v=255&n[20]|(255&n[21])<<8|(255&n[22])<<16|(255&n[23])<<24,y=255&n[24]|(255&n[25])<<8|(255&n[26])<<16|(255&n[27])<<24,m=255&n[28]|(255&n[29])<<8|(255&n[30])<<16|(255&n[31])<<24,w=255&r[12]|(255&r[13])<<8|(255&r[14])<<16|(255&r[15])<<24,_=s,S=o,k=a,C=h,T=c,E=u,A=l,P=d,x=f,O=p,L=g,U=b,R=v,M=y,I=m,N=w,j=0;j<20;j+=2)_^=(i=(R^=(i=(x^=(i=(T^=(i=_+R|0)<<7|i>>>25)+_|0)<<9|i>>>23)+T|0)<<13|i>>>19)+x|0)<<18|i>>>14,E^=(i=(S^=(i=(M^=(i=(O^=(i=E+S|0)<<7|i>>>25)+E|0)<<9|i>>>23)+O|0)<<13|i>>>19)+M|0)<<18|i>>>14,L^=(i=(A^=(i=(k^=(i=(I^=(i=L+A|0)<<7|i>>>25)+L|0)<<9|i>>>23)+I|0)<<13|i>>>19)+k|0)<<18|i>>>14,N^=(i=(U^=(i=(P^=(i=(C^=(i=N+U|0)<<7|i>>>25)+N|0)<<9|i>>>23)+C|0)<<13|i>>>19)+P|0)<<18|i>>>14,_^=(i=(C^=(i=(k^=(i=(S^=(i=_+C|0)<<7|i>>>25)+_|0)<<9|i>>>23)+S|0)<<13|i>>>19)+k|0)<<18|i>>>14,E^=(i=(T^=(i=(P^=(i=(A^=(i=E+T|0)<<7|i>>>25)+E|0)<<9|i>>>23)+A|0)<<13|i>>>19)+P|0)<<18|i>>>14,L^=(i=(O^=(i=(x^=(i=(U^=(i=L+O|0)<<7|i>>>25)+L|0)<<9|i>>>23)+U|0)<<13|i>>>19)+x|0)<<18|i>>>14,N^=(i=(I^=(i=(M^=(i=(R^=(i=N+I|0)<<7|i>>>25)+N|0)<<9|i>>>23)+R|0)<<13|i>>>19)+M|0)<<18|i>>>14;_=_+s|0,S=S+o|0,k=k+a|0,C=C+h|0,T=T+c|0,E=E+u|0,A=A+l|0,P=P+d|0,x=x+f|0,O=O+p|0,L=L+g|0,U=U+b|0,R=R+v|0,M=M+y|0,I=I+m|0,N=N+w|0,t[0]=_>>>0&255,t[1]=_>>>8&255,t[2]=_>>>16&255,t[3]=_>>>24&255,t[4]=S>>>0&255,t[5]=S>>>8&255,t[6]=S>>>16&255,t[7]=S>>>24&255,t[8]=k>>>0&255,t[9]=k>>>8&255,t[10]=k>>>16&255,t[11]=k>>>24&255,t[12]=C>>>0&255,t[13]=C>>>8&255,t[14]=C>>>16&255,t[15]=C>>>24&255,t[16]=T>>>0&255,t[17]=T>>>8&255,t[18]=T>>>16&255,t[19]=T>>>24&255,t[20]=E>>>0&255,t[21]=E>>>8&255,t[22]=E>>>16&255,t[23]=E>>>24&255,t[24]=A>>>0&255,t[25]=A>>>8&255,t[26]=A>>>16&255,t[27]=A>>>24&255,t[28]=P>>>0&255,t[29]=P>>>8&255,t[30]=P>>>16&255,t[31]=P>>>24&255,t[32]=x>>>0&255,t[33]=x>>>8&255,t[34]=x>>>16&255,t[35]=x>>>24&255,t[36]=O>>>0&255,t[37]=O>>>8&255,t[38]=O>>>16&255,t[39]=O>>>24&255,t[40]=L>>>0&255,t[41]=L>>>8&255,t[42]=L>>>16&255,t[43]=L>>>24&255,t[44]=U>>>0&255,t[45]=U>>>8&255,t[46]=U>>>16&255,t[47]=U>>>24&255,t[48]=R>>>0&255,t[49]=R>>>8&255,t[50]=R>>>16&255,t[51]=R>>>24&255,t[52]=M>>>0&255,t[53]=M>>>8&255,t[54]=M>>>16&255,t[55]=M>>>24&255,t[56]=I>>>0&255,t[57]=I>>>8&255,t[58]=I>>>16&255,t[59]=I>>>24&255,t[60]=N>>>0&255,t[61]=N>>>8&255,t[62]=N>>>16&255,t[63]=N>>>24&255}(t,e,n,r)}function m(t,e,n,r){!function(t,e,n,r){for(var i,s=255&r[0]|(255&r[1])<<8|(255&r[2])<<16|(255&r[3])<<24,o=255&n[0]|(255&n[1])<<8|(255&n[2])<<16|(255&n[3])<<24,a=255&n[4]|(255&n[5])<<8|(255&n[6])<<16|(255&n[7])<<24,h=255&n[8]|(255&n[9])<<8|(255&n[10])<<16|(255&n[11])<<24,c=255&n[12]|(255&n[13])<<8|(255&n[14])<<16|(255&n[15])<<24,u=255&r[4]|(255&r[5])<<8|(255&r[6])<<16|(255&r[7])<<24,l=255&e[0]|(255&e[1])<<8|(255&e[2])<<16|(255&e[3])<<24,d=255&e[4]|(255&e[5])<<8|(255&e[6])<<16|(255&e[7])<<24,f=255&e[8]|(255&e[9])<<8|(255&e[10])<<16|(255&e[11])<<24,p=255&e[12]|(255&e[13])<<8|(255&e[14])<<16|(255&e[15])<<24,g=255&r[8]|(255&r[9])<<8|(255&r[10])<<16|(255&r[11])<<24,b=255&n[16]|(255&n[17])<<8|(255&n[18])<<16|(255&n[19])<<24,v=255&n[20]|(255&n[21])<<8|(255&n[22])<<16|(255&n[23])<<24,y=255&n[24]|(255&n[25])<<8|(255&n[26])<<16|(255&n[27])<<24,m=255&n[28]|(255&n[29])<<8|(255&n[30])<<16|(255&n[31])<<24,w=255&r[12]|(255&r[13])<<8|(255&r[14])<<16|(255&r[15])<<24,_=0;_<20;_+=2)s^=(i=(v^=(i=(f^=(i=(c^=(i=s+v|0)<<7|i>>>25)+s|0)<<9|i>>>23)+c|0)<<13|i>>>19)+f|0)<<18|i>>>14,u^=(i=(o^=(i=(y^=(i=(p^=(i=u+o|0)<<7|i>>>25)+u|0)<<9|i>>>23)+p|0)<<13|i>>>19)+y|0)<<18|i>>>14,g^=(i=(l^=(i=(a^=(i=(m^=(i=g+l|0)<<7|i>>>25)+g|0)<<9|i>>>23)+m|0)<<13|i>>>19)+a|0)<<18|i>>>14,w^=(i=(b^=(i=(d^=(i=(h^=(i=w+b|0)<<7|i>>>25)+w|0)<<9|i>>>23)+h|0)<<13|i>>>19)+d|0)<<18|i>>>14,s^=(i=(h^=(i=(a^=(i=(o^=(i=s+h|0)<<7|i>>>25)+s|0)<<9|i>>>23)+o|0)<<13|i>>>19)+a|0)<<18|i>>>14,u^=(i=(c^=(i=(d^=(i=(l^=(i=u+c|0)<<7|i>>>25)+u|0)<<9|i>>>23)+l|0)<<13|i>>>19)+d|0)<<18|i>>>14,g^=(i=(p^=(i=(f^=(i=(b^=(i=g+p|0)<<7|i>>>25)+g|0)<<9|i>>>23)+b|0)<<13|i>>>19)+f|0)<<18|i>>>14,w^=(i=(m^=(i=(y^=(i=(v^=(i=w+m|0)<<7|i>>>25)+w|0)<<9|i>>>23)+v|0)<<13|i>>>19)+y|0)<<18|i>>>14;t[0]=s>>>0&255,t[1]=s>>>8&255,t[2]=s>>>16&255,t[3]=s>>>24&255,t[4]=u>>>0&255,t[5]=u>>>8&255,t[6]=u>>>16&255,t[7]=u>>>24&255,t[8]=g>>>0&255,t[9]=g>>>8&255,t[10]=g>>>16&255,t[11]=g>>>24&255,t[12]=w>>>0&255,t[13]=w>>>8&255,t[14]=w>>>16&255,t[15]=w>>>24&255,t[16]=l>>>0&255,t[17]=l>>>8&255,t[18]=l>>>16&255,t[19]=l>>>24&255,t[20]=d>>>0&255,t[21]=d>>>8&255,t[22]=d>>>16&255,t[23]=d>>>24&255,t[24]=f>>>0&255,t[25]=f>>>8&255,t[26]=f>>>16&255,t[27]=f>>>24&255,t[28]=p>>>0&255,t[29]=p>>>8&255,t[30]=p>>>16&255,t[31]=p>>>24&255}(t,e,n,r)}var w=new Uint8Array([101,120,112,97,110,100,32,51,50,45,98,121,116,101,32,107]);function _(t,e,n,r,i,s,o){var a,h,c=new Uint8Array(16),u=new Uint8Array(64);for(h=0;h<16;h++)c[h]=0;for(h=0;h<8;h++)c[h]=s[h];for(;i>=64;){for(y(u,c,o,w),h=0;h<64;h++)t[e+h]=n[r+h]^u[h];for(a=1,h=8;h<16;h++)a=a+(255&c[h])|0,c[h]=255&a,a>>>=8;i-=64,e+=64,r+=64}if(i>0)for(y(u,c,o,w),h=0;h<i;h++)t[e+h]=n[r+h]^u[h];return 0}function S(t,e,n,r,i){var s,o,a=new Uint8Array(16),h=new Uint8Array(64);for(o=0;o<16;o++)a[o]=0;for(o=0;o<8;o++)a[o]=r[o];for(;n>=64;){for(y(h,a,i,w),o=0;o<64;o++)t[e+o]=h[o];for(s=1,o=8;o<16;o++)s=s+(255&a[o])|0,a[o]=255&s,s>>>=8;n-=64,e+=64}if(n>0)for(y(h,a,i,w),o=0;o<n;o++)t[e+o]=h[o];return 0}function k(t,e,n,r,i){var s=new Uint8Array(32);m(s,r,i,w);for(var o=new Uint8Array(8),a=0;a<8;a++)o[a]=r[a+16];return S(t,e,n,o,s)}function C(t,e,n,r,i,s,o){var a=new Uint8Array(32);m(a,s,o,w);for(var h=new Uint8Array(8),c=0;c<8;c++)h[c]=s[c+16];return _(t,e,n,r,i,h,a)}var T=function(t){var e,n,r,i,s,o,a,h;this.buffer=new Uint8Array(16),this.r=new Uint16Array(10),this.h=new Uint16Array(10),this.pad=new Uint16Array(8),this.leftover=0,this.fin=0,e=255&t[0]|(255&t[1])<<8,this.r[0]=8191&e,n=255&t[2]|(255&t[3])<<8,this.r[1]=8191&(e>>>13|n<<3),r=255&t[4]|(255&t[5])<<8,this.r[2]=7939&(n>>>10|r<<6),i=255&t[6]|(255&t[7])<<8,this.r[3]=8191&(r>>>7|i<<9),s=255&t[8]|(255&t[9])<<8,this.r[4]=255&(i>>>4|s<<12),this.r[5]=s>>>1&8190,o=255&t[10]|(255&t[11])<<8,this.r[6]=8191&(s>>>14|o<<2),a=255&t[12]|(255&t[13])<<8,this.r[7]=8065&(o>>>11|a<<5),h=255&t[14]|(255&t[15])<<8,this.r[8]=8191&(a>>>8|h<<8),this.r[9]=h>>>5&127,this.pad[0]=255&t[16]|(255&t[17])<<8,this.pad[1]=255&t[18]|(255&t[19])<<8,this.pad[2]=255&t[20]|(255&t[21])<<8,this.pad[3]=255&t[22]|(255&t[23])<<8,this.pad[4]=255&t[24]|(255&t[25])<<8,this.pad[5]=255&t[26]|(255&t[27])<<8,this.pad[6]=255&t[28]|(255&t[29])<<8,this.pad[7]=255&t[30]|(255&t[31])<<8};function E(t,e,n,r,i,s){var o=new T(s);return o.update(n,r,i),o.finish(t,e),0}function A(t,e,n,r,i,s){var o=new Uint8Array(16);return E(o,0,n,r,i,s),b(t,e,o,0)}function P(t,e,n,r,i){var s;if(n<32)return-1;for(C(t,0,e,0,n,r,i),E(t,16,t,32,n-32,t),s=0;s<16;s++)t[s]=0;return 0}function x(t,e,n,r,i){var s,o=new Uint8Array(32);if(n<32)return-1;if(k(o,0,32,r,i),0!==A(e,16,e,32,n-32,o))return-1;for(C(t,0,e,0,n,r,i),s=0;s<32;s++)t[s]=0;return 0}function O(t,e){var n;for(n=0;n<16;n++)t[n]=0|e[n]}function L(t){var e,n,r=1;for(e=0;e<16;e++)n=t[e]+r+65535,r=Math.floor(n/65536),t[e]=n-65536*r;t[0]+=r-1+37*(r-1)}function U(t,e,n){for(var r,i=~(n-1),s=0;s<16;s++)r=i&(t[s]^e[s]),t[s]^=r,e[s]^=r}function R(t,n){var r,i,s,o=e(),a=e();for(r=0;r<16;r++)a[r]=n[r];for(L(a),L(a),L(a),i=0;i<2;i++){for(o[0]=a[0]-65517,r=1;r<15;r++)o[r]=a[r]-65535-(o[r-1]>>16&1),o[r-1]&=65535;o[15]=a[15]-32767-(o[14]>>16&1),s=o[15]>>16&1,o[14]&=65535,U(a,o,1-s)}for(r=0;r<16;r++)t[2*r]=255&a[r],t[2*r+1]=a[r]>>8}function M(t,e){var n=new Uint8Array(32),r=new Uint8Array(32);return R(n,t),R(r,e),v(n,0,r,0)}function I(t){var e=new Uint8Array(32);return R(e,t),1&e[0]}function N(t,e){var n;for(n=0;n<16;n++)t[n]=e[2*n]+(e[2*n+1]<<8);t[15]&=32767}function j(t,e,n){for(var r=0;r<16;r++)t[r]=e[r]+n[r]}function D(t,e,n){for(var r=0;r<16;r++)t[r]=e[r]-n[r]}function z(t,e,n){var r,i,s=0,o=0,a=0,h=0,c=0,u=0,l=0,d=0,f=0,p=0,g=0,b=0,v=0,y=0,m=0,w=0,_=0,S=0,k=0,C=0,T=0,E=0,A=0,P=0,x=0,O=0,L=0,U=0,R=0,M=0,I=0,N=n[0],j=n[1],D=n[2],z=n[3],H=n[4],B=n[5],F=n[6],q=n[7],Y=n[8],K=n[9],$=n[10],J=n[11],X=n[12],W=n[13],G=n[14],V=n[15];s+=(r=e[0])*N,o+=r*j,a+=r*D,h+=r*z,c+=r*H,u+=r*B,l+=r*F,d+=r*q,f+=r*Y,p+=r*K,g+=r*$,b+=r*J,v+=r*X,y+=r*W,m+=r*G,w+=r*V,o+=(r=e[1])*N,a+=r*j,h+=r*D,c+=r*z,u+=r*H,l+=r*B,d+=r*F,f+=r*q,p+=r*Y,g+=r*K,b+=r*$,v+=r*J,y+=r*X,m+=r*W,w+=r*G,_+=r*V,a+=(r=e[2])*N,h+=r*j,c+=r*D,u+=r*z,l+=r*H,d+=r*B,f+=r*F,p+=r*q,g+=r*Y,b+=r*K,v+=r*$,y+=r*J,m+=r*X,w+=r*W,_+=r*G,S+=r*V,h+=(r=e[3])*N,c+=r*j,u+=r*D,l+=r*z,d+=r*H,f+=r*B,p+=r*F,g+=r*q,b+=r*Y,v+=r*K,y+=r*$,m+=r*J,w+=r*X,_+=r*W,S+=r*G,k+=r*V,c+=(r=e[4])*N,u+=r*j,l+=r*D,d+=r*z,f+=r*H,p+=r*B,g+=r*F,b+=r*q,v+=r*Y,y+=r*K,m+=r*$,w+=r*J,_+=r*X,S+=r*W,k+=r*G,C+=r*V,u+=(r=e[5])*N,l+=r*j,d+=r*D,f+=r*z,p+=r*H,g+=r*B,b+=r*F,v+=r*q,y+=r*Y,m+=r*K,w+=r*$,_+=r*J,S+=r*X,k+=r*W,C+=r*G,T+=r*V,l+=(r=e[6])*N,d+=r*j,f+=r*D,p+=r*z,g+=r*H,b+=r*B,v+=r*F,y+=r*q,m+=r*Y,w+=r*K,_+=r*$,S+=r*J,k+=r*X,C+=r*W,T+=r*G,E+=r*V,d+=(r=e[7])*N,f+=r*j,p+=r*D,g+=r*z,b+=r*H,v+=r*B,y+=r*F,m+=r*q,w+=r*Y,_+=r*K,S+=r*$,k+=r*J,C+=r*X,T+=r*W,E+=r*G,A+=r*V,f+=(r=e[8])*N,p+=r*j,g+=r*D,b+=r*z,v+=r*H,y+=r*B,m+=r*F,w+=r*q,_+=r*Y,S+=r*K,k+=r*$,C+=r*J,T+=r*X,E+=r*W,A+=r*G,P+=r*V,p+=(r=e[9])*N,g+=r*j,b+=r*D,v+=r*z,y+=r*H,m+=r*B,w+=r*F,_+=r*q,S+=r*Y,k+=r*K,C+=r*$,T+=r*J,E+=r*X,A+=r*W,P+=r*G,x+=r*V,g+=(r=e[10])*N,b+=r*j,v+=r*D,y+=r*z,m+=r*H,w+=r*B,_+=r*F,S+=r*q,k+=r*Y,C+=r*K,T+=r*$,E+=r*J,A+=r*X,P+=r*W,x+=r*G,O+=r*V,b+=(r=e[11])*N,v+=r*j,y+=r*D,m+=r*z,w+=r*H,_+=r*B,S+=r*F,k+=r*q,C+=r*Y,T+=r*K,E+=r*$,A+=r*J,P+=r*X,x+=r*W,O+=r*G,L+=r*V,v+=(r=e[12])*N,y+=r*j,m+=r*D,w+=r*z,_+=r*H,S+=r*B,k+=r*F,C+=r*q,T+=r*Y,E+=r*K,A+=r*$,P+=r*J,x+=r*X,O+=r*W,L+=r*G,U+=r*V,y+=(r=e[13])*N,m+=r*j,w+=r*D,_+=r*z,S+=r*H,k+=r*B,C+=r*F,T+=r*q,E+=r*Y,A+=r*K,P+=r*$,x+=r*J,O+=r*X,L+=r*W,U+=r*G,R+=r*V,m+=(r=e[14])*N,w+=r*j,_+=r*D,S+=r*z,k+=r*H,C+=r*B,T+=r*F,E+=r*q,A+=r*Y,P+=r*K,x+=r*$,O+=r*J,L+=r*X,U+=r*W,R+=r*G,M+=r*V,w+=(r=e[15])*N,o+=38*(S+=r*D),a+=38*(k+=r*z),h+=38*(C+=r*H),c+=38*(T+=r*B),u+=38*(E+=r*F),l+=38*(A+=r*q),d+=38*(P+=r*Y),f+=38*(x+=r*K),p+=38*(O+=r*$),g+=38*(L+=r*J),b+=38*(U+=r*X),v+=38*(R+=r*W),y+=38*(M+=r*G),m+=38*(I+=r*V),s=(r=(s+=38*(_+=r*j))+(i=1)+65535)-65536*(i=Math.floor(r/65536)),o=(r=o+i+65535)-65536*(i=Math.floor(r/65536)),a=(r=a+i+65535)-65536*(i=Math.floor(r/65536)),h=(r=h+i+65535)-65536*(i=Math.floor(r/65536)),c=(r=c+i+65535)-65536*(i=Math.floor(r/65536)),u=(r=u+i+65535)-65536*(i=Math.floor(r/65536)),l=(r=l+i+65535)-65536*(i=Math.floor(r/65536)),d=(r=d+i+65535)-65536*(i=Math.floor(r/65536)),f=(r=f+i+65535)-65536*(i=Math.floor(r/65536)),p=(r=p+i+65535)-65536*(i=Math.floor(r/65536)),g=(r=g+i+65535)-65536*(i=Math.floor(r/65536)),b=(r=b+i+65535)-65536*(i=Math.floor(r/65536)),v=(r=v+i+65535)-65536*(i=Math.floor(r/65536)),y=(r=y+i+65535)-65536*(i=Math.floor(r/65536)),m=(r=m+i+65535)-65536*(i=Math.floor(r/65536)),w=(r=w+i+65535)-65536*(i=Math.floor(r/65536)),s=(r=(s+=i-1+37*(i-1))+(i=1)+65535)-65536*(i=Math.floor(r/65536)),o=(r=o+i+65535)-65536*(i=Math.floor(r/65536)),a=(r=a+i+65535)-65536*(i=Math.floor(r/65536)),h=(r=h+i+65535)-65536*(i=Math.floor(r/65536)),c=(r=c+i+65535)-65536*(i=Math.floor(r/65536)),u=(r=u+i+65535)-65536*(i=Math.floor(r/65536)),l=(r=l+i+65535)-65536*(i=Math.floor(r/65536)),d=(r=d+i+65535)-65536*(i=Math.floor(r/65536)),f=(r=f+i+65535)-65536*(i=Math.floor(r/65536)),p=(r=p+i+65535)-65536*(i=Math.floor(r/65536)),g=(r=g+i+65535)-65536*(i=Math.floor(r/65536)),b=(r=b+i+65535)-65536*(i=Math.floor(r/65536)),v=(r=v+i+65535)-65536*(i=Math.floor(r/65536)),y=(r=y+i+65535)-65536*(i=Math.floor(r/65536)),m=(r=m+i+65535)-65536*(i=Math.floor(r/65536)),w=(r=w+i+65535)-65536*(i=Math.floor(r/65536)),s+=i-1+37*(i-1),t[0]=s,t[1]=o,t[2]=a,t[3]=h,t[4]=c,t[5]=u,t[6]=l,t[7]=d,t[8]=f,t[9]=p,t[10]=g,t[11]=b,t[12]=v,t[13]=y,t[14]=m,t[15]=w}function H(t,e){z(t,e,e)}function B(t,n){var r,i=e();for(r=0;r<16;r++)i[r]=n[r];for(r=253;r>=0;r--)H(i,i),2!==r&&4!==r&&z(i,i,n);for(r=0;r<16;r++)t[r]=i[r]}function F(t,n){var r,i=e();for(r=0;r<16;r++)i[r]=n[r];for(r=250;r>=0;r--)H(i,i),1!==r&&z(i,i,n);for(r=0;r<16;r++)t[r]=i[r]}function q(t,n,r){var i,s,o=new Uint8Array(32),a=new Float64Array(80),c=e(),u=e(),l=e(),d=e(),f=e(),p=e();for(s=0;s<31;s++)o[s]=n[s];for(o[31]=127&n[31]|64,o[0]&=248,N(a,r),s=0;s<16;s++)u[s]=a[s],d[s]=c[s]=l[s]=0;for(c[0]=d[0]=1,s=254;s>=0;--s)U(c,u,i=o[s>>>3]>>>(7&s)&1),U(l,d,i),j(f,c,l),D(c,c,l),j(l,u,d),D(u,u,d),H(d,f),H(p,c),z(c,l,c),z(l,u,f),j(f,c,l),D(c,c,l),H(u,c),D(l,d,p),z(c,l,h),j(c,c,d),z(l,l,c),z(c,d,p),z(d,u,a),H(u,f),U(c,u,i),U(l,d,i);for(s=0;s<16;s++)a[s+16]=c[s],a[s+32]=l[s],a[s+48]=u[s],a[s+64]=d[s];var g=a.subarray(32),b=a.subarray(16);return B(g,g),z(b,b,g),R(t,b),0}function Y(t,e){return q(t,e,s)}function K(t,e){return r(e,32),Y(t,e)}function $(t,e,n){var r=new Uint8Array(32);return q(r,n,e),m(t,i,r,w)}T.prototype.blocks=function(t,e,n){for(var r,i,s,o,a,h,c,u,l,d,f,p,g,b,v,y,m,w,_,S=this.fin?0:2048,k=this.h[0],C=this.h[1],T=this.h[2],E=this.h[3],A=this.h[4],P=this.h[5],x=this.h[6],O=this.h[7],L=this.h[8],U=this.h[9],R=this.r[0],M=this.r[1],I=this.r[2],N=this.r[3],j=this.r[4],D=this.r[5],z=this.r[6],H=this.r[7],B=this.r[8],F=this.r[9];n>=16;)d=l=0,d+=(k+=8191&(r=255&t[e+0]|(255&t[e+1])<<8))*R,d+=(C+=8191&(r>>>13|(i=255&t[e+2]|(255&t[e+3])<<8)<<3))*(5*F),d+=(T+=8191&(i>>>10|(s=255&t[e+4]|(255&t[e+5])<<8)<<6))*(5*B),d+=(E+=8191&(s>>>7|(o=255&t[e+6]|(255&t[e+7])<<8)<<9))*(5*H),l=(d+=(A+=8191&(o>>>4|(a=255&t[e+8]|(255&t[e+9])<<8)<<12))*(5*z))>>>13,d&=8191,d+=(P+=a>>>1&8191)*(5*D),d+=(x+=8191&(a>>>14|(h=255&t[e+10]|(255&t[e+11])<<8)<<2))*(5*j),d+=(O+=8191&(h>>>11|(c=255&t[e+12]|(255&t[e+13])<<8)<<5))*(5*N),d+=(L+=8191&(c>>>8|(u=255&t[e+14]|(255&t[e+15])<<8)<<8))*(5*I),f=l+=(d+=(U+=u>>>5|S)*(5*M))>>>13,f+=k*M,f+=C*R,f+=T*(5*F),f+=E*(5*B),l=(f+=A*(5*H))>>>13,f&=8191,f+=P*(5*z),f+=x*(5*D),f+=O*(5*j),f+=L*(5*N),l+=(f+=U*(5*I))>>>13,f&=8191,p=l,p+=k*I,p+=C*M,p+=T*R,p+=E*(5*F),l=(p+=A*(5*B))>>>13,p&=8191,p+=P*(5*H),p+=x*(5*z),p+=O*(5*D),p+=L*(5*j),g=l+=(p+=U*(5*N))>>>13,g+=k*N,g+=C*I,g+=T*M,g+=E*R,l=(g+=A*(5*F))>>>13,g&=8191,g+=P*(5*B),g+=x*(5*H),g+=O*(5*z),g+=L*(5*D),b=l+=(g+=U*(5*j))>>>13,b+=k*j,b+=C*N,b+=T*I,b+=E*M,l=(b+=A*R)>>>13,b&=8191,b+=P*(5*F),b+=x*(5*B),b+=O*(5*H),b+=L*(5*z),v=l+=(b+=U*(5*D))>>>13,v+=k*D,v+=C*j,v+=T*N,v+=E*I,l=(v+=A*M)>>>13,v&=8191,v+=P*R,v+=x*(5*F),v+=O*(5*B),v+=L*(5*H),y=l+=(v+=U*(5*z))>>>13,y+=k*z,y+=C*D,y+=T*j,y+=E*N,l=(y+=A*I)>>>13,y&=8191,y+=P*M,y+=x*R,y+=O*(5*F),y+=L*(5*B),m=l+=(y+=U*(5*H))>>>13,m+=k*H,m+=C*z,m+=T*D,m+=E*j,l=(m+=A*N)>>>13,m&=8191,m+=P*I,m+=x*M,m+=O*R,m+=L*(5*F),w=l+=(m+=U*(5*B))>>>13,w+=k*B,w+=C*H,w+=T*z,w+=E*D,l=(w+=A*j)>>>13,w&=8191,w+=P*N,w+=x*I,w+=O*M,w+=L*R,_=l+=(w+=U*(5*F))>>>13,_+=k*F,_+=C*B,_+=T*H,_+=E*z,l=(_+=A*D)>>>13,_&=8191,_+=P*j,_+=x*N,_+=O*I,_+=L*M,k=d=8191&(l=(l=((l+=(_+=U*R)>>>13)<<2)+l|0)+(d&=8191)|0),C=f+=l>>>=13,T=p&=8191,E=g&=8191,A=b&=8191,P=v&=8191,x=y&=8191,O=m&=8191,L=w&=8191,U=_&=8191,e+=16,n-=16;this.h[0]=k,this.h[1]=C,this.h[2]=T,this.h[3]=E,this.h[4]=A,this.h[5]=P,this.h[6]=x,this.h[7]=O,this.h[8]=L,this.h[9]=U},T.prototype.finish=function(t,e){var n,r,i,s,o=new Uint16Array(10);if(this.leftover){for(s=this.leftover,this.buffer[s++]=1;s<16;s++)this.buffer[s]=0;this.fin=1,this.blocks(this.buffer,0,16)}for(n=this.h[1]>>>13,this.h[1]&=8191,s=2;s<10;s++)this.h[s]+=n,n=this.h[s]>>>13,this.h[s]&=8191;for(this.h[0]+=5*n,n=this.h[0]>>>13,this.h[0]&=8191,this.h[1]+=n,n=this.h[1]>>>13,this.h[1]&=8191,this.h[2]+=n,o[0]=this.h[0]+5,n=o[0]>>>13,o[0]&=8191,s=1;s<10;s++)o[s]=this.h[s]+n,n=o[s]>>>13,o[s]&=8191;for(o[9]-=8192,r=(1^n)-1,s=0;s<10;s++)o[s]&=r;for(r=~r,s=0;s<10;s++)this.h[s]=this.h[s]&r|o[s];for(this.h[0]=65535&(this.h[0]|this.h[1]<<13),this.h[1]=65535&(this.h[1]>>>3|this.h[2]<<10),this.h[2]=65535&(this.h[2]>>>6|this.h[3]<<7),this.h[3]=65535&(this.h[3]>>>9|this.h[4]<<4),this.h[4]=65535&(this.h[4]>>>12|this.h[5]<<1|this.h[6]<<14),this.h[5]=65535&(this.h[6]>>>2|this.h[7]<<11),this.h[6]=65535&(this.h[7]>>>5|this.h[8]<<8),this.h[7]=65535&(this.h[8]>>>8|this.h[9]<<5),i=this.h[0]+this.pad[0],this.h[0]=65535&i,s=1;s<8;s++)i=(this.h[s]+this.pad[s]|0)+(i>>>16)|0,this.h[s]=65535&i;t[e+0]=this.h[0]>>>0&255,t[e+1]=this.h[0]>>>8&255,t[e+2]=this.h[1]>>>0&255,t[e+3]=this.h[1]>>>8&255,t[e+4]=this.h[2]>>>0&255,t[e+5]=this.h[2]>>>8&255,t[e+6]=this.h[3]>>>0&255,t[e+7]=this.h[3]>>>8&255,t[e+8]=this.h[4]>>>0&255,t[e+9]=this.h[4]>>>8&255,t[e+10]=this.h[5]>>>0&255,t[e+11]=this.h[5]>>>8&255,t[e+12]=this.h[6]>>>0&255,t[e+13]=this.h[6]>>>8&255,t[e+14]=this.h[7]>>>0&255,t[e+15]=this.h[7]>>>8&255},T.prototype.update=function(t,e,n){var r,i;if(this.leftover){for((i=16-this.leftover)>n&&(i=n),r=0;r<i;r++)this.buffer[this.leftover+r]=t[e+r];if(n-=i,e+=i,this.leftover+=i,this.leftover<16)return;this.blocks(this.buffer,0,16),this.leftover=0}if(n>=16&&(i=n-n%16,this.blocks(t,e,i),e+=i,n-=i),n){for(r=0;r<n;r++)this.buffer[this.leftover+r]=t[e+r];this.leftover+=n}};var J=P,X=x;var W=[1116352408,3609767458,1899447441,602891725,3049323471,3964484399,3921009573,2173295548,961987163,4081628472,1508970993,3053834265,2453635748,2937671579,2870763221,3664609560,3624381080,2734883394,310598401,1164996542,607225278,1323610764,1426881987,3590304994,1925078388,4068182383,2162078206,991336113,2614888103,633803317,3248222580,3479774868,3835390401,2666613458,4022224774,944711139,264347078,2341262773,604807628,2007800933,770255983,1495990901,1249150122,1856431235,1555081692,3175218132,1996064986,2198950837,2554220882,3999719339,2821834349,766784016,2952996808,2566594879,3210313671,3203337956,3336571891,1034457026,3584528711,2466948901,113926993,3758326383,338241895,168717936,666307205,1188179964,773529912,1546045734,1294757372,1522805485,1396182291,2643833823,1695183700,2343527390,1986661051,1014477480,2177026350,1206759142,2456956037,344077627,2730485921,1290863460,2820302411,3158454273,3259730800,3505952657,3345764771,106217008,3516065817,3606008344,3600352804,1432725776,4094571909,1467031594,275423344,851169720,430227734,3100823752,506948616,1363258195,659060556,3750685593,883997877,3785050280,958139571,3318307427,1322822218,3812723403,1537002063,2003034995,1747873779,3602036899,1955562222,1575990012,2024104815,1125592928,2227730452,2716904306,2361852424,442776044,2428436474,593698344,2756734187,3733110249,3204031479,2999351573,3329325298,3815920427,3391569614,3928383900,3515267271,566280711,3940187606,3454069534,4118630271,4000239992,116418474,1914138554,174292421,2731055270,289380356,3203993006,460393269,320620315,685471733,587496836,852142971,1086792851,1017036298,365543100,1126000580,2618297676,1288033470,3409855158,1501505948,4234509866,1607167915,987167468,1816402316,1246189591];function G(t,e,n,r){for(var i,s,o,a,h,c,u,l,d,f,p,g,b,v,y,m,w,_,S,k,C,T,E,A,P,x,O=new Int32Array(16),L=new Int32Array(16),U=t[0],R=t[1],M=t[2],I=t[3],N=t[4],j=t[5],D=t[6],z=t[7],H=e[0],B=e[1],F=e[2],q=e[3],Y=e[4],K=e[5],$=e[6],J=e[7],X=0;r>=128;){for(S=0;S<16;S++)k=8*S+X,O[S]=n[k+0]<<24|n[k+1]<<16|n[k+2]<<8|n[k+3],L[S]=n[k+4]<<24|n[k+5]<<16|n[k+6]<<8|n[k+7];for(S=0;S<80;S++)if(i=U,s=R,o=M,a=I,h=N,c=j,u=D,z,d=H,f=B,p=F,g=q,b=Y,v=K,y=$,J,E=65535&(T=J),A=T>>>16,P=65535&(C=z),x=C>>>16,E+=65535&(T=(Y>>>14|N<<18)^(Y>>>18|N<<14)^(N>>>9|Y<<23)),A+=T>>>16,P+=65535&(C=(N>>>14|Y<<18)^(N>>>18|Y<<14)^(Y>>>9|N<<23)),x+=C>>>16,E+=65535&(T=Y&K^~Y&$),A+=T>>>16,P+=65535&(C=N&j^~N&D),x+=C>>>16,E+=65535&(T=W[2*S+1]),A+=T>>>16,P+=65535&(C=W[2*S]),x+=C>>>16,C=O[S%16],A+=(T=L[S%16])>>>16,P+=65535&C,x+=C>>>16,P+=(A+=(E+=65535&T)>>>16)>>>16,E=65535&(T=_=65535&E|A<<16),A=T>>>16,P=65535&(C=w=65535&P|(x+=P>>>16)<<16),x=C>>>16,E+=65535&(T=(H>>>28|U<<4)^(U>>>2|H<<30)^(U>>>7|H<<25)),A+=T>>>16,P+=65535&(C=(U>>>28|H<<4)^(H>>>2|U<<30)^(H>>>7|U<<25)),x+=C>>>16,A+=(T=H&B^H&F^B&F)>>>16,P+=65535&(C=U&R^U&M^R&M),x+=C>>>16,l=65535&(P+=(A+=(E+=65535&T)>>>16)>>>16)|(x+=P>>>16)<<16,m=65535&E|A<<16,E=65535&(T=g),A=T>>>16,P=65535&(C=a),x=C>>>16,A+=(T=_)>>>16,P+=65535&(C=w),x+=C>>>16,R=i,M=s,I=o,N=a=65535&(P+=(A+=(E+=65535&T)>>>16)>>>16)|(x+=P>>>16)<<16,j=h,D=c,z=u,U=l,B=d,F=f,q=p,Y=g=65535&E|A<<16,K=b,$=v,J=y,H=m,S%16==15)for(k=0;k<16;k++)C=O[k],E=65535&(T=L[k]),A=T>>>16,P=65535&C,x=C>>>16,C=O[(k+9)%16],E+=65535&(T=L[(k+9)%16]),A+=T>>>16,P+=65535&C,x+=C>>>16,w=O[(k+1)%16],E+=65535&(T=((_=L[(k+1)%16])>>>1|w<<31)^(_>>>8|w<<24)^(_>>>7|w<<25)),A+=T>>>16,P+=65535&(C=(w>>>1|_<<31)^(w>>>8|_<<24)^w>>>7),x+=C>>>16,w=O[(k+14)%16],A+=(T=((_=L[(k+14)%16])>>>19|w<<13)^(w>>>29|_<<3)^(_>>>6|w<<26))>>>16,P+=65535&(C=(w>>>19|_<<13)^(_>>>29|w<<3)^w>>>6),x+=C>>>16,x+=(P+=(A+=(E+=65535&T)>>>16)>>>16)>>>16,O[k]=65535&P|x<<16,L[k]=65535&E|A<<16;E=65535&(T=H),A=T>>>16,P=65535&(C=U),x=C>>>16,C=t[0],A+=(T=e[0])>>>16,P+=65535&C,x+=C>>>16,x+=(P+=(A+=(E+=65535&T)>>>16)>>>16)>>>16,t[0]=U=65535&P|x<<16,e[0]=H=65535&E|A<<16,E=65535&(T=B),A=T>>>16,P=65535&(C=R),x=C>>>16,C=t[1],A+=(T=e[1])>>>16,P+=65535&C,x+=C>>>16,x+=(P+=(A+=(E+=65535&T)>>>16)>>>16)>>>16,t[1]=R=65535&P|x<<16,e[1]=B=65535&E|A<<16,E=65535&(T=F),A=T>>>16,P=65535&(C=M),x=C>>>16,C=t[2],A+=(T=e[2])>>>16,P+=65535&C,x+=C>>>16,x+=(P+=(A+=(E+=65535&T)>>>16)>>>16)>>>16,t[2]=M=65535&P|x<<16,e[2]=F=65535&E|A<<16,E=65535&(T=q),A=T>>>16,P=65535&(C=I),x=C>>>16,C=t[3],A+=(T=e[3])>>>16,P+=65535&C,x+=C>>>16,x+=(P+=(A+=(E+=65535&T)>>>16)>>>16)>>>16,t[3]=I=65535&P|x<<16,e[3]=q=65535&E|A<<16,E=65535&(T=Y),A=T>>>16,P=65535&(C=N),x=C>>>16,C=t[4],A+=(T=e[4])>>>16,P+=65535&C,x+=C>>>16,x+=(P+=(A+=(E+=65535&T)>>>16)>>>16)>>>16,t[4]=N=65535&P|x<<16,e[4]=Y=65535&E|A<<16,E=65535&(T=K),A=T>>>16,P=65535&(C=j),x=C>>>16,C=t[5],A+=(T=e[5])>>>16,P+=65535&C,x+=C>>>16,x+=(P+=(A+=(E+=65535&T)>>>16)>>>16)>>>16,t[5]=j=65535&P|x<<16,e[5]=K=65535&E|A<<16,E=65535&(T=$),A=T>>>16,P=65535&(C=D),x=C>>>16,C=t[6],A+=(T=e[6])>>>16,P+=65535&C,x+=C>>>16,x+=(P+=(A+=(E+=65535&T)>>>16)>>>16)>>>16,t[6]=D=65535&P|x<<16,e[6]=$=65535&E|A<<16,E=65535&(T=J),A=T>>>16,P=65535&(C=z),x=C>>>16,C=t[7],A+=(T=e[7])>>>16,P+=65535&C,x+=C>>>16,x+=(P+=(A+=(E+=65535&T)>>>16)>>>16)>>>16,t[7]=z=65535&P|x<<16,e[7]=J=65535&E|A<<16,X+=128,r-=128}return r}function V(t,e,n){var r,i=new Int32Array(8),s=new Int32Array(8),o=new Uint8Array(256),a=n;for(i[0]=1779033703,i[1]=3144134277,i[2]=1013904242,i[3]=2773480762,i[4]=1359893119,i[5]=2600822924,i[6]=528734635,i[7]=1541459225,s[0]=4089235720,s[1]=2227873595,s[2]=4271175723,s[3]=1595750129,s[4]=2917565137,s[5]=725511199,s[6]=4215389547,s[7]=327033209,G(i,s,e,n),n%=128,r=0;r<n;r++)o[r]=e[a-n+r];for(o[n]=128,o[(n=256-128*(n<112?1:0))-9]=0,p(o,n-8,a/536870912|0,a<<3),G(i,s,o,n),r=0;r<8;r++)p(t,8*r,i[r],s[r]);return 0}function Z(t,n){var r=e(),i=e(),s=e(),o=e(),a=e(),h=e(),c=e(),l=e(),d=e();D(r,t[1],t[0]),D(d,n[1],n[0]),z(r,r,d),j(i,t[0],t[1]),j(d,n[0],n[1]),z(i,i,d),z(s,t[3],n[3]),z(s,s,u),z(o,t[2],n[2]),j(o,o,o),D(a,i,r),D(h,o,s),j(c,o,s),j(l,i,r),z(t[0],a,h),z(t[1],l,c),z(t[2],c,h),z(t[3],a,l)}function Q(t,e,n){var r;for(r=0;r<4;r++)U(t[r],e[r],n)}function tt(t,n){var r=e(),i=e(),s=e();B(s,n[2]),z(r,n[0],s),z(i,n[1],s),R(t,i),t[31]^=I(r)<<7}function et(t,e,n){var r,i;for(O(t[0],o),O(t[1],a),O(t[2],a),O(t[3],o),i=255;i>=0;--i)Q(t,e,r=n[i/8|0]>>(7&i)&1),Z(e,t),Z(t,t),Q(t,e,r)}function nt(t,n){var r=[e(),e(),e(),e()];O(r[0],l),O(r[1],d),O(r[2],a),z(r[3],l,d),et(t,r,n)}function rt(t,n,i){var s,o=new Uint8Array(64),a=[e(),e(),e(),e()];for(i||r(n,32),V(o,n,32),o[0]&=248,o[31]&=127,o[31]|=64,nt(a,o),tt(t,a),s=0;s<32;s++)n[s+32]=t[s];return 0}var it=new Float64Array([237,211,245,92,26,99,18,88,214,156,247,162,222,249,222,20,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,16]);function st(t,e){var n,r,i,s;for(r=63;r>=32;--r){for(n=0,i=r-32,s=r-12;i<s;++i)e[i]+=n-16*e[r]*it[i-(r-32)],n=Math.floor((e[i]+128)/256),e[i]-=256*n;e[i]+=n,e[r]=0}for(n=0,i=0;i<32;i++)e[i]+=n-(e[31]>>4)*it[i],n=e[i]>>8,e[i]&=255;for(i=0;i<32;i++)e[i]-=n*it[i];for(r=0;r<32;r++)e[r+1]+=e[r]>>8,t[r]=255&e[r]}function ot(t){var e,n=new Float64Array(64);for(e=0;e<64;e++)n[e]=t[e];for(e=0;e<64;e++)t[e]=0;st(t,n)}function at(t,n,r,i){var s,o,a=new Uint8Array(64),h=new Uint8Array(64),c=new Uint8Array(64),u=new Float64Array(64),l=[e(),e(),e(),e()];V(a,i,32),a[0]&=248,a[31]&=127,a[31]|=64;var d=r+64;for(s=0;s<r;s++)t[64+s]=n[s];for(s=0;s<32;s++)t[32+s]=a[32+s];for(V(c,t.subarray(32),r+32),ot(c),nt(l,c),tt(t,l),s=32;s<64;s++)t[s]=i[s];for(V(h,t,r+64),ot(h),s=0;s<64;s++)u[s]=0;for(s=0;s<32;s++)u[s]=c[s];for(s=0;s<32;s++)for(o=0;o<32;o++)u[s+o]+=h[s]*a[o];return st(t.subarray(32),u),d}function ht(t,n,r,i){var s,h=new Uint8Array(32),u=new Uint8Array(64),l=[e(),e(),e(),e()],d=[e(),e(),e(),e()];if(r<64)return-1;if(function(t,n){var r=e(),i=e(),s=e(),h=e(),u=e(),l=e(),d=e();return O(t[2],a),N(t[1],n),H(s,t[1]),z(h,s,c),D(s,s,t[2]),j(h,t[2],h),H(u,h),H(l,u),z(d,l,u),z(r,d,s),z(r,r,h),F(r,r),z(r,r,s),z(r,r,h),z(r,r,h),z(t[0],r,h),H(i,t[0]),z(i,i,h),M(i,s)&&z(t[0],t[0],f),H(i,t[0]),z(i,i,h),M(i,s)?-1:(I(t[0])===n[31]>>7&&D(t[0],o,t[0]),z(t[3],t[0],t[1]),0)}(d,i))return-1;for(s=0;s<r;s++)t[s]=n[s];for(s=0;s<32;s++)t[s+32]=i[s];if(V(u,t,r),ot(u),et(l,d,u),nt(d,n.subarray(32)),Z(l,d),tt(h,l),r-=64,v(n,0,h,0)){for(s=0;s<r;s++)t[s]=0;return-1}for(s=0;s<r;s++)t[s]=n[s+64];return r}function ct(t,e){if(32!==t.length)throw new Error("bad key size");if(24!==e.length)throw new Error("bad nonce size")}function ut(){for(var t=0;t<arguments.length;t++)if(!(arguments[t]instanceof Uint8Array))throw new TypeError("unexpected type, use Uint8Array")}function lt(t){for(var e=0;e<t.length;e++)t[e]=0}t.lowlevel={crypto_core_hsalsa20:m,crypto_stream_xor:C,crypto_stream:k,crypto_stream_salsa20_xor:_,crypto_stream_salsa20:S,crypto_onetimeauth:E,crypto_onetimeauth_verify:A,crypto_verify_16:b,crypto_verify_32:v,crypto_secretbox:P,crypto_secretbox_open:x,crypto_scalarmult:q,crypto_scalarmult_base:Y,crypto_box_beforenm:$,crypto_box_afternm:J,crypto_box:function(t,e,n,r,i,s){var o=new Uint8Array(32);return $(o,i,s),J(t,e,n,r,o)},crypto_box_open:function(t,e,n,r,i,s){var o=new Uint8Array(32);return $(o,i,s),X(t,e,n,r,o)},crypto_box_keypair:K,crypto_hash:V,crypto_sign:at,crypto_sign_keypair:rt,crypto_sign_open:ht,crypto_secretbox_KEYBYTES:32,crypto_secretbox_NONCEBYTES:24,crypto_secretbox_ZEROBYTES:32,crypto_secretbox_BOXZEROBYTES:16,crypto_scalarmult_BYTES:32,crypto_scalarmult_SCALARBYTES:32,crypto_box_PUBLICKEYBYTES:32,crypto_box_SECRETKEYBYTES:32,crypto_box_BEFORENMBYTES:32,crypto_box_NONCEBYTES:24,crypto_box_ZEROBYTES:32,crypto_box_BOXZEROBYTES:16,crypto_sign_BYTES:64,crypto_sign_PUBLICKEYBYTES:32,crypto_sign_SECRETKEYBYTES:64,crypto_sign_SEEDBYTES:32,crypto_hash_BYTES:64,gf:e,D:c,L:it,pack25519:R,unpack25519:N,M:z,A:j,S:H,Z:D,pow2523:F,add:Z,set25519:O,modL:st,scalarmult:et,scalarbase:nt},t.randomBytes=function(t){var e=new Uint8Array(t);return r(e,t),e},t.secretbox=function(t,e,n){ut(t,e,n),ct(n,e);for(var r=new Uint8Array(32+t.length),i=new Uint8Array(r.length),s=0;s<t.length;s++)r[s+32]=t[s];return P(i,r,r.length,e,n),i.subarray(16)},t.secretbox.open=function(t,e,n){ut(t,e,n),ct(n,e);for(var r=new Uint8Array(16+t.length),i=new Uint8Array(r.length),s=0;s<t.length;s++)r[s+16]=t[s];return r.length<32||0!==x(i,r,r.length,e,n)?null:i.subarray(32)},t.secretbox.keyLength=32,t.secretbox.nonceLength=24,t.secretbox.overheadLength=16,t.scalarMult=function(t,e){if(ut(t,e),32!==t.length)throw new Error("bad n size");if(32!==e.length)throw new Error("bad p size");var n=new Uint8Array(32);return q(n,t,e),n},t.scalarMult.base=function(t){if(ut(t),32!==t.length)throw new Error("bad n size");var e=new Uint8Array(32);return Y(e,t),e},t.scalarMult.scalarLength=32,t.scalarMult.groupElementLength=32,t.box=function(e,n,r,i){var s=t.box.before(r,i);return t.secretbox(e,n,s)},t.box.before=function(t,e){ut(t,e),function(t,e){if(32!==t.length)throw new Error("bad public key size");if(32!==e.length)throw new Error("bad secret key size")}(t,e);var n=new Uint8Array(32);return $(n,t,e),n},t.box.after=t.secretbox,t.box.open=function(e,n,r,i){var s=t.box.before(r,i);return t.secretbox.open(e,n,s)},t.box.open.after=t.secretbox.open,t.box.keyPair=function(){var t=new Uint8Array(32),e=new Uint8Array(32);return K(t,e),{publicKey:t,secretKey:e}},t.box.keyPair.fromSecretKey=function(t){if(ut(t),32!==t.length)throw new Error("bad secret key size");var e=new Uint8Array(32);return Y(e,t),{publicKey:e,secretKey:new Uint8Array(t)}},t.box.publicKeyLength=32,t.box.secretKeyLength=32,t.box.sharedKeyLength=32,t.box.nonceLength=24,t.box.overheadLength=t.secretbox.overheadLength,t.sign=function(t,e){if(ut(t,e),64!==e.length)throw new Error("bad secret key size");var n=new Uint8Array(64+t.length);return at(n,t,t.length,e),n},t.sign.open=function(t,e){if(ut(t,e),32!==e.length)throw new Error("bad public key size");var n=new Uint8Array(t.length),r=ht(n,t,t.length,e);if(r<0)return null;for(var i=new Uint8Array(r),s=0;s<i.length;s++)i[s]=n[s];return i},t.sign.detached=function(e,n){for(var r=t.sign(e,n),i=new Uint8Array(64),s=0;s<i.length;s++)i[s]=r[s];return i},t.sign.detached.verify=function(t,e,n){if(ut(t,e,n),64!==e.length)throw new Error("bad signature size");if(32!==n.length)throw new Error("bad public key size");var r,i=new Uint8Array(64+t.length),s=new Uint8Array(64+t.length);for(r=0;r<64;r++)i[r]=e[r];for(r=0;r<t.length;r++)i[r+64]=t[r];return ht(s,i,i.length,n)>=0},t.sign.keyPair=function(){var t=new Uint8Array(32),e=new Uint8Array(64);return rt(t,e),{publicKey:t,secretKey:e}},t.sign.keyPair.fromSecretKey=function(t){if(ut(t),64!==t.length)throw new Error("bad secret key size");for(var e=new Uint8Array(32),n=0;n<e.length;n++)e[n]=t[32+n];return{publicKey:e,secretKey:new Uint8Array(t)}},t.sign.keyPair.fromSeed=function(t){if(ut(t),32!==t.length)throw new Error("bad seed size");for(var e=new Uint8Array(32),n=new Uint8Array(64),r=0;r<32;r++)n[r]=t[r];return rt(e,n,!0),{publicKey:e,secretKey:n}},t.sign.publicKeyLength=32,t.sign.secretKeyLength=64,t.sign.seedLength=32,t.sign.signatureLength=64,t.hash=function(t){ut(t);var e=new Uint8Array(64);return V(e,t,t.length),e},t.hash.hashLength=64,t.verify=function(t,e){return ut(t,e),0!==t.length&&0!==e.length&&(t.length===e.length&&0===g(t,0,e,0,t.length))},t.setPRNG=function(t){r=t},function(){var e="undefined"!=typeof self?self.crypto||self.msCrypto:null;if(e&&e.getRandomValues){t.setPRNG((function(t,n){var r,i=new Uint8Array(n);for(r=0;r<n;r+=65536)e.getRandomValues(i.subarray(r,r+Math.min(n-r,65536)));for(r=0;r<n;r++)t[r]=i[r];lt(i)}))}else(e=n(17))&&e.randomBytes&&t.setPRNG((function(t,n){var r,i=e.randomBytes(n);for(r=0;r<n;r++)t[r]=i[r];lt(i)}))}()}(t.exports?t.exports:self.nacl=self.nacl||{})},function(t,e,n){t.exports=n(16).default},function(t,e,n){"use strict";n.r(e),n.d(e,"default",(function(){return o}));var r=n(5),i=n(7),s=n(14);class o extends r.a{constructor(t,e){r.a.logToConsole=o.logToConsole,r.a.log=o.log,Object(i.a)(e),e.nacl=s,super(t,e)}}},function(t,e){}]);
//# sourceMappingURL=pusher.js.map