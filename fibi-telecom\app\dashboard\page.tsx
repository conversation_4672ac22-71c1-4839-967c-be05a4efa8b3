'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/hooks/useAuth'
import { Phone, Wifi, Shield, Settings, LogOut, User, CreditCard, BarChart3 } from 'lucide-react'

export default function DashboardPage() {
  const { user, profile, loading, signOut } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!loading && !user) {
      router.push('/auth/login')
    }
  }, [user, loading, router])

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Загрузка...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return null
  }

  const handleSignOut = async () => {
    await signOut()
    router.push('/')
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <Phone className="h-8 w-8 text-blue-600 mr-2" />
              <span className="text-2xl font-bold text-gray-900">Fibi Telecom</span>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-gray-700">
                Добро пожаловать, {profile?.full_name || user.email}
              </span>
              <button
                onClick={handleSignOut}
                className="flex items-center text-gray-700 hover:text-red-600"
              >
                <LogOut className="h-5 w-5 mr-1" />
                Выйти
              </button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Welcome Section */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Личный кабинет
          </h1>
          <p className="text-gray-600">
            Управляйте вашими услугами и настройками
          </p>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <Wifi className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Интернет</p>
                <p className="text-2xl font-bold text-gray-900">100 Мбит/с</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <Phone className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Телефония</p>
                <p className="text-2xl font-bold text-gray-900">Активна</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <CreditCard className="h-8 w-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Баланс</p>
                <p className="text-2xl font-bold text-gray-900">1,250 ₽</p>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Services */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-medium text-gray-900">Мои услуги</h2>
              </div>
              <div className="p-6">
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                    <div className="flex items-center">
                      <Wifi className="h-6 w-6 text-blue-600 mr-3" />
                      <div>
                        <h3 className="font-medium">Интернет "Стандарт"</h3>
                        <p className="text-sm text-gray-600">100 Мбит/с • Безлимит</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">990 ₽/мес</p>
                      <p className="text-sm text-green-600">Активна</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                    <div className="flex items-center">
                      <Phone className="h-6 w-6 text-green-600 mr-3" />
                      <div>
                        <h3 className="font-medium">IP-телефония</h3>
                        <p className="text-sm text-gray-600">+7 (495) 123-45-67</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">290 ₽/мес</p>
                      <p className="text-sm text-green-600">Активна</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg opacity-50">
                    <div className="flex items-center">
                      <Shield className="h-6 w-6 text-gray-400 mr-3" />
                      <div>
                        <h3 className="font-medium">Антивирус</h3>
                        <p className="text-sm text-gray-600">Защита устройств</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">190 ₽/мес</p>
                      <p className="text-sm text-gray-500">Не подключена</p>
                    </div>
                  </div>
                </div>
                
                <button className="mt-6 w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors">
                  Подключить новую услугу
                </button>
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Account Info */}
            <div className="bg-white rounded-lg shadow">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-medium text-gray-900">Информация об аккаунте</h2>
              </div>
              <div className="p-6">
                <div className="space-y-3">
                  <div className="flex items-center">
                    <User className="h-5 w-5 text-gray-400 mr-3" />
                    <div>
                      <p className="text-sm text-gray-600">Имя</p>
                      <p className="font-medium">{profile?.full_name || 'Не указано'}</p>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <Phone className="h-5 w-5 text-gray-400 mr-3" />
                    <div>
                      <p className="text-sm text-gray-600">Email</p>
                      <p className="font-medium">{user.email}</p>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <CreditCard className="h-5 w-5 text-gray-400 mr-3" />
                    <div>
                      <p className="text-sm text-gray-600">ID клиента</p>
                      <p className="font-medium">#{user.id.slice(0, 8)}</p>
                    </div>
                  </div>
                </div>
                
                <button className="mt-4 w-full border border-gray-300 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-50 transition-colors">
                  Редактировать профиль
                </button>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="bg-white rounded-lg shadow">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-medium text-gray-900">Быстрые действия</h2>
              </div>
              <div className="p-6">
                <div className="space-y-3">
                  <button className="w-full flex items-center text-left p-3 rounded-lg hover:bg-gray-50 transition-colors">
                    <CreditCard className="h-5 w-5 text-blue-600 mr-3" />
                    Пополнить баланс
                  </button>
                  <button className="w-full flex items-center text-left p-3 rounded-lg hover:bg-gray-50 transition-colors">
                    <BarChart3 className="h-5 w-5 text-green-600 mr-3" />
                    Статистика использования
                  </button>
                  <button className="w-full flex items-center text-left p-3 rounded-lg hover:bg-gray-50 transition-colors">
                    <Settings className="h-5 w-5 text-gray-600 mr-3" />
                    Настройки
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
