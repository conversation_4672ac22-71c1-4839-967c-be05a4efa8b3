'use client'

import { useState, useEffect } from 'react'
import { 
  LineChart, 
  Line, 
  AreaChart, 
  Area, 
  BarChart, 
  Bar, 
  PieChart, 
  Pie, 
  Cell,
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer 
} from 'recharts'
import { 
  TrendingUp, 
  TrendingDown, 
  Users, 
  DollarSign, 
  Activity,
  Calendar
} from 'lucide-react'
import { supabase } from '@/lib/supabase'
import { format, subDays, startOfDay } from 'date-fns'
import { ru } from 'date-fns/locale'

interface AnalyticsData {
  revenue: Array<{ date: string; amount: number }>
  users: Array<{ date: string; count: number }>
  services: Array<{ name: string; count: number; revenue: number }>
  payments: Array<{ status: string; count: number }>
}

interface StatsCard {
  title: string
  value: string
  change: number
  icon: React.ComponentType<any>
  color: string
}

export default function Analytics() {
  const [data, setData] = useState<AnalyticsData>({
    revenue: [],
    users: [],
    services: [],
    payments: []
  })
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d'>('30d')
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    fetchAnalyticsData()
  }, [timeRange])

  const fetchAnalyticsData = async () => {
    setIsLoading(true)
    try {
      const days = timeRange === '7d' ? 7 : timeRange === '30d' ? 30 : 90
      const startDate = startOfDay(subDays(new Date(), days))

      // Получаем данные о доходах
      const { data: paymentsData } = await supabase
        .from('payments')
        .select('amount, created_at, status')
        .gte('created_at', startDate.toISOString())
        .eq('status', 'completed')

      // Получаем данные о пользователях
      const { data: usersData } = await supabase
        .from('profiles')
        .select('created_at')
        .gte('created_at', startDate.toISOString())

      // Получаем данные об услугах
      const { data: servicesData } = await supabase
        .from('user_services')
        .select(`
          service_id,
          services(name),
          created_at
        `)
        .gte('created_at', startDate.toISOString())

      // Обрабатываем данные для графиков
      const revenueByDate = processRevenueData(paymentsData || [])
      const usersByDate = processUsersData(usersData || [])
      const serviceStats = processServicesData(servicesData || [])
      const paymentStats = processPaymentStats(paymentsData || [])

      setData({
        revenue: revenueByDate,
        users: usersByDate,
        services: serviceStats,
        payments: paymentStats
      })
    } catch (error) {
      console.error('Error fetching analytics:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const processRevenueData = (payments: any[]) => {
    const revenueMap = new Map()
    
    payments.forEach(payment => {
      const date = format(new Date(payment.created_at), 'yyyy-MM-dd')
      const current = revenueMap.get(date) || 0
      revenueMap.set(date, current + payment.amount / 100)
    })

    return Array.from(revenueMap.entries()).map(([date, amount]) => ({
      date: format(new Date(date), 'dd MMM', { locale: ru }),
      amount
    }))
  }

  const processUsersData = (users: any[]) => {
    const usersMap = new Map()
    
    users.forEach(user => {
      const date = format(new Date(user.created_at), 'yyyy-MM-dd')
      const current = usersMap.get(date) || 0
      usersMap.set(date, current + 1)
    })

    return Array.from(usersMap.entries()).map(([date, count]) => ({
      date: format(new Date(date), 'dd MMM', { locale: ru }),
      count
    }))
  }

  const processServicesData = (services: any[]) => {
    const servicesMap = new Map()
    
    services.forEach(service => {
      const name = service.services?.name || 'Неизвестно'
      const current = servicesMap.get(name) || { count: 0, revenue: 0 }
      servicesMap.set(name, {
        count: current.count + 1,
        revenue: current.revenue + 990 // Примерная цена
      })
    })

    return Array.from(servicesMap.entries()).map(([name, stats]) => ({
      name,
      ...stats
    }))
  }

  const processPaymentStats = (payments: any[]) => {
    const statusMap = new Map()
    
    payments.forEach(payment => {
      const current = statusMap.get(payment.status) || 0
      statusMap.set(payment.status, current + 1)
    })

    return Array.from(statusMap.entries()).map(([status, count]) => ({
      status: status === 'completed' ? 'Успешные' : 'Неуспешные',
      count
    }))
  }

  const statsCards: StatsCard[] = [
    {
      title: 'Общий доход',
      value: `${data.revenue.reduce((sum, item) => sum + item.amount, 0).toLocaleString('ru-RU')} ₽`,
      change: 12.5,
      icon: DollarSign,
      color: 'text-green-600'
    },
    {
      title: 'Новые пользователи',
      value: data.users.reduce((sum, item) => sum + item.count, 0).toString(),
      change: 8.2,
      icon: Users,
      color: 'text-blue-600'
    },
    {
      title: 'Активные услуги',
      value: data.services.reduce((sum, item) => sum + item.count, 0).toString(),
      change: -2.1,
      icon: Activity,
      color: 'text-purple-600'
    },
    {
      title: 'Конверсия',
      value: '94.2%',
      change: 1.8,
      icon: TrendingUp,
      color: 'text-orange-600'
    }
  ]

  const COLORS = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6']

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Time Range Selector */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900">Аналитика</h2>
        <div className="flex space-x-2">
          {(['7d', '30d', '90d'] as const).map((range) => (
            <button
              key={range}
              onClick={() => setTimeRange(range)}
              className={`px-4 py-2 rounded-md text-sm font-medium ${
                timeRange === range
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {range === '7d' ? '7 дней' : range === '30d' ? '30 дней' : '90 дней'}
            </button>
          ))}
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statsCards.map((card, index) => {
          const IconComponent = card.icon
          return (
            <div key={index} className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className={`p-2 rounded-lg ${card.color.replace('text-', 'bg-').replace('600', '100')}`}>
                  <IconComponent className={`h-6 w-6 ${card.color}`} />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">{card.title}</p>
                  <p className="text-2xl font-bold text-gray-900">{card.value}</p>
                </div>
              </div>
              <div className="mt-4 flex items-center">
                {card.change > 0 ? (
                  <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
                ) : (
                  <TrendingDown className="h-4 w-4 text-red-500 mr-1" />
                )}
                <span className={`text-sm font-medium ${
                  card.change > 0 ? 'text-green-600' : 'text-red-600'
                }`}>
                  {Math.abs(card.change)}%
                </span>
                <span className="text-sm text-gray-500 ml-1">за период</span>
              </div>
            </div>
          )
        })}
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Revenue Chart */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Доходы</h3>
          <ResponsiveContainer width="100%" height={300}>
            <AreaChart data={data.revenue}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip formatter={(value) => [`${value} ₽`, 'Доход']} />
              <Area type="monotone" dataKey="amount" stroke="#3B82F6" fill="#3B82F6" fillOpacity={0.1} />
            </AreaChart>
          </ResponsiveContainer>
        </div>

        {/* Users Chart */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Новые пользователи</h3>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={data.users}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="count" fill="#10B981" />
            </BarChart>
          </ResponsiveContainer>
        </div>

        {/* Services Chart */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Популярные услуги</h3>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={data.services}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="count"
              >
                {data.services.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        </div>

        {/* Payment Status Chart */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Статус платежей</h3>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={data.payments}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="status" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="count" fill="#F59E0B" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>
    </div>
  )
}
