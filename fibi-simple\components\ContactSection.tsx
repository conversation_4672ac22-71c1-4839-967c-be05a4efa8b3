import { Phone, Mail, MapPin } from 'lucide-react'
import ContactForm from './ContactForm'

const contactInfo = [
  {
    icon: Phone,
    title: 'Телефон',
    value: '+7 (800) 123-45-67',
    description: 'Звоните с 9:00 до 21:00'
  },
  {
    icon: Mail,
    title: 'Email',
    value: '<EMAIL>',
    description: 'Ответим в течение часа'
  },
  {
    icon: MapPin,
    title: 'Адрес',
    value: 'г. Москва, ул. Примерная, д. 123',
    description: 'Офис открыт с 9:00 до 18:00'
  }
]

export default function ContactSection() {
  return (
    <section id="contact" className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Свяжитесь с нами
          </h2>
          <p className="text-xl text-gray-600">
            Мы всегда готовы помочь и ответить на ваши вопросы
          </p>
        </div>
        
        <div className="grid lg:grid-cols-2 gap-12">
          {/* Contact Info */}
          <div>
            <h3 className="text-2xl font-bold text-gray-900 mb-8">
              Контактная информация
            </h3>
            <div className="space-y-6">
              {contactInfo.map((info, index) => {
                const IconComponent = info.icon
                return (
                  <div key={index} className="flex items-start">
                    <div className="flex-shrink-0">
                      <IconComponent className="h-6 w-6 text-blue-600 mt-1" />
                    </div>
                    <div className="ml-4">
                      <h4 className="text-lg font-semibold text-gray-900">
                        {info.title}
                      </h4>
                      <p className="text-gray-900 font-medium">{info.value}</p>
                      <p className="text-gray-600 text-sm">{info.description}</p>
                    </div>
                  </div>
                )
              })}
            </div>
            
            <div className="mt-8 p-6 bg-blue-50 rounded-lg">
              <h4 className="text-lg font-semibold text-blue-900 mb-2">
                Быстрая консультация
              </h4>
              <p className="text-blue-700 mb-4">
                Оставьте заявку, и наш специалист свяжется с вами в течение 15 минут
              </p>
              <div className="flex items-center text-blue-600">
                <Phone className="h-4 w-4 mr-2" />
                <span className="text-sm">Бесплатная консультация</span>
              </div>
            </div>
          </div>
          
          {/* Contact Form */}
          <div>
            <h3 className="text-2xl font-bold text-gray-900 mb-8">
              Отправить сообщение
            </h3>
            <div className="bg-gray-50 p-6 rounded-lg">
              <ContactForm />
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
