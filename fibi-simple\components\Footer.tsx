import { Phone } from 'lucide-react'

const footerSections = [
  {
    title: 'Услуги',
    links: [
      { name: 'Интернет', href: '#services' },
      { name: 'Телефония', href: '#services' },
      { name: 'Безопасность', href: '#services' }
    ]
  },
  {
    title: 'Компания',
    links: [
      { name: 'О нас', href: '#about' },
      { name: 'Карьер<PERSON>', href: '#' },
      { name: 'Новости', href: '#' }
    ]
  },
  {
    title: 'Поддержка',
    links: [
      { name: 'Помощь', href: '#contact' },
      { name: 'Ко<PERSON>та<PERSON>ты', href: '#contact' },
      { name: 'FAQ', href: '#' }
    ]
  }
]

export default function Footer() {
  return (
    <footer className="bg-gray-900 text-white py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid md:grid-cols-4 gap-8">
          <div>
            <div className="flex items-center mb-4">
              <Phone className="h-6 w-6 mr-2" />
              <span className="text-xl font-bold">Fibi Telecom</span>
            </div>
            <p className="text-gray-400 mb-4">
              Ваш надежный партнер в мире телекоммуникаций
            </p>
            <div className="text-sm text-gray-400">
              <p>Лицензия №123456</p>
              <p>ИНН: 1234567890</p>
            </div>
          </div>
          
          {footerSections.map((section, index) => (
            <div key={index}>
              <h3 className="font-semibold mb-4">{section.title}</h3>
              <ul className="space-y-2 text-gray-400">
                {section.links.map((link, linkIndex) => (
                  <li key={linkIndex}>
                    <a
                      href={link.href}
                      className="hover:text-white transition-colors"
                    >
                      {link.name}
                    </a>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>
        
        <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
          <p>&copy; 2024 Fibi Telecom. Все права защищены.</p>
        </div>
      </div>
    </footer>
  )
}
