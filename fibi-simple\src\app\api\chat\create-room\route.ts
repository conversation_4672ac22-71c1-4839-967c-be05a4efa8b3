import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase-server'
import { pusherServer, getAdminChannelName } from '@/lib/pusher'

export async function POST(request: NextRequest) {
  try {
    const supabase = createClient()
    
    // Проверяем авторизацию
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Необходима авторизация' },
        { status: 401 }
      )
    }

    // Получаем профиль пользователя
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('full_name, role')
      .eq('id', user.id)
      .single()

    if (profileError) {
      return NextResponse.json(
        { error: 'Профиль не найден' },
        { status: 404 }
      )
    }

    // Проверяем, есть ли уже открытый чат у пользователя
    const { data: existingChat, error: existingChatError } = await supabase
      .from('chat_rooms')
      .select('*')
      .eq('user_id', user.id)
      .eq('status', 'open')
      .single()

    if (existingChat) {
      return NextResponse.json({ chatRoom: existingChat })
    }

    // Создаем новый чат
    const { data: chatRoom, error: chatError } = await supabase
      .from('chat_rooms')
      .insert({
        user_id: user.id,
        status: 'open'
      })
      .select()
      .single()

    if (chatError) {
      console.error('Error creating chat room:', chatError)
      return NextResponse.json(
        { error: 'Ошибка создания чата' },
        { status: 500 }
      )
    }

    // Создаем приветственное сообщение
    const { data: welcomeMessage, error: messageError } = await supabase
      .from('chat_messages')
      .insert({
        content: 'Здравствуйте! Как мы можем вам помочь?',
        user_id: 'system',
        user_name: 'Система поддержки',
        user_role: 'admin',
        chat_id: chatRoom.id
      })
      .select()
      .single()

    if (messageError) {
      console.error('Error creating welcome message:', messageError)
    }

    // Уведомляем админов о новом чате
    await pusherServer.trigger(getAdminChannelName(), 'new-chat-room', {
      chatRoom,
      userName: profile.full_name || user.email
    })

    return NextResponse.json({ chatRoom })

  } catch (error) {
    console.error('Chat room creation error:', error)
    return NextResponse.json(
      { error: 'Ошибка создания чата' },
      { status: 500 }
    )
  }
}
